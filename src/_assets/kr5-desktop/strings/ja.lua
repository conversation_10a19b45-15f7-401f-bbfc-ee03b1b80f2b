-- ------------------------------------------------
-- -- WARNING: DO NOT EDIT BY HAND                 
-- -- Generated by kr-i18n/tools/strings-export.lua
-- ------------------------------------------------
return {
["!!!COMMENT_LOCALIZATION_SOURCE"] = "Keywords + testers (fiew for kr2)",
["%d Life"] = "%d ライフ",
["%d Lives"] = "%d ライフ",
["%i sec."] = "%i 秒",
["- if heroes are allowed"] = "- 英雄が使える場合",
["- max lvl allowed"] = "- 許可される最大レベル",
["- max upgrade level allowed"] = "- 許可される最大アップグレードレベル",
["- no heroes"] = "- 英雄なし",
["A good challenge!"] = "手強いチャレンジ！",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_1_NAME"] = "アボミネイテッド・ウィリー",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_2_NAME"] = "アボミネイテッド・ヘンリー",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_3_NAME"] = "アボミネイテッド・ジェフリー",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_4_NAME"] = "便秘のニコラス",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_5_NAME"] = "エドミネーション",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_6_NAME"] = "ホボミネーション",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_7_NAME"] = "オドミネーション",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_8_NAME"] = "アボミネイテッド・セドリック",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_9_NAME"] = "ハルボミネーション",
["ACHIEVEMENT"] = "達成",
["ACHIEVEMENTS"] = "達成",
["ACHIEVEMENTS_TITLE"] = "実績",
["ACHIEVEMENT_AGE_OF_HEROES_DESCRIPTION"] = "ヒロイックモードのキャンペーンチャレンジを全て制覇する。",
["ACHIEVEMENT_AGE_OF_HEROES_NAME"] = "英雄の時代",
["ACHIEVEMENT_ALL_THE_SMALL_THINGS_DESCRIPTION"] = "182のブリンカーを排除する。",
["ACHIEVEMENT_ALL_THE_SMALL_THINGS_NAME"] = "小さなすべてのもの",
["ACHIEVEMENT_ARACHNED_DESCRIPTION"] = "スパイダークイーン、ミガルを倒せ。",
["ACHIEVEMENT_ARACHNED_NAME"] = "武器との別れ",
["ACHIEVEMENT_A_COON_OF_SURPRISES_DESCRIPTION"] = "フレドが脱出できるよう助けよう。",
["ACHIEVEMENT_A_COON_OF_SURPRISES_NAME"] = "驚きの繭",
["ACHIEVEMENT_A_TEST_OF_PROWESS_DESCRIPTION"] = "ステージを3つ星で勝利する。",
["ACHIEVEMENT_A_TEST_OF_PROWESS_NAME"] = "技量のテスト",
["ACHIEVEMENT_BREAKER_OF_CHAINS_DESCRIPTION"] = "カーマイン鉱山で4人のエルフを救う",
["ACHIEVEMENT_BREAKER_OF_CHAINS_NAME"] = "鎖を断つ者",
["ACHIEVEMENT_BUTTERTENTACLES_DESCRIPTION"] = "醜悪な塔を完了し、あなたのユニットがMydriasに捕らえられるのを防ぎます。",
["ACHIEVEMENT_BUTTERTENTACLES_NAME"] = "滑りやすい兵士",
["ACHIEVEMENT_BYE_BYE_BEAUTIFUL_DESCRIPTION"] = "シーレス・ミドリアスを倒す",
["ACHIEVEMENT_BYE_BYE_BEAUTIFUL_NAME"] = "バイバイ、美しい",
["ACHIEVEMENT_CIRCLE_OF_LIFE_DESCRIPTION"] = "新生のアーボレアンのプレゼンテーションに参加する。",
["ACHIEVEMENT_CIRCLE_OF_LIFE_NAME"] = "命の輪",
["ACHIEVEMENT_CLEANSE_THE_KING_DESCRIPTION"] = "リニリアン王を救出せよ",
["ACHIEVEMENT_CLEANSE_THE_KING_NAME"] = "王に栄光を",
["ACHIEVEMENT_CLEANUP_IS_OPTIONAL_DESCRIPTION"] = "戦略的なポイントの瓦礫を片付けずに荒廃した郊外を完了させます。",
["ACHIEVEMENT_CLEANUP_IS_OPTIONAL_NAME"] = "掃除は任意です",
["ACHIEVEMENT_CONJUNTIVICTORY_DESCRIPTION"] = "監視者を倒す。",
["ACHIEVEMENT_CONJUNTIVICTORY_NAME"] = "結膜炎的勝利",
["ACHIEVEMENT_CONQUEROR_OF_THE_VOID_DESCRIPTION"] = "虚空の彼方の各ステージで3つ星を獲得してください。",
["ACHIEVEMENT_CONQUEROR_OF_THE_VOID_NAME"] = "虚無の征服者",
["ACHIEVEMENT_CRAFTING_IN_THE_MINES_DESCRIPTION"] = "ワイルドビーストの巣で三つのポークチョップをすべて集める",
["ACHIEVEMENT_CRAFTING_IN_THE_MINES_NAME"] = "鉱山でのクラフト",
["ACHIEVEMENT_CROWD_CONTROL_DESCRIPTION"] = "穴から肉のベヒモスが現れることなく、腐敗の谷を完了してください。",
["ACHIEVEMENT_CROWD_CONTROL_NAME"] = "群衆制御",
["ACHIEVEMENT_CROW_SCARER_DESCRIPTION"] = "ブリークバレーのすべてのカラスを怖がらせてください。",
["ACHIEVEMENT_CROW_SCARER_NAME"] = "カラスよけ",
["ACHIEVEMENT_CRYSTAL_CLEAR_DESCRIPTION"] = "見捨てられたキャニオンの各ステージで3つ星を獲得してください。",
["ACHIEVEMENT_CRYSTAL_CLEAR_NAME"] = "クリスタルクリア",
["ACHIEVEMENT_DARK_LIEUTENANT_DESCRIPTION"] = "レイリンでレベル10に到達する。",
["ACHIEVEMENT_DARK_LIEUTENANT_NAME"] = "ダーク・ルーテナント",
["ACHIEVEMENT_DARK_RUTHLESSNESS_DESCRIPTION"] = "ダークアーミーのタワーとヒーローのみを使用してステージを勝ち取る",
["ACHIEVEMENT_DARK_RUTHLESSNESS_NAME"] = "冷酷な闇",
["ACHIEVEMENT_DISTURBING_THE_PEACE_DESCRIPTION"] = "ドミニオンドームで作業員の昼休みを邪魔する。",
["ACHIEVEMENT_DISTURBING_THE_PEACE_NAME"] = "平和の乱れ",
["ACHIEVEMENT_DLC1_WIN_BOSS_DESCRIPTION"] = "グリムビアードを倒し、戦争機械の建設を止める。",
["ACHIEVEMENT_DLC1_WIN_BOSS_NAME"] = "自己失業",
["ACHIEVEMENT_DLC2_GATHER_ENVELOPS_DESCRIPTION"] = "テンペスト島でホンバオを8個集めよう。",
["ACHIEVEMENT_DLC2_GATHER_ENVELOPS_NAME"] = "富と繁栄をお祈りします。",
["ACHIEVEMENT_DLC2_WIN_BOSS_KING_DESCRIPTION"] = "砦で牛魔王を倒せ。",
["ACHIEVEMENT_DLC2_WIN_BOSS_KING_NAME"] = "猿の王の帰還",
["ACHIEVEMENT_DLC2_WIN_BOSS_PRINCESS_DESCRIPTION"] = "鉄扇姫とその水軍を打ち倒せ。",
["ACHIEVEMENT_DLC2_WIN_BOSS_PRINCESS_NAME"] = "邪悪な風が立ち上がる",
["ACHIEVEMENT_DLC2_WIN_BOSS_REDBOY_DESCRIPTION"] = "紅孩児とその火の軍勢を倒せ。",
["ACHIEVEMENT_DLC2_WIN_BOSS_REDBOY_NAME"] = "すべてが変わった……",
["ACHIEVEMENT_DOMO_ARIGATO_DESCRIPTION"] = "コロッサルコアで20体の敵を巨大な拳で押しつぶす。",
["ACHIEVEMENT_DOMO_ARIGATO_NAME"] = "ドーモアリガト",
["ACHIEVEMENT_FACTORY_STRIKE_DESCRIPTION"] = "グリムビアードに機械を操作させずにフランティックアセンブリーを完了してください。",
["ACHIEVEMENT_FACTORY_STRIKE_NAME"] = "工場ストライキ",
["ACHIEVEMENT_FIELD_TRIP_RUINER_DESCRIPTION"] = "キャンパーの火を消してください。",
["ACHIEVEMENT_FIELD_TRIP_RUINER_NAME"] = "遠足の台無し",
["ACHIEVEMENT_FOREST_PROTECTOR_DESCRIPTION"] = "ニュルとレベル10に到達する。",
["ACHIEVEMENT_FOREST_PROTECTOR_NAME"] = "森の守り手",
["ACHIEVEMENT_GARBAGE_DISPOSAL_DESCRIPTION"] = "10体の狂った発明家をスクラップドローンを生成させる前に排除する。",
["ACHIEVEMENT_GARBAGE_DISPOSAL_NAME"] = "ゴミ処理",
["ACHIEVEMENT_GEM_SPILLER_DESCRIPTION"] = "宝石の籠を全て壊してください。",
["ACHIEVEMENT_GEM_SPILLER_NAME"] = "宝石をこぼす者",
["ACHIEVEMENT_GET_THE_PARTY_STARTED_DESCRIPTION"] = "パズルを解いてバンドを召喚してください。",
["ACHIEVEMENT_GET_THE_PARTY_STARTED_NAME"] = "パーティーを始めよう",
["ACHIEVEMENT_GIFT_OF_LIFE_DESCRIPTION"] = "レプリケーションチャンバーでクローン実験を解放する。",
["ACHIEVEMENT_GIFT_OF_LIFE_NAME"] = "命の贈り物",
["ACHIEVEMENT_GREENLIT_ALLIES_DESCRIPTION"] = "10のアーボリアン・ソーンスピアを召喚する",
["ACHIEVEMENT_GREENLIT_ALLIES_NAME"] = "承認された同盟",
["ACHIEVEMENT_HAIL_TO_THE_K_BABY_DESCRIPTION"] = "ワニ王を見つけ出せ。",
["ACHIEVEMENT_HAIL_TO_THE_K_BABY_NAME"] = "ケーに敬礼、ベイビー！",
["ACHIEVEMENT_HEARTLESS_VICTORY_DESCRIPTION"] = "森の心臓を、アーボレアンの心臓の力を使わずにクリアする。",
["ACHIEVEMENT_HEARTLESS_VICTORY_NAME"] = "冷酷な勝利",
["ACHIEVEMENT_INTO_THE_OGREVERSE_DESCRIPTION"] = "謎の蜘蛛人間の秘密を見つけよう。",
["ACHIEVEMENT_INTO_THE_OGREVERSE_NAME"] = "無愛想な隣人",
["ACHIEVEMENT_IRONCLAD_DESCRIPTION"] = "アイアンモードのキャンペーンチャレンジを全て制覇する。",
["ACHIEVEMENT_IRONCLAD_NAME"] = "鉄鎧",
["ACHIEVEMENT_ITS_A_SECRET_TO_EVERYONE_DESCRIPTION"] = "ランクが5ルピー釣るのを手伝って",
["ACHIEVEMENT_ITS_A_SECRET_TO_EVERYONE_NAME"] = "みんなには秘密だ",
["ACHIEVEMENT_KEPT_YOU_WAITING_DESCRIPTION"] = "コロッサルコアでこっそりとした兵士を見つける。",
["ACHIEVEMENT_KEPT_YOU_WAITING_NAME"] = "待たせたな、な？",
["ACHIEVEMENT_LEARNING_THE_ROPES_DESCRIPTION"] = "チュートリアルを3つ星で終える。",
["ACHIEVEMENT_LEARNING_THE_ROPES_NAME"] = "ロープを学ぶ",
["ACHIEVEMENT_LINIREAN_RESISTANCE_DESCRIPTION"] = "リニレアンの塔とヒーローのみを使用してステージに勝利してください。",
["ACHIEVEMENT_LINIREAN_RESISTANCE_NAME"] = "リニリアンレジスタンス",
["ACHIEVEMENT_LUCAS_SPIDER_DESCRIPTION"] = "ルーカスが満足するまで遊ぼう。",
["ACHIEVEMENT_LUCAS_SPIDER_NAME"] = "クモのルーカス",
["ACHIEVEMENT_MASTER_TACTICIAN_DESCRIPTION"] = "不可能な難易度でキャンペーンを完了する。",
["ACHIEVEMENT_MASTER_TACTICIAN_NAME"] = "戦術の達人",
["ACHIEVEMENT_MECHANICAL_BURNOUT_DESCRIPTION"] = "ダークスチールゲートで機械を過剰に供給する。",
["ACHIEVEMENT_MECHANICAL_BURNOUT_NAME"] = "機械的な燃え尽き",
["ACHIEVEMENT_MIGHTY_III_DESCRIPTION"] = "10000体の敵を倒す。",
["ACHIEVEMENT_MIGHTY_III_NAME"] = "マイティIII",
["ACHIEVEMENT_MIGHTY_II_DESCRIPTION"] = "3000体の敵を倒す。",
["ACHIEVEMENT_MIGHTY_II_NAME"] = "マイティII",
["ACHIEVEMENT_MIGHTY_I_DESCRIPTION"] = "500体の敵を倒す。",
["ACHIEVEMENT_MIGHTY_I_NAME"] = "マイティI",
["ACHIEVEMENT_MOST_DELICIOUS_DESCRIPTION"] = "ビギー（樹木人）にはちみつを少し与えてあげてください",
["ACHIEVEMENT_MOST_DELICIOUS_NAME"] = "最も美味しい",
["ACHIEVEMENT_NATURES_WRATH_DESCRIPTION"] = "樹人の心を使って30の敵を倒す。",
["ACHIEVEMENT_NATURES_WRATH_NAME"] = "自然の怒り",
["ACHIEVEMENT_NONE_SHALL_PASS_DESCRIPTION"] = "ワイルドビーストの巣をクリアし、余計な敵をドアから通さないでください。",
["ACHIEVEMENT_NONE_SHALL_PASS_NAME"] = "誰も通さない！",
["ACHIEVEMENT_NOT_A_MOMENT_TO_WASTE_DESCRIPTION"] = "15の波を早めに呼び出す。",
["ACHIEVEMENT_NOT_A_MOMENT_TO_WASTE_NAME"] = "無駄にできる瞬間はない",
["ACHIEVEMENT_NO_FLY_ZONE_DESCRIPTION"] = "バルーニング・スパイダーを50体倒せ。",
["ACHIEVEMENT_NO_FLY_ZONE_NAME"] = "飛行禁止区域",
["ACHIEVEMENT_OBLITERATE_DESCRIPTION"] = "巨大な脅威の各ステージで禁じられたロボットの部品を見つける。",
["ACHIEVEMENT_OBLITERATE_NAME"] = "壊滅！",
["ACHIEVEMENT_ONE_SHOT_TOWER_DESCRIPTION"] = "ダークレイタワーのビーム一本で敵を10体排除します。",
["ACHIEVEMENT_ONE_SHOT_TOWER_NAME"] = "栄光への一撃",
["ACHIEVEMENT_OUTBACK_BARBEQUICK_DESCRIPTION"] = "不可能な難易度でジャンプする前にゴアグラインドを倒す",
["ACHIEVEMENT_OUTBACK_BARBEQUICK_NAME"] = "もつれた",
["ACHIEVEMENT_OVER_THE_EDGE_DESCRIPTION"] = "樹木の上からアーボリアンを押し下ろしてください。",
["ACHIEVEMENT_OVER_THE_EDGE_NAME"] = "ゲームオーバー",
["ACHIEVEMENT_OVINE_JOURNALISM_DESCRIPTION"] = "各キャンペーンの地形でSheepyを見つけてください。",
["ACHIEVEMENT_OVINE_JOURNALISM_NAME"] = "羊のジャーナリズム",
["ACHIEVEMENT_PEST_CONTROL_DESCRIPTION"] = "300のグレアリングを倒す。",
["ACHIEVEMENT_PEST_CONTROL_NAME"] = "害虫駆除",
["ACHIEVEMENT_PLAYFUL_FRIENDS_DESCRIPTION"] = "森の心の中で全ての樹木人と「穴倉」を遊ぶ。",
["ACHIEVEMENT_PLAYFUL_FRIENDS_NAME"] = "遊び好きな友達",
["ACHIEVEMENT_PORKS_OFF_THE_MENU_DESCRIPTION"] = "ゴアグラインドを倒す。",
["ACHIEVEMENT_PORKS_OFF_THE_MENU_NAME"] = "ポークはメニューから外れた",
["ACHIEVEMENT_PROMOTION_DENIED_DESCRIPTION"] = "カルトの司祭を30人殺し、彼らが憎悪に変わる前に倒してください。",
["ACHIEVEMENT_PROMOTION_DENIED_NAME"] = "昇進拒否",
["ACHIEVEMENT_ROCK_BEATS_ROCK_DESCRIPTION"] = "像を自身に勝たせろ。",
["ACHIEVEMENT_ROCK_BEATS_ROCK_NAME"] = "ロック・ビーッツ…ロック？",
["ACHIEVEMENT_ROOM_achievement_claim"] = "報酬を請求する！",
["ACHIEVEMENT_ROYAL_CAPTAIN_DESCRIPTION"] = "ヴェスパーでレベル10に到達する。",
["ACHIEVEMENT_ROYAL_CAPTAIN_NAME"] = "ロイヤルキャプテン",
["ACHIEVEMENT_RUNEQUEST_DESCRIPTION"] = "エヴェラディアントの森を通じてすべての6つのルーンを活性化させよう。",
["ACHIEVEMENT_RUNEQUEST_NAME"] = "ルーンクエスト",
["ACHIEVEMENT_RUST_IN_PEACE_DESCRIPTION"] = "アニメーテッドアーマーが再生することを許さずにステージをクリアせよ。",
["ACHIEVEMENT_RUST_IN_PEACE_NAME"] = "ラスト・イン・ピース",
["ACHIEVEMENT_SAVIOUR_OF_THE_FOREST_DESCRIPTION"] = "ステージをクリアし、一つもアルボリアンの花を失わないでください。",
["ACHIEVEMENT_SAVIOUR_OF_THE_FOREST_NAME"] = "森の救世主",
["ACHIEVEMENT_SAVIOUR_OF_THE_GREEN_DESCRIPTION"] = "エヴェラディアントの森の各ステージで3つ星を獲得してください。",
["ACHIEVEMENT_SAVIOUR_OF_THE_GREEN_NAME"] = "グリーンの救世主",
["ACHIEVEMENT_SCRAMBLED_EGGS_DESCRIPTION"] = "孵化前に50のクロキンダーを倒せ。",
["ACHIEVEMENT_SCRAMBLED_EGGS_NAME"] = "スクランブルエッグ",
["ACHIEVEMENT_SEASONED_GENERAL_DESCRIPTION"] = "ベテラン難易度でキャンペーンを完了する。",
["ACHIEVEMENT_SEASONED_GENERAL_NAME"] = "熟練した将軍",
["ACHIEVEMENT_SEE_YA_LATER_ALLIGATOR_DESCRIPTION"] = "アボミノール、食らう者を倒せ。",
["ACHIEVEMENT_SEE_YA_LATER_ALLIGATOR_NAME"] = "またね、アリゲーター",
["ACHIEVEMENT_SHUT_YOUR_MOUTH_DESCRIPTION"] = "ドミニオンドームを完了し、グリムビアードがタワーを燃やさないようにする。",
["ACHIEVEMENT_SHUT_YOUR_MOUTH_NAME"] = "口を閉じろ！",
["ACHIEVEMENT_SIGNATURE_TECHNIQUES_DESCRIPTION"] = "ヒーローパワーを500回使う。",
["ACHIEVEMENT_SIGNATURE_TECHNIQUES_NAME"] = "シグネチャーテクニック",
["ACHIEVEMENT_SILVER_FOR_MONSTERS_DESCRIPTION"] = "ゲルハルトが木の怪物を倒すのを助ける。",
["ACHIEVEMENT_SILVER_FOR_MONSTERS_NAME"] = "モンスターのための銀",
["ACHIEVEMENT_SMOOTH_OPER_GATOR_DESCRIPTION"] = "フレンドリーなゲーターがボートを始動させるのを手伝ってください。",
["ACHIEVEMENT_SMOOTH_OPER_GATOR_NAME"] = "スムーズ・オペルゲーター",
["ACHIEVEMENT_SPECTRAL_FURY_DESCRIPTION"] = "ナヴィラを倒し、リヴァナントの侵略を止めろ。",
["ACHIEVEMENT_SPECTRAL_FURY_NAME"] = "スペクトラルフューリー",
["ACHIEVEMENT_STARLIGHT_DESCRIPTION"] = "フレドとサミーが巨大なクモから逃げるのを手伝ってください。",
["ACHIEVEMENT_STARLIGHT_NAME"] = "星明り",
["ACHIEVEMENT_TAKE_ME_HOME_DESCRIPTION"] = "リフ・ザ・ゴブリンを彼の故郷の次元に戻して。",
["ACHIEVEMENT_TAKE_ME_HOME_NAME"] = "テイク・オン・ミー",
["ACHIEVEMENT_THE_CAVALRY_IS_HERE_DESCRIPTION"] = "1000の援軍を召喚する。",
["ACHIEVEMENT_THE_CAVALRY_IS_HERE_NAME"] = "騎兵隊、到着！",
["ACHIEVEMENT_TIPPING_THE_SCALES_DESCRIPTION"] = "ロビン・ウッドを川に投げろ。",
["ACHIEVEMENT_TIPPING_THE_SCALES_NAME"] = "天秤を傾ける",
["ACHIEVEMENT_TREE_HUGGER_DESCRIPTION"] = "少なくとも1本のウィアードウッドを残して霧の廃墟をクリアせよ。",
["ACHIEVEMENT_TREE_HUGGER_NAME"] = "ツリーハガー",
["ACHIEVEMENT_TURN_A_BLIND_EYE_DESCRIPTION"] = "眩光の効果がある間に100の腐敗のスポーンを殺す。",
["ACHIEVEMENT_TURN_A_BLIND_EYE_NAME"] = "見て見ぬふりをする",
["ACHIEVEMENT_UNBOUND_VICTORY_DESCRIPTION"] = "悪夢が装甲悪夢に変わることなく、邪悪な交差点を完了してください。",
["ACHIEVEMENT_UNBOUND_VICTORY_NAME"] = "解き放たれた勝利",
["ACHIEVEMENT_UNENDING_RICHES_DESCRIPTION"] = "合計で150000ゴールドを集める。",
["ACHIEVEMENT_UNENDING_RICHES_NAME"] = "終わりなき富",
["ACHIEVEMENT_UNTAMED_BEAST_DESCRIPTION"] = "グリムソンとレベル10に到達する。",
["ACHIEVEMENT_UNTAMED_BEAST_NAME"] = "手懐けられない獣",
["ACHIEVEMENT_WAR_MASONRY_DESCRIPTION"] = "100の塔を建てる。",
["ACHIEVEMENT_WAR_MASONRY_NAME"] = "戦争の石工",
["ACHIEVEMENT_WEIRDER_THINGS_DESCRIPTION"] = "アーニーとダストンが枯れた平原でブリンカーを撃退するのを手伝って",
["ACHIEVEMENT_WEIRDER_THINGS_NAME"] = "より奇妙なこと",
["ACHIEVEMENT_WE_ARE_ALL_MAD_HERE_DESCRIPTION"] = "アンデッドフューリーの各キャンペーンステージで見つけにくい猫を見つけろ。",
["ACHIEVEMENT_WE_ARE_ALL_MAD_HERE_NAME"] = "我ら皆、狂っている",
["ACHIEVEMENT_WE_RE_NOT_GONNA_TAKE_IT_DESCRIPTION"] = "ねじれたシスターを15体倒して、ナイトメアが発生する前に阻止してください。",
["ACHIEVEMENT_WE_RE_NOT_GONNA_TAKE_IT_NAME"] = "受け入れない",
["ACHIEVEMENT_WOBBA_LUBBA_DUB_DUB_DESCRIPTION"] = "ニックとマーティのポータルガンを修理します。",
["ACHIEVEMENT_WOBBA_LUBBA_DUB_DUB_NAME"] = "ヲッバ-ルッバ-ダブ-ダブ!",
["ACHIEVEMENT_YOU_SHALL_NOT_CAST_DESCRIPTION"] = "不可能な難易度で占い師ミドリアスに投影をさせずに堕落したデナスを倒す",
["ACHIEVEMENT_YOU_SHALL_NOT_CAST_NAME"] = "発動させない！",
["ADS_MESSAGE_OK"] = "OK",
["ADS_MESSAGE_TITLE"] = "もっとジェムを入手",
["ALERT_VERSION"] = "ゲームの新バージョンが公開されました。ストアからダウンロードしてください。",
["APPLY_SETTINGS_AND_RESTART"] = "変更を適用するには再起動しますか?",
["ARCHER TOWER"] = "アーチャータワー",
["ARE YOU SURE YOU WANT TO QUIT?"] = "本当に終了しますか？",
["ARMORED ENEMIES!"] = "重装甲の敵だ！",
["ARTILLERY"] = "砲兵",
["Achievements"] = "実績",
["Advanced"] = "上級",
["Average"] = "平均的",
["BARRACKS"] = "兵舎",
["BOSS_BULL_KING_DESCRIPTION"] = "冷酷かつ権威的な指導者であり、戦争のベテランにして現実的な戦略家。計り知れない力、執念深い性格、そして武芸の腕前で有名。",
["BOSS_BULL_KING_EXTRA"] = "-高い装甲と魔法耐性\n- ユニットとタワーへの広範囲スタン",
["BOSS_BULL_KING_NAME"] = "牛魔王",
["BOSS_CORRUPTED_DENAS_DESCRIPTION"] = "リニレアの敗れた王、今や監察者のカルトの暗黒の力によって巨大な憎悪へと変わった。",
["BOSS_CORRUPTED_DENAS_EXTRA"] = "- グレアリングを生成する ",
["BOSS_CORRUPTED_DENAS_NAME"] = "コラプテッドデナス",
["BOSS_CROCS_DESCRIPTION"] = "具現化した飢餓、制御されなければ世界自体を貪り食うことができる古代の存在。",
["BOSS_CROCS_EXTRA"] = "- タワーを食べる\n- 空腹を満たした後に進化する\n- コーキンダーを召喚",
["BOSS_CROCS_LVL1_DESCRIPTION"] = "具現化した飢餓、制御されなければ世界自体を貪り食うことができる古代の存在。",
["BOSS_CROCS_LVL1_EXTRA"] = "- タワーを食べる\n- 空腹を満たした後に進化する\n- コーキンダーを召喚",
["BOSS_CROCS_LVL1_NAME"] = "アボミノール",
["BOSS_CROCS_LVL2_DESCRIPTION"] = "具現化した飢餓、制御されなければ世界自体を貪り食うことができる古代の存在。",
["BOSS_CROCS_LVL2_EXTRA"] = "- タワーを食べる\n- 空腹を満たした後に進化する\n- コーキンダーを召喚",
["BOSS_CROCS_LVL2_NAME"] = "アボミノール",
["BOSS_CROCS_LVL3_DESCRIPTION"] = "具現化した飢餓、制御されなければ世界自体を貪り食うことができる古代の存在。",
["BOSS_CROCS_LVL3_EXTRA"] = "- タワーを食べる\n- 空腹を満たした後に進化する\n- コーキンダーを召喚",
["BOSS_CROCS_LVL3_NAME"] = "アボミノール",
["BOSS_CROCS_LVL4_DESCRIPTION"] = "具現化した飢餓、制御されなければ世界自体を貪り食うことができる古代の存在。",
["BOSS_CROCS_LVL4_EXTRA"] = "- タワーを食べる\n- 空腹を満たした後に進化する\n- コーキンダーを召喚",
["BOSS_CROCS_LVL4_NAME"] = "アボミノール",
["BOSS_CROCS_LVL5_DESCRIPTION"] = "具現化した飢餓、制御されなければ世界自体を貪り食うことができる古代の存在。",
["BOSS_CROCS_LVL5_EXTRA"] = "- タワーを食べる\n- 空腹を満たした後に進化する\n- コーキンダーを召喚",
["BOSS_CROCS_LVL5_NAME"] = "アボミノール",
["BOSS_CROCS_NAME"] = "アボミノール",
["BOSS_CULT_LEADER_DESCRIPTION"] = "カルトの現在のリーダーであるミドリアスは、監督者の手として世界の侵略を指揮している。",
["BOSS_CULT_LEADER_EXTRA"] = "- ブロックされていない間は、高いアーマーと魔法耐性\n - 高い範囲ダメージ",
["BOSS_CULT_LEADER_NAME"] = "予言者ミドリアス",
["BOSS_GRYMBEARD_DESCRIPTION"] = "危険なほど狂気に満ちた、誇大妄想のエゴイストなドワーフ。",
["BOSS_GRYMBEARD_EXTRA"] = "- プレイヤーユニットに向けてロケットフィストを発射する",
["BOSS_GRYMBEARD_NAME"] = "グリムビアード",
["BOSS_MACHINIST_DESCRIPTION"] = "この最新の発明に乗り、グリムビアードは敵を追い火と金属の雨を降らせる。",
["BOSS_MACHINIST_EXTRA"] = "- 飛行ユニット\n- ユニットにスクラップを発射する",
["BOSS_MACHINIST_NAME"] = "グリムビアード",
["BOSS_NAVIRA_DESCRIPTION"] = "恩寵から堕ち、禁断の死の魔法に手を伸ばすナヴィラは、エルフたちに栄光を取り戻すことを目指している。",
["BOSS_NAVIRA_EXTRA"] = "- ファイアボールでタワーをブロックする\n- ブロック不可能な竜巻に変身する",
["BOSS_NAVIRA_NAME"] = "ナヴィラ",
["BOSS_PIG_DESCRIPTION"] = "野獣の王と自称する唯一の者が、巨大なフレイルを使って敵を粉砕する。",
["BOSS_PIG_EXTRA"] = "- 道を跳んで大きな距離を跳ぶ",
["BOSS_PIG_NAME"] = "血肉挽歌",
["BOSS_PRINCESS_IRON_FAN_DESCRIPTION"] = "優雅でありながら致命的な鉄扇公主は、炎を消し嵐を起こすことができる伝説の鉄扇を操る。",
["BOSS_PRINCESS_IRON_FAN_EXTRA"] = "- 自身のクローンを作る\n- ヒーローをフラスコに閉じ込める\n- タワーを敵の出現装置に変える",
["BOSS_PRINCESS_IRON_FAN_NAME"] = "Princess Iron Fan",
["BOSS_REDBOY_TEEN_DESCRIPTION"] = "炎と誇り高き若き悪魔の王子。短気で傲慢、そして容赦ない野心家。三昧火の使い手で、槍の達人。",
["BOSS_REDBOY_TEEN_EXTRA"] = "- 広範囲にダメージを与える攻撃\n- ドラゴンに命じてタワーをスタンさせる",
["BOSS_REDBOY_TEEN_NAME"] = "Red Boy",
["BOSS_SPIDER_QUEEN_DESCRIPTION"] = "古代のスパイダークイーン、正当な領地を取り戻すために長い眠りから目覚めた原初の存在。",
["BOSS_SPIDER_QUEEN_EXTRA"] = "- タワーを気絶させる\n- 周囲の敵から生命を吸収する\n- ライフスティール・スパイダーを召喚する\n- 目にクモの巣を投げつける",
["BOSS_SPIDER_QUEEN_NAME"] = "ミガレ",
["BRIEFING_LEVEL_WARNING"] = "このキャンペーンは難易度が高いです。",
["BUILD HERE!"] = "ここに建設するんだ！",
["BUTTON_BUG_CRASH"] = "ゲームの不具合",
["BUTTON_BUG_OTHER"] = "その他",
["BUTTON_BUG_REPORT"] = "エラー",
["BUTTON_BUY"] = "購入",
["BUTTON_BUY_UPGRADE"] = "アップグレードを購入",
["BUTTON_CLOSE"] = "閉じる",
["BUTTON_CONFIRM"] = "同意",
["BUTTON_CONTINUE"] = "続行",
["BUTTON_DISABLE"] = "無効にする",
["BUTTON_DONE"] = "OK",
["BUTTON_ENDLESS_QUIT"] = "終了",
["BUTTON_ENDLESS_TRYAGAIN"] = "再トライ",
["BUTTON_GET_GEMS"] = "アイテムを入手",
["BUTTON_LEVEL_SELECT_FIGHT"] = "戦え！",
["BUTTON_LOST_CONTENT"] = "失われたコンテンツ",
["BUTTON_MAIN_MENU"] = "メインメニュー",
["BUTTON_NO"] = "いいえ",
["BUTTON_OK"] = "OK!",
["BUTTON_OPEN"] = "開く",
["BUTTON_QUIT"] = "終了",
["BUTTON_RESET"] = "リセット",
["BUTTON_RESTART"] = "再スタート",
["BUTTON_RESUME"] = "再開",
["BUTTON_TO_BATTLE_1"] = "バトルに",
["BUTTON_TO_BATTLE_2"] = "向かう",
["BUTTON_UNDO"] = "取消",
["BUTTON_YES"] = "はい",
["BUY UPGRADES!"] = "アップグレード を購入！",
["Basic"] = "ノーマル",
["Basic Tower Types"] = "タワーのノーマルタイプ",
["CARD_REWARDS_CAMPAIGN"] = "新しいキャンペーン",
["CARD_REWARDS_DLC_1"] = "巨大な脅威",
["CARD_REWARDS_DLC_2"] = "悟空の旅",
["CARD_REWARDS_HERO"] = "新しいヒーロー！",
["CARD_REWARDS_TOWER"] = "新しいタワー！",
["CARD_REWARDS_TOWER_LEVEL"] = "新しいタワーレベル！",
["CARD_REWARDS_TOWER_LEVEL_PREFIX"] = "レベル",
["CARD_REWARDS_UPDATE_01"] = "不滅の激怒",
["CARD_REWARDS_UPDATE_02"] = "古い飢餓",
["CARD_REWARDS_UPDATE_03"] = "アラクノフォビア",
["CARD_REWARDS_UPGRADES"] = "アップグレードポイント！",
["CArmor0"] = "無し",
["CArmor1"] = "低",
["CArmor2"] = "中",
["CArmor3"] = "高",
["CArmor4"] = "素晴らしい",
["CArmor9"] = "耐性",
["CArmorSmall0"] = "無し",
["CArmorSmall1"] = "低",
["CArmorSmall2"] = "中",
["CArmorSmall3"] = "高",
["CArmorSmall4"] = "偉大",
["CArmorSmall9"] = "耐性",
["CHANGE_LANGUAGE_QUESTION"] = "本当に言語設定を変更しますか？",
["CINEMATICS_TAP_TO_CONTINUE_KR1"] = "クリックして続ける…",
["CINEMATICS_TAP_TO_CONTINUE_KR2"] = "クリックして続ける…",
["CINEMATICS_TAP_TO_CONTINUE_KR3"] = "クリックして続ける…",
["CINEMATICS_TAP_TO_CONTINUE_KR5"] = "クリックして続ける…",
["CLAIM_GIFT"] = "ギフトを受け取る",
["CLICK HERE TO SKIP.\nPLEASE DON'T"] = "ここをクリックしてスキップ。\n以下を行わないでください：",
["CLICK HERE!"] = "ここをクリック！",
["CLICK ON THE ROAD"] = "道をクリック",
["CLICK TO CALL IT EARLY"] = "クリックして早く呼び込む",
["CLOUDSYNC_PLEASE_WAIT"] = "クラウドに保存されたゲームを更新しています...",
["CLOUD_DIALOG_NO"] = "いいえ",
["CLOUD_DIALOG_OK"] = "OK",
["CLOUD_DIALOG_YES"] = "はい",
["CLOUD_DOWNLOAD_QUESTION"] = "iCloud からセーブデータをダウンロードしますか？",
["CLOUD_DOWNLOAD_TITLE"] = "iCloud からダウンロード",
["CLOUD_SAVE"] = "クラウドセーブ",
["CLOUD_SAVE_DISABLE_EXTRA"] = "注意:ゲームをアンインストールした場合、ゲームのセーブデータを失う恐れがあります",
["CLOUD_SAVE_DISABLE_GENERIC_DESCRIPTION"] = "クラウドにゲーム進行をセーブする機能を本当に無効にしますか？",
["CLOUD_SAVE_OFF"] = "クラウドストレージオフ",
["CLOUD_SAVE_ON"] = "アクティブなクラウドストレージ",
["CLOUD_UPLOAD_QUESTION"] = "セーブデータを iCloud にアップロードしますか？",
["CLOUD_UPLOAD_TITLE"] = "iCloud にアップロード",
["COMIC_10_1_KR5_KR5"] = "私を解放しろ！王国のために最善を尽くしているんだ！",
["COMIC_10_2_KR5_KR5"] = "この冒涜をやめろ、兄弟。これがエルフのやり方ではない。",
["COMIC_10_3_KR5_KR5"] = "ありがとう、私の古い弟子よ。ここからは私たちが引き継ぐ。",
["COMIC_10_4_KR5_KR5"] = "後に、キャンプで。",
["COMIC_10_5_KR5_KR5"] = "それで...ヴェズナンは信頼できると確信しているのか？",
["COMIC_10_6_KR5_KR5"] = "彼を監視しているが、今のところ彼は大人しくしているようだ。",
["COMIC_10_7_KR5_KR5"] = "「...でも、今のところは大人しくしているようだ。」",
["COMIC_10_8_KR5_KR5"] = "へえ。今のところはね...",
["COMIC_11_1_KR5_KR5"] = "沼地が目覚めたようだ...",
["COMIC_11_2_KR5_KR5"] = "...私たちを見ているかのように...",
["COMIC_11_3_KR5_KR5"] = "...進んで潜んでいる...",
["COMIC_11_4_KR5_KR5"] = "...私たちを飲み込む準備ができている。",
["COMIC_11_5_KR5_KR5"] = "注意して！",
["COMIC_11_6_KR5_KR5"] = "私たちは攻撃されています！",
["COMIC_11_7_KR5_KR5"] = "行け、小さなウィスプよ！私たちの安全は君の急ぎにかかっている！",
["COMIC_12_1_KR5_KR5"] = "単にあなたを閉じ込めることは間違いだった。繰り返さない。",
["COMIC_12_2_KR5_KR5"] = "いやああああ!!!",
["COMIC_12_3_KR5_KR5"] = "永遠にあなたを追放します！！",
["COMIC_12_4_KR5_KR5"] = "コフ！",
["COMIC_12_5_KR5_KR5"] = "コフ、コフ！",
["COMIC_12_6_KR5_KR5"] = "うーん、練習不足かもしれないな。",
["COMIC_13_1_KR5_KR5"] = "彼らはそれを狂気だと言った。",
["COMIC_13_2_KR5_KR5"] = "そんな武器は不可能だと言った。",
["COMIC_13_3_KR5_KR5"] = "しかし、間もなく彼らは自分たちがどれほど間違っていたかを知り…",
["COMIC_13_4_KR5_KR5"] = "…グリムビアードの天才の前に屈するだろう！",
["COMIC_14_1_KR5_KR5"] = "私たちは彼らをどうするつもりですか？",
["COMIC_14_2_KR5_KR5"] = "私に任せて！",
["COMIC_14_3_KR5_KR5"] = "ちょうどいい場所を知っている。",
["COMIC_14_4_KR5_KR5"] = "これで終わりですか？",
["COMIC_14_5_KR5_KR5"] = "グリムビアードをセルで腐らせる？",
["COMIC_14_6_KR5_KR5"] = "全く逆だよ、私の小さな友達…",
["COMIC_14_7_KR5_KR5"] = "...君の大きな頭脳のために大きな計画があるんだ！",
["COMIC_15_10_KR5_KR5"] = "…だが、無事とは言えない。",
["COMIC_15_1_KR5_KR5"] = "山のどこかに。",
["COMIC_15_2_KR5_KR5"] = "おい、ゴブリン！",
["COMIC_15_3_KR5_KR5"] = "働け！",
["COMIC_15_4_KR5_KR5"] = "お前は伝令を届けなければならない。",
["COMIC_15_5_KR5_KR5"] = "もっと斥候を派遣すべきだ。あのカルト信者どもがうろついている限り、安心できない。",
["COMIC_15_6_KR5_KR5"] = "ウィスプを送って支援できるかもしれない…",
["COMIC_15_7_KR5_KR5"] = "ダークロード！緊急の知らせです！",
["COMIC_15_8_KR5_KR5"] = "まあ…",
["COMIC_15_9_KR5_KR5"] = "斥候を見つけたが…",
["COMIC_16_1_KR5_KR5"] = "私は復讐を果たす！",
["COMIC_16_2_KR5_KR5"] = "姉さん…な、なにぃ！？",
["COMIC_17_10_KR5_KR5"] = "止めなければ、すべての王国が滅ぼされてしまう！",
["COMIC_17_11_KR5_KR5"] = "彼を助けなければ！",
["COMIC_17_12_KR5_KR5"] = "ああ、そうだな。",
["COMIC_17_13_KR5_KR5"] = "そうだな……",
["COMIC_17_1_KR5_KR5"] = "素敵な午後ですね？",
["COMIC_17_2_KR5_KR5"] = "この平和に慣れてしまいそうだ。",
["COMIC_17_3_KR5_KR5"] = "やめておいた方がいい。",
["COMIC_17_4_KR5_KR5"] = "太陽なのか！？ 手を振ってくれればよかったのに…",
["COMIC_17_5_KR5_KR5"] = "みんな、大変なことが起きたんだ……",
["COMIC_17_6_KR5_KR5"] = "カメの中で平和に瞑想していたら…",
["COMIC_17_7_KR5_KR5"] = "三人の魔王が突然現れた！",
["COMIC_17_8_KR5_KR5"] = "言うまでもなく、私は勇敢に戦ったが…",
["COMIC_17_9_KR5_KR5"] = "不名誉な方法で私の天球を奪ったのです！",
["COMIC_18_1_KR5_KR5"] = "牛魔王の棲み処の岸辺近くで…",
["COMIC_18_2_KR5_KR5"] = "目標発見！",
["COMIC_18_3_KR5_KR5"] = "あの要塞を吹き飛ばそう！",
["COMIC_18_4_KR5_KR5"] = "俺の城壁はお前らの小石なんて笑っちまうぞ！",
["COMIC_18_5_KR5_KR5"] = "リニレアのために！",
["COMIC_18_6_KR5_KR5"] = "下がれ、皆！ここに突破口が必要だ！",
["COMIC_19_1_KR5_KR5"] = "天の球はお前たちの管理下には置けない、馬鹿げている！",
["COMIC_19_2_KR5_KR5"] = "ああ、それ気をつけろよ、相棒。",
["COMIC_19_3_KR5_KR5"] = "とても賢明だったな、高貴な猿よ！",
["COMIC_19_4_KR5_KR5"] = "お前たち3人をどうしてやろうか？",
["COMIC_1_1_KR5"] = "この地に到着してから1ヶ月が経ちました、私たちは失われた王を探しています...",
["COMIC_1_2B_KR5"] = "...Vez'nan、暗黒の魔法使いに追放された後で。",
["COMIC_1_4_KR5"] = "場所を見つけてキャンプを設置し、力を回復することにしました...",
["COMIC_1_5_KR5"] = "...平和に...",
["COMIC_1_8_KR5"] = "...しかし、それは今終わったようだ。",
["COMIC_2_1_KR5"] = "万歳!",
["COMIC_2_3_KR5"] = "ヴェズナン?!",
["COMIC_2_4a_KR5"] = "落ち着いて... 提案に来たんだ...",
["COMIC_2_4b_KR5"] = "...取引を。",
["COMIC_2_5_KR5"] = "私たちの王国に何をしたというのか？",
["COMIC_2_6_KR5"] = "デナス王の目を開かせる必要があった。",
["COMIC_2_7_KR5"] = "王国を蝕む危険を見ようとしなかった。",
["COMIC_2_8_1_KR5"] = "でも、あなたの王を見つけましょう...",
["COMIC_2_8_2_KR5"] = "...そしてこの脅威に終止符を打ちましょう。",
["COMIC_2_8b_KR5"] = "...一緒に。",
["COMIC_3_1_KR5"] = "おやおや！ここには何が…？",
["COMIC_3_2_KR5"] = "エリニーの強力な剣！",
["COMIC_3_3_KR5"] = "いたっ！",
["COMIC_3_4a_KR5"] = "もちろん...",
["COMIC_3_4b_KR5"] = "時間の無駄使いはやめて！",
["COMIC_3_5a_KR5"] = "ああ...でも、あなたが思っているよりも近いんだ。",
["COMIC_3_5b_KR5"] = "私たちの王はまだ行方不明です。",
["COMIC_3_6_KR5"] = "それは厳しい戦いになるかもしれないが。",
["COMIC_4_10a_KR5"] = "はは！俺はいつもそうだった。",
["COMIC_4_10b_KR5"] = "それで...これからどうなるの？",
["COMIC_4_11_KR5"] = "私たちには違いがあるかもしれない...",
["COMIC_4_12_KR5"] = "...しかし、私たちには共通の大きな脅威がある。",
["COMIC_4_1_KR5"] = "エリニー...",
["COMIC_4_2_KR5"] = "...彼に力を！",
["COMIC_4_4_KR5"] = "アーアーグ！",
["COMIC_4_7a_KR5"] = "あなたの'休暇'がとても良い影響を与えたようだね！",
["COMIC_4_7b_KR5"] = "お前!!!",
["COMIC_4_8_KR5"] = "お前はいたずらの代償を払うべきだ！",
["COMIC_4_9_KR5"] = "だけど、君の言っていたことは正しかった。",
["COMIC_5_1_KR2"] = "勝利！",
["COMIC_5_1_KR5_KR5"] = "あなたたちの虫は止められない...",
["COMIC_5_2_KR2"] = "勝利！",
["COMIC_5_2_KR5_KR5"] = "新しい世界！",
["COMIC_5_6_KR5_KR5"] = "目覚めた！",
["COMIC_5_7a_KR5_KR5"] = "これがそれか...",
["COMIC_5_7b_KR5_KR5"] = "最後の対決。",
["COMIC_6_1a_KR5_KR5"] = "私に挑戦するとは勇敢だ...",
["COMIC_6_1b_KR5_KR5"] = "しかし、それはここに属さない！",
["COMIC_6_4_KR5_KR5"] = "ねえ！",
["COMIC_6_5_KR5_KR5"] = "お前、宇宙のナメクジよ...",
["COMIC_6_6_KR5_KR5"] = "...私の力を甘く見るな!",
["COMIC_6_8_KR5_KR5"] = "準備してください。これを長くは保てない！",
["COMIC_7_1_KR5_KR5"] = "いや！これ...あり得ない！！！",
["COMIC_7_3_KR5_KR5"] = "それで...  何を？",
["COMIC_7_4a_KR5_KR5"] = "さて、私の任務は終わった...",
["COMIC_7_4b_KR5_KR5"] = "...そして、彼らは彼らの王を必要としていると思います。",
["COMIC_7_5_2_KR2"] = "いいえ",
["COMIC_7_6_KR5_KR5"] = "次回まで、親愛なる敵よ。",
["COMIC_7_7_KR5_KR5"] = "後で、エベラディアントの森で",
["COMIC_8_1_KR5_KR5"] = "ああ、ついに",
["COMIC_8_2_KR5_KR5"] = "この力は、再び...",
["COMIC_8_4_KR5_KR5"] = "... 私のものだ！",
["COMIC_8_5_KR5_KR5"] = "ムアハハハ！",
["COMIC_9_1_KR5_KR5"] = "そう昔のことではない、私たちエルフは魔法と優雅さで敬われていました...",
["COMIC_9_2_KR5_KR5"] = "...しかし、我々の神聖な遺物が堕落し、私たちはかつての影となった。",
["COMIC_9_3_KR5_KR5"] = "しかし、この軍隊で栄光を取り戻し...",
["COMIC_9_4_KR5_KR5"] = "...そしてエルフが支配する新しい世界を導くのだ!!!",
["COMIC_BALLOON_0002_KR1"] = "勝利！",
["COMIC_BALLOON_02_KR1"] = "勝利！",
["COMIC_balloon_0002_KR1"] = "勝利！",
["COMMAND YOUR TROOPS!"] = "部隊に命令せよ！",
["CONFIRM_EXIT"] = "終了しますか？",
["CONFIRM_RESTART"] = "再起動しますか？",
["CONTROLLER_STAGE_16_OVERSEER_DESCRIPTION"] = "他の世界を侵略し征服してそのエネルギーを吸収する異次元の怪物。あらゆる代償を払っても止めなければならない。",
["CONTROLLER_STAGE_16_OVERSEER_EXTRA"] = "- プレイヤーのタワーを交換する\n- グレアリングをスポーンする\n- ホルダーを破壊する",
["CONTROLLER_STAGE_16_OVERSEER_NAME"] = "監督者",
["CREDITS"] = "クレジット",
["CREDITS_COPYRIGHT"] = "© 2014 Ironhide Game Studio.All rights reserved.",
["CREDITS_POWERED_BY"] = "Powered by",
["CREDITS_SUBTITLE_01"] = "(アルファベット順)",
["CREDITS_SUBTITLE_07"] = "(アルファベット順)",
["CREDITS_SUBTITLE_09"] = "(アルファベット順)",
["CREDITS_SUBTITLE_16"] = "(アルファベット順)",
["CREDITS_TEXT_18"] = "家族、友人、そしてコミュニティーの皆様",
["CREDITS_TEXT_18_2"] = "長年にわたる支援を心から感謝します。",
["CREDITS_TITLE_01"] = "クリエイティブディレクター&エグゼクティブプロデューサー",
["CREDITS_TITLE_01_CREATIVE_DIRECTORS"] = "クリエイティブディレクター",
["CREDITS_TITLE_01_EXECUTIVE_PRODUCERS"] = "エグゼクティブプロデューサー",
["CREDITS_TITLE_02"] = "リードゲームデザイナー",
["CREDITS_TITLE_02_LEAD_GAME_DESIGNERS"] = "リードゲームデザイナー",
["CREDITS_TITLE_03"] = "ゲームデザイナー",
["CREDITS_TITLE_03_GAME_DESIGNER"] = "ゲームデザイナー",
["CREDITS_TITLE_04"] = "ストーリーライター",
["CREDITS_TITLE_04_STORY_WRITERS"] = "ストーリーライター",
["CREDITS_TITLE_05"] = "テキストライター",
["CREDITS_TITLE_06"] = "リードプログラマー",
["CREDITS_TITLE_06_LEAD_PROGRAMMERS"] = "リードプログラマー",
["CREDITS_TITLE_07"] = "プログラマー",
["CREDITS_TITLE_08"] = "リードアーティスト",
["CREDITS_TITLE_09"] = "アーティスト",
["CREDITS_TITLE_10"] = "コミックアーティスト",
["CREDITS_TITLE_11"] = "コミックライター",
["CREDITS_TITLE_12"] = "テクニカルアーティスト",
["CREDITS_TITLE_13"] = "効果音",
["CREDITS_TITLE_14"] = "オリジナル音楽",
["CREDITS_TITLE_15"] = "ボイスアクター",
["CREDITS_TITLE_16"] = "品質保証",
["CREDITS_TITLE_17"] = "βテスター",
["CREDITS_TITLE_18"] = "スペシャルサンクス",
["CREDITS_TITLE_19_PMO"] = "プロジェクトマネジメントオフィス",
["CREDITS_TITLE_20_PRODUCER"] = "プロデューサー",
["CREDITS_TITLE_21_MARKETING"] = "マーケティング",
["CREDITS_TITLE_22_SPECIAL_COLLAB"] = "特別なコラボレーター",
["CREDITS_TITLE_ANCIENT_HUNGER_UPDATE"] = "古い飢餓 / アラクノフォビア / 悟空の旅",
["CREDITS_TITLE_GAME_ENGINE_PROGRAMMER"] = "ゲームエンジンプログラマー",
["CREDITS_TITLE_LOCALIZATION"] = "ローカライズ",
["CREDITS_TITLE_LOGO"] = "ゲーム BY",
["CRange0"] = "ショート",
["CRange1"] = "平均的",
["CRange2"] = "ロング",
["CRange3"] = "素晴らしい",
["CRange4"] = "エクストリーム",
["CReload0"] = "非常に遅い",
["CReload1"] = "遅い",
["CReload2"] = "平均的",
["CReload3"] = "速い",
["CReload4"] = "非常に速い",
["CSpeed0"] = "遅い",
["CSpeed1"] = "中",
["CSpeed2"] = "速い",
["C_DIFFICULTY_EASY"] = "カジュアルをクリア済み",
["C_DIFFICULTY_HARD"] = "ベテランをクリア済み",
["C_DIFFICULTY_IMPOSSIBLE"] = "ジゴクでクリア済",
["C_DIFFICULTY_NORMAL"] = "ノーマルをクリア済み",
["C_REWARD"] = "報酬：",
["Campaign"] = "キャンペーン",
["Campaing"] = "キャンペーン",
["Casual"] = "カジュアル",
["Challenge Rules"] = "チャレンジルール",
["Clear_progress"] = "進捗をクリア",
["Click on the path to move the hero."] = "経路をクリックして英雄を移動させよう。",
["Click to select"] = "クリックで選択",
["Coming soon"] = "近日登場",
["Community Manager"] = "コミュニティ管理者",
["Continue"] = "続行",
["Credits"] = "クレジット",
["DAYS_ABBREVIATION"] = "日",
["DEFEAT"] = "敗北",
["DELETE SLOT?"] = "スロットを削除しますか？",
["DIFFICULTY LEVEL"] = "難易度選択",
["DIFFICULTY_SELECTION_EASY_DESCRIPTION"] = "ストラテジーゲームのビギナー向け！",
["DIFFICULTY_SELECTION_HARD_DESCRIPTION"] = "激ムズ！プレイは自己責任でどうぞ！",
["DIFFICULTY_SELECTION_IMPOSSIBLE_DESCRIPTION"] = "挑めるは 真の強者のみ！",
["DIFFICULTY_SELECTION_IMPOSSIBLE_LOCKED_DESCRIPTION"] = "ストーリーをクリアで このモード解放",
["DIFFICULTY_SELECTION_NORMAL_DESCRIPTION"] = "手強いチャレンジ！",
["DIFFICULTY_SELECTION_NOTE"] = "難易度はステージ選択中にいつでも変更可能です。",
["DIFFICULTY_SELECTION_TITLE"] = "難易度を選ぼう！",
["DISCOUNT"] = "ディスカウント",
["DLC_OWNED"] = "購入した",
["Defeat"] = "敗北",
["Difficulty Level"] = "難易度選択",
["Done"] = "OK",
["ELITE STAGE!"] = "エリートステージ！",
["ENEMY_ACOLYTE_DESCRIPTION"] = "小さくておとなしい、信者たちは戦いの中でその数を数える。",
["ENEMY_ACOLYTE_EXTRA"] = "- 死んだ時に触手を生成する",
["ENEMY_ACOLYTE_NAME"] = "カルトの信者",
["ENEMY_ACOLYTE_SPECIAL"] = "死亡時に触手が出現する",
["ENEMY_ACOLYTE_TENTACLE_DESCRIPTION"] = "最後の手段として、侍僧は監督者に自分の命を犠牲にし、致命的な触手を生み出す。",
["ENEMY_ACOLYTE_TENTACLE_EXTRA"] = "- 死んだ信者から生まれる。",
["ENEMY_ACOLYTE_TENTACLE_NAME"] = "信者の触手",
["ENEMY_AMALGAM_DESCRIPTION"] = "肉体と虚無の土から作られた怪物。彼らの遅さにもかかわらず、ベヒモスは戦場を横断する際に恐怖を広げます。",
["ENEMY_AMALGAM_EXTRA"] = "- ミニボス\n- 死ぬと爆発する",
["ENEMY_AMALGAM_NAME"] = "肉のベヒモス",
["ENEMY_ANIMATED_ARMOR_DESCRIPTION"] = "過去の戦いからの傷だらけの遺物で、今やスペクターによって操られ、戦いに巻き込まれる。",
["ENEMY_ANIMATED_ARMOR_EXTRA"] = "- 敗北すると、スペクターによって再生可能",
["ENEMY_ANIMATED_ARMOR_NAME"] = "アニメーテッドアーマー",
["ENEMY_ARMORED_NIGHTMARE_DESCRIPTION"] = "カルトの魔法のおかげで鎧を身につけたこれらのナイトメアは、真っ先に戦いに突入する",
["ENEMY_ARMORED_NIGHTMARE_EXTRA"] = "- 高い防御力\n- 敗れるとナイトメアに変わる",
["ENEMY_ARMORED_NIGHTMARE_NAME"] = "束縛された悪夢",
["ENEMY_ARMORED_NIGHTMARE_SPECIAL"] = "敗れるとナイトメアに変わる.",
["ENEMY_ASH_SPIRIT_DESCRIPTION"] = "強大な精霊たちが、溶岩と灰と悲しみから生まれた恐ろしい怪物に変わった。",
["ENEMY_ASH_SPIRIT_EXTRA"] = "- 高い体力\n- 高い装甲\n- 炎の地面で体力を回復する",
["ENEMY_ASH_SPIRIT_NAME"] = "Ash Spirit",
["ENEMY_BALLOONING_SPIDER_DESCRIPTION"] = "素早く狡猾なクモで、危険を回避する術に長けている。",
["ENEMY_BALLOONING_SPIDER_EXTRA"] = "- 追い詰められると飛び始める\n- 中装甲",
["ENEMY_BALLOONING_SPIDER_FLYER_DESCRIPTION"] = "素早く狡猾なクモで、危険を回避する術に長けている。",
["ENEMY_BALLOONING_SPIDER_FLYER_EXTRA"] = "- 追い詰められると飛び始める\n- 中装甲",
["ENEMY_BALLOONING_SPIDER_FLYER_NAME"] = "バルーニング・スパイダー",
["ENEMY_BALLOONING_SPIDER_NAME"] = "バルーニング・スパイダー",
["ENEMY_BANE_WOLF_DESCRIPTION"] = "来るのが見えないほど遅い者たちを狙う、ねじれたオオカミ。",
["ENEMY_BANE_WOLF_EXTRA"] = "- ダメージを受けるたびに移動速度が速くなる",
["ENEMY_BANE_WOLF_NAME"] = "ベインウルフ",
["ENEMY_BEAR_VANGUARD_DESCRIPTION"] = "大きく、幅広く、悪い彼らは、敵をダース単位で引き裂く。",
["ENEMY_BEAR_VANGUARD_EXTRA"] = "- 高い鎧\n- 近くでクマが死ぬと激怒する。",
["ENEMY_BEAR_VANGUARD_NAME"] = "熊の先鋒",
["ENEMY_BEAR_VANGUARD_SPECIAL"] = "近くにいる別のクマが死ぬと、狂乱状態になる。",
["ENEMY_BEAR_WOODCUTTER_DESCRIPTION"] = "勤務中に寝る傾向がありますが、目を覚ますと事態は深刻になります。",
["ENEMY_BEAR_WOODCUTTER_EXTRA"] = "- 高い装甲\n- 近くで熊が死ぬと激怒する",
["ENEMY_BEAR_WOODCUTTER_NAME"] = "熊の木こり",
["ENEMY_BIG_TERRACOTA_DESCRIPTION"] = "殺意に駆られた複数の魂が融合して生まれた人型の泥の塊。",
["ENEMY_BIG_TERRACOTA_EXTRA"] = "- 近接",
["ENEMY_BIG_TERRACOTA_NAME"] = "モンスター幻影デコイ",
["ENEMY_BLAZE_RAIDER_DESCRIPTION"] = "誇り高く屈強な隊長たち。炎の道のイニシエートであり、蛇槍を操り敵を翻弄する。",
["ENEMY_BLAZE_RAIDER_EXTRA"] = "- 装甲が低い\n- 炎上した地面で特殊攻撃",
["ENEMY_BLAZE_RAIDER_NAME"] = "ブレイズレイダー",
["ENEMY_BLINKER_DESCRIPTION"] = "威嚇する視線とコウモリのような翼で、ブリンカーは油断する敵を狙う。",
["ENEMY_BLINKER_EXTRA"] = "- プレイヤーユニットをスタンする",
["ENEMY_BLINKER_NAME"] = "虚空の点滅者",
["ENEMY_BLINKER_SPECIAL"] = "プレイヤーユニットをスタン",
["ENEMY_BOSS_BULL_KING_NAME"] = "Bull Demon King",
["ENEMY_BOSS_CORRUPTED_DENAS_NAME"] = "コラプテッドデナス",
["ENEMY_BOSS_CROCS_2_NAME"] = "アボミノールの毒",
["ENEMY_BOSS_CROCS_3_NAME"] = "アボミノールの火",
["ENEMY_BOSS_CROCS_NAME"] = "アボミノール",
["ENEMY_BOSS_CULT_LEADER_NAME"] = "予言者ミドリアス",
["ENEMY_BOSS_DEFORMED_GRYMBEARD_NAME"] = "歪んだグリムビアード",
["ENEMY_BOSS_GRYMBEARD_NAME"] = "グリムビアード",
["ENEMY_BOSS_MACHINIST_NAME"] = "グリムビアード",
["ENEMY_BOSS_NAVIRA_NAME"] = "ナヴィラ",
["ENEMY_BOSS_OVERSEER_NAME"] = "監督者",
["ENEMY_BOSS_PIG_NAME"] = "血肉研磨",
["ENEMY_BOSS_PRINCESS_IRON_FAN_CLONE_NAME"] = "鉄扇公主のクローン",
["ENEMY_BOSS_PRINCESS_IRON_FAN_NAME"] = "Princess Iron Fan",
["ENEMY_BOSS_REDBOY_TEEN_NAME"] = "赤い少年",
["ENEMY_BOSS_SPIDER_QUEEN_NAME"] = "ミガレ",
["ENEMY_BRUTE_WELDER_DESCRIPTION"] = "挑発されずに敵にトーチを使う作業者。",
["ENEMY_BRUTE_WELDER_EXTRA"] = "- 倒されると塔をブロックする",
["ENEMY_BRUTE_WELDER_NAME"] = "ブルート・ウェルダー",
["ENEMY_BURNING_TREANT_DESCRIPTION"] = "燃え盛る森の中で生まれた、邪悪な意志を持つ木のクリーチャー。",
["ENEMY_BURNING_TREANT_EXTRA"] = "- 範囲ダメージ\n- 攻撃時に炎の地面を残す",
["ENEMY_BURNING_TREANT_NAME"] = "Burning Treant",
["ENEMY_CITIZEN_1_DESCRIPTION"] = "姫に仕える不気味な漁師たちが、闇市場を密かに通り抜ける。",
["ENEMY_CITIZEN_1_EXTRA"] = "- 弱い",
["ENEMY_CITIZEN_1_NAME"] = "老魚屋",
["ENEMY_CITIZEN_2_DESCRIPTION"] = "姫に仕える不気味な漁師たちが、闇市場をすり抜ける。",
["ENEMY_CITIZEN_2_EXTRA"] = "- 弱い",
["ENEMY_CITIZEN_2_NAME"] = "ブラックウォーターの釣り師",
["ENEMY_CITIZEN_3_DESCRIPTION"] = "姫に仕える不気味な漁師たちが、闇市場を密かに通り抜ける。",
["ENEMY_CITIZEN_3_EXTRA"] = "- 弱い",
["ENEMY_CITIZEN_3_NAME"] = "インクの密売人",
["ENEMY_CITIZEN_4_DESCRIPTION"] = "姫に仕える不気味な漁師たちが、闇市場を密かに通り抜ける。",
["ENEMY_CITIZEN_4_EXTRA"] = "- 弱い",
["ENEMY_CITIZEN_4_NAME"] = "タイド・ポーチャー",
["ENEMY_COMMON_CLONE_DESCRIPTION"] = "特筆すべき点もなく、オリジナルと同様に特別でもない。",
["ENEMY_COMMON_CLONE_EXTRA"] = "- 考えなしに突き進む",
["ENEMY_COMMON_CLONE_NAME"] = "クローン",
["ENEMY_CORRUPTED_ELF_DESCRIPTION"] = "遠くから敵を狙う再生されたエルフたち。死んでもなお、彼らは非常に有効です。",
["ENEMY_CORRUPTED_ELF_EXTRA"] = "- 死ぬとスペクターを召喚する",
["ENEMY_CORRUPTED_ELF_NAME"] = "リヴァナントレンジャー",
["ENEMY_CORRUPTED_STALKER_DESCRIPTION"] = "アコライトによって飼い慣らされたクラウドストーカーは、今ではカルトのための乗り物として奉仕している.",
["ENEMY_CORRUPTED_STALKER_EXTRA"] = "- 飛行中",
["ENEMY_CORRUPTED_STALKER_NAME"] = "飼いならされたストーカー",
["ENEMY_CORRUPTED_STALKER_SPECIAL"] = "飛行",
["ENEMY_CROCS_BASIC_DESCRIPTION"] = "誇り高きクロック戦士、まだ人生の早い段階にあり、殺人マシンに変身するのは数カロリーしか離れていない。 ",
["ENEMY_CROCS_BASIC_EGG_DESCRIPTION"] = "生まれたばかりで、足元では止められない、「彼らは本当に早く成長する」は、これらの小さな驚きに満ちた者たちのおかげで発明されたフレーズでした。 ",
["ENEMY_CROCS_BASIC_EGG_EXTRA"] = "- ブロック不可能\n- 低いアーマー\n- 数秒後にゲーターに変化する ",
["ENEMY_CROCS_BASIC_EGG_NAME"] = "クロキンダー ",
["ENEMY_CROCS_BASIC_EXTRA"] = "- 近接 ",
["ENEMY_CROCS_BASIC_NAME"] = "ゲーター",
["ENEMY_CROCS_EGG_SPAWNER_DESCRIPTION"] = "このクロックは、トラブルがいっぱいの巣を運んでいます！ 数歩歩く度に、卵を落とし、それが孵ってクロッキンダーの狂乱になります。それはモバイル保育園のようなものですが、噛み付きは格段に強いです！",
["ENEMY_CROCS_EGG_SPAWNER_EXTRA"] = "- パスにクロキンダーを生成",
["ENEMY_CROCS_EGG_SPAWNER_NAME"] = "巣作りゲーター",
["ENEMY_CROCS_FLIER_DESCRIPTION"] = "自然進化を軽蔑し、空中での優位性を得るために独自の翼を鍛えた狡猾なクロックたち。",
["ENEMY_CROCS_FLIER_EXTRA"] = "- 飛行",
["ENEMY_CROCS_FLIER_NAME"] = "有翼のクロック",
["ENEMY_CROCS_HYDRA_DESCRIPTION"] = "二つの頭は一つよりも優れており、ヒドラはそれを証明しています。このような三つ頭の獣についての古い神話がありますが、それはおそらく嘘でしょう。",
["ENEMY_CROCS_HYDRA_EXTRA"] = "- 高い体力\n- 高いダメージ\n- 高い魔法耐性\n- 死亡時に三つ目の頭が生成される\n- 地面に毒を吐く",
["ENEMY_CROCS_HYDRA_NAME"] = "ヒドラ",
["ENEMY_CROCS_QUICKFEET_GATOR_NAME"] = "クイックフィート",
["ENEMY_CROCS_RANGED_DESCRIPTION"] = "素早くてこそこそ動く、長距離スリングショットを使って敵対者に対処するハンターリザード。 ",
["ENEMY_CROCS_RANGED_EXTRA"] = "- 速い\n- 遠距離 ",
["ENEMY_CROCS_RANGED_NAME"] = "リザードショット ",
["ENEMY_CROCS_SHAMAN_DESCRIPTION"] = "クロックにとって非常に重要な魔法の存在。結局のところ、冷血種にとって、空の気まぐれを予見する能力は生死に関わる問題である。",
["ENEMY_CROCS_SHAMAN_EXTRA"] = "- 遠距離魔法ダメージ\n- 高い魔法耐性\n- 他のワニを回復\n- タワーをスタンさせる",
["ENEMY_CROCS_SHAMAN_NAME"] = "賢いクロック",
["ENEMY_CROCS_TANK_DESCRIPTION"] = "クロックスの力の基となるもの、\"良い防御は最高の攻撃である\"というメンタリティを持ち、彼らはいくつかのシェルを盗み、それが最良の方法だと考えて使用し始めました。",
["ENEMY_CROCS_TANK_EXTRA"] = "- 高い体力\n- 高い防御\n- ブロックされたときに回転する",
["ENEMY_CROCS_TANK_NAME"] = "タンクザード",
["ENEMY_CRYSTAL_GOLEM_DESCRIPTION"] = "そのクリスタルからの異世界の魔法で満たされた、これらの石の像はほぼ止められない.",
["ENEMY_CRYSTAL_GOLEM_EXTRA"] = "- ミニボス\n- 非常に高い装甲",
["ENEMY_CRYSTAL_GOLEM_NAME"] = "クリスタルゴーレム",
["ENEMY_CULTBROOD_DESCRIPTION"] = "半分クモ、半分カルト信者の異形の怪物。恐れも慈悲もなく戦場に突進する。",
["ENEMY_CULTBROOD_EXTRA"] = "- 俊敏\n- 毒攻撃\n- 敵が毒で死亡すると、別のカルトブルードが生まれる。",
["ENEMY_CULTBROOD_NAME"] = "カルトブルード",
["ENEMY_CUTTHROAT_RAT_DESCRIPTION"] = "性質が狡猾で悪賢いため、ラットは鋭い暗殺者および潜入者である。",
["ENEMY_CUTTHROAT_RAT_EXTRA"] = "- 高速\n- 敵に攻撃した後、姿を消す。",
["ENEMY_CUTTHROAT_RAT_NAME"] = "切喉鼠",
["ENEMY_CUTTHROAT_RAT_SPECIAL"] = "敵に攻撃した後、姿を消す。",
["ENEMY_DARKSTEEL_ANVIL_DESCRIPTION"] = "戦争の太鼓に対するドワーフの答え。見た目が重ければ重いほど、大きな音を奏でる。",
["ENEMY_DARKSTEEL_ANVIL_EXTRA"] = "- 敵にアーマーとスピードのバフを付与する",
["ENEMY_DARKSTEEL_ANVIL_NAME"] = "ダークスチール・アンビル",
["ENEMY_DARKSTEEL_FIST_DESCRIPTION"] = "金属を曲げるように機械的に強化されているが、代わりに他の者を殴る。",
["ENEMY_DARKSTEEL_FIST_EXTRA"] = "- 特殊攻撃でプレイヤーユニットを気絶させる",
["ENEMY_DARKSTEEL_FIST_NAME"] = "ダークスチール・フィスト",
["ENEMY_DARKSTEEL_GUARDIAN_DESCRIPTION"] = "ドワーフ戦士が操縦し、炎のエンジンで動く頑丈な戦闘スーツ。まさに戦闘準備万端。",
["ENEMY_DARKSTEEL_GUARDIAN_EXTRA"] = "- ミニボス\n- 低体力時に狂乱状態になる",
["ENEMY_DARKSTEEL_GUARDIAN_NAME"] = "ダークスチール・ガーディアン",
["ENEMY_DARKSTEEL_HAMMERER_DESCRIPTION"] = "その好む武器と同じくらい鈍感な戦士たち。",
["ENEMY_DARKSTEEL_HAMMERER_EXTRA"] = " ",
["ENEMY_DARKSTEEL_HAMMERER_NAME"] = "ダークスチール・ハンマー",
["ENEMY_DARKSTEEL_HULK_DESCRIPTION"] = "不機嫌で、溶けた鋼が血管を流れる、最も重量級のドワーフ。",
["ENEMY_DARKSTEEL_HULK_EXTRA"] = "- ミニボス\n- 低体力時に道を突進しダメージを与える",
["ENEMY_DARKSTEEL_HULK_NAME"] = "ダークスチール・ハルク",
["ENEMY_DARKSTEEL_SHIELDER_DESCRIPTION"] = "巨大な盾で守られ、前進しながら敵を押しのける。",
["ENEMY_DARKSTEEL_SHIELDER_EXTRA"] = "- 倒されるとハンマーに変化する",
["ENEMY_DARKSTEEL_SHIELDER_NAME"] = "ダークスチール・シールダー",
["ENEMY_DEATHWOOD_DESCRIPTION"] = "今や森をさまようダークスペクターによって汚染されたウィアードウッド。",
["ENEMY_DEATHWOOD_EXTRA"] = "- ミニボス\n- エリアダメージを与える呪われたドングリを投げる",
["ENEMY_DEATHWOOD_NAME"] = "デスウッド",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_DESCRIPTION"] = "グリムビアードの傲慢さが生んだ結果。その知力はその醜悪さに匹敵する。",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_EXTRA"] = "- 飛行ユニット\n- 高い魔法耐性のシールド",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_NAME"] = "歪んだクローン",
["ENEMY_DEMON_MINOTAUR_DESCRIPTION"] = "半人半牛のハイブリッド悪魔で、破壊的な突進攻撃を繰り出す。容赦という言葉を知らない。",
["ENEMY_DEMON_MINOTAUR_EXTRA"] = "- 突撃攻撃\n- 即死しない",
["ENEMY_DEMON_MINOTAUR_NAME"] = "Demon Minotaur",
["ENEMY_DOOM_BRINGER_DESCRIPTION"] = "どんな代償を払っても破滅をもたらす恐るべき戦士。",
["ENEMY_DOOM_BRINGER_EXTRA"] = "- タワーをスタンさせる",
["ENEMY_DOOM_BRINGER_NAME"] = "Doombringer",
["ENEMY_DRAINBROOD_DESCRIPTION"] = "古代のクモで、致命的な毒牙を持つ。他のクモを結晶化させた元凶ではないかと推測されている。",
["ENEMY_DRAINBROOD_EXTRA"] = "- 敵を結晶化させながら生命を吸収する",
["ENEMY_DRAINBROOD_NAME"] = "ライフスティール・スパイダー",
["ENEMY_DREADEYE_VIPER_DESCRIPTION"] = "矢に自分の毒を塗りつけ、遠くから致命的な敵となる。",
["ENEMY_DREADEYE_VIPER_EXTRA"] = "- 低魔法耐性\n- 毒攻撃",
["ENEMY_DREADEYE_VIPER_NAME"] = "恐怖の目を持つヴァイパー",
["ENEMY_DREADEYE_VIPER_SPECIAL"] = "矢は標的に毒を塗る。",
["ENEMY_DUST_CRYPTID_DESCRIPTION"] = "かつては素晴らしい光景だったものが、今や遠くまでさまよいすぎた者たちにとって恐ろしい姿となる。",
["ENEMY_DUST_CRYPTID_EXTRA"] = "- 飛行\n- 敵を無敵にする花粉の雲を残す",
["ENEMY_DUST_CRYPTID_NAME"] = "ダストクリプティッド",
["ENEMY_EVOLVING_SCOURGE_DESCRIPTION"] = "最初はかわいらしく見えるかもしれませんが、スカージが倒れた獲物に餌を与えると、事態は急速に悪化します。",
["ENEMY_EVOLVING_SCOURGE_EXTRA"] = "- 倒れたユニットを食べてより強い形態に進化します\n - Glareの影響を受けたときには、最終形態に即座に進化します",
["ENEMY_EVOLVING_SCOURGE_NAME"] = "進化するスカージ",
["ENEMY_FAN_GUARD_DESCRIPTION"] = "強く非常に多才な女性戦士で、痛みを与えることにも、魔法の扇子で身を守ることにも長けている。",
["ENEMY_FAN_GUARD_EXTRA"] = "- ブロックされていない間は中装甲と魔法耐性を持つ。",
["ENEMY_FAN_GUARD_NAME"] = "Fan Guard",
["ENEMY_FIRE_FOX_DESCRIPTION"] = "火から生まれた、愛らしくも捉えどころのないキツネたち。速すぎて、気まぐれすぎて、手なずけることはできない。",
["ENEMY_FIRE_FOX_EXTRA"] = "- 低い魔法耐性\n- 炎の地面では速くなる\n- 死亡時に炎の地面を残す",
["ENEMY_FIRE_FOX_NAME"] = "火の狐",
["ENEMY_FIRE_PHOENIX_DESCRIPTION"] = "神話の飛行生物で、火そのものを糧として生きている。燃え盛る炎の中に生き、そして死ぬ。",
["ENEMY_FIRE_PHOENIX_EXTRA"] = "- 飛行\n- 死亡時に炎の地面を残す",
["ENEMY_FIRE_PHOENIX_NAME"] = "火のフェニックス",
["ENEMY_FLAME_GUARD_DESCRIPTION"] = "師の承認を得るために奮闘する下級の弟子たちは、小さな刃物の扱いに長けている。",
["ENEMY_FLAME_GUARD_EXTRA"] = "- 火の地面で特殊攻撃",
["ENEMY_FLAME_GUARD_NAME"] = "炎の守護者",
["ENEMY_GALE_WARRIOR_DESCRIPTION"] = "優雅で洗練されたこれらの戦士たちは、姫に選ばれ、命を懸けて彼女に仕える覚悟がある。",
["ENEMY_GALE_WARRIOR_EXTRA"] = "- 中装甲\n- 3回攻撃ごとに出血を引き起こす",
["ENEMY_GALE_WARRIOR_NAME"] = "Gale Warrior",
["ENEMY_GLAREBROOD_CRYSTAL_NAME"] = "グレアブルードクリスタル",
["ENEMY_GLARELING_DESCRIPTION"] = "放っておかれた場合、これらのおとなしい生き物は、最も頑強な軍隊でさえも打ち負かすことができる。",
["ENEMY_GLARELING_EXTRA"] = "- 高速",
["ENEMY_GLARELING_NAME"] = "グレアリング",
["ENEMY_GLARENWARDEN_DESCRIPTION"] = "この忌まわしいクモたちはグレアブルードの融合によって生まれ、これまで以上に強く頑丈になった。",
["ENEMY_GLARENWARDEN_EXTRA"] = "- 高い装甲\n- 攻撃時ライフスティール",
["ENEMY_GLARENWARDEN_NAME"] = "グレアウォーデン",
["ENEMY_GOLDEN_EYED_DESCRIPTION"] = "敵の心に恐怖を刻む咆哮を持つ巨大な獣。",
["ENEMY_GOLDEN_EYED_EXTRA"] = "- ミニボス\n- 味方の移動速度を上昇させる",
["ENEMY_GOLDEN_EYED_NAME"] = "Golden-Eyed Beast",
["ENEMY_HARDENED_HORROR_DESCRIPTION"] = "このホラーの種族は手が鋭利な刃になっており、敵を切り裂いて道を作ります。",
["ENEMY_HARDENED_HORROR_EXTRA"] = "- Glareの影響を受けると高速で転がり、ブロックできません。",
["ENEMY_HARDENED_HORROR_NAME"] = "ブレードクロウ・ホラー",
["ENEMY_HELLFIRE_WARLOCK_DESCRIPTION"] = "極めて危険なウォーロックたち。地獄の深淵からクリーチャーと炎を召喚する達人。",
["ENEMY_HELLFIRE_WARLOCK_EXTRA"] = "- 火の玉を放つ\n- 九尾の狐を召喚",
["ENEMY_HELLFIRE_WARLOCK_NAME"] = "Hellfire Warlock",
["ENEMY_HOG_INVADER_DESCRIPTION"] = "汚くて無秩序な問題を起こす人たち。野生の獣の軍隊の大部分。",
["ENEMY_HOG_INVADER_EXTRA"] = "- 低HP",
["ENEMY_HOG_INVADER_NAME"] = "ホグ・インベーダー",
["ENEMY_HYENA5_DESCRIPTION"] = "倒れた敵に饗宴を楽しむ残忍な戦士たち。",
["ENEMY_HYENA5_EXTRA"] = "- 中間装備\n- 倒れたプレイヤーユニットを食べることで回復する",
["ENEMY_HYENA5_NAME"] = "腐れ牙ハイエナ",
["ENEMY_HYENA5_SPECIAL"] = "塞がれた敵を倒して食べることで回復する。",
["ENEMY_KILLERTILE_DESCRIPTION"] = "強力な破壊者たち、戦闘の経験年数（または鶏）が彼らに強力で致命的な咬傷を与えました。 ",
["ENEMY_KILLERTILE_EXTRA"] = "- 高い健康\n高いダメージ",
["ENEMY_KILLERTILE_NAME"] = "キラータイル ",
["ENEMY_LESSER_EYE_DESCRIPTION"] = "戦場上を浮遊する邪悪な目、邪悪なスポナーの斥候として行動します。",
["ENEMY_LESSER_EYE_EXTRA"] = "- 飛んで",
["ENEMY_LESSER_EYE_NAME"] = "小さい目",
["ENEMY_LESSER_SISTER_DESCRIPTION"] = "彼女たちの邪悪な魔法で、ねじれた姉妹は、悪夢を現実の世界に容易に導きます。 ",
["ENEMY_LESSER_SISTER_EXTRA"] = "- 高い魔法耐性\n- 悪夢を召喚する ",
["ENEMY_LESSER_SISTER_NAME"] = "ツイステッド・シスター",
["ENEMY_LESSER_SISTER_NIGHTMARE_DESCRIPTION"] = "カルトの姉妹たちの呪文の本から織りなされたエーテリアルな影",
["ENEMY_LESSER_SISTER_NIGHTMARE_EXTRA"] = "- 近接ユニットによってブロックされていない限り、ターゲットにできません",
["ENEMY_LESSER_SISTER_NIGHTMARE_NAME"] = "悪夢",
["ENEMY_LESSER_SISTER_SPECIAL"] = "ナイトメアを召喚する",
["ENEMY_MACHINIST_DESCRIPTION"] = "歯車とエンジンに取り憑かれたこのドワーフは、産業オートメーションと戦争のために生きている。",
["ENEMY_MACHINIST_EXTRA"] = "- センチネルを生成する組立ラインを運営する",
["ENEMY_MACHINIST_NAME"] = "グリムビアード",
["ENEMY_MAD_TINKERER_DESCRIPTION"] = "廃品から物を作る以外に何も気にしないティンカーラー。",
["ENEMY_MAD_TINKERER_EXTRA"] = "- 他のユニットが残したスクラップを使ってドローンを作成する",
["ENEMY_MAD_TINKERER_NAME"] = "マッド・ティンカーラー",
["ENEMY_MINDLESS_HUSK_DESCRIPTION"] = "彼らの外見からすると、Husksは弱い敵のように見えますが、彼ら一人一人が戦場に驚きをもたらします。",
["ENEMY_MINDLESS_HUSK_EXTRA"] = "- 死亡時にグレアリングを生成します。",
["ENEMY_MINDLESS_HUSK_NAME"] = "無心の殻",
["ENEMY_NINE_TAILED_FOX_DESCRIPTION"] = "美しくも強大な謎の生物。燃え盛る焚き火のように敵を蹴散らす。",
["ENEMY_NINE_TAILED_FOX_EXTRA"] = "- 中程度の魔法耐性\n- 前方にテレポートし、到着時に敵を気絶させる\n- 範囲ダメージ",
["ENEMY_NINE_TAILED_FOX_NAME"] = "九尾の狐",
["ENEMY_NOXIOUS_HORROR_DESCRIPTION"] = "両生類のような生き物で、獲物に毒胆汁を吐きかけます。近距離でも危険です。",
["ENEMY_NOXIOUS_HORROR_EXTRA"] = "- Glareの影響を受けると魔法耐性を得て、毒のオーラを放ちます。",
["ENEMY_NOXIOUS_HORROR_NAME"] = "有毒なスピッター",
["ENEMY_PALACE_GUARD_DESCRIPTION"] = "プリンセスの願いを叶えることだけが唯一の原動力となっている、才能の低い訓練生。",
["ENEMY_PALACE_GUARD_EXTRA"] = "- 近接\n- 低装甲",
["ENEMY_PALACE_GUARD_NAME"] = "宮殿の護衛",
["ENEMY_PUMPKIN_WITCH_DESCRIPTION"] = "敵がパンプリングに変身。簡単に踏みつぶせます。",
["ENEMY_PUMPKIN_WITCH_EXTRA"] = "- ブロック不可能",
["ENEMY_PUMPKIN_WITCH_FLYING_DESCRIPTION"] = "敵がパンプリングに変身。簡単に踏みつぶせます。",
["ENEMY_PUMPKIN_WITCH_FLYING_EXTRA"] = "- ブロック不可能",
["ENEMY_PUMPKIN_WITCH_FLYING_NAME"] = "パンプリング",
["ENEMY_PUMPKIN_WITCH_NAME"] = "パンプリング",
["ENEMY_QIONGQI_DESCRIPTION"] = "雷の力で攻撃する獰猛な飛行ライオン。嵐の王者。",
["ENEMY_QIONGQI_EXTRA"] = "- 飛行\n- 非常に高いダメージ\n- 中程度の魔法耐性",
["ENEMY_QIONGQI_NAME"] = "Qiongqi",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_DESCRIPTION"] = "兄弟たちに鶏を配達して数年、彼らはとても速くなったので、時々鶏を持ってくることさえ忘れることがあります。",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_EXTRA"] = "- 高速\n- 遠隔\n- 注意！ゲーターに鶏の脚を届けて進化させることができます",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_NAME"] = "クイックフィート",
["ENEMY_QUICKFEET_GATOR_DESCRIPTION"] = "兄弟たちに鶏を配達して数年、彼らはとても速くなったので、時々鶏を持ってくることさえ忘れることがあります。",
["ENEMY_QUICKFEET_GATOR_EXTRA"] = "- 高速\n- 遠隔\n- 注意！ゲーターに鶏の脚を届けて進化させることができます",
["ENEMY_QUICKFEET_GATOR_NAME"] = "クイックフィート",
["ENEMY_REVENANT_HARVESTER_DESCRIPTION"] = "古の巫女が今や森をさまよい、スペクターを通じてその影響力を広げる。",
["ENEMY_REVENANT_HARVESTER_EXTRA"] = "- 近くのスペクターをリヴァナントハーヴェスターに変える",
["ENEMY_REVENANT_HARVESTER_NAME"] = "リヴァナントハーヴェスター",
["ENEMY_REVENANT_SOULCALLER_DESCRIPTION"] = "死の魔法に引き寄せられたエルフの魔法使いが地から蘇り、倒れた者のスペクターを召喚する。",
["ENEMY_REVENANT_SOULCALLER_EXTRA"] = "- タワーを無効にする\n- スペクターを召喚する",
["ENEMY_REVENANT_SOULCALLER_NAME"] = "リヴァナントソウルコーラー",
["ENEMY_RHINO_DESCRIPTION"] = "戦場を蔑ろに踏みにじる生きた破城槌。",
["ENEMY_RHINO_EXTRA"] = "- ミニボス\n- 敵に向かって突進します",
["ENEMY_RHINO_NAME"] = "破壊するサイ",
["ENEMY_RHINO_SPECIAL"] = "敵に向かって突進する。",
["ENEMY_ROLLING_SENTRY_DESCRIPTION"] = "撃墜されても地上で獲物を狙い続ける。",
["ENEMY_ROLLING_SENTRY_EXTRA"] = "- 倒されるとスクラップになる\n- 遠隔",
["ENEMY_ROLLING_SENTRY_NAME"] = "ローリングセンチネル",
["ENEMY_SCRAP_DRONE_DESCRIPTION"] = "部隊を悩ませることだけを目的に粗雑に組み立てられた機械。",
["ENEMY_SCRAP_DRONE_EXTRA"] = "- 飛行ユニット",
["ENEMY_SCRAP_DRONE_NAME"] = "スクラップ・ドローン",
["ENEMY_SCRAP_SPEEDSTER_DESCRIPTION"] = "うるさくて厄介、スピードを求める存在。",
["ENEMY_SCRAP_SPEEDSTER_EXTRA"] = "- 倒されるとスクラップになる",
["ENEMY_SCRAP_SPEEDSTER_NAME"] = "スクラップ・スピードスター",
["ENEMY_SKUNK_BOMBARDIER_DESCRIPTION"] = "自然界の毒素を別のレベルに引き上げ、スカンクは敵のラインに混乱を広げる。",
["ENEMY_SKUNK_BOMBARDIER_EXTRA"] = "- 低速度\n- 中等の魔法耐性\n- 攻撃はプレイヤーのユニットを弱化させる。\n- 死んだときに爆発する",
["ENEMY_SKUNK_BOMBARDIER_NAME"] = "スカンク・ボンバルディア",
["ENEMY_SKUNK_BOMBARDIER_SPECIAL"] = "攻撃はプレイヤーのユニットを弱化させる。死んだときに爆発し、ダメージを与える。",
["ENEMY_SMALL_STALKER_DESCRIPTION"] = "カルトの魔法で腐敗したこれらのクラウドストーカーは、戦場をテレポートして混乱をまき散らす.",
["ENEMY_SMALL_STALKER_EXTRA"] = "- 攻撃されたときに前方へテレポートする",
["ENEMY_SMALL_STALKER_NAME"] = "コラプテッドストーカー",
["ENEMY_SMALL_STALKER_SPECIAL"] = "短距離をテレポートし、攻撃を回避する.",
["ENEMY_SPECTER_DESCRIPTION"] = "地上の崩壊を超えて奴隷化され、生者を脅かす存在。",
["ENEMY_SPECTER_EXTRA"] = "- 他の敵や要素と相互作用できる",
["ENEMY_SPECTER_NAME"] = "スペクター",
["ENEMY_SPIDEAD_DESCRIPTION"] = "スパイダークイーン・ミガレの直系の子孫であるこれらのクモは、死後でさえも厄介な存在であり続ける。",
["ENEMY_SPIDEAD_EXTRA"] = "- 魔法耐性\n- 死亡時にクモの巣を生成",
["ENEMY_SPIDEAD_NAME"] = "シルクドーター",
["ENEMY_SPIDERLING_DESCRIPTION"] = "カルトの魔法で強化されたクモ。速くて激しい。噛みつくだろう。",
["ENEMY_SPIDERLING_EXTRA"] = "- 高速\n- 低い魔法耐性",
["ENEMY_SPIDERLING_NAME"] = "グレアブルード",
["ENEMY_SPIDER_PRIEST_DESCRIPTION"] = "新たな神に絡め取られた司祭たちは、闇の魔法を振るいながら戦場を進む。",
["ENEMY_SPIDER_PRIEST_EXTRA"] = "- 高い魔法耐性\n- 瀕死時にグレアウォーデンに変化",
["ENEMY_SPIDER_PRIEST_NAME"] = "蜘蛛の巫",
["ENEMY_SPIDER_SISTER_DESCRIPTION"] = "スパイダークイーンを深く信奉する彼女たちは、魔法を操りその眷属を召喚する。",
["ENEMY_SPIDER_SISTER_EXTRA"] = "- 魔法耐性\n- グレアブルードを召喚",
["ENEMY_SPIDER_SISTER_NAME"] = "スパイダーシスター",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_DESCRIPTION"] = "戦いに介入するためにミドリアスが使用する影のドッペルゲンガー。",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_EXTRA"] = "- 敵をダメージから守る\n- 闇の触手でタワーを罠にかける",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_NAME"] = "ミドリアスの幻影",
["ENEMY_STORM_ELEMENTAL_DESCRIPTION"] = "台風、雷、怒りから生まれた強力なエレメンタル。アッシュスピリットの遠い親戚。",
["ENEMY_STORM_ELEMENTAL_EXTRA"] = "- 高い装甲\n- 遠距離攻撃\n- 死亡時に近くのタワーをスタンさせる",
["ENEMY_STORM_ELEMENTAL_NAME"] = "テンペストスピリット",
["ENEMY_STORM_SPIRIT_DESCRIPTION"] = "嵐の雲を飛び跳ねる小さなドラゴンたちが、巧みに危険と敵をかわしていく。",
["ENEMY_STORM_SPIRIT_EXTRA"] = "- 飛行\n- 低い魔法耐性\n- ダメージを受けると前方に突進",
["ENEMY_STORM_SPIRIT_NAME"] = "ストーム・ドレクリング",
["ENEMY_SURVEILLANCE_SENTRY_DESCRIPTION"] = "空から敵を監視するためにドワーフが設計した機械。",
["ENEMY_SURVEILLANCE_SENTRY_EXTRA"] = "- 飛行ユニット",
["ENEMY_SURVEILLANCE_SENTRY_NAME"] = "飛行センチネル",
["ENEMY_SURVEYOR_HARPY_DESCRIPTION"] = "腐肉を探して、ハゲワシは野生の獣をどこへでも追いかける。",
["ENEMY_SURVEYOR_HARPY_EXTRA"] = "- 飛行中",
["ENEMY_SURVEYOR_HARPY_NAME"] = "パトロール・ヴァルチャー",
["ENEMY_SURVEYOR_HARPY_SPECIAL"] = "飛行中.",
["ENEMY_TERRACOTA_DESCRIPTION"] = "気をそらすために現れた影。",
["ENEMY_TERRACOTA_EXTRA"] = "- 近接戦闘",
["ENEMY_TERRACOTA_NAME"] = "幻影のデコイ",
["ENEMY_TOWER_RAY_SHEEP_DESCRIPTION"] = "メェェェェェェ.",
["ENEMY_TOWER_RAY_SHEEP_EXTRA"] = "- ブロック不可",
["ENEMY_TOWER_RAY_SHEEP_FLYING_DESCRIPTION"] = "メェェェェェェ.",
["ENEMY_TOWER_RAY_SHEEP_FLYING_EXTRA"] = "- 飛んで",
["ENEMY_TOWER_RAY_SHEEP_FLYING_NAME"] = "飛ぶ羊",
["ENEMY_TOWER_RAY_SHEEP_NAME"] = "羊",
["ENEMY_TURTLE_SHAMAN_DESCRIPTION"] = "平和に見えるが意地悪な精神を持つシャーマンは、野生の獣を修理して戦闘準備をさせている。",
["ENEMY_TURTLE_SHAMAN_EXTRA"] = "- 低速\n- 高HP\n- 高魔法耐性\n- 敵ユニットを回復させる",
["ENEMY_TURTLE_SHAMAN_NAME"] = "タートルシャーマン",
["ENEMY_TURTLE_SHAMAN_SPECIAL"] = "敵ユニットを回復させる。",
["ENEMY_TUSKED_BRAWLER_DESCRIPTION"] = "侵略者よりも執念深く、がたがたの鎧を身に着けている。いつでも乱闘に備えている。",
["ENEMY_TUSKED_BRAWLER_EXTRA"] = "- 低い防具",
["ENEMY_TUSKED_BRAWLER_NAME"] = "タスクド・ブロウラー",
["ENEMY_UNBLINDED_ABOMINATION_DESCRIPTION"] = "カルトの完全に腐敗した司祭は、戦闘での野蛮さで知られている。",
["ENEMY_UNBLINDED_ABOMINATION_EXTRA"] = "- 体力が低いユニットを食べる",
["ENEMY_UNBLINDED_ABOMINATION_NAME"] = "カルトの憎悪",
["ENEMY_UNBLINDED_ABOMINATION_SPECIAL"] = "時々、体力が低いユニットを食べる",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_DESCRIPTION"] = "エルフを奴隷化した後、一部のアボミネーションが鉱山での作業がスムーズに行われるよう指名されました。",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_EXTRA"] = "- エルフを解放するために殺されるべき",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_NAME"] = "監督官アボミネーション",
["ENEMY_UNBLINDED_PRIEST_DESCRIPTION"] = "祈りとオカルトの間で、司祭たちは黒魔術を振るって戦いに入る。",
["ENEMY_UNBLINDED_PRIEST_EXTRA"] = "- 高い魔法耐性\n- 死に近づくとアボミネーションに変わる",
["ENEMY_UNBLINDED_PRIEST_NAME"] = "カルトの司祭",
["ENEMY_UNBLINDED_PRIEST_SPECIAL"] = "体力が低下すると、アボミネーションに変わる。",
["ENEMY_UNBLINDED_SHACKLER_DESCRIPTION"] = "腕に埋め込まれたクリスタルを通じて邪悪な魔法を流し込む、シャックラーは近距離で恐ろしい敵である",
["ENEMY_UNBLINDED_SHACKLER_EXTRA"] = "- 中程度の魔法耐性\n- 体力が少ない時に塔を無効化する",
["ENEMY_UNBLINDED_SHACKLER_NAME"] = "シャックラー",
["ENEMY_UNBLINDED_SHACKLER_SPECIAL"] = "塔を束縛し、攻撃を防ぐ",
["ENEMY_VILE_SPAWNER_DESCRIPTION"] = "敵に多くの飛ぶ目を投げつけて、邪悪なスポナーはいつもあらゆる方向を見ています。",
["ENEMY_VILE_SPAWNER_EXTRA"] = "- 小さい目を生成します。",
["ENEMY_VILE_SPAWNER_NAME"] = "邪悪なスポナー",
["ENEMY_WATER_SORCERESS_DESCRIPTION"] = "古のエレメンタルキャスター。水の力で味方を癒し、遠くの敵を討つ。",
["ENEMY_WATER_SORCERESS_EXTRA"] = "- 遠距離\n- 中程度の魔法耐性\n- 味方を回復する",
["ENEMY_WATER_SORCERESS_NAME"] = "Water Master",
["ENEMY_WATER_SPIRIT_DESCRIPTION"] = "魂のない水の存在が容赦ない波となって押し寄せ、怒りとともに岸を襲う。",
["ENEMY_WATER_SPIRIT_EXTRA"] = "- 低い魔法耐性\n- 水から出現可能",
["ENEMY_WATER_SPIRIT_NAME"] = "Water Spirit",
["ENEMY_WATER_SPIRIT_SPAWNLESS_DESCRIPTION"] = "魂のない水の存在が容赦ない波となって押し寄せ、怒りとともに岸を襲う。",
["ENEMY_WATER_SPIRIT_SPAWNLESS_EXTRA"] = "- 低い魔法耐性\n- 水から出現可能",
["ENEMY_WATER_SPIRIT_SPAWNLESS_NAME"] = "Water Spirit",
["ENEMY_WUXIAN_DESCRIPTION"] = "強力で耐久性のある魔術師で、魔法で敵を壊滅させる。",
["ENEMY_WUXIAN_EXTRA"] = "- 遠距離\n- 中装甲\n- 炎上地面での特殊攻撃",
["ENEMY_WUXIAN_NAME"] = "Wuxian",
["ERROR_MESSAGE_GENERIC"] = "おっと！何らかの問題が発生しました。",
["Earn huge bonus points and gold by calling waves earlier!"] = "敵部隊を早く呼び込んで膨大なボーナスポイントとゴールドを獲得しよう！",
["Encyclopedia"] = "エンサイクロペディア",
["Enemies"] = "敵",
["Extreme"] = "エクストリーム",
["FIRST_WEEK_PACK"] = "贈り物 ",
["Face an endless unrelenting enemy force and try to defeat as many as possible to comete for the best score!"] = "容赦を知らぬ終わりなき敵軍に挑み、できるだけ多くの敵を撃破して最高スコアを目指そう！",
["Face an endless unrelenting enemy force and try to defeat as many as possible to compete for the best score!"] = "容赦を知らぬ終わりなき敵軍に挑み、できるだけ多くの敵を撃破して最高スコアを目指そう！",
["Fast"] = "高速",
["For beginners to strategy games!"] = "ストラテジーゲームのビギナー向け！",
["GAME_TITLE_KR5"] = "キングダム ラッシュ 5：アライアンス",
["GEMS_BARREL_NAME"] = "樽一杯のジェム",
["GEMS_CHEST_NAME"] = "箱一杯のジェム",
["GEMS_HANDFUL_NAME"] = "ひとつかみのジェム",
["GEMS_MOUNTAIN_NAME"] = "ジェムの山",
["GEMS_POUCH_NAME"] = "袋一杯のジェム",
["GEMS_WAGON_NAME"] = "ジェムの荷車",
["GET_ALL_AWESOME_HEROES"] = "最高の英雄たちをすべて手に入れよう",
["GET_THIS_AWESOME"] = "この強力な英雄を\n手に入れよう",
["GET_THIS_AWESOME_2"] = "この強力な英雄たちを\n手に入れよう",
["GET_THIS_AWESOME_3"] = "この強力な英雄たちを\n手に入れよう",
["GIFT_CLAIMED"] = "ギフトを受け取りました！",
["GOOGLE_PLAY"] = "Google Play",
["Got it!"] = "了解！",
["Great"] = "素晴らしい",
["HERO LEVEL UP!"] = "英雄がレベルアップ！",
["HERO ROOM"] = "ヒーローズ",
["HERO UNLOCKED!"] = "英雄をアンロック！",
["HEROES"] = "英雄",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_1"] = "エリア上空を飛んで%$heroes.hero_bird.ultimate.bird.duration[2]%$秒間敵を襲い、毎回%$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[2]%$のダメージを与えるグリフォンを召喚します。",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_2"] = "エリア上空を飛んで%$heroes.hero_bird.ultimate.bird.duration[3]%$秒間敵を襲い、毎回%$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[3]%$のダメージを与えるグリフォンを召喚します。",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_3"] = "エリア上空を飛んで%$heroes.hero_bird.ultimate.bird.duration[4]%$秒間敵を襲い、毎回%$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[4]%$のダメージを与えるグリフォンを召喚します。",
["HERO_BIRD_BIRDS_OF_PREY_MENUBOTTOM_DESCRIPTION"] = "エリア上空を飛び敵を攻撃するグリフォンを召喚する。",
["HERO_BIRD_BIRDS_OF_PREY_MENUBOTTOM_NAME"] = "戦いの鳥",
["HERO_BIRD_BIRDS_OF_PREY_TITLE"] = "戦いの鳥たち",
["HERO_BIRD_CLASS"] = "エースライダー",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_1"] = "敵に向けて爆発物を投げ、それぞれに%$heroes.hero_bird.cluster_bomb.explosion_damage_min[1]%$のダメージを与え、%$heroes.hero_bird.cluster_bomb.fire_duration[1]%$秒間、床に火をつけ、3秒間にわたって敵を%$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$のダメージで燃やします。",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_2"] = "敵に向けて爆発物を投げ、それぞれに%$heroes.hero_bird.cluster_bomb.explosion_damage_min[2]%$のダメージを与え、%$heroes.hero_bird.cluster_bomb.fire_duration[2]%$秒間、床に火をつけ、3秒間にわたって敵を%$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$のダメージで燃やします。",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_3"] = "敵に向けて爆発物を投げ、それぞれに%$heroes.hero_bird.cluster_bomb.explosion_damage_min[3]%$のダメージを与え、%$heroes.hero_bird.cluster_bomb.fire_duration[3]%$秒間、床に火をつけ、3秒間にわたって敵を%$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$のダメージで燃やします。",
["HERO_BIRD_CLUSTER_BOMB_TITLE"] = "絨毯爆撃",
["HERO_BIRD_DESC"] = "勇敢なグリフォンライダーは、鋼鉄と火の武器を振りながら戦いに飛び込む。ダークアーミーが彼の家を侵略して以来渋々アライアンスに加わったが、ブローデンはリニレアの既成概念を回復する手段としてカルトに破壊を降り注ぐことに同意した。",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_1"] = "グリフォンは地面に飛び込み、最大%$heroes.hero_bird.eat_instakill.hp_max[1]%$の体力を持つ敵を食べます。",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_2"] = "グリフォンは地面に飛び込み、最大%$heroes.hero_bird.eat_instakill.hp_max[2]%$の体力を持つ敵を食べます。",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_3"] = "グリフォンは地面に飛び込み、最大%$heroes.hero_bird.eat_instakill.hp_max[3]%$の体力を持つ敵を食べます。",
["HERO_BIRD_EAT_INSTAKILL_TITLE"] = "ハンティング・ダイブ",
["HERO_BIRD_GATTLING_DESCRIPTION_1"] = "敵に対して弾丸を降らせ、%$heroes.hero_bird.gattling.s_damage_min[1]%$-%$heroes.hero_bird.gattling.s_damage_max[1]%$の物理ダメージを与える。",
["HERO_BIRD_GATTLING_DESCRIPTION_2"] = "敵に対して弾丸を降らせ、%$heroes.hero_bird.gattling.s_damage_min[2]%$-%$heroes.hero_bird.gattling.s_damage_max[2]%$の物理ダメージを与える。",
["HERO_BIRD_GATTLING_DESCRIPTION_3"] = "敵に対して弾丸を降らせ、%$heroes.hero_bird.gattling.s_damage_min[3]%$-%$heroes.hero_bird.gattling.s_damage_max[3]%$の物理ダメージを与える。",
["HERO_BIRD_GATTLING_TITLE"] = "模範的なリード",
["HERO_BIRD_NAME"] = "ブローデン",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_1"] = "グリフォンは耳をつんざくような悲鳴をあげ、敵を%$heroes.hero_bird.shout_stun.stun_duration[1]%$秒間気絶させ、その後%$heroes.hero_bird.shout_stun.slow_duration[1]%$秒間遅くさせます。",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_2"] = "グリフォンは耳をつんざくような悲鳴をあげ、敵を%$heroes.hero_bird.shout_stun.stun_duration[2]%$秒間気絶させ、その後%$heroes.hero_bird.shout_stun.slow_duration[2]%$秒間遅くさせます。",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_3"] = "グリフォンは耳をつんざくような悲鳴をあげ、敵を%$heroes.hero_bird.shout_stun.stun_duration[3]%$秒間気絶させ、その後%$heroes.hero_bird.shout_stun.slow_duration[3]%$秒間遅くさせます。",
["HERO_BIRD_SHOUT_STUN_TITLE"] = "テラー・シュリーク",
["HERO_BUILDER_CLASS"] = "マスターフォアマン",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_1"] = "通りがかりの敵を%$heroes.hero_builder.defensive_turret.duration[1]%$秒間攻撃する応急の塔を建設し、攻撃ごとに%$heroes.hero_builder.defensive_turret.attack.damage_min[1]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[1]%$の物理ダメージを与えます。",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_2"] = "通りがかりの敵を%$heroes.hero_builder.defensive_turret.duration[2]%$秒間攻撃する応急の塔を建設し、攻撃ごとに%$heroes.hero_builder.defensive_turret.attack.damage_min[2]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[2]%$の物理ダメージを与えます。",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_3"] = "通りがかりの敵を%$heroes.hero_builder.defensive_turret.duration[3]%$秒間攻撃する応急の塔を建設し、攻撃ごとに%$heroes.hero_builder.defensive_turret.attack.damage_min[3]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[3]%$の物理ダメージを与えます。",
["HERO_BUILDER_DEFENSIVE_TURRET_TITLE"] = "防御タレット",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_1"] = "素早く木製のビームを回転させ、周囲の敵に%$heroes.hero_builder.demolition_man.s_damage_min[1]%$-%$heroes.hero_builder.demolition_man.s_damage_max[1]%$の物理ダメージを与えます。",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_2"] = "素早く木製のビームを回転させ、周囲の敵に%$heroes.hero_builder.demolition_man.s_damage_min[2]%$-%$heroes.hero_builder.demolition_man.s_damage_max[2]%$の物理ダメージを与えます。",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_3"] = "素早く木製のビームを回転させ、周囲の敵に%$heroes.hero_builder.demolition_man.s_damage_min[3]%$-%$heroes.hero_builder.demolition_man.s_damage_max[3]%$の物理ダメージを与えます。",
["HERO_BUILDER_DEMOLITION_MAN_TITLE"] = "解体工",
["HERO_BUILDER_DESC"] = "リニリアの防衛を指揮してきた年月は、トレスに戦いについての一つや二つを教え込んだ。今や全王国が危機に瀕している（そして傍観に飽きている）ため、彼はその全ての道具と知識を戦いに活かしている。",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_1"] = "トーレスはスナックを食べるために戦いを止め、%$heroes.hero_builder.lunch_break.heal_hp[1]%$の健康を回復します。",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_2"] = "トーレスはスナックを食べるために戦いを止め、%$heroes.hero_builder.lunch_break.heal_hp[2]%$の健康を回復します。",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_3"] = "トーレスはスナックを食べるために戦いを止め、%$heroes.hero_builder.lunch_break.heal_hp[3]%$の健康を回復します。",
["HERO_BUILDER_LUNCH_BREAK_TITLE"] = "ランチブレイク",
["HERO_BUILDER_NAME"] = "トーレス",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_1"] = "彼の側で戦う2人の建設者を%$heroes.hero_builder.overtime_work.soldier.duration%$秒間呼び出す。",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_2"] = "建設者は%$heroes.hero_builder.overtime_work.soldier.hp_max[2]%$の体力を持ち、%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_max[2]%$の物理ダメージを与えます。彼らは%$heroes.hero_builder.overtime_work.soldier.duration%$秒間戦います。",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_3"] = "建築家は%$heroes.hero_builder.overtime_work.soldier.hp_max[3]%$の体力を持ち、%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_max[3]%$の物理ダメージを与えます。彼らは%$heroes.hero_builder.overtime_work.soldier.duration%$秒間戦います。 ",
["HERO_BUILDER_OVERTIME_WORK_TITLE"] = "作業中の男性",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_1"] = "道に巨大な鉄球を落とし、%$heroes.hero_builder.ultimate.damage[2]%$の物理ダメージを与え、%$heroes.hero_builder.ultimate.stun_duration[2]%$秒間敵を気絶させます。",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_2"] = "道に巨大な鉄球を落とし、%$heroes.hero_builder.ultimate.damage[3]%$の物理ダメージを与え、%$heroes.hero_builder.ultimate.stun_duration[3]%$秒間敵を気絶させます。",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_3"] = "道に巨大な鉄球を落とし、%$heroes.hero_builder.ultimate.damage[4]%$の物理ダメージを与え、%$heroes.hero_builder.ultimate.stun_duration[4]%$秒間敵を気絶させます。",
["HERO_BUILDER_WRECKING_BALL_MENUBOTTOM_DESCRIPTION"] = "道に破壊ボールを落とし、敵にダメージを与えます。",
["HERO_BUILDER_WRECKING_BALL_MENUBOTTOM_NAME"] = "破壊ボール",
["HERO_BUILDER_WRECKING_BALL_TITLE"] = "破壊ボール",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_1"] = "Sylvaraは%$heroes.hero_dragon_arb.ultimate.duration[2]%$秒間、真の姿を解き放ち、%$heroes.hero_dragon_arb.ultimate.s_bonuses[2]%$%のダメージ、速度、耐性を得て、彼女の能力のいくつかを進化させます。",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_2"] = "Sylvaraは%$heroes.hero_dragon_arb.ultimate.duration[3]%$秒間、真の姿を解き放ち、%$heroes.hero_dragon_arb.ultimate.s_bonuses[3]%$%のダメージ、速度、耐性を得て、彼女の能力のいくつかを進化させます。",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_3"] = "Sylvaraは%$heroes.hero_dragon_arb.ultimate.duration[4]%$秒間、真の姿を解き放ち、%$heroes.hero_dragon_arb.ultimate.s_bonuses[4]%$%のダメージ、速度、耐性を得て、彼女の能力のいくつかを進化させます。",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_MENUBOTTOM_DESCRIPTION"] = "シルヴァラの真の姿を解放せよ。",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_MENUBOTTOM_NAME"] = "内なる本質",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_TITLE"] = "内なる自然",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_1"] = "緑のパッチを、%$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[1]%$秒間戦うアーボリアンに変換し、内なる自然の間、より強力なアーボリアンを召喚します。",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_2"] = "緑のパッチを、%$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[2]%$秒間戦うアーボリアンに変換し、内なる自然の間、より強力なアーボリアンを召喚します。",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_3"] = "緑のパッチを、%$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[3]%$秒間戦うアーボリアンに変換し、内なる自然の間、より強力なアーボリアンを召喚します。",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_TITLE"] = "森の呼び声",
["HERO_DRAGON_ARB_CLASS"] = "自然の力",
["HERO_DRAGON_ARB_DESC"] = "自然のドラゴンでありアーボリアンの守護者である彼女は、息で森を織り成し、翼で風を踊らせます。自然そのもののように、彼女は慈愛と罰の両方を持ち合わせています。ゴミを捨てないようにしてください！",
["HERO_DRAGON_ARB_NAME"] = "シルバラ",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_1"] = "%$heroes.hero_dragon_arb.thorn_bleed.cooldown[1]%$秒ごとに、シルヴァラは次のブレスを強化して敵をその速度に応じてダメージを与え、内なる自然の間には%$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[1]%$%の確率で即死させるチャンスがあります。",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_2"] = "%$heroes.hero_dragon_arb.thorn_bleed.cooldown[2]%$秒ごとに、シルヴァラは次のブレスを強化して敵をその速度に応じてダメージを与え、内なる自然の間には%$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[2]%$%の確率で即死させるチャンスがあります。",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_3"] = "%$heroes.hero_dragon_arb.thorn_bleed.cooldown[3]%$秒ごとに、シルヴァラは次のブレスを強化して敵をその速度に応じてダメージを与え、内なる自然の間には%$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[3]%$%の確率で即死させるチャンスがあります。",
["HERO_DRAGON_ARB_THORN BLEED_TITLE"] = "棘の息",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_1"] = "近くのタワーのダメージを %$heroes.hero_dragon_arb.tower_runes.s_damage_factor[1]%$% 増加させ、%$heroes.hero_dragon_arb.tower_runes.duration[1]%$ 秒間持続します。",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_2"] = "近くのタワーのダメージを %$heroes.hero_dragon_arb.tower_runes.s_damage_factor[2]%$% 増加させ、%$heroes.hero_dragon_arb.tower_runes.duration[2]%$ 秒間持続します。",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_3"] = "近くのタワーのダメージを %$heroes.hero_dragon_arb.tower_runes.s_damage_factor[3]%$% 増加させ、%$heroes.hero_dragon_arb.tower_runes.duration[3]%$ 秒間持続します。",
["HERO_DRAGON_ARB_TOWER RUNES_TITLE"] = "深い根",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_1"] = "タワーの近くに %$heroes.hero_dragon_arb.tower_plants.duration[1]%$ 秒続く植物を召喚します。その忠誠に応じて、ダメージを与えて速度を遅くする毒の植物になるか、味方を回復させる治療の植物になります。",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_2"] = "タワーの近くに %$heroes.hero_dragon_arb.tower_plants.duration[2]%$ 秒続く植物を召喚します。その忠誠に応じて、ダメージを与えて速度を遅くする毒の植物になるか、味方を回復させる治療の植物になります。",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_3"] = "タワーの近くに %$heroes.hero_dragon_arb.tower_plants.duration[3]%$ 秒続く植物を召喚します。その忠誠に応じて、ダメージを与えて速度を遅くする毒の植物になるか、味方を回復させる治療の植物になります。",
["HERO_DRAGON_ARB_TOWER_PLANTS_TITLE"] = "命のもたらし手",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_1"] = "%$heroes.hero_dragon_bone.burst.proj_count[1]%$の魔法の弾を発射し、それぞれが%$heroes.hero_dragon_bone.burst.damage_min[1]%$-%$heroes.hero_dragon_bone.burst.damage_max[1]%$の真のダメージを与え、疫病を適用します。",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_2"] = "%$heroes.hero_dragon_bone.burst.proj_count[2]%$の魔法の弾を発射し、それぞれが%$heroes.hero_dragon_bone.burst.damage_min[2]%$-%$heroes.hero_dragon_bone.burst.damage_max[2]%$の真のダメージを与え、疫病を適用します。",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_3"] = "%$heroes.hero_dragon_bone.burst.proj_count[3]%$の魔法の弾を発射し、それぞれが%$heroes.hero_dragon_bone.burst.damage_min[3]%$-%$heroes.hero_dragon_bone.burst.damage_max[3]%$の真のダメージを与え、疫病を適用します。",
["HERO_DRAGON_BONE_BURST_TITLE"] = "スプレッディングバースト",
["HERO_DRAGON_BONE_CLASS"] = "ドラコリッチ",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_1"] = "疫病を適用し、敵を%$heroes.hero_dragon_bone.cloud.duration[1]%$秒間減速させる病的な雲でエリアを覆います。",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_2"] = "疫病を適用し、敵を%$heroes.hero_dragon_bone.cloud.duration[2]%$秒間減速させる病的な雲でエリアを覆います。",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_3"] = "疫病を適用し、敵を%$heroes.hero_dragon_bone.cloud.duration[3]%$秒間減速させる病的な雲でエリアを覆います。",
["HERO_DRAGON_BONE_CLOUD_TITLE"] = "疫病の雲",
["HERO_DRAGON_BONE_DESC"] = "ヴェズナンが征服のキャンペーン中に解放した後、ボーンハートはダークウィザードの計画に脅威をもたらす可能性のある魔法使いを探し出すためにその力を使うことで、彼への借りを返すことを申し出ました。",
["HERO_DRAGON_BONE_NAME"] = "ボーンハート",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_1"] = "道に突進し、%$heroes.hero_dragon_bone.nova.damage_min[1]%$-%$heroes.hero_dragon_bone.nova.damage_max[1]%$の爆発ダメージを敵に与え、疫病を適用します。",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_2"] = "道に突進し、%$heroes.hero_dragon_bone.nova.damage_min[2]%$-%$heroes.hero_dragon_bone.nova.damage_max[2]%$の爆発ダメージを敵に与え、疫病を適用します。",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_3"] = "道に突進し、%$heroes.hero_dragon_bone.nova.damage_min[3]%$-%$heroes.hero_dragon_bone.nova.damage_max[3]%$の爆発ダメージを敵に与え、疫病を適用します。",
["HERO_DRAGON_BONE_NOVA_TITLE"] = "疫病ノヴァ",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_1"] = "%$heroes.hero_dragon_bone.rain.bones_count[1]%$の骨のスパインを敵に向かって投げ、%$heroes.hero_dragon_bone.rain.damage_min[1]%$-%$heroes.hero_dragon_bone.rain.damage_max[1]%$の真のダメージを与え、一時的にスタンさせます。",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_2"] = "%$heroes.hero_dragon_bone.rain.bones_count[2]%$の骨のスパインを敵に向かって投げ、%$heroes.hero_dragon_bone.rain.damage_min[2]%$-%$heroes.hero_dragon_bone.rain.damage_max[2]%$の真のダメージを与え、一時的にスタンさせます。",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_3"] = "%$heroes.hero_dragon_bone.rain.bones_count[3]%$の骨のスパインを敵に向かって投げ、%$heroes.hero_dragon_bone.rain.damage_min[3]%$-%$heroes.hero_dragon_bone.rain.damage_max[3]%$の真のダメージを与え、一時的にスタンさせます。",
["HERO_DRAGON_BONE_RAIN_TITLE"] = "スパインレイン",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_1"] = "2体のボーンドレイクを召喚します。それぞれのドレイクは%$heroes.hero_dragon_bone.ultimate.dog.hp[2]%$の体力を持ち、%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[2]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[2]%$の物理ダメージを与えます。",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_2"] = "2体のボーンドレイクを召喚します。それぞれのドレイクは%$heroes.hero_dragon_bone.ultimate.dog.hp[3]%$の体力を持ち、%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[3]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[3]%$の物理ダメージを与えます。",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_3"] = "2体のボーンドレイクを召喚します。それぞれのドレイクは%$heroes.hero_dragon_bone.ultimate.dog.hp[4]%$の体力を持ち、%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[4]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[4]%$の物理ダメージを与えます。",
["HERO_DRAGON_BONE_RAISE_DRAKES_MENUBOTTOM_DESCRIPTION"] = "2体のボーンドレイクを召喚します。",
["HERO_DRAGON_BONE_RAISE_DRAKES_MENUBOTTOM_NAME"] = "ドラゴン召喚",
["HERO_DRAGON_BONE_RAISE_DRAKES_TITLE"] = "ドラゴン召喚",
["HERO_DRAGON_GEM_CLASS"] = "折れない",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_1"] = "数秒間、敵をクリスタルで包み込む。その後、クリスタルが爆発してターゲットを即死させ、周囲に%$heroes.hero_dragon_gem.crystal_instakill.s_damage[1]%$の真実のダメージを与える。",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_2"] = "数秒間、敵をクリスタルで包み込む。その後、クリスタルが爆発してターゲットを即死させ、周囲に%$heroes.hero_dragon_gem.crystal_instakill.s_damage[2]%$の真実のダメージを与える。",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_3"] = "数秒間、敵をクリスタルで包み込む。その後、クリスタルが爆発してターゲットを即死させ、周囲に%$heroes.hero_dragon_gem.crystal_instakill.s_damage[3]%$の真実のダメージを与える。",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_TITLE"] = "ガーネットの墓",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_1"] = "道にクリスタルを投げて敵のスピードを%$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$%減速させ、周囲に1秒ごとに%$heroes.hero_dragon_gem.crystal_totem.s_damage[1]%$の魔法ダメージを与える。%$heroes.hero_dragon_gem.crystal_totem.duration[1]%$秒間持続する。",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_2"] = "道にクリスタルを投げて敵のスピードを%$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$%減速させ、周囲に1秒ごとに%$heroes.hero_dragon_gem.crystal_totem.s_damage[2]%$の魔法ダメージを与える。%$heroes.hero_dragon_gem.crystal_totem.duration[2]%$秒間持続する。",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_3"] = "道にクリスタルを投げて敵のスピードを%$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$%減速させ、周囲に1秒ごとに%$heroes.hero_dragon_gem.crystal_totem.s_damage[3]%$の魔法ダメージを与える。%$heroes.hero_dragon_gem.crystal_totem.duration[3]%$秒間持続する。",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_TITLE"] = "パワーコンジット",
["HERO_DRAGON_GEM_DESC"] = "コスミアの孤独な生活は、カルトが見捨てられた峡谷での活動を開始したときに中断されました。侵入者を排除したいと思って、ドラゴンは共通の敵に対する同盟に加わるためにヴェズナンと取引をしました。",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_1"] = "%$heroes.hero_dragon_gem.ultimate.max_shards[2]%$のクリスタル弾幕を召喚し、エリア内の敵に%$heroes.hero_dragon_gem.ultimate.damage_min[2]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[2]%$の真ダメージを与える。",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_2"] = "%$heroes.hero_dragon_gem.ultimate.max_shards[3]%$のクリスタル弾幕を召喚し、エリア内の敵に%$heroes.hero_dragon_gem.ultimate.damage_min[3]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[3]%$の真ダメージを与える。",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_3"] = "%$heroes.hero_dragon_gem.ultimate.max_shards[4]%$のクリスタル弾幕を召喚し、エリア内の敵に%$heroes.hero_dragon_gem.ultimate.damage_min[4]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[4]%$の真ダメージを与える。",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_MENUBOTTOM_DESCRIPTION"] = "敵に対して様々なクリスタルの弾幕を投げる。",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_MENUBOTTOM_NAME"] = "クリスタルアバランチ",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_TITLE"] = "クリスタルアバランチ",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_1"] = "自分の周りの道に結晶の棘を生やし、当たった各敵に%$heroes.hero_dragon_gem.floor_impact.damage_min[1]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[1]%$の物理ダメージを与える。",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_2"] = "自分の周りの道に結晶の棘を生やし、当たった各敵に%$heroes.hero_dragon_gem.floor_impact.damage_min[2]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[2]%$の物理ダメージを与える。",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_3"] = "自分の周りの道に結晶の棘を生やし、当たった各敵に%$heroes.hero_dragon_gem.floor_impact.damage_min[3]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[3]%$の物理ダメージを与える。",
["HERO_DRAGON_GEM_FLOOR_IMPACT_TITLE"] = "プリズマティックシャード",
["HERO_DRAGON_GEM_NAME"] = "コスミル",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_1"] = "敵のグループを結晶化し、%$heroes.hero_dragon_gem.stun.duration[1]%$秒間スタンさせる。",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_2"] = "敵のグループを結晶化し、%$heroes.hero_dragon_gem.stun.duration[2]%$秒間スタンさせる。",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_3"] = "敵のグループを結晶化し、%$heroes.hero_dragon_gem.stun.duration[3]%$秒間スタンさせる。",
["HERO_DRAGON_GEM_STUN_TITLE"] = "麻痺する息",
["HERO_HUNTER_BEASTS_DESCRIPTION_1"] = "近くの敵に攻撃する2匹のコウモリを召喚し、%$heroes.hero_hunter.beasts.duration[1]%$秒間、%$heroes.hero_hunter.beasts.damage_min[1]%$-%$heroes.hero_hunter.beasts.damage_max[1]%$の物理ダメージを与えます。各コウモリは、対象から%$heroes.hero_hunter.beasts.gold_to_steal[1]%$ゴールドを盗むチャンスがあります。",
["HERO_HUNTER_BEASTS_DESCRIPTION_2"] = "近くの敵に攻撃する2匹のコウモリを召喚し、%$heroes.hero_hunter.beasts.duration[2]%$秒間、%$heroes.hero_hunter.beasts.damage_min[2]%$-%$heroes.hero_hunter.beasts.damage_max[2]%$の物理ダメージを与えます。各コウモリは、対象から%$heroes.hero_hunter.beasts.gold_to_steal[2]%$ゴールドを盗むチャンスがあります。",
["HERO_HUNTER_BEASTS_DESCRIPTION_3"] = "近くの敵に攻撃する2匹のコウモリを召喚し、%$heroes.hero_hunter.beasts.duration[3]%$秒間、%$heroes.hero_hunter.beasts.damage_min[3]%$-%$heroes.hero_hunter.beasts.damage_max[3]%$の物理ダメージを与えます。各コウモリは、対象から%$heroes.hero_hunter.beasts.gold_to_steal[3]%$ゴールドを盗むチャンスがあります。",
["HERO_HUNTER_BEASTS_TITLE"] = "ダスクビースト",
["HERO_HUNTER_CLASS"] = "シルバーハントレス",
["HERO_HUNTER_DESC"] = "吸血鬼と著名なハンターの結合から生まれたアーニャは、父の足跡をたどり、暗闇の住人と戦っている。カルト教団員への執拗な狩りは、彼女をすぐに南の地へと導き、連合への加入となった。",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_1"] = "7回目の近接攻撃ごとに、%$heroes.hero_hunter.heal_strike.damage_min[1]%$から%$heroes.hero_hunter.heal_strike.damage_max[1]%$の真実のダメージを与え、目標の最大体力の%$heroes.hero_hunter.heal_strike.heal_factor[1]%$%分アーニャを回復します。",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_2"] = "7回目の近接攻撃ごとに、%$heroes.hero_hunter.heal_strike.damage_min[2]%$から%$heroes.hero_hunter.heal_strike.damage_max[2]%$の真実のダメージを与え、目標の最大体力の%$heroes.hero_hunter.heal_strike.heal_factor[2]%$%分アーニャを回復します。",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_3"] = "7回目の近接攻撃ごとに、%$heroes.hero_hunter.heal_strike.damage_min[3]%$から%$heroes.hero_hunter.heal_strike.damage_max[3]%$の真実のダメージを与え、目標の最大体力の%$heroes.hero_hunter.heal_strike.heal_factor[3]%$%分アーニャを回復します。",
["HERO_HUNTER_HEAL_STRIKE_TITLE"] = "吸血鬼の爪",
["HERO_HUNTER_NAME"] = "アーニャ",
["HERO_HUNTER_RICOCHET_DESCRIPTION_1"] = "アーニャは霧に変化し、%$heroes.hero_hunter.ricochet.s_bounces[1]%$敵間で跳ね返り、それぞれに%$heroes.hero_hunter.ricochet.damage_min[1]%$-%$heroes.hero_hunter.ricochet.damage_max[1]%$の物理ダメージを与える。",
["HERO_HUNTER_RICOCHET_DESCRIPTION_2"] = "アーニャは霧に変化し、%$heroes.hero_hunter.ricochet.s_bounces[2]%$敵間で跳ね返り、それぞれに%$heroes.hero_hunter.ricochet.damage_min[2]%$-%$heroes.hero_hunter.ricochet.damage_max[2]%$の物理ダメージを与える。",
["HERO_HUNTER_RICOCHET_DESCRIPTION_3"] = "アーニャは霧に変化し、%$heroes.hero_hunter.ricochet.s_bounces[3]%$敵間で跳ね返り、それぞれに%$heroes.hero_hunter.ricochet.damage_min[3]%$-%$heroes.hero_hunter.ricochet.damage_max[3]%$の物理ダメージを与える。",
["HERO_HUNTER_RICOCHET_TITLE"] = "ミスティ・ステップ",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_1"] = "周りの敵全員に対して攻撃を行い、それぞれに%$heroes.hero_hunter.shoot_around.s_damage_min[1]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[1]%$の真のダメージを与える。",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_2"] = "周りの敵全員に対して攻撃を行い、それぞれに%$heroes.hero_hunter.shoot_around.s_damage_min[2]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[2]%$の真実のダメージを毎秒与える。",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_3"] = "周りの敵全員に対して攻撃を行い、それぞれに%$heroes.hero_hunter.shoot_around.s_damage_min[3]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[3]%$の真実のダメージを毎秒与える。",
["HERO_HUNTER_SHOOT_AROUND_TITLE"] = "シルバーストーム",
["HERO_HUNTER_SPIRIT_DESCRIPTION_1"] = "ダンテの投影を召喚し、%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[2]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[2]%$の真のダメージを%$heroes.hero_hunter.ultimate.duration%$秒間、毎秒与える。アーニャの体が近くにある場合、彼女を復活させる。",
["HERO_HUNTER_SPIRIT_DESCRIPTION_2"] = "ダンテの投影を召喚し、%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[3]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[3]%$の真のダメージを%$heroes.hero_hunter.ultimate.duration%$秒間、毎秒与える。アーニャの体が近くにある場合、彼女を復活させる。",
["HERO_HUNTER_SPIRIT_DESCRIPTION_3"] = "ダンテの投影を召喚し、%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[4]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[4]%$の真のダメージを%$heroes.hero_hunter.ultimate.duration%$秒間、毎秒与える。アーニャの体が近くにある場合、彼女を復活させる。",
["HERO_HUNTER_SPIRIT_MENUBOTTOM_DESCRIPTION"] = "ダンテの投影を召喚し、敵を遅らせて攻撃します。",
["HERO_HUNTER_SPIRIT_MENUBOTTOM_NAME"] = "ハンターの援助",
["HERO_HUNTER_SPIRIT_TITLE"] = "ハンターズエイド",
["HERO_HUNTER_ULTIMATE_ENTITY_NAME"] = "ダンテの投影",
["HERO_LAVA_CLASS"] = "溶岩の怒り",
["HERO_LAVA_DESC"] = "悪い気性を持つ火のような破壊的な存在で、Grymbeardの活動によって深い眠りから目覚めた。対話は得意ではないため、Kratoaは敵の陣営を突破して、再び眠ることができるように冷静になるまで戦い続ける。",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_1"] = "溶岩の玉を投げ、%$heroes.hero_lava.double_trouble.s_damage[1]%$の爆発ダメージを敵に与え、%$heroes.hero_lava.double_trouble.soldier.hp_max[1]%$のHPを持つマグマイトを召喚し、%$heroes.hero_lava.double_trouble.soldier.duration%$秒間戦わせる。",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_2"] = "溶岩の玉を投げ、%$heroes.hero_lava.double_trouble.s_damage[2]%$の爆発ダメージを敵に与え、%$heroes.hero_lava.double_trouble.soldier.hp_max[2]%$のHPを持つマグマイトを召喚し、%$heroes.hero_lava.double_trouble.soldier.duration%$秒間戦わせる。",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_3"] = "溶岩の玉を投げ、%$heroes.hero_lava.double_trouble.s_damage[3]%$の爆発ダメージを敵に与え、%$heroes.hero_lava.double_trouble.soldier.hp_max[3]%$のHPを持つマグマイトを召喚し、%$heroes.hero_lava.double_trouble.soldier.duration%$秒間戦わせる。",
["HERO_LAVA_DOUBLE_TROUBLE_SOLDIER_NAME"] = "マグマイト",
["HERO_LAVA_DOUBLE_TROUBLE_TITLE"] = "ダブルトラブル",
["HERO_LAVA_HOTHEADED_DESCRIPTION_1"] = "Kratoaが復活すると、%$heroes.hero_lava.hotheaded.s_damage_factors[1]%$%のダメージバフを近くのタワーに%$heroes.hero_lava.hotheaded.durations[1]%$秒間付与する。",
["HERO_LAVA_HOTHEADED_DESCRIPTION_2"] = "Kratoaが復活すると、%$heroes.hero_lava.hotheaded.s_damage_factors[2]%$%のダメージバフを近くのタワーに%$heroes.hero_lava.hotheaded.durations[2]%$秒間付与する。",
["HERO_LAVA_HOTHEADED_DESCRIPTION_3"] = "Kratoaが復活すると、%$heroes.hero_lava.hotheaded.s_damage_factors[3]%$%のダメージバフを近くのタワーに%$heroes.hero_lava.hotheaded.durations[3]%$秒間付与する。",
["HERO_LAVA_HOTHEADED_TITLE"] = "短気",
["HERO_LAVA_NAME"] = "クレートア",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_1"] = "繰り返し敵を叩き、%$heroes.hero_lava.temper_tantrum.s_damage_min[1]%$-%$heroes.hero_lava.temper_tantrum.s_damage_max[1]%$の物理的ダメージを与え、%$heroes.hero_lava.temper_tantrum.duration[1]%$秒間対象をスタンさせる。",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_2"] = "繰り返し敵を叩き、%$heroes.hero_lava.temper_tantrum.s_damage_min[2]%$-%$heroes.hero_lava.temper_tantrum.s_damage_max[2]%$の物理的ダメージを与え、%$heroes.hero_lava.temper_tantrum.duration[2]%$秒間対象をスタンさせる。",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_3"] = "繰り返し敵を叩き、%$heroes.hero_lava.temper_tantrum.s_damage_min[3]%$-%$heroes.hero_lava.temper_tantrum.s_damage_max[3]%$の物理的ダメージを与え、%$heroes.hero_lava.temper_tantrum.duration[3]%$秒間対象をスタンさせる。",
["HERO_LAVA_TEMPER_TANTRUM_TITLE"] = "癇癪",
["HERO_LAVA_ULTIMATE_DESCRIPTION_1"] = "%$heroes.hero_lava.ultimate.fireball_count[2]%$ 発の溶岩を道に投げ、それぞれが命中した敵に %$heroes.hero_lava.ultimate.bullet.s_damage[2]%$ の真のダメージを与え、%$heroes.hero_lava.ultimate.bullet.scorch.duration%$ 秒間燃焼させます。",
["HERO_LAVA_ULTIMATE_DESCRIPTION_2"] = "%$heroes.hero_lava.ultimate.fireball_count[3]%$ 発の溶岩を道に投げ、それぞれが命中した敵に %$heroes.hero_lava.ultimate.bullet.s_damage[3]%$ の真のダメージを与え、%$heroes.hero_lava.ultimate.bullet.scorch.duration%$ 秒間燃焼させます。",
["HERO_LAVA_ULTIMATE_DESCRIPTION_3"] = "%$heroes.hero_lava.ultimate.fireball_count[4]%$ 発の溶岩を道に投げ、それぞれが命中した敵に %$heroes.hero_lava.ultimate.bullet.s_damage[4]%$ の真のダメージを与え、%$heroes.hero_lava.ultimate.bullet.scorch.duration%$ 秒間燃焼させます。",
["HERO_LAVA_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "道に溶岩を投げ込み、地面を焼く。",
["HERO_LAVA_ULTIMATE_MENUBOTTOM_NAME"] = "怒りの爆発",
["HERO_LAVA_ULTIMATE_TITLE"] = "怒りの爆発",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_1"] = "敵に溶岩を吹きかけ、毎秒%$heroes.hero_lava.wild_eruption.s_damage[1]%$の真のダメージを与え、%$heroes.hero_lava.wild_eruption.duration[1]%$秒間敵を焼く。",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_2"] = "敵に溶岩を吹きかけ、毎秒%$heroes.hero_lava.wild_eruption.s_damage[2]%$の真のダメージを与え、%$heroes.hero_lava.wild_eruption.duration[2]%$秒間敵を焼く。",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_3"] = "敵に溶岩を吹きかけ、毎秒%$heroes.hero_lava.wild_eruption.s_damage[3]%$の真のダメージを与え、%$heroes.hero_lava.wild_eruption.duration[3]%$秒間敵を焼く。",
["HERO_LAVA_WILD_ERUPTION_TITLE"] = "ワイルドエラプション",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_1"] = "光の戦士を %$heroes.hero_lumenir.ultimate.soldier_count[1]%$ 体召喚し、近くの敵を短時間スタンさせ、%$heroes.hero_lumenir.ultimate.damage_min[1]%$-%$heroes.hero_lumenir.ultimate.damage_max[1]%$ の真実のダメージを与える。",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_2"] = "光の戦士を %$heroes.hero_lumenir.ultimate.soldier_count[2]%$ 体召喚し、近くの敵を短時間スタンさせ、%$heroes.hero_lumenir.ultimate.damage_min[2]%$-%$heroes.hero_lumenir.ultimate.damage_max[2]%$ の真実のダメージを与える。",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_3"] = "光の戦士を %$heroes.hero_lumenir.ultimate.soldier_count[3]%$ 体召喚し、近くの敵を短時間スタンさせ、%$heroes.hero_lumenir.ultimate.damage_min[3]%$-%$heroes.hero_lumenir.ultimate.damage_max[3]%$ の真実のダメージを与える。",
["HERO_LUMENIR_ARROW_STORM_MENUBOTTOM_DESCRIPTION"] = "神の戦士を召喚して、敵と戦う。",
["HERO_LUMENIR_ARROW_STORM_MENUBOTTOM_NAME"] = "トライアンフの呼び声",
["HERO_LUMENIR_ARROW_STORM_TITLE"] = "トリンプの呼び声",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_1"] = "近くの最も強力な敵に神聖な光の剣を投げ、%$heroes.hero_lumenir.celestial_judgement.damage[1]%$の真実のダメージを与え、%$heroes.hero_lumenir.celestial_judgement.stun_duration[1]%$秒間スタンさせます。",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_2"] = "近くの最も強力な敵に神聖な光の剣を投げ、%$heroes.hero_lumenir.celestial_judgement.damage[2]%$の真実のダメージを与え、%$heroes.hero_lumenir.celestial_judgement.stun_duration[2]%$秒間スタンさせます。",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_3"] = "近くの最も強力な敵に神聖な光の剣を投げ、%$heroes.hero_lumenir.celestial_judgement.damage[3]%$の真実のダメージを与え、%$heroes.hero_lumenir.celestial_judgement.stun_duration[3]%$秒間スタンさせます。",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_TITLE"] = "天の審判",
["HERO_LUMENIR_CLASS"] = "光をもたらす者",
["HERO_LUMENIR_DESC"] = "領域間を飛び交い、正義と決意の化身として立つルメニル。彼女は伝説の光をもたらす者であり、リニレアのパラディン達に崇められ、彼らに祝福を与え、悪との戦いにおいて大いなる力を授ける。",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_1"] = "神聖な光の %$heroes.hero_lumenir.fire_balls.flames_count[1]%$ 個の球を放ち、進路上の敵にダメージを与える。各球は通過する敵一体に対して、%$heroes.hero_lumenir.fire_balls.flame_damage_min[1]%$-%$heroes.hero_lumenir.fire_balls.flame_damage_max[1]%$ の真のダメージを与える。",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_2"] = "神聖な光の %$heroes.hero_lumenir.fire_balls.flames_count[2]%$ 個の球を放ち、進路上の敵にダメージを与える。各球は通過する敵一体に対して、%$heroes.hero_lumenir.fire_balls.flame_damage_min[2]%$-%$heroes.hero_lumenir.fire_balls.flame_damage_max[2]%$ の真のダメージを与える。",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_3"] = "神聖な光の %$heroes.hero_lumenir.fire_balls.flames_count[3]%$ 個の球を放ち、進路上の敵にダメージを与える。各球は通過する敵一体に対して、%$heroes.hero_lumenir.fire_balls.flame_damage_min[3]%$-%$heroes.hero_lumenir.fire_balls.flame_damage_max[3]%$ の真のダメージを与える。",
["HERO_LUMENIR_FIRE_BALLS_TITLE"] = "ラディアントウェーブ",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_1"] = "小さな光の竜を召喚し、装備された別のヒーローを%$heroes.hero_lumenir.mini_dragon.dragon.duration[1]%$秒間追います。各ドラゴンは攻撃ごとに%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[1]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[1]%$の物理ダメージを与えます。",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_2"] = "小さな光の竜を召喚し、装備された別のヒーローを%$heroes.hero_lumenir.mini_dragon.dragon.duration[2]%$秒間追います。各ドラゴンは攻撃ごとに%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[2]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[2]%$の物理ダメージを与えます。",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_3"] = "小さな光の竜を召喚し、装備された別のヒーローを%$heroes.hero_lumenir.mini_dragon.dragon.duration[3]%$秒間追います。各ドラゴンは攻撃ごとに%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[3]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[3]%$の物理ダメージを与えます。",
["HERO_LUMENIR_MINI_DRAGON_TITLE"] = "光の仲間",
["HERO_LUMENIR_NAME"] = "ルメニル",
["HERO_LUMENIR_SHIELD_DESCRIPTION_1"] = "味方ユニットに%$heroes.hero_lumenir.shield.armor[1]%$%のアーマーシールドを付与し、%$heroes.hero_lumenir.shield.spiked_armor[1]%$%のダメージを敵に反射します。",
["HERO_LUMENIR_SHIELD_DESCRIPTION_2"] = "味方ユニットに%$heroes.hero_lumenir.shield.armor[2]%$%のアーマーシールドを付与し、%$heroes.hero_lumenir.shield.spiked_armor[2]%$%のダメージを敵に反射します。",
["HERO_LUMENIR_SHIELD_DESCRIPTION_3"] = "味方ユニットに%$heroes.hero_lumenir.shield.armor[3]%$%のアーマーシールドを付与し、%$heroes.hero_lumenir.shield.spiked_armor[3]%$%のダメージを敵に反射します。",
["HERO_LUMENIR_SHIELD_TITLE"] = "報復の祝福",
["HERO_MECHA_CLASS"] = "移動する脅威",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_1"] = "ゴブリンの飛行船を呼び出し、目標エリア近くの敵に爆撃を行い、攻撃ごとに%$heroes.hero_mecha.ultimate.ranged_attack.damage_min[2]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[2]%$の真のエリアダメージを与えます。",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_2"] = "ゴブリンの飛行船を呼び出し、目標エリア近くの敵に爆撃を行い、攻撃ごとに%$heroes.hero_mecha.ultimate.ranged_attack.damage_min[3]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[3]%$の真のエリアダメージを与えます。",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_3"] = "ゴブリンの飛行船を呼び出し、目標エリア近くの敵に爆撃を行い、攻撃ごとに%$heroes.hero_mecha.ultimate.ranged_attack.damage_min[4]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[4]%$の真のエリアダメージを与えます。",
["HERO_MECHA_DEATH_FROM_ABOVE_MENUBOTTOM_DESCRIPTION"] = "エリア内の敵に爆撃を行う飛行船を召喚します。",
["HERO_MECHA_DEATH_FROM_ABOVE_MENUBOTTOM_NAME"] = "上からの死",
["HERO_MECHA_DEATH_FROM_ABOVE_TITLE"] = "上からの死",
["HERO_MECHA_DESC"] = "二人の狂ったゴブリンの考案者の頭脳から生まれ、盗まれたドワーフの技術の基盤の上に構築されたオナグロは、究極のグリーンスキン戦争マシーンであり、ダークアーミーの敵にとって恐ろしい光景です。",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_1"] = "%$heroes.hero_mecha.goblidrones.units%$のドローンを召喚し、%$heroes.hero_mecha.goblidrones.drone.duration[1]%$秒間敵を攻撃し、攻撃ごとに%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[1]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[1]%$の物理ダメージを与えます。",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_2"] = "%$heroes.hero_mecha.goblidrones.units%$のドローンを召喚し、%$heroes.hero_mecha.goblidrones.drone.duration[2]%$秒間敵を攻撃し、攻撃ごとに%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[2]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[2]%$の物理ダメージを与えます。",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_3"] = "%$heroes.hero_mecha.goblidrones.units%$のドローンを召喚し、%$heroes.hero_mecha.goblidrones.drone.duration[3]%$秒間敵を攻撃し、攻撃ごとに%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[3]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[3]%$の物理ダメージを与えます。",
["HERO_MECHA_GOBLIDRONES_TITLE"] = "ゴブリドローン",
["HERO_MECHA_MINE_DROP_DESCRIPTION_1"] = "静止している間、メックは定期的に最大で%$heroes.hero_mecha.mine_drop.max_mines[1]%$の爆発地雷を道に設置します。地雷は爆発し、それぞれ%$heroes.hero_mecha.mine_drop.damage_min[1]%$-%$heroes.hero_mecha.mine_drop.damage_max[1]%$の爆発ダメージを与えます。",
["HERO_MECHA_MINE_DROP_DESCRIPTION_2"] = "静止している間、メックは定期的に最大で%$heroes.hero_mecha.mine_drop.max_mines[2]%$の爆発地雷を道に設置します。地雷は爆発し、それぞれ%$heroes.hero_mecha.mine_drop.damage_min[2]%$-%$heroes.hero_mecha.mine_drop.damage_max[2]%$の爆発ダメージを与えます。",
["HERO_MECHA_MINE_DROP_DESCRIPTION_3"] = "静止している間、メックは定期的に最大で%$heroes.hero_mecha.mine_drop.max_mines[3]%$の爆発地雷を道に設置します。地雷は爆発し、それぞれ%$heroes.hero_mecha.mine_drop.damage_min[3]%$-%$heroes.hero_mecha.mine_drop.damage_max[3]%$の爆発ダメージを与えます。",
["HERO_MECHA_MINE_DROP_TITLE"] = "地雷投下",
["HERO_MECHA_NAME"] = "オナガー",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_1"] = "メックが地面を叩き、一時的に近くの敵をスタンさせ、%$heroes.hero_mecha.power_slam.s_damage[1]%$の物理ダメージを与えます。",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_2"] = "メックが地面を叩き、一時的に近くの敵をスタンさせ、%$heroes.hero_mecha.power_slam.s_damage[2]%$の物理ダメージを与えます。",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_3"] = "メックが地面を叩き、一時的に近くの敵をスタンさせ、%$heroes.hero_mecha.power_slam.s_damage[3]%$の物理ダメージを与えます。",
["HERO_MECHA_POWER_SLAM_TITLE"] = "パワースラム",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_1"] = "道にタールをまき散らす爆弾を投げ、敵の速度を%$heroes.hero_mecha.tar_bomb.duration[1]%$秒間%$heroes.hero_mecha.tar_bomb.slow_factor%$%遅らせます。",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_2"] = "道にタールをまき散らす爆弾を投げ、敵の速度を%$heroes.hero_mecha.tar_bomb.duration[2]%$秒間%$heroes.hero_mecha.tar_bomb.slow_factor%$%遅らせます。",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_3"] = "道にタールをまき散らす爆弾を投げ、敵の速度を%$heroes.hero_mecha.tar_bomb.duration[3]%$秒間%$heroes.hero_mecha.tar_bomb.slow_factor%$%遅らせます。",
["HERO_MECHA_TAR_BOMB_TITLE"] = "タールボム",
["HERO_MUYRN_CLASS"] = "森の守護者",
["HERO_MUYRN_DESC"] = "子どもっぽい外見にもかかわらず、いたずら好きのニュルは、自然の力とのつながりを使って何百年もの間森を守ってきました。彼は、自宅を脅かす侵略者の増加する波に終止符を打つために連合に加わりました。",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_1"] = "エリア内のすべての敵を魅了し、%$heroes.hero_muyrn.faery_dust.duration[1]%$秒間、その攻撃ダメージを%$heroes.hero_muyrn.faery_dust.s_damage_factor[1]%$%減少させます。",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_2"] = "エリア内のすべての敵を魅了し、%$heroes.hero_muyrn.faery_dust.duration[2]%$秒間、その攻撃ダメージを%$heroes.hero_muyrn.faery_dust.s_damage_factor[2]%$%減少させます。",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_3"] = "エリア内のすべての敵を魅了し、%$heroes.hero_muyrn.faery_dust.duration[3]%$秒間、その攻撃ダメージを%$heroes.hero_muyrn.faery_dust.s_damage_factor[3]%$%減少させます。",
["HERO_MUYRN_FAERY_DUST_TITLE"] = "弱体化の魅了",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_1"] = "戦闘中、Nyruは自分の周りに葉の盾を作ります。この盾は毎秒%$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[1]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[1]%$の魔法ダメージを与え、%$heroes.hero_muyrn.leaf_whirlwind.duration[1]%$秒間Nyruを回復させます。",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_2"] = "戦闘中、Nyruは自分の周りに葉の盾を作ります。この盾は毎秒%$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[2]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[2]%$の魔法ダメージを与え、%$heroes.hero_muyrn.leaf_whirlwind.duration[2]%$秒間Nyruを回復させます。",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_3"] = "戦闘中、Nyruは自分の周りに葉の盾を作ります。この盾は毎秒%$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[3]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[3]%$の魔法ダメージを与え、%$heroes.hero_muyrn.leaf_whirlwind.duration[3]%$秒間Nyruを回復させます。",
["HERO_MUYRN_LEAF_WHIRLWIND_TITLE"] = "リーフワールウィンド",
["HERO_MUYRN_NAME"] = "ニュル",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_1"] = "エリアに%$heroes.hero_muyrn.ultimate.duration[2]%$秒間ルーツを発生させ、敵を遅らせ、秒間に%$heroes.hero_muyrn.ultimate.s_damage_min[2]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[2]%$の実ダメージを与える。",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_2"] = "エリアに%$heroes.hero_muyrn.ultimate.duration[3]%$秒間ルーツを発生させ、敵を遅らせ、秒間に%$heroes.hero_muyrn.ultimate.s_damage_min[3]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[3]%$の実ダメージを与える。",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_3"] = "エリアに%$heroes.hero_muyrn.ultimate.duration[4]%$秒間ルーツを発生させ、敵を遅らせ、秒間に%$heroes.hero_muyrn.ultimate.s_damage_min[4]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[4]%$の実ダメージを与える。",
["HERO_MUYRN_ROOT_DEFENDER_MENUBOTTOM_DESCRIPTION"] = "敵をダメージし、遅らせる根を発生させる。",
["HERO_MUYRN_ROOT_DEFENDER_MENUBOTTOM_NAME"] = "ルートディフェンダー",
["HERO_MUYRN_ROOT_DEFENDER_TITLE"] = "ルートディフェンダー",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_1"] = "%$heroes.hero_muyrn.sentinel_wisps.max_summons[1]%$を召喚して、%$heroes.hero_muyrn.sentinel_wisps.wisp.duration[1]%$秒間Nyruに従います。ウィスプは%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[1]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[1]%$の魔法ダメージを与えます。",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_2"] = "%$heroes.hero_muyrn.sentinel_wisps.max_summons[2]%$を召喚し、%$heroes.hero_muyrn.sentinel_wisps.wisp.duration[2]%$秒間Nyruに従います。ウィスプは%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[2]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[2]%$の魔法ダメージを与えます。",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_3"] = "%$heroes.hero_muyrn.sentinel_wisps.max_summons[3]%$を召喚し、%$heroes.hero_muyrn.sentinel_wisps.wisp.duration[3]%$秒間Nyruに従います。ウィスプは%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[3]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[3]%$の魔法ダメージを与えます。",
["HERO_MUYRN_SENTINEL_WISPS_TITLE"] = "センチネル・ウィスプス",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_1"] = "敵に向けて緑のエネルギーの爆発を放ち、%$heroes.hero_muyrn.verdant_blast.s_damage[1]%$の魔法ダメージを与える。",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_2"] = "敵に向けて緑のエネルギーの爆発を放ち、%$heroes.hero_muyrn.verdant_blast.s_damage[2]%$の魔法ダメージを与える。",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_3"] = "敵に向けて緑のエネルギーの爆発を放ち、%$heroes.hero_muyrn.verdant_blast.s_damage[3]%$の魔法ダメージを与える。",
["HERO_MUYRN_VERDANT_BLAST_TITLE"] = "ヴァーダント・ブラスト",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_1"] = "剣で敵に容赦なく打撃を与え、%$heroes.hero_raelyn.brutal_slash.s_damage[1]%$の真ダメージを与える。",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_2"] = "剣で敵に容赦なく打撃を与え、%$heroes.hero_raelyn.brutal_slash.s_damage[2]%$の真ダメージを与える。",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_3"] = "剣で敵に容赦なく打撃を与え、%$heroes.hero_raelyn.brutal_slash.s_damage[3]%$の真ダメージを与える。",
["HERO_RAELYN_BRUTAL_SLASH_TITLE"] = "ブルータルスラッシュ",
["HERO_RAELYN_CLASS"] = "ダーク・ルーテナント",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_1"] = "%$heroes.hero_raelyn.ultimate.entity.hp_max[2]%$の健康を持つダークナイトを召喚し、%$heroes.hero_raelyn.ultimate.entity.damage_min[2]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[2]%$の真ダメージを与えます。",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_2"] = "ダークナイトは%$heroes.hero_raelyn.ultimate.entity.hp_max[3]%$の健康を持ち、%$heroes.hero_raelyn.ultimate.entity.damage_min[3]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[3]%$の真ダメージを与えます。",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_3"] = "ダークナイトは%$heroes.hero_raelyn.ultimate.entity.hp_max[4]%$の健康を持ち、%$heroes.hero_raelyn.ultimate.entity.damage_min[4]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[4]%$の真ダメージを与えます。",
["HERO_RAELYN_COMMAND_ORDERS_MENUBOTTOM_DESCRIPTION"] = "戦場にダークナイトを召喚する。",
["HERO_RAELYN_COMMAND_ORDERS_MENUBOTTOM_NAME"] = "指揮命令",
["HERO_RAELYN_COMMAND_ORDERS_TITLE"] = "指揮命令",
["HERO_RAELYN_DESC"] = "威圧感のあるレイリンは、先鋒としてダークナイツを率いることに生きがいを感じている。彼女の残忍さと粘り強さはヴェズナンの認識とリニレアンの恐怖を獲得した。いつでも良い戦いに備えている彼女は、ダークウィザードの隊列に参加する最初のボランティアだった。",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_1"] = "近くの敵を%$heroes.hero_raelyn.inspire_fear.stun_duration[1]%$秒間スタンさせ、%$heroes.hero_raelyn.inspire_fear.damage_duration[1]%$秒間、彼らの攻撃ダメージを%$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[1]%$%減少させます。",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_2"] = "近くの敵を%$heroes.hero_raelyn.inspire_fear.stun_duration[2]%$秒間スタンさせ、%$heroes.hero_raelyn.inspire_fear.damage_duration[2]%$秒間、彼らの攻撃ダメージを%$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[2]%$%減少させます。",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_3"] = "近くの敵を%$heroes.hero_raelyn.inspire_fear.stun_duration[3]%$秒間スタンさせ、%$heroes.hero_raelyn.inspire_fear.damage_duration[3]%$秒間、彼らの攻撃ダメージを%$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[3]%$%減少させます。",
["HERO_RAELYN_INSPIRE_FEAR_TITLE"] = "恐怖を与える",
["HERO_RAELYN_NAME"] = "レイリン",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_1"] = "%$heroes.hero_raelyn.onslaught.duration[1]%$秒間、レイリンはより速く攻撃し、主要なターゲットの周囲の小さなエリアに彼女の攻撃ダメージの%$heroes.hero_raelyn.onslaught.damage_factor[1]%$%を与えます。",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_2"] = "%$heroes.hero_raelyn.onslaught.duration[2]%$秒間、レイリンはより速く攻撃し、主要なターゲットの周囲の小さなエリアに彼女の攻撃ダメージの%$heroes.hero_raelyn.onslaught.damage_factor[2]%$%を与えます。",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_3"] = "%$heroes.hero_raelyn.onslaught.duration[3]%$秒間、レイリンはより速く攻撃し、主要なターゲットの周囲の小さなエリアに彼女の攻撃ダメージの%$heroes.hero_raelyn.onslaught.damage_factor[3]%$%を与えます。",
["HERO_RAELYN_ONSLAUGHT_TITLE"] = "猛攻",
["HERO_RAELYN_ULTIMATE_ENTITY_NAME"] = "ダークナイト",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_1"] = "戦闘中、レイリンは彼女の周りの敵の数に基づいてヘルスシールドを生成します（%$heroes.hero_raelyn.unbreakable.max_targets%$敵までのそれぞれに対して彼女の生命総量の%$heroes.hero_raelyn.unbreakable.shield_per_enemy[1]%$%）",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_2"] = "戦闘中、レイリンは近くにいる敵の数に基づいてヘルスシールドを生成します（敵1人あたり彼女の生命総量の%$heroes.hero_raelyn.unbreakable.shield_per_enemy[2]%$%、最大%$heroes.hero_raelyn.unbreakable.max_targets%$敵まで）",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_3"] = "戦闘中、レイリンは近くにいる敵の数に基づいてヘルスシールドを生成します（敵1人あたり彼女の生命総量の%$heroes.hero_raelyn.unbreakable.shield_per_enemy[3]%$%、最大%$heroes.hero_raelyn.unbreakable.max_targets%$敵まで）",
["HERO_RAELYN_UNBREAKABLE_TITLE"] = "不屈",
["HERO_ROBOT_CLASS"] = "攻城ゴーレム",
["HERO_ROBOT_DESC"] = "暗黒軍の鍛冶職人たちは、戦闘用オートマトンを作り出し、それを適切にウォーヘッドと名付けることで自らを超えた。炎のようなエンジンによって強化され、感情に動じることなく、ウォーヘッドは友も敵も区別せずに戦いに突進する。",
["HERO_ROBOT_EXPLODE_DESCRIPTION_1"] = "炎の爆発を生成し、敵に%$heroes.hero_robot.explode.damage_min[1]%$-%$heroes.hero_robot.explode.damage_max[1]%$の爆発ダメージを与え、%$heroes.hero_robot.explode.burning_duration%$秒間燃やします。燃焼は毎秒%$heroes.hero_robot.explode.s_burning_damage[1]%$のダメージを与えます。",
["HERO_ROBOT_EXPLODE_DESCRIPTION_2"] = "炎の爆発を生成し、敵に%$heroes.hero_robot.explode.damage_min[2]%$-%$heroes.hero_robot.explode.damage_max[2]%$の爆発ダメージを与え、%$heroes.hero_robot.explode.burning_duration%$秒間燃やします。燃焼は毎秒%$heroes.hero_robot.explode.s_burning_damage[2]%$のダメージを与えます。",
["HERO_ROBOT_EXPLODE_DESCRIPTION_3"] = "炎の爆発を生成し、敵に%$heroes.hero_robot.explode.damage_min[3]%$-%$heroes.hero_robot.explode.damage_max[3]%$の爆発ダメージを与え、%$heroes.hero_robot.explode.burning_duration%$秒間燃やします。燃焼は毎秒%$heroes.hero_robot.explode.s_burning_damage[3]%$のダメージを与えます。",
["HERO_ROBOT_EXPLODE_TITLE"] = "焼却",
["HERO_ROBOT_FIRE_DESCRIPTION_1"] = "炎の燃えさしでいっぱいの大砲を発射し、%$heroes.hero_robot.fire.damage_min[1]%$-%$heroes.hero_robot.fire.damage_max[1]%$の物理ダメージを与え、敵を%$heroes.hero_robot.fire.s_slow_duration[1]%$秒間遅くします。",
["HERO_ROBOT_FIRE_DESCRIPTION_2"] = "炎の燃えさしでいっぱいの大砲を発射し、%$heroes.hero_robot.fire.damage_min[2]%$-%$heroes.hero_robot.fire.damage_max[2]%$の物理ダメージを与え、敵を%$heroes.hero_robot.fire.s_slow_duration[1]%$秒間遅くします。",
["HERO_ROBOT_FIRE_DESCRIPTION_3"] = "炎の燃えさしでいっぱいの大砲を発射し、%$heroes.hero_robot.fire.damage_min[3]%$-%$heroes.hero_robot.fire.damage_max[3]%$の物理ダメージを与え、敵を%$heroes.hero_robot.fire.s_slow_duration[1]%$秒間遅くします。",
["HERO_ROBOT_FIRE_TITLE"] = "スモークスクリーン",
["HERO_ROBOT_JUMP_DESCRIPTION_1"] = "敵の上を跳び越え、%$heroes.hero_robot.jump.stun_duration[1]%$秒間スタンさせ、エリア内に%$heroes.hero_robot.jump.s_damage[1]%$の物理ダメージを与えます。",
["HERO_ROBOT_JUMP_DESCRIPTION_2"] = "敵の上を跳び越え、%$heroes.hero_robot.jump.stun_duration[2]%$秒間スタンさせ、エリア内に%$heroes.hero_robot.jump.s_damage[2]%$の物理ダメージを与えます。",
["HERO_ROBOT_JUMP_DESCRIPTION_3"] = "敵の上を跳び越え、%$heroes.hero_robot.jump.stun_duration[3]%$秒間スタンさせ、エリア内に%$heroes.hero_robot.jump.s_damage[3]%$の物理ダメージを与えます。",
["HERO_ROBOT_JUMP_TITLE"] = "ディープインパクト",
["HERO_ROBOT_NAME"] = "弾頭",
["HERO_ROBOT_TRAIN_DESCRIPTION_1"] = "道を進む戦車を召喚し、敵に%$heroes.hero_robot.ultimate.s_damage[2]%$のダメージを与え、%$heroes.hero_robot.ultimate.burning_duration%$秒間焼き続けます。燃焼は毎秒%$heroes.hero_robot.ultimate.s_burning_damage%$のダメージを与えます。",
["HERO_ROBOT_TRAIN_DESCRIPTION_2"] = "道を進む戦車を召喚し、敵に%$heroes.hero_robot.ultimate.s_damage[3]%$のダメージを与え、%$heroes.hero_robot.ultimate.burning_duration%$秒間焼き続けます。燃焼は毎秒%$heroes.hero_robot.ultimate.s_burning_damage%$のダメージを与えます。",
["HERO_ROBOT_TRAIN_DESCRIPTION_3"] = "道を進む戦車を召喚し、敵に%$heroes.hero_robot.ultimate.s_damage[4]%$のダメージを与え、%$heroes.hero_robot.ultimate.burning_duration%$秒間焼き続けます。燃焼は毎秒%$heroes.hero_robot.ultimate.s_burning_damage%$のダメージを与えます。",
["HERO_ROBOT_TRAIN_MENUBOTTOM_DESCRIPTION"] = "敵を踏みつける戦車を召喚します。",
["HERO_ROBOT_TRAIN_MENUBOTTOM_NAME"] = "モーターヘッド",
["HERO_ROBOT_TRAIN_TITLE"] = "モーターヘッド",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_1"] = "体力が%$heroes.hero_robot.uppercut.s_life_threshold[1]%$%未満の敵を攻撃し、即座に仕留めます。",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_2"] = "体力が%$heroes.hero_robot.uppercut.s_life_threshold[2]%$%未満の敵を攻撃し、即座に仕留めます。",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_3"] = "体力が%$heroes.hero_robot.uppercut.s_life_threshold[3]%$%未満の敵を攻撃し、即座に仕留めます。",
["HERO_ROBOT_UPPERCUT_TITLE"] = "アイアンアッパーカット",
["HERO_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_1"] = "このヒーローは巨大な脅威キャンペーンに含まれています",
["HERO_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_2"] = "このヒーローは「悟空の旅」キャンペーンに含まれています。",
["HERO_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_1"] = "巨大な脅威キャンペーン",
["HERO_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_2"] = "悟空の旅キャンペーン",
["HERO_ROOM_EQUIPPED_HEROES"] = "装備されたヒーロー",
["HERO_ROOM_GET_DLC"] = "それを手に入れろ",
["HERO_ROOM_LABEL_ROSTER_THUMB_NEW"] = "新しい！",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_1"] = "テリエンの魔法の反射を召喚し、敵に攻撃し、%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[1]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[1]%$の魔法ダメージを与えます。",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_2"] = "テリエンの魔法の反射を召喚し、敵に攻撃し、%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[2]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[2]%$の魔法ダメージを与えます。",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_3"] = "テリエンの魔法の反射を召喚し、敵に攻撃し、%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[3]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[3]%$の魔法ダメージを与えます。",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_ENTITY_NAME"] = "アストラルリフレクション",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_TITLE"] = "アストラルリフレクション",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_1"] = "味方ユニットをシールドで守り、%$heroes.hero_space_elf.black_aegis.shield_base[1]%$ダメージまでを防ぎます。シールドは一瞬後に爆発し、エリア内に%$heroes.hero_space_elf.black_aegis.explosion_damage[1]%$の魔法ダメージを与えます。",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_2"] = "味方ユニットをシールドで守り、%$heroes.hero_space_elf.black_aegis.shield_base[2]%$ダメージまでを防ぎます。この爆発的なシールドは、現在エリア内に%$heroes.hero_space_elf.black_aegis.explosion_damage[2]%$の魔法ダメージを与えます。",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_3"] = "味方ユニットをシールドで守り、%$heroes.hero_space_elf.black_aegis.shield_base[3]%$ダメージまでを防ぎます。この爆発的なシールドは、現在エリア内に%$heroes.hero_space_elf.black_aegis.explosion_damage[3]%$の魔法ダメージを与えます。",
["HERO_SPACE_ELF_BLACK_AEGIS_TITLE"] = "ブラックイージス",
["HERO_SPACE_ELF_CLASS"] = "ヴォイドマンサー",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_1"] = "%$heroes.hero_space_elf.ultimate.duration[2]%$秒間、敵のグループを虚無に閉じ込め、%$heroes.hero_space_elf.ultimate.damage[2]%$のダメージを与える。",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_2"] = "%$heroes.hero_space_elf.ultimate.duration[3]%$秒間、敵のグループを虚無に閉じ込め、%$heroes.hero_space_elf.ultimate.damage[3]%$のダメージを与える。",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_3"] = "%$heroes.hero_space_elf.ultimate.duration[4]%$秒間、敵のグループを虚無に閉じ込め、%$heroes.hero_space_elf.ultimate.damage[4]%$のダメージを与える。",
["HERO_SPACE_ELF_COSMIC_PRISON_MENUBOTTOM_DESCRIPTION"] = "エリア内の敵を捕らえ、ダメージを与える。",
["HERO_SPACE_ELF_COSMIC_PRISON_MENUBOTTOM_NAME"] = "コズミックプリズン",
["HERO_SPACE_ELF_COSMIC_PRISON_TITLE"] = "コズミックプリズン",
["HERO_SPACE_ELF_DESC"] = "未知の力や異世界の力に手を出したことで仲間たちから長年避けられてきたヴォイドマンサーのセリエンは、今や監視者を理解し、この次元を超えるあらゆる力について知るアライアンスの最大の財産の1つとなっています。",
["HERO_SPACE_ELF_NAME"] = "セリエン",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_1"] = "すべてのタワーの周りの空間を%$heroes.hero_space_elf.spatial_distortion.duration[1]%$秒間歪め、その射程を%$heroes.hero_space_elf.spatial_distortion.s_range_factor[1]%$%増加させます。 ",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_2"] = "すべてのタワーの周りの空間を%$heroes.hero_space_elf.spatial_distortion.duration[2]%$秒間歪め、その射程を%$heroes.hero_space_elf.spatial_distortion.s_range_factor[2]%$%増加させます。 ",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_3"] = "すべてのタワーの周りの空間を%$heroes.hero_space_elf.spatial_distortion.duration[3]%$秒間歪め、その射程を%$heroes.hero_space_elf.spatial_distortion.s_range_factor[3]%$%増加させます。 ",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_TITLE"] = "空間歪曲",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_1"] = "%$heroes.hero_space_elf.void_rift.cracks_amount[1]%$の亀裂を道に開き、%$heroes.hero_space_elf.void_rift.duration[1]%$秒間、その上に立っている敵に対して秒間%$heroes.hero_space_elf.void_rift.s_damage_min[1]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[1]%$のダメージを与える。",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_2"] = "%$heroes.hero_space_elf.void_rift.cracks_amount[2]%$の亀裂を道に開き、%$heroes.hero_space_elf.void_rift.duration[2]%$秒間、その上に立っている敵に対して秒間%$heroes.hero_space_elf.void_rift.s_damage_min[2]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[2]%$のダメージを与える。",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_3"] = "%$heroes.hero_space_elf.void_rift.cracks_amount[3]%$の亀裂を道に開き、%$heroes.hero_space_elf.void_rift.duration[3]%$秒間、その上に立っている敵に対して秒間%$heroes.hero_space_elf.void_rift.s_damage_min[3]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[3]%$のダメージを与える。",
["HERO_SPACE_ELF_VOID_RIFT_TITLE"] = "ヴォイドリフト",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_1"] = "%$heroes.hero_spider.ultimate.spawn_amount[2]%$ 匹のクモを召喚し、%$heroes.hero_spider.ultimate.spider.duration[2]%$ 秒間戦わせ、命中時に敵を気絶させる。",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_2"] = "%$heroes.hero_spider.ultimate.spawn_amount[3]%$ 匹のクモを召喚し、%$heroes.hero_spider.ultimate.spider.duration[3]%$ 秒間戦わせ、命中時に敵を気絶させる。",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_3"] = "%$heroes.hero_spider.ultimate.spawn_amount[4]%$ 匹のクモを召喚し、%$heroes.hero_spider.ultimate.spider.duration[4]%$ 秒間戦わせ、命中時に敵を気絶させる。",
["HERO_SPIDER_ARACNID_SPAWNER_MENUBOTTOM_DESCRIPTION"] = "眩惑のクモの群れを召喚する。",
["HERO_SPIDER_ARACNID_SPAWNER_MENUBOTTOM_NAME"] = "狩人の呼び声",
["HERO_SPIDER_ARACNID_SPAWNER_TITLE"] = "狩人の呼び声",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_1"] = "%$heroes.hero_spider.area_attack.cooldown[1]%$ 秒ごとに、スパイダルはその存在を示し、周囲の敵を %$heroes.hero_spider.area_attack.s_stun_time[1]%$ 秒間気絶させる。",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_2"] = "%$heroes.hero_spider.area_attack.cooldown[2]%$ 秒ごとに、スパイダルはその存在を示し、周囲の敵を %$heroes.hero_spider.area_attack.s_stun_time[2]%$ 秒間気絶させる。",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_3"] = "%$heroes.hero_spider.area_attack.cooldown[3]%$ 秒ごとに、スパイダーはその存在を示し、周囲の敵を %$heroes.hero_spider.area_attack.s_stun_time[3]%$ 秒間スタンさせる。",
["HERO_SPIDER_AREA_ATTACK_TITLE"] = "圧倒的な存在感",
["HERO_SPIDER_DESC"] = "スパイダーは、スパイダー・クイーンのカルトを殲滅する使命を帯びたトワイライト・エルフの最後の生き残りである。比類なき狩猟技術に闇魔法を加えた彼女は、全王国で最も恐れられる暗殺者の一人とされている。",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_1"] = "%$heroes.hero_spider.instakill_melee.cooldown[1]%$ 秒ごとに、スパイダーはスタン状態の敵を即死させることができる（体力が %$heroes.hero_spider.instakill_melee.life_threshold[1]%$ 以下の場合）。",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_2"] = "%$heroes.hero_spider.instakill_melee.cooldown[2]%$ 秒ごとに、スパイダーはスタンした敵の体力が %$heroes.hero_spider.instakill_melee.life_threshold[2]%$ 以下の場合、即座に処刑できる。",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_3"] = "%$heroes.hero_spider.instakill_melee.cooldown[3]%$秒ごとに、スパイダイルはスタンした敵を処刑できる（HPが%$heroes.hero_spider.instakill_melee.life_threshold[3]%$以下の場合）。",
["HERO_SPIDER_INSTAKILL_MELEE_TITLE"] = "死の掌握",
["HERO_SPIDER_NAME"] = "スパイダー",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_1"] = "瞬く間に、スパイダーは最も体力の高い敵の元へテレポートし、%$heroes.hero_spider.supreme_hunter.damage_min[1]%$-%$heroes.hero_spider.supreme_hunter.damage_max[1]%$のダメージを与える。",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_2"] = "瞬く間に、スパイダーは最も体力の高い敵の元へテレポートし、%$heroes.hero_spider.supreme_hunter.damage_min[2]%$-%$heroes.hero_spider.supreme_hunter.damage_max[2]%$のダメージを与える。",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_3"] = "瞬く間に、スパイダーは最も体力の高い敵の元へテレポートし、%$heroes.hero_spider.supreme_hunter.damage_min[3]%$-%$heroes.hero_spider.supreme_hunter.damage_max[3]%$のダメージを与える。",
["HERO_SPIDER_SUPREME_HUNTER_TITLE"] = "シャドウステップ",
["HERO_SPIDER_TUNNELING_DESCRIPTION_1"] = "スパイディアのトンネルは再浮上時に %$heroes.hero_spider.tunneling.damage_min[1]%$-%$heroes.hero_spider.tunneling.damage_max[1]%$ のダメージを与えるようになった。",
["HERO_SPIDER_TUNNELING_DESCRIPTION_2"] = "スパイディアのトンネルは再浮上時に %$heroes.hero_spider.tunneling.damage_min[2]%$-%$heroes.hero_spider.tunneling.damage_max[2]%$ のダメージを与えるようになった。",
["HERO_SPIDER_TUNNELING_DESCRIPTION_3"] = "スパイディアのトンネルは再浮上時に %$heroes.hero_spider.tunneling.damage_min[3]%$-%$heroes.hero_spider.tunneling.damage_max[3]%$ のダメージを与えるようになった。",
["HERO_SPIDER_TUNNELING_TITLE"] = "トンネル掘削",
["HERO_VENOM_CLASS"] = "汚染されたスレイヤー",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_1"] = "粘着性の物質でエリアを満たし、敵を遅くし、しばらくすると%$heroes.hero_venom.ultimate.s_damage[2]%$の真ダメージを与える貫通スパイクに変わります。",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_2"] = "粘着性の物質でエリアを満たし、敵を遅くし、しばらくすると%$heroes.hero_venom.ultimate.s_damage[3]%$の真ダメージを与える貫通スパイクに変わります。",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_3"] = "粘着性の物質でエリアを満たし、敵を遅くし、しばらくすると%$heroes.hero_venom.ultimate.s_damage[4]%$の真ダメージを与える貫通スパイクに変わります。",
["HERO_VENOM_CREEPING_DEATH_MENUBOTTOM_DESCRIPTION"] = "道に粘着性の物質を召喚し、敵を遅らせてダメージを与える。",
["HERO_VENOM_CREEPING_DEATH_MENUBOTTOM_NAME"] = "忍び寄る死",
["HERO_VENOM_CREEPING_DEATH_TITLE"] = "忍び寄る死",
["HERO_VENOM_DESC"] = "カルトによって悪魔にされることを拒んだ傭兵のグリムソンは、投獄され腐るまで放置された。その苦痛のプロセスはグリムソンに変身能力を授け、彼はカルトから脱出し、復讐を誓った。",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_1"] = "グリムソンは、%$heroes.hero_venom.eat_enemy.hp_trigger%$%未満の体力を持つ敵を飲み込み、その過程で自分の総体力の%$heroes.hero_venom.eat_enemy.regen[1]%$%を回復します。",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_2"] = "グリムソンは、%$heroes.hero_venom.eat_enemy.hp_trigger%$%未満の体力を持つ敵を飲み込み、その過程で自分の総体力の%$heroes.hero_venom.eat_enemy.regen[2]%$%を回復します。",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_3"] = "グリムソンは、%$heroes.hero_venom.eat_enemy.hp_trigger%$%未満の体力を持つ敵を飲み込み、その過程で自分の総体力の%$heroes.hero_venom.eat_enemy.regen[3]%$%を回復します。",
["HERO_VENOM_EAT_ENEMY_TITLE"] = "肉体を新たにする",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_1"] = "パス上に棘のある触手を広げ、近くの敵にスパイクごとに%$heroes.hero_venom.floor_spikes.s_damage[1]%$の真ダメージを与える。",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_2"] = "パス上に棘のある触手を広げ、近くの敵にスパイクごとに%$heroes.hero_venom.floor_spikes.s_damage[2]%$の真ダメージを与える。",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_3"] = "パス上に棘のある触手を広げ、近くの敵にスパイクごとに%$heroes.hero_venom.floor_spikes.s_damage[3]%$の真ダメージを与える。",
["HERO_VENOM_FLOOR_SPIKES_TITLE"] = "デッドリースパイクス",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_1"] = "体力が%$heroes.hero_venom.inner_beast.trigger_hp%$%未満になると、グリムソンは完全に変身し、%$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[1]%$%の追加ダメージを得て、%$heroes.hero_venom.inner_beast.duration%$秒間にわたりヒットごとに自身の総ライフの%$heroes.hero_venom.inner_beast.basic_melee.regen_health%$%を回復します。",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_2"] = "体力が%$heroes.hero_venom.inner_beast.trigger_hp%$%未満になると、グリムソンは完全に変身し、%$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[2]%$%の追加ダメージを得て、%$heroes.hero_venom.inner_beast.duration%$秒間にわたりヒットごとに自身の総ライフの%$heroes.hero_venom.inner_beast.basic_melee.regen_health%$%を回復します。",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_3"] = "体力が%$heroes.hero_venom.inner_beast.trigger_hp%$%未満になると、グリムソンは完全に変身し、%$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[3]%$%の追加ダメージを得て、%$heroes.hero_venom.inner_beast.duration%$秒間にわたりヒットごとに自身の総ライフの%$heroes.hero_venom.inner_beast.basic_melee.regen_health%$%を回復します。",
["HERO_VENOM_INNER_BEAST_TITLE"] = "インナービースト",
["HERO_VENOM_NAME"] = "グリムソン",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_1"] = "遠隔の敵を攻撃し、%$heroes.hero_venom.ranged_tentacle.s_damage[1]%$の物理ダメージを与え、%$heroes.hero_venom.ranged_tentacle.bleed_chance[1]%$%の確率で出血を引き起こす。出血は%$heroes.hero_venom.ranged_tentacle.bleed_duration[1]%$秒間にわたって秒間に%$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$のダメージを与える。",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_2"] = "遠隔の敵を攻撃し、%$heroes.hero_venom.ranged_tentacle.s_damage[2]%$の物理ダメージを与え、%$heroes.hero_venom.ranged_tentacle.bleed_chance[2]%$%の確率で出血を引き起こす。出血は%$heroes.hero_venom.ranged_tentacle.bleed_duration[2]%$秒間にわたって秒間に%$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$のダメージを与える。",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_3"] = "遠隔の敵を攻撃し、%$heroes.hero_venom.ranged_tentacle.s_damage[3]%$の物理ダメージを与え、%$heroes.hero_venom.ranged_tentacle.bleed_chance[3]%$%の確率で出血を引き起こす。出血は%$heroes.hero_venom.ranged_tentacle.bleed_duration[3]%$秒間にわたって秒間に%$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$のダメージを与える。",
["HERO_VENOM_RANGED_TENTACLE_TITLE"] = "ハートシーカー",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_1"] = "エリアを%$heroes.hero_vesper.ultimate.s_spread[2]%$の矢で覆い、それぞれが敵に%$heroes.hero_vesper.ultimate.damage[2]%$の物理ダメージを与える。",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_2"] = "エリアを%$heroes.hero_vesper.ultimate.s_spread[3]%$の矢で覆い、それぞれが敵に%$heroes.hero_vesper.ultimate.damage[3]%$の物理ダメージを与える。",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_3"] = "エリアを%$heroes.hero_vesper.ultimate.s_spread[4]%$の矢で覆い、それぞれが敵に%$heroes.hero_vesper.ultimate.damage[4]%$の物理ダメージを与える。",
["HERO_VESPER_ARROW_STORM_MENUBOTTOM_DESCRIPTION"] = "矢でエリアを覆い、敵にダメージを与える。",
["HERO_VESPER_ARROW_STORM_MENUBOTTOM_NAME"] = "アローストーム",
["HERO_VESPER_ARROW_STORM_TITLE"] = "矢の嵐",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_1"] = "敵を%$heroes.hero_vesper.arrow_to_the_knee.stun_duration[1]%$秒間気絶させる矢を放ち、%$heroes.hero_vesper.arrow_to_the_knee.s_damage[1]%$の物理ダメージを与える。",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_2"] = "敵を%$heroes.hero_vesper.arrow_to_the_knee.stun_duration[2]%$秒間気絶させる矢を放ち、%$heroes.hero_vesper.arrow_to_the_knee.s_damage[2]%$の物理ダメージを与える。",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_3"] = "敵を%$heroes.hero_vesper.arrow_to_the_knee.stun_duration[3]%$秒間気絶させる矢を放ち、%$heroes.hero_vesper.arrow_to_the_knee.s_damage[3]%$の物理ダメージを与える。",
["HERO_VESPER_ARROW_TO_THE_KNEE_TITLE"] = "膝に矢",
["HERO_VESPER_CLASS"] = "ロイヤルキャプテン",
["HERO_VESPER_DESC"] = "剣と弓の両方を使いこなすヴェスパーは、リニアの軍隊の指揮官としての地位を勝ち取った。リニアが陥落し、デナス王が姿を消した後、彼はできる限りの兵力を集め、かつての支配者を取り戻すための十字軍を始めた。",
["HERO_VESPER_DISENGAGE_DESCRIPTION_1"] = "体力が%$heroes.hero_vesper.disengage.hp_to_trigger%$%以下になると、ヴェスパーは後方に跳びながら次の近接攻撃を回避します。その後、近くの敵にそれぞれ%$heroes.hero_vesper.disengage.s_damage[1]%$の物理ダメージを与える3本の矢を撃ちます。",
["HERO_VESPER_DISENGAGE_DESCRIPTION_2"] = "体力が%$heroes.hero_vesper.disengage.hp_to_trigger%$%以下になると、ヴェスパーは後方に跳びながら次の近接攻撃を回避します。その後、近くの敵にそれぞれ%$heroes.hero_vesper.disengage.s_damage[2]%$の物理ダメージを与える3本の矢を撃ちます。",
["HERO_VESPER_DISENGAGE_DESCRIPTION_3"] = "体力が%$heroes.hero_vesper.disengage.hp_to_trigger%$%以下になると、ヴェスパーは後方に跳びながら次の近接攻撃を回避します。その後、近くの敵にそれぞれ%$heroes.hero_vesper.disengage.s_damage[3]%$の物理ダメージを与える3本の矢を撃ちます。",
["HERO_VESPER_DISENGAGE_TITLE"] = "ディスエンゲージ",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_1"] = "敵に3回攻撃を加え、%$heroes.hero_vesper.martial_flourish.s_damage[1]%$の物理ダメージを与える。",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_2"] = "敵に3回攻撃を加え、%$heroes.hero_vesper.martial_flourish.s_damage[2]%$の物理ダメージを与える。",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_3"] = "敵に3回攻撃を加え、%$heroes.hero_vesper.martial_flourish.s_damage[3]%$の物理ダメージを与える。",
["HERO_VESPER_MARTIAL_FLOURISH_TITLE"] = "武術の華",
["HERO_VESPER_NAME"] = "ヴェスパー",
["HERO_VESPER_RICOCHET_DESCRIPTION_1"] = "%$heroes.hero_vesper.ricochet.s_bounces[1]%$人の敵の間で跳ね返る矢を放ち、毎回%$heroes.hero_vesper.ricochet.s_damage[1]%$の物理ダメージを与える。",
["HERO_VESPER_RICOCHET_DESCRIPTION_2"] = "%$heroes.hero_vesper.ricochet.s_bounces[2]%$人の敵の間で跳ね返る矢を放ち、毎回%$heroes.hero_vesper.ricochet.s_damage[2]%$の物理ダメージを与える。",
["HERO_VESPER_RICOCHET_DESCRIPTION_3"] = "%$heroes.hero_vesper.ricochet.s_bounces[3]%$人の敵の間で跳ね返る矢を放ち、毎回%$heroes.hero_vesper.ricochet.s_damage[3]%$の物理ダメージを与える。",
["HERO_VESPER_RICOCHET_TITLE"] = "跳ね返り矢",
["HERO_WITCH_CLASS"] = "トリックスターウィッチ",
["HERO_WITCH_DESC"] = "彼女はフェアリーフォレストを通り過ぎる見知らぬ人々を楽しく無害ないたずらで驚かせるのが大好きですが、森や彼女の仲間のノームに脅威を与える者は、彼女の遊び心のある笑顔の裏に、手強い魔女が隠れていることをすぐに知ることになるでしょう。",
["HERO_WITCH_DISENGAGE_DESCRIPTION_1"] = "Stregiの体力が%$heroes.hero_witch.disengage.hp_to_trigger%$%未満になると、彼女は後方にテレポートし、代わりに戦うデコイを残します。デコイは%$heroes.hero_witch.disengage.decoy.hp_max[1]%$の体力を持ち、破壊されると爆発し、%$heroes.hero_witch.disengage.decoy.explotion.stun_duration[1]%$秒間敵をスタンさせます。",
["HERO_WITCH_DISENGAGE_DESCRIPTION_2"] = "Stregiの体力が%$heroes.hero_witch.disengage.hp_to_trigger%$%未満になると、彼女は後方にテレポートし、代わりに戦うデコイを残します。デコイは%$heroes.hero_witch.disengage.decoy.hp_max[2]%$の体力を持ち、破壊されると爆発し、%$heroes.hero_witch.disengage.decoy.explotion.stun_duration[2]%$秒間敵をスタンさせます。",
["HERO_WITCH_DISENGAGE_DESCRIPTION_3"] = "Stregiの体力が%$heroes.hero_witch.disengage.hp_to_trigger%$%未満になると、彼女は後方にテレポートし、代わりに戦うデコイを残します。デコイは%$heroes.hero_witch.disengage.decoy.hp_max[3]%$の体力を持ち、破壊されると爆発し、%$heroes.hero_witch.disengage.decoy.explotion.stun_duration[3]%$秒間敵をスタンさせます。",
["HERO_WITCH_DISENGAGE_TITLE"] = "ダズリング・デコイ",
["HERO_WITCH_NAME"] = "ストレジ",
["HERO_WITCH_PATH_AOE_DESCRIPTION_1"] = "巨大なポーションを道に投げ、%$heroes.hero_witch.skill_path_aoe.s_damage[1]%$の魔法ダメージを与え、%$heroes.hero_witch.skill_path_aoe.duration[1]%$秒間、敵をスローダウンさせる。",
["HERO_WITCH_PATH_AOE_DESCRIPTION_2"] = "巨大なポーションを道に投げ、%$heroes.hero_witch.skill_path_aoe.s_damage[2]%$の魔法ダメージを与え、%$heroes.hero_witch.skill_path_aoe.duration[2]%$秒間、敵をスローダウンさせる。",
["HERO_WITCH_PATH_AOE_DESCRIPTION_3"] = "巨大なポーションを道に投げ、%$heroes.hero_witch.skill_path_aoe.s_damage[3]%$の魔法ダメージを与え、%$heroes.hero_witch.skill_path_aoe.duration[3]%$秒間、敵をスローダウンさせる。",
["HERO_WITCH_PATH_AOE_TITLE"] = "スウィッシュ・アンド・スクワッシュ",
["HERO_WITCH_POLYMORPH_DESCRIPTION_1"] = "敵を%$heroes.hero_witch.skill_polymorph.duration[1]%$秒間、パンプリングに変える。パンプリングは、ターゲットの体力の%$heroes.hero_witch.skill_polymorph.pumpkin.hp[1]%$%を持っている。",
["HERO_WITCH_POLYMORPH_DESCRIPTION_2"] = "敵を%$heroes.hero_witch.skill_polymorph.duration[2]%$秒間、パンプリングに変える。パンプリングは、ターゲットの体力の%$heroes.hero_witch.skill_polymorph.pumpkin.hp[2]%$%を持っている。",
["HERO_WITCH_POLYMORPH_DESCRIPTION_3"] = "敵を%$heroes.hero_witch.skill_polymorph.duration[3]%$秒間、パンプリングに変える。パンプリングは、ターゲットの体力の%$heroes.hero_witch.skill_polymorph.pumpkin.hp[3]%$%を持っている。",
["HERO_WITCH_POLYMORPH_TITLE"] = "野菜化！",
["HERO_WITCH_SOLDIERS_DESCRIPTION_1"] = "%$heroes.hero_witch.skill_soldiers.soldiers_amount[1]%$匹の敵と戦う猫を召喚します。猫は%$heroes.hero_witch.skill_soldiers.soldier.hp_max[1]%$の体力を持ち、%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[1]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[1]%$の物理ダメージを与えます。",
["HERO_WITCH_SOLDIERS_DESCRIPTION_2"] = "%$heroes.hero_witch.skill_soldiers.soldiers_amount[2]%$匹の敵と戦う猫を召喚します。猫は%$heroes.hero_witch.skill_soldiers.soldier.hp_max[2]%$の体力を持ち、%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[2]%$の物理ダメージを与えます。",
["HERO_WITCH_SOLDIERS_DESCRIPTION_3"] = "%$heroes.hero_witch.skill_soldiers.soldiers_amount[3]%$匹の敵と戦う猫を召喚します。猫は%$heroes.hero_witch.skill_soldiers.soldier.hp_max[3]%$の体力を持ち、%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[3]%$の物理ダメージを与えます。",
["HERO_WITCH_SOLDIERS_TITLE"] = "ナイトフューリーズ",
["HERO_WITCH_ULTIMATE_DESCRIPTION_1"] = "%$heroes.hero_witch.ultimate.max_targets[2]%$体の敵を後方にテレポートさせ、一時的に%$heroes.hero_witch.ultimate.duration[2]%$秒間眠らせます。",
["HERO_WITCH_ULTIMATE_DESCRIPTION_2"] = "%$heroes.hero_witch.ultimate.max_targets[3]%$体の敵を後方にテレポートさせ、一時的に%$heroes.hero_witch.ultimate.duration[3]%$秒間眠らせます。",
["HERO_WITCH_ULTIMATE_DESCRIPTION_3"] = "%$heroes.hero_witch.ultimate.max_targets[4]%$体の敵を後方にテレポートさせ、一時的に%$heroes.hero_witch.ultimate.duration[4]%$秒間眠らせます。",
["HERO_WITCH_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "敵を道の後方にテレポートさせ、一時的に眠らせます。",
["HERO_WITCH_ULTIMATE_MENUBOTTOM_NAME"] = "眠りの帰還",
["HERO_WITCH_ULTIMATE_TITLE"] = "眠りの帰還",
["HERO_WUKONG_CLASS"] = "孫悟空 - 猿の王",
["HERO_WUKONG_DESC"] = "陰陽の霊石から生まれた孫悟空は、力と敏捷性、不死の力を授かった。しかし、魔王たちがその力の珠を奪った。今、伝説のトリックスターが、それらを取り戻すために立ち上がる。手遅れになる前に。",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_1"] = "如意棒を大きくして敵を踏みつけ、即座に倒し、対象の周囲に%$heroes.hero_wukong.giant_staff.area_damage.damage_min[1]%$〜%$heroes.hero_wukong.giant_staff.area_damage.damage_max[1]%$の範囲ダメージを与える。",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_2"] = "如意棒变大猛砸敌人，立即击杀目标，并对其周围造成 %$heroes.hero_wukong.giant_staff.area_damage.damage_min[2]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[2]%$ 范围伤害。",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_3"] = "敵を踏みつけるために如意棒を振り回し巨大化させ、即座に敵を倒し、ターゲット周辺の範囲に%$heroes.hero_wukong.giant_staff.area_damage.damage_min[3]%$～%$heroes.hero_wukong.giant_staff.area_damage.damage_max[3]%$のダメージを与える。",
["HERO_WUKONG_GIANT_STAFF_TITLE"] = "如意棒の技",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_1"] = "孫悟空の髪の毛から分身を2体召喚して共に戦わせる。分身は%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[1]%$～%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[1]%$のダメージを与え、%$heroes.hero_wukong.hair_clones.soldier.duration[1]%$秒間持続する。",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_2"] = "孫悟空の髪の毛のクローンを2体召喚し、共に戦わせる。それぞれ%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[2]%$～%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[2]%$のダメージを与え、%$heroes.hero_wukong.hair_clones.soldier.duration[2]%$秒間持続する。",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_3"] = "孫悟空の髪の毛のクローンを2体召喚して共に戦わせる。それぞれ%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[3]%$〜%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[3]%$のダメージを与え、%$heroes.hero_wukong.hair_clones.soldier.duration[3]%$秒間持続する。",
["HERO_WUKONG_HAIR_CLONES_TITLE"] = "Hair Clones",
["HERO_WUKONG_NAME"] = "Sun Wukong",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_1"] = "Jingu Bangを空中に打ち上げ、%$heroes.hero_wukong.pole_ranged.pole_amounts[1]%$本に分裂させて敵に降らせる。それぞれが%$heroes.hero_wukong.pole_ranged.damage_min[1]%$ダメージを与え、小範囲の敵を気絶させる。",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_2"] = "Jingu Bangを空中に打ち上げ、%$heroes.hero_wukong.pole_ranged.pole_amounts[2]%$本に分裂させて敵に降らせる。それぞれが%$heroes.hero_wukong.pole_ranged.damage_min[2]%$ダメージを与え、小範囲の敵を気絶させる。",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_3"] = "Jingu Bangを空中に打ち上げ、%$heroes.hero_wukong.pole_ranged.pole_amounts[3]%$本に分裂させて敵に降らせる。それぞれが%$heroes.hero_wukong.pole_ranged.damage_min[3]%$ダメージを与え、小範囲の敵を気絶させる。",
["HERO_WUKONG_POLE_RANGED_TITLE"] = "ポールバラージ",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_1"] = "ホワイトドラゴンが猛烈な勢いで地面に突進し、%$heroes.hero_wukong.ultimate.damage_total[2]%$の確定ダメージを与え、減速エリアを残す。",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_2"] = "ホワイトドラゴンが猛烈な勢いで地面に飛び込み、%$heroes.hero_wukong.ultimate.damage_total[3]%$の確定ダメージを与え、スロー効果のあるエリアを残す。",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_3"] = "ホワイトドラゴンが凄まじい力で地面に突入し、%$heroes.hero_wukong.ultimate.damage_total[4]%$の確定ダメージを与え、スロウ効果のある範囲を残す。",
["HERO_WUKONG_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "ホワイトドラゴンを召喚する",
["HERO_WUKONG_ULTIMATE_MENUBOTTOM_NAME"] = "ホワイトドラゴン",
["HERO_WUKONG_ULTIMATE_TITLE"] = "ホワイトドラゴン",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_1"] = "猪八戒は孫悟空の忠実な仲間で、常に彼と行動を共にします。%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[1]%$〜%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[1]%$のダメージを与え、低確率で広範囲に大ダメージを与える攻撃を行います。",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_2"] = "猪八戒は孫悟空の忠実な仲間で、常に彼と行動を共にします。%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[2]%$〜%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[2]%$のダメージを与え、低確率で広範囲に大ダメージを与える攻撃を行います。",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_3"] = "猪八戒は孫悟空の忠実な仲間で、彼の後をどこへでもついて行く。%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[3]%$～%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[3]%$のダメージを与え、稀に強力な範囲攻撃を行うことがある。",
["HERO_WUKONG_ZHU_APPRENTICE_TITLE"] = "朱の弟子",
["HINT"] = "ヒント",
["HOURS_ABBREVIATION"] = "時",
["Hardcore! play at your own risk!"] = "激ムズ！プレイは自己責任でどうぞ！",
["Help"] = "ヘルプ",
["Hero at your command!"] = "英雄を指揮！",
["Heroes"] = "英雄",
["Heroes are elite units that can face strong enemies and support your forces."] = "英雄は強大な敵に対抗できるだけの力を持ち、自軍を支援するための上級ユニットだ。",
["Heroes gain experience every time they damage an enemy or use an ability."] = "英雄は敵にダメージを与えるか、アビリティを使う度に経験値を獲得する。",
["Heroic"] = "英雄的",
["Heroic challenge"] = "英雄的チャレンジ",
["High"] = "高",
["I'm ready. Now bring it on!"] = "準備はできてる。いつでも来い！",
["INCOMING NEXT WAVE!"] = "次の敵部隊が到着！",
["INCOMING WAVE"] = "次の敵部隊",
["INGAME_BALLOON_BUILD_HERE"] = "ここに建てる！",
["INGAME_BALLOON_GOAL"] = "この地点を越えて敵を通さないでください",
["INGAME_BALLOON_GOLD"] = "敵を倒してゴールドを稼ぐ",
["INGAME_BALLOON_INCOMING"] = "次の波が来ます！",
["INGAME_BALLOON_NEW_HERO"] = "新しいヒーロー！",
["INGAME_BALLOON_NEW_POWER"] = "新しい力！",
["INGAME_BALLOON_NOTIFICATION_TAP_HERE"] = "ここをタップ！",
["INGAME_BALLOON_SELECT_HERO"] = "選択するためにタップしてください！",
["INGAME_BALLOON_START_BATTLE"] = "バトルを開始！",
["INGAME_BALLOON_TAP_HERE"] = "道をタップ",
["INGAME_BALLOON_TAP_TO_CALL"] = "早く呼ぶためにタップしてください",
["INGAME_BALLOON_TAP_TWICE_BUILD"] = "クリックで 塔を建設",
["INGAME_BALLOON_TAP_TWICE_START"] = "2回タップして戦闘を開始",
["INGAME_BALLOON_TAP_TWICE_WAVE"] = "クリックで 次の敵を呼ぶ",
["INGAME_TUTORIAL1_HELP1"] = "この地点を敵に通過させないでください。",
["INGAME_TUTORIAL1_HELP2"] = "道を守るために塔を建てる。",
["INGAME_TUTORIAL1_HELP3"] = "敵を倒してゴールドを得る。",
["INGAME_TUTORIAL1_SUBTITLE1"] = "敵の攻撃からあなたの土地を守ってください。",
["INGAME_TUTORIAL1_SUBTITLE2"] = "道に沿って防御塔を建てて彼らを止めてください。",
["INGAME_TUTORIAL1_TITLE"] = "目的",
["INGAME_TUTORIAL_GOTCHA_1"] = "わかった！",
["INGAME_TUTORIAL_GOTCHA_2"] = "準備はいい、さあ来い！",
["INGAME_TUTORIAL_HINT"] = "ヒント",
["INGAME_TUTORIAL_INSTRUCTIONS"] = "指示",
["INGAME_TUTORIAL_NEW_TIP"] = "新しいヒント",
["INGAME_TUTORIAL_NEXT"] = "次！",
["INGAME_TUTORIAL_OK"] = "オーケー！",
["INGAME_TUTORIAL_SKIP"] = "これをスキップ！",
["INGAME_TUTORIAL_TIP_CHALLENGE"] = "警告",
["INSTRUCTIONS"] = "指示",
["ITEM_CLUSTER_BOMB_BOTTOM_DESC"] = "ポップコーンのようですが、ずっと楽しくて味は劣ります。",
["ITEM_CLUSTER_BOMB_BOTTOM_INFO"] = "新しい小さな爆弾を作り出す爆弾です。",
["ITEM_CLUSTER_BOMB_DESC"] = "その周囲に小さな爆弾を投げつけ、エリア内の敵にダメージを与える爆弾を投げます。",
["ITEM_CLUSTER_BOMB_NAME"] = "クラスター爆弾",
["ITEM_DEATHS_TOUCH_BOTTOM_DESC"] = "死の神のように感じたいときに最適です...！",
["ITEM_DEATHS_TOUCH_BOTTOM_INFO"] = "選択します。目標をタップします。殺します。",
["ITEM_DEATHS_TOUCH_DESC"] = "死の力を身につけ、任意の敵をタップして即座に排除します。ボスやミニボスには効果がありません。",
["ITEM_DEATHS_TOUCH_NAME"] = "死のタッチ",
["ITEM_LOOT_BOX_BOTTOM_DESC"] = "これを数個持っていれば一生安泰です。",
["ITEM_LOOT_BOX_BOTTOM_INFO"] = "道にクレートを落とし、敵にダメージを与え、即座に金を獲得します。",
["ITEM_LOOT_BOX_DESC"] = "道にクレートを落とし、敵にダメージを与えて即座に300ゴールドを獲得します。",
["ITEM_LOOT_BOX_NAME"] = "母脈ボックス",
["ITEM_MEDICAL_KIT_BOTTOM_DESC"] = "元に戻るために必要なすべてがここにあります、将軍。",
["ITEM_MEDICAL_KIT_BOTTOM_INFO"] = "プレイヤーに最大3つのハートを回復します。",
["ITEM_MEDICAL_KIT_DESC"] = "プレイヤーに最大3つのハートを回復する特別なキット。",
["ITEM_MEDICAL_KIT_NAME"] = "医療キット",
["ITEM_PORTABLE_COIL_BOTTOM_DESC"] = "ジップ！ザップ！ネズミのように揚げられた！",
["ITEM_PORTABLE_COIL_BOTTOM_INFO"] = "エリア内の敵にダメージを与え、スタンさせるトラップを設置します。",
["ITEM_PORTABLE_COIL_DESC"] = "近くの敵に向かって連鎖するその効果とともに、それを起動した敵にダメージとスタンを与えるエリアトラップを設定します。",
["ITEM_PORTABLE_COIL_NAME"] = "携帯型コイル",
["ITEM_ROOM_EQUIP"] = "装備する",
["ITEM_ROOM_EQUIPPED"] = "装備済み",
["ITEM_ROOM_EQUIPPED_ITEMS"] = "装備されたアイテム",
["ITEM_SCROLL_OF_SPACESHIFT_BOTTOM_DESC"] = "敵と戦う時間がなくなったことはありますか？もう心配無用です！",
["ITEM_SCROLL_OF_SPACESHIFT_BOTTOM_INFO"] = "敵のグループを道に沿って後方にテレポートさせます。",
["ITEM_SCROLL_OF_SPACESHIFT_DESC"] = "敵のグループを道に沿って後方にテレポートさせます。",
["ITEM_SCROLL_OF_SPACESHIFT_NAME"] = "スペースシフトの巻物",
["ITEM_SECOND_BREATH_BOTTOM_DESC"] = "墓から立ち上がれ、アンデッドの不利益なしで。",
["ITEM_SECOND_BREATH_BOTTOM_INFO"] = "倒れた英雄を蘇生させ、負傷者を治療し、ヒーローの能力のクールダウンをリセットする。",
["ITEM_SECOND_BREATH_DESC"] = "倒れた英雄を蘇生し、負傷者を癒し、英雄の力のクールダウンをリセットする神聖な祝福。",
["ITEM_SECOND_BREATH_NAME"] = "第二の呼吸",
["ITEM_SUMMON_BLACKBURN_BOTTOM_DESC"] = "唯一無二。ただ一つ。比類なきもの。",
["ITEM_SUMMON_BLACKBURN_BOTTOM_INFO"] = "強力なブラックバーンを召喚してあなたのそばで戦ってください。",
["ITEM_SUMMON_BLACKBURN_DESC"] = "強力な戦士レヴェナントを召喚して敵を倒します。",
["ITEM_SUMMON_BLACKBURN_NAME"] = "ブラックバーンの兜",
["ITEM_VEZNAN_WRATH_BOTTOM_DESC"] = "闇の魔法使いの無限の力を少し味わわせてあげよう！",
["ITEM_VEZNAN_WRATH_BOTTOM_INFO"] = "戦場のすべての敵を壊滅させます。",
["ITEM_VEZNAN_WRATH_DESC"] = "ヴェズナンは戦場のすべての敵を壊滅させる強力な呪文を唱えます。",
["ITEM_VEZNAN_WRATH_NAME"] = "ヴェズナンの怒り",
["ITEM_WINTER_AGE_BOTTOM_DESC"] = "夏が本当に嫌いな場合にも役立ちます。",
["ITEM_WINTER_AGE_BOTTOM_INFO"] = "画面上のすべての敵を凍結させます。",
["ITEM_WINTER_AGE_DESC"] = "敵を数秒間凍結させる冷たい風を生み出す強力な呪文です。",
["ITEM_WINTER_AGE_NAME"] = "冬の時代",
["Impossible"] = "不可能",
["Iron"] = "鉄",
["Iron Challenge"] = "鉄のチャレンジ",
["Iron challenge"] = "鉄のチャレンジ",
["JOYSTICK_CONFIG_AXIS_DEAD_ZONE"] = "スティックの遊び",
["JOYSTICK_CONFIG_AXIS_DEAD_ZONE_XBOX"] = "スティックの遊び",
["JOYSTICK_CONFIG_FIRST_REPEAT_DELAY"] = "初回キーリピート待ち時間",
["JOYSTICK_CONFIG_POINTER_ACCEL"] = "ポインター加速度",
["JOYSTICK_CONFIG_POINTER_MAX_ACCEL"] = "ポインター最高加速度",
["JOYSTICK_CONFIG_POINTER_SENS"] = "ポインター感度",
["JOYSTICK_CONFIG_POINTER_SPEED"] = "ポインター速度",
["JOYSTICK_CONFIG_REPEAT_DELAY"] = "キーリピート待ち時間",
["JOYSTICK_CONFIG_SWAP_ABXY"] = "A/B と X/Y 入れ替え",
["JOYSTICK_HELP_INGAME_A"] = "選択",
["JOYSTICK_HELP_INGAME_AXIS_LEFT"] = "移動",
["JOYSTICK_HELP_INGAME_AXIS_LEFT_BUTTON"] = "ポインター切り替え",
["JOYSTICK_HELP_INGAME_B"] = "キャンセル/戻る",
["JOYSTICK_HELP_INGAME_BACK"] = "情報カード表示",
["JOYSTICK_HELP_INGAME_DPAD_DOWN"] = "増援を移動する",
["JOYSTICK_HELP_INGAME_DPAD_LEFT"] = "援軍要請",
["JOYSTICK_HELP_INGAME_DPAD_RIGHT"] = "ヒーローパワー2",
["JOYSTICK_HELP_INGAME_DPAD_UP"] = "ヒーローパワー1",
["JOYSTICK_HELP_INGAME_ESCAPE"] = "キャンセル/戻る",
["JOYSTICK_HELP_INGAME_LB"] = "メインヒーロー",
["JOYSTICK_HELP_INGAME_MOVE_HEROES"] = "ヒーローを移動する",
["JOYSTICK_HELP_INGAME_MOVE_REINFORCEMENTS"] = "増援を移動する",
["JOYSTICK_HELP_INGAME_NX_A"] = "選択",
["JOYSTICK_HELP_INGAME_NX_AXIS_LEFT"] = "移動",
["JOYSTICK_HELP_INGAME_NX_AXIS_LEFT_BUTTON"] = "ポインター切り替え",
["JOYSTICK_HELP_INGAME_NX_B"] = "キャンセル/戻る",
["JOYSTICK_HELP_INGAME_NX_L"] = "メインヒーロー",
["JOYSTICK_HELP_INGAME_NX_MINUS"] = "情報カード表示",
["JOYSTICK_HELP_INGAME_NX_PLUS"] = "ポーズ/再開",
["JOYSTICK_HELP_INGAME_NX_R"] = "サブヒーロー",
["JOYSTICK_HELP_INGAME_NX_X"] = "敵襲開始",
["JOYSTICK_HELP_INGAME_NX_Y"] = "敵襲情報",
["JOYSTICK_HELP_INGAME_POWERS"] = "パワー",
["JOYSTICK_HELP_INGAME_RB"] = "サブヒーロー",
["JOYSTICK_HELP_INGAME_START"] = "ポーズ/再開",
["JOYSTICK_HELP_INGAME_X"] = "敵襲開始",
["JOYSTICK_HELP_INGAME_Y"] = "敵襲情報",
["JOYSTICK_HELP_MAP_A"] = "選択",
["JOYSTICK_HELP_MAP_AXIS_LEFT"] = "移動",
["JOYSTICK_HELP_MAP_B"] = "キャンセル/戻る",
["JOYSTICK_HELP_MAP_BACK"] = "オプションの表示/非表",
["JOYSTICK_HELP_MAP_LB"] = "前のレベル/ページ",
["JOYSTICK_HELP_MAP_NX_A"] = "選択",
["JOYSTICK_HELP_MAP_NX_AXIS_LEFT"] = "移動",
["JOYSTICK_HELP_MAP_NX_B"] = "キャンセル/戻る",
["JOYSTICK_HELP_MAP_NX_L"] = "前のレベル/ページ",
["JOYSTICK_HELP_MAP_NX_MINUS"] = "オプションの表示/非表",
["JOYSTICK_HELP_MAP_NX_PLUS"] = "オプションの表示/非表",
["JOYSTICK_HELP_MAP_NX_R"] = "次のレベル/ページ",
["JOYSTICK_HELP_MAP_RB"] = "次のレベル/ページ",
["JOYSTICK_HELP_MAP_START"] = "オプションの表示/非表",
["JOYSTICK_HELP_SLOTS_A"] = "選択",
["JOYSTICK_HELP_SLOTS_AXIS_LEFT"] = "移動",
["JOYSTICK_HELP_SLOTS_B"] = "キャンセル/戻る",
["JOYSTICK_HELP_SLOTS_BACK"] = "オプションの表示/非表",
["JOYSTICK_HELP_SLOTS_NX_A"] = "選択",
["JOYSTICK_HELP_SLOTS_NX_AXIS_LEFT"] = "移動",
["JOYSTICK_HELP_SLOTS_NX_B"] = "キャンセル/戻る",
["JOYSTICK_HELP_SLOTS_NX_MINUS"] = "オプションの表示/非表",
["JOYSTICK_HELP_SLOTS_NX_PLUS"] = "オプションの表示/非表",
["JOYSTICK_HELP_SLOTS_START"] = "オプションの表示/非表",
["KEYBOARD_KEY_ESCAPE"] = "ESCAPE",
["KEYBOARD_KEY_PAGE_DOWN"] = "PAGE DOWN",
["KEYBOARD_KEY_PAGE_UP"] = "PAGE UP",
["KEYBOARD_KEY_RETURN"] = "RETURN",
["KEYBOARD_KEY_SPACE"] = "SPACE",
["LEVEL_10_HEROIC"] = "ヒロイック説明10",
["LEVEL_10_HISTORY"] = "峡谷のすぐ外に、カルトが採掘した結晶を使って不吉な外見のアーティファクトを建造していることがわかった。奇妙なエネルギーでブンブンと鳴り、その場所を取り囲む空気は重たい感じがする。進む前にそれが破壊されることを確実にしなければならない。",
["LEVEL_10_IRON"] = "鉄の説明10",
["LEVEL_10_IRON_UNLOCK"] = "未定",
["LEVEL_10_MODES_UPGRADES"] = "レベル5マックス",
["LEVEL_10_TITLE"] = "10. 寺院の中庭",
["LEVEL_11_HEROIC"] = "ヒロイック説明11",
["LEVEL_11_HISTORY"] = "ついに峡谷を抜けたが、まだ先は長い。今、私たちはクリスタルが埋め込まれた巨大なポータルの前に立っているが、予言者ミドリアスが彼女の儀式を終えようとしている。何が向こうから来るのかはわからないが、私たちはまだ準備ができている。覚悟せよ！",
["LEVEL_11_IRON"] = "鉄の説明11",
["LEVEL_11_IRON_UNLOCK"] = "未定",
["LEVEL_11_MODES_UPGRADES"] = "レベル5マックス",
["LEVEL_11_TITLE"] = "11. キャニオンプラトー",
["LEVEL_12_HEROIC"] = "ヒロイック説明12",
["LEVEL_12_HISTORY"] = "デナスが再び私たちの側に戻り、未知へとポータルを渡った。この奇妙な世界はリニレアのねじれた反映のようだが、疫病に飲み込まれたものだ。一歩一歩気をつけて、カルトよりも悪い何かが暗闇に潜んでいる。",
["LEVEL_12_IRON"] = "鉄の説明12",
["LEVEL_12_IRON_UNLOCK"] = "未定",
["LEVEL_12_MODES_UPGRADES"] = "レベル5マックス",
["LEVEL_12_TITLE"] = "12. 枯れた農地",
["LEVEL_13_HEROIC"] = "ヒロイック説明13",
["LEVEL_13_HISTORY"] = "嵐雲の寺院の見慣れた姿が地平線に浮かんでいます。道は十分に明確で、悪臭と腐敗が増すにつれてそれに従い、その全ての源を見つけ出すでしょう。私たちは、地球自体から現れるように見えるねじれた恐怖を生き延びるだけです。",
["LEVEL_13_IRON"] = "鉄の説明13",
["LEVEL_13_IRON_UNLOCK"] = "未定",
["LEVEL_13_MODES_UPGRADES"] = "レベル5マックス",
["LEVEL_13_TITLE"] = "13. 冒涜された寺院",
["LEVEL_14_HEROIC"] = "ヒロイック説明14",
["LEVEL_14_HISTORY"] = "どこからともなく現れる、この呪われた生物たち！部隊は落ち着かない、私たちが触れるもの全てが生きていて、まるで土地自体が全力で私たちに対抗しているかのように私たちを襲おうとする。預言者ミドリアスと彼女の手下たちは近くにいるに違いない。",
["LEVEL_14_IRON"] = "鉄の説明14",
["LEVEL_14_IRON_UNLOCK"] = "未定",
["LEVEL_14_MODES_UPGRADES"] = "レベル5マックス",
["LEVEL_14_TITLE"] = "14. コラプションバレー",
["LEVEL_15_HEROIC"] = "英雄的な説明15",
["LEVEL_15_HISTORY"] = "私たちは谷から勝利して出てきた、そして今、私たちと監督者の間に立つ唯一のものはミドリアス自身です。彼女が峡谷で何をするかは見ましたが、ここでは、彼女の主の視線と力の下で、彼女が優位に立っています。それが以前に私たちを止めたことがあるわけではありません。元気を出して！",
["LEVEL_15_IRON"] = "鉄の説明15",
["LEVEL_15_IRON_UNLOCK"] = "定義される",
["LEVEL_15_MODES_UPGRADES"] = "レベル5最大",
["LEVEL_15_TITLE"] = "15. 目障りなタワー",
["LEVEL_16_HEROIC"] = "英雄的な説明16",
["LEVEL_16_HISTORY"] = "Mydriasはもう存在せず、監督者が残る最大の敵です。これがカルトと侵略に終止符を打つ最後のチャンスです。次に何が起ころうと、最後に一度だけでも団結しなければ意味がありません。行きましょう！",
["LEVEL_16_IRON"] = "鉄の説明16",
["LEVEL_16_IRON_UNLOCK"] = "未定",
["LEVEL_16_MODES_UPGRADES"] = "レベル5最大",
["LEVEL_16_TITLE"] = "16. ハンガーのピーク",
["LEVEL_17_HISTORY"] = "幻想的なフェアリックフォレストの周辺が敵対的で恐ろしい場所に変わりつつあります。倒れたエルフの戦士たちや幽霊のような存在が、この地を徘徊し、旅人を襲い、その存在によって森自体を腐敗させていると言われています。将軍、もっと徹底的に調査しなければなりません。",
["LEVEL_17_TITLE"] = "17. 霧の廃墟",
["LEVEL_18_HISTORY"] = "ディープリーフ前哨基地からメッセージが届き、いくつかのエルフが復活した軍団の進撃に辛うじて抵抗していると伝えられました。彼らとその隊長エリダンを救うために、急がなければなりません。前哨基地がしっかりと確保されれば、この侵略の根源にたどり着くことができるでしょう。",
["LEVEL_18_TITLE"] = "18. ディープリーフ前哨基地",
["LEVEL_19_HISTORY"] = "疲れ果てたエリダンが、魂の曲げ手ナヴィラと名乗る魔術師に率いられ、大陸を侵略するために復活した軍団が進軍する堕落者の神殿への道を示してくれました。何としても彼を止めなければなりません！",
["LEVEL_19_TITLE"] = "19. 堕落者の神殿",
["LEVEL_1_HEROIC"] = "英雄的な説明1",
["LEVEL_1_HISTORY"] = "南の森を数ヶ月間探し続けてきましたが、デナス王はどこにも見つかりません。その間に、自然の精霊であるアーボリアンと友達になり、戦いを続ける隣人、ワイルドビーストに出会いました。彼らは私たちを見るとすぐに攻撃してきます。\nこの戦いを終わらせて、王を探し続けましょう。",
["LEVEL_1_IRON"] = "鉄の説明1",
["LEVEL_1_IRON_UNLOCK"] = "王室の弓兵\nパラディンの盟約",
["LEVEL_1_MODES_UPGRADES"] = "レベル1最大",
["LEVEL_1_TITLE"] = "1. 樹海",
["LEVEL_20_HISTORY"] = "森のふちにいるアーボリアンから緊急のウィスプを受け取りました。彼らは必死に助けを求めています。彼らは relentless Croks に攻撃されています。もう長くは持ちません。慎重に行動してください、将軍。Croksは鱗の下に多くの策略を持っています。",
["LEVEL_20_TITLE"] = "20. アルボリアン・ハムレット",
["LEVEL_21_HISTORY"] = "町の安全を確保した後、アーボリアンは攻撃直前に古代の封印が弱まっているのを感じたと明かしました。クロックの突然の侵攻についての手がかりを手に、私たちは沼地の心臓部へと潜り込みました。私たちは古いアーボリアンの石の輪を見つけました、それは隠れ家のようです...何か巨大なものの隠れ家のようです。",
["LEVEL_21_TITLE"] = "21. 沈没した遺跡",
["LEVEL_22_HISTORY"] = "古代の寺院に到着したとき、私たちの最悪の恐れが確認されました。私たちの世界をアボミノール — 領域を飲み込むもの — から長い間保護してきた封印はほとんど解けかけており、アーボリアンのシャーマンたちの必死の結合魔法によってのみ保持されていました。将軍、アボミノールを止めないと、王国はその飽くなき口によって消費されるでしょう。",
["LEVEL_22_TITLE"] = "22. 飢えたホロー",
["LEVEL_23_HISTORY"] = "偵察兵が近くの山で不自然な地滑りを報告し、それが見知らぬドワーフによって引き起こされたことを発見しました。彼らは山の南側で巨大なオートマタを組み立てています。将軍、確認するべきです。",
["LEVEL_23_TITLE"] = "23. 鍛冶の門",
["LEVEL_24_HISTORY"] = "ドワーフは昔から奇妙な発明家でしたが、この「ダークスチール」一族はその金属への執着を行き過ぎたところまで押し進めています。ボルガーの民よりもさらに速く、自らを「改良」するために鍛冶を利用しています。この狂気の背後には誰がいるのか？調査する必要があります！",
["LEVEL_24_TITLE"] = "24. 狂乱の組立",
["LEVEL_25_HISTORY"] = "私たちの懸念通り、山の内部にはこのオートマタを作成できるほどの巨大な鍛冶場があります。ここに何人のドワーフがいるのでしょうか？我々の進軍を阻止しながらも、彼らは鍛造と溶接を続けています。そしてさらに奇妙なことに、彼らは全員同じ姿をしています。何かがおかしいです。",
["LEVEL_25_TITLE"] = "25. 巨大な核",
["LEVEL_26_HISTORY"] = "山の中を進むうちに、液槽で満たされた部屋に到達しました。それらは空ではありませんでした。彼らの数が多く、鍛造と溶接の技術を持っている理由が明らかです。彼らはすべて同じドワーフ、グリムビアードです。彼は邪悪な科学を使って自身の複製を作り出していました。将軍、これを止めなければなりません！",
["LEVEL_26_TITLE"] = "26. 複製の間",
["LEVEL_27_HISTORY"] = "山でのダークスチール作戦の大部分を混乱させることに成功しましたが、グリムビアードがまだ自由であればすべて無駄になります。彼はきっとオートマタの頭部の最終仕上げに取り組んでいるでしょう。将軍、部隊を山頂へ送り、今回は正しいドワーフと対決できることを祈りましょう。",
["LEVEL_27_TITLE"] = "27. 支配のドーム",
["LEVEL_28_HISTORY"] = "我々の斥候が残した手がかりをたどり、あの呪われたカルト信者たちの残党へと続く道を発見した。どうやら彼らは新たな女神を見つけたようだ—忌まわしい蜘蛛の巣を張る怪物を… カルト信者と蜘蛛？ こんな組み合わせで良いことが起こるはずがない。",
["LEVEL_28_TITLE"] = "28. 穢れた寺院",
["LEVEL_29_HISTORY"] = "奥へ進めば進むほど、この恐怖が長い間地下で成長し、攻撃の機会をうかがっていたことがはっきりしてくる。周囲に絡みつく蜘蛛の巣の密度と、首筋に息吹を感じるような不気味な暗闇から判断すると、おそらく我々は奴らの巣の中心部に近づいている。",
["LEVEL_29_TITLE"] = "29. 繁殖の間",
["LEVEL_2_HEROIC"] = "英雄的な説明2",
["LEVEL_2_HISTORY"] = "気をつけろ、ウィスパーによって私たちに知らせが届いたぞ！森の心臓部が攻撃されている！私たちは戻って、アーボリアンズを助けなければならない。いくつかのダークアーミーの勢力が戦場で私たちに加わる予定なので、気をつけて。今は同じ舟に乗っているかもしれないが、それはいつでも変わるかもしれない。",
["LEVEL_2_IRON"] = "鉄の説明2",
["LEVEL_2_IRON_UNLOCK"] = "奥术巫师\n三管炮",
["LEVEL_2_MODES_UPGRADES"] = "レベル2マックス",
["LEVEL_2_TITLE"] = "2. 守護者の門",
["LEVEL_30_HISTORY"] = "ついに、奴らが神と崇める存在の巣へとたどり着いた—廃れ果て、長らく放置された神殿が、自らの忘れ去られた過去の重みに押しつぶされるように崩れ落ちている。見捨てられた神にふさわしい玉座だ。今度こそ、一匹も逃さず根絶やしにしてやる。",
["LEVEL_30_TITLE"] = "30. 忘れられた玉座",
["LEVEL_31_HISTORY"] = "すべての戦いや苦闘の末、王国にようやく平和が戻った。今や、波の音に耳を傾け、古い友を待ちながらボードゲームをすることだけが、やるべきことだ。それでも、すべてが穏やかに見える中で、この平和がいつまで続くのかと思わずにはいられない…。",
["LEVEL_31_TITLE"] = "31. 天上の猿の森",
["LEVEL_32_HISTORY"] = "我々の追跡は火山の奥深く、かつて炎に捧げられた忘れられた神殿の跡地へと続いた。\n\nしかし、かつてこの灼熱の深淵を中立的に守護していた大火竜が、今や不自然な怒りに駆られている。全ての兆候が、レッドボーイの影響によってその意思が歪められたことを示している。",
["LEVEL_32_TITLE"] = "32. 火竜の洞窟",
["LEVEL_33_HISTORY"] = "レッドボーイとの激闘の末、我々はテンペスト島へと進んだ。足を踏み入れた瞬間、稲妻を孕んだ雲と荒々しい突風が奇妙にねじれた模様で唸りを上げた。それでも進むしかない。この島には姫の宮殿への唯一の入口があるのだ。覚悟しろ…嵐が来る。",
["LEVEL_33_TITLE"] = "33. 嵐の島",
["LEVEL_34_HISTORY"] = "この苦難を乗り越えられたのは、姫とその鉄の扇のおかげだ。橋を渡り、最も激しい嵐を乗り越え、今、私たちはその中心に立っている。この場所は無傷のまま—偽りのように静かで美しい。気を緩めるわけにはいかない。たとえ魔の王族であっても、我々の行く手を阻むことはできない。",
["LEVEL_34_TITLE"] = "34. 嵐の目",
["LEVEL_35_HISTORY"] = "ついに来た。牛魔王がその難攻不落の要塞に堂々と立ちはだかっている。残された我々の部隊で、力と知恵を尽くして正面から突撃する。やつが宝珠の力を完全に解き放つ前に打たねばならない。\nこの尊き大地であなたが大切にするすべてのもののために… 立ち上がれ、同盟よ！",
["LEVEL_35_TITLE"] = "35. 魔王の要塞",
["LEVEL_3_HEROIC"] = "英雄的な説明3",
["LEVEL_3_HISTORY"] = "間一髪でハートに帰り着いたが、既にワイルドビーストが通り抜けている。機敏に行動し、陣地を固めよ！ハートを何としても守れ。さもなくば森とアーボリアンは確実に滅びるだろう。",
["LEVEL_3_IRON"] = "鉄の説明3",
["LEVEL_3_IRON_UNLOCK"] = "ロイヤルアーチャー\nパラディンコヴェナント",
["LEVEL_3_MODES_UPGRADES"] = "レベル3最大",
["LEVEL_3_TITLE"] = "3. 森の心",
["LEVEL_4_HEROIC"] = "英雄的な説明4",
["LEVEL_4_HISTORY"] = "森の心が安全になった今、我々は再編成して有利を追求する必要がある。ワイルドビーストの領域へ戦いを持ち込む時だ。部隊を森の樹々の上に連れて行き、上から彼らのキャンプを探せ。",
["LEVEL_4_IRON"] = "鉄の説明4",
["LEVEL_4_IRON_UNLOCK"] = "トリキャノン\nアルボレアン大使",
["LEVEL_4_MODES_UPGRADES"] = "レベル4最大",
["LEVEL_4_TITLE"] = "4. エメラルドの樹冠",
["LEVEL_5_HEROIC"] = "英雄的な説明5",
["LEVEL_5_HISTORY"] = "高地を取るという努力のおかげで、森の限界を超えた古代の遺跡にワイルドビーストのキャンプを発見しました。その領域に向けて軍を進め、彼らの戦術に注意してください。また一戦を勝ち取ったかもしれませんが、これで終わりではありません。",
["LEVEL_5_IRON"] = "鉄の説明5",
["LEVEL_5_IRON_UNLOCK"] = "アーケインウィザード\nパラディンの契約",
["LEVEL_5_MODES_UPGRADES"] = "レベル5最大",
["LEVEL_5_TITLE"] = "5. 荒廃した郊外",
["LEVEL_6_HEROIC"] = "英雄的な説明6",
["LEVEL_6_HISTORY"] = "ワイルドビーストには優位に立っているかもしれませんが、リーダーのゴアグラインドと対峙する必要があります。自称ワイルドビーストの王は強力な敵なので、彼の奇行に騙されないように、さもなければその牙の下で終わりを迎えることになります。",
["LEVEL_6_IRON"] = "鉄の説明6",
["LEVEL_6_IRON_UNLOCK"] = "王立の射手\nデーモンの穴",
["LEVEL_6_MODES_UPGRADES"] = "レベル5最大",
["LEVEL_6_TITLE"] = "6. ワイルドビーストの巣",
["LEVEL_7_HEROIC"] = "英雄的な説明7",
["LEVEL_7_HISTORY"] = "野獣を助けて森の一部を焼き払ったカルトの跡をたどり、彼らが奇怪な計画を実行していると疑われる荒れ果てた場所に到着しました。私たちは何と戦っているのか正確にはわからないので注意が必要です...しかし、彼らはフードの下にいくつかの策略を持っているようです。",
["LEVEL_7_IRON"] = "鉄の説明7",
["LEVEL_7_IRON_UNLOCK"] = "王立の射手なし",
["LEVEL_7_MODES_UPGRADES"] = "レベル5最大",
["LEVEL_7_TITLE"] = "7. 悲観的な谷 ",
["LEVEL_8_HEROIC"] = "英雄的な説明8",
["LEVEL_8_HISTORY"] = "私たちが邪教徒の領域に入ったとき、奇妙な魔法で鳴り響く結晶でいっぱいの巨大な洞窟のセットに到着しました。カルトはこれらの結晶を採掘しており、確かにそれらをパワーソースとして使用するためでしょう。どのような目的であるかはわかりませんが、彼らの活動を妨害することは、彼らの階級内で混乱を引き起こす良い方法です。 ",
["LEVEL_8_IRON"] = "鉄の説明8",
["LEVEL_8_IRON_UNLOCK"] = "トリキャノン\nパラディンの契約",
["LEVEL_8_MODES_UPGRADES"] = "レベル5マックス",
["LEVEL_8_TITLE"] = "8. カーマイン鉱山",
["LEVEL_9_HEROIC"] = "ヒロイック説明9",
["LEVEL_9_HISTORY"] = "これらのトンネルの曲がりくねりは気が狂いそうになるが、カルト活動が増え続けていることから、私たちは正しい道を進んでいることを知っている。さらに進むにつれて、新たな種類の恐怖に直面するが、それはカルトの階級内での腐敗がどれほど深いかという疑問を投げかける。",
["LEVEL_9_IRON"] = "鉄の説明9",
["LEVEL_9_IRON_UNLOCK"] = "魔王の穴\n秘術の魔法使い",
["LEVEL_9_MODES_UPGRADES"] = "レベル5マックス",
["LEVEL_9_TITLE"] = "9. ウィキッドクロッシング",
["LEVEL_DEFEAT_TITLE"] = "敗北！",
["LEVEL_MODE_CAMPAIGN"] = "キャンペーン",
["LEVEL_MODE_HEROIC"] = "英雄的チャレンジ",
["LEVEL_MODE_HEROIC_DESCRIPTION"] = "手強い敵軍を相手に高度な戦術が試されるチャレンジ。最も英雄的な防衛者のためだけに用意されている！",
["LEVEL_MODE_IRON"] = "鉄のチャレンジ",
["LEVEL_MODE_IRON_DESCRIPTION"] = "究極の防衛者を試す「鉄のチャレンジ」は限界を超えた戦術が必要となる。",
["LEVEL_MODE_LOCKED_DESCRIPTION"] = "このステージを3つ星でクリアして、このモードをアンロックしよう。",
["LEVEL_SELECT_AVAILABLE_TOWERS"] = "使用可能タワー",
["LEVEL_SELECT_CHALLENGE_ONE_ELITE_WAVE"] = "1つのエリート部隊",
["LEVEL_SELECT_CHALLENGE_ONE_LIFE"] = "合計1ライフ",
["LEVEL_SELECT_CHALLENGE_RULES"] = "チャレンジルール",
["LEVEL_SELECT_CHALLENGE_SIX_ELITE_WAVE"] = "6つのエリート部隊",
["LEVEL_SELECT_DIFFICULTY_CASUAL"] = "カジュアル",
["LEVEL_SELECT_DIFFICULTY_IMPOSSIBLE"] = "不可能",
["LEVEL_SELECT_DIFFICULTY_NORMAL"] = "ノーマル",
["LEVEL_SELECT_DIFFICULTY_VETERAN"] = "ベテラン",
["LEVEL_SELECT_GET_DLC"] = "それを手に入れろ",
["LEVEL_SELECT_MODE_LOCKED1"] = "モードがロック中",
["LEVEL_SELECT_MODE_LOCKED2"] = "このステージをクリアしてこのモードを解除してください。",
["LEVEL_SELECT_TO_BATTLE"] = "バトル\n開始",
["LV22_BOSS_BEFORE_FIGHT_EAT_01"] = "美味しい！ハ ハ ハ",
["LV22_BOSS_BEFORE_FIGHT_EAT_02"] = "植物が嫌い",
["LV22_BOSS_BEFORE_FIGHT_EAT_03"] = "食べるものがあなたを作る",
["LV22_BOSS_BEFORE_FIGHT_EAT_04"] = "この一口はさわやかだった",
["LV22_BOSS_BEFORE_FIGHT_EAT_05"] = "もう疲れたの？",
["LV22_BOSS_BEFORE_FIGHT_EAT_06"] = "もう二度とお腹を空かせない",
["LV22_BOSS_BEFORE_FIGHT_EAT_07"] = "偉大な塔だった、ハハハ",
["LV22_BOSS_BEFORE_FIGHT_EAT_08"] = "自由の味がする",
["LV22_BOSS_INTRO_01"] = "最初の食事にスナックを持ってくる。",
["LV22_BOSS_INTRO_02"] = "カリカリしてるみたい...",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_01"] = "クリーパーしか味わえないよ。",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_02"] = "緑は友達、食べ物ではない",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_03"] = "そして、これ以上何も食べることはない！",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_04"] = "お前の牢獄へ戻れ、怪物よ！",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_05"] = "食べてはならない！！",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_06"] = "緑を守る！",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_07"] = "最後には笑わないだろう",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_08"] = "彼は強くなっている！助けて！！",
["LV22_MAGE_INTRO_01"] = "口を閉じろ！",
["LV22_MAGE_INTRO_02"] = "急いで！もう長くは彼を止められない！",
["Level"] = "レベル",
["Localization Manager"] = "ローカライズマネージャー",
["Long"] = "ロング",
["Low"] = "低",
["MAGES’ GUILD"] = "メイジギルド",
["MAGIC RESISTANT ENEMIES!"] = "魔法防御を持つ敵！",
["MAP_BALLON_BUY_UPGRADES_DESCRIPTION"] = "獲得した星を使ってタワーとパワーを強化しよう！",
["MAP_BALLON_BUY_UPGRADES_TITLE"] = "アップグレード を購入！",
["MAP_BALLON_HERO_LEVELUP_DESCRIPTION"] = "獲得した英雄ポイントを使って英雄を訓練しよう！",
["MAP_BALLON_HERO_LEVELUP_TITLE"] = "英雄がレベルアップ！",
["MAP_BALLON_HERO_UNLOCKED"] = "英雄をアンロック！",
["MAP_BALLON_START_HERE"] = "ここでスタート！",
["MAP_BUTTON_ACHIEVEMENTS"] = "アチーブメント",
["MAP_BUTTON_HERO_ROOM"] = "ヒーローズ",
["MAP_BUTTON_ITEMS"] = "アイテムズ",
["MAP_BUTTON_SHOP"] = "ショップ",
["MAP_BUTTON_TOWER_ROOM"] = "タワーズ",
["MAP_BUTTON_UPGRADES"] = "アップグレード",
["MAP_HEROROOM_HELP1"] = "アビリティを選んで訓練しよう！",
["MAP_HEROROOM_HELP2"] = "タップして選択",
["MAP_HEROROOM_HELP3"] = "英雄のパワーを強化！",
["MAP_HERO_ROOM_GET_IT_NOW"] = "今すぐゲット！",
["MAP_HERO_ROOM_SELECT"] = "装備する",
["MAP_HERO_ROOM_SELECTED"] = "装備済み",
["MAP_HERO_ROOM_TRAIN"] = "訓練",
["MAP_HERO_ROOM_UNLOCK"] = "ステージ  %s でアンロック",
["MAP_HERO_ROOM_UNLOCK_10"] = "ステージ10でアンロック",
["MAP_HERO_ROOM_UNLOCK_14"] = "ステージ14でアンロック",
["MAP_HERO_ROOM_UNLOCK_15"] = "ステージ15でアンロック",
["MAP_HERO_ROOM_UNLOCK_4"] = "ステージ4でアンロック",
["MAP_HERO_ROOM_UNLOCK_7"] = "ステージ7でアンロック",
["MAP_HERO_ROOM_UNLOCK_9"] = "ステージ9でアンロック",
["MAP_HERO_ROOM_UNLOCK_AFTER_CAMPAIGN"] = "ゲームをクリアすると解除されます ",
["MAP_INAPPS_BUBBLE_INFO_1"] = "ゲームをプレイしてジェムを集めよう。",
["MAP_INAPPS_BUBBLE_INFO_2"] = "ジェムを使ってスペシャルアイテムを購入しよう！",
["MAP_INAPPS_BUBBLE_MORE_GEMS"] = "もっとジェムが必要だ！",
["MAP_INAPPS_BUBBLE_SUCCESSFUL"] = "購入\n成功！",
["MAP_INAPP_GEMS_GEM_SHOP_TITLE"] = "ジェムショップ",
["MAP_INAPP_GEM_PACK_1"] = "ひとつかみのジェム",
["MAP_INAPP_GEM_PACK_2"] = "袋一杯のジェム",
["MAP_INAPP_GEM_PACK_3"] = "樽一杯のジェム",
["MAP_INAPP_GEM_PACK_4"] = "箱一杯のジェム",
["MAP_INAPP_GEM_PACK_5"] = "ジェムの荷車",
["MAP_INAPP_GEM_PACK_6"] = "ジェムの山",
["MAP_INAPP_GEM_PACK_BAG"] = "袋いっぱいのジェム",
["MAP_INAPP_GEM_PACK_BARREL"] = "樽いっぱいのジェム",
["MAP_INAPP_GEM_PACK_CHEST"] = "箱いっぱいのジェム",
["MAP_INAPP_GEM_PACK_FREE"] = "ボーナスジェム",
["MAP_INAPP_GEM_PACK_HANDFUL"] = "ひとつかみのジェム",
["MAP_INAPP_GEM_PACK_VAULT"] = "金庫いっぱいのジェム",
["MAP_INAPP_GEM_PACK_WAGON"] = "貨車いっぱいのジェム",
["MAP_INAPP_MORE_GEMS"] = "もっとジェムを入手",
["MAP_INAPP_TEXT_1"] = "ひとつかみのジェム",
["MAP_INAPP_TEXT_2"] = "袋いっぱいのジェム",
["MAP_INAPP_TEXT_3"] = "箱いっぱいのジェム",
["MAP_INAPP_TEXT_4"] = "ボーナスジェム",
["MAP_INAPP_TEXT_GEMS"] = "ジェム",
["MAP_NEW_GAMEMODE_UNLOCKED_DESCRIPTION"] = "エンドレスの敵と戦って、ベストスコアを目指そう！",
["MAP_NEW_GAMEMODE_UNLOCKED_TITLE"] = "新しいチャレンジ！",
["MAP_NEW_HERO_ALERT"] = "新しいヒーロー！",
["MAP_NEW_TOWER_ALERT"] = "新しいタワー！",
["MAP_TOWER_ROOM_SELECT"] = "装備する",
["MAP_TOWER_ROOM_SELECTED"] = "装備済み",
["MENU_HUD_WAVES"] = "%i/%i",
["MINUTES_ABBREVIATION"] = "分",
["MORE_GAMES"] = "その他のゲーム",
["MUSIC"] = "BGM",
["Magic resistant enemies take less damage from mages."] = "魔法防御を持つ敵はメイジの攻撃によるダメージを軽減する。",
["Medium"] = "中",
["Music"] = "BGM",
["NEW POWER!"] = "新しいパワー！",
["NEW SPECIAL POWER!"] = "新しいスペシャルパワー！",
["NEW TOWER UNLOCKED"] = "新しいタワーをアンロック",
["NEW TOWER UPGRADES"] = "新しいタワーアップグレード",
["NEW TOWERS UNLOCKED"] = "新しいタワーをアンロック",
["NEWS"] = "ニュース",
["NEW_ENEMY_ALERT_ICON"] = "新しい敵",
["NO HEROES"] = "英雄なし",
["NOTIFICATION_NEW_ENEMY_TITLE"] = "新しい敵",
["NOTIFICATION_NEW_SPECIAL_TITLE"] = "新しい特別な力!",
["NOTIFICATION_NEW_TOWERS_SUB_DESCRIPTION"] = "これで、タワーをレベル%dまでアップグレードできるようになりました。",
["NOTIFICATION_NEW_TOWERS_SUB_TITLE"] = "レベル%dのタワーが利用可能",
["NOTIFICATION_NEW_TOWERS_TITLE"] = "新しいタワーアップグレード",
["NOTIFICATION_NEW_TOWER_TITLE"] = "新しいタワーがアンロックされました",
["NOTIFICATION_armored_enemies_desc_body_1"] = "一部の敵は異なる強度の鎧を着ており、非魔法の攻撃から保護されます。",
["NOTIFICATION_armored_enemies_desc_body_2"] = "のダメージに耐える",
["NOTIFICATION_armored_enemies_desc_body_3"] = "装甲の敵は、マークスマン、兵舎、および砲塔からのダメージをより少なく受けます。",
["NOTIFICATION_armored_enemies_desc_title"] = "装甲の敵！",
["NOTIFICATION_armored_enemies_enemy_name"] = "牙突き闘士",
["NOTIFICATION_bottom_info_desc_body"] = "ユニットとその肖像をクリックすると、いつでも敵の情報を確認できます。",
["NOTIFICATION_bottom_info_desc_title"] = "敵情報",
["NOTIFICATION_bottom_info_tap_portrait_desc"] = "再開するにはここをクリックしてください。",
["NOTIFICATION_button_ok"] = "オーケー",
["NOTIFICATION_glare_desc_body"] = "監督者は戦場を見つめ、その腐敗した視線で近くの敵を強化します。",
["NOTIFICATION_glare_desc_bullets"] = " - エリア内にいる敵を回復させる\n- 敵のユニークな能力を発動させる",
["NOTIFICATION_glare_desc_title"] = "監督者の視線",
["NOTIFICATION_hero_desc"] = "レベル、健康、経験を表示します。",
["NOTIFICATION_hero_desc_baloon_1"] = "ポートレートまたは英雄ユニットをクリックして選択。ホットキー：スペースキー",
["NOTIFICATION_hero_desc_baloon_2"] = "経路をクリックして英雄を移動させよう。",
["NOTIFICATION_hero_desc_body_1"] = "英雄は、強力な敵に立ち向かい、あなたの軍をサポートできるエリートユニットです。",
["NOTIFICATION_hero_desc_body_2"] = "英雄は、敵を攻撃したり、能力を使用するたびに経験値を得ます。",
["NOTIFICATION_hero_desc_title"] = "あなたの命令でヒーロー！",
["NOTIFICATION_magic_resistant_enemies_desc_body_1"] = "一部の敵には、魔法攻撃から守る魔法耐性の異なるレベルがあります。",
["NOTIFICATION_magic_resistant_enemies_desc_body_2"] = "のダメージに耐える",
["NOTIFICATION_magic_resistant_enemies_desc_body_3"] = "魔法抵抗の敵は、魔法使いの塔からのダメージをより少なく受けます。",
["NOTIFICATION_magic_resistant_enemies_desc_title"] = "魔法に抵抗する敵！",
["NOTIFICATION_magic_resistant_enemies_enemy_name"] = "カメのシャーマン",
["NOTIFICATION_rally_point_desc_body_1"] = "兵舎の集結地点を調整して、部隊に別のエリアを防御させることができます。",
["NOTIFICATION_rally_point_desc_body_2"] = "集結地操作を選択",
["NOTIFICATION_rally_point_desc_body_3"] = "兵士を移動させたい場所を選択",
["NOTIFICATION_rally_point_desc_subtitle"] = "集会範囲",
["NOTIFICATION_rally_point_desc_title"] = "あなたの部隊を指揮してください！",
["NOTIFICATION_special_desc_body"] = "戦場であなたを助けるために、追加の兵士を召喚することができます。",
["NOTIFICATION_special_desc_bullets"] = "援軍は敵を止めるのに最適です。",
["NOTIFICATION_special_desc_title"] = "援軍を呼ぶ",
["NOTIFICATION_title_enemy"] = "敵情報",
["NOTIFICATION_title_glare"] = "新しいヒント！",
["NOTIFICATION_title_hint"] = "ヒーロー解除",
["NOTIFICATION_title_new_tip"] = "新しいヒント",
["NOTIFICATION_title_special"] = "スペシャルがアンロックされました",
["Next!"] = "次！",
["No"] = "いいえ",
["None"] = "無し",
["Nope"] = "いいえ",
["Normal"] = "ノーマル",
["OFFER_GET_IT_NOW"] = "今すぐゲット",
["OFFER_GET_THEM_NOW"] = "今すぐゲット",
["OFFER_OFF"] = "オフ",
["OFFER_REGULAR"] = "通常価格",
["OK!"] = "OK!",
["ONE_TIME_OFFER"] = "一度きりの提供！",
["OPTIONS"] = "オプション",
["OPTIONS_PAGE_CONTROLS"] = "操作方法",
["OPTIONS_PAGE_HELP"] = "ヘルプ",
["OPTIONS_PAGE_SHORTCUTS"] = "キーボードヘルプ",
["OPTIONS_PAGE_VIDEO"] = "ビデオ",
["Objective"] = "目的",
["Options"] = "オプション",
["Over 50 stars are recommended to face this stage."] = "このステージへの挑戦には星を50個以上持っていることが推奨される。",
["POPUP_CLEAR_PROGRESS_CONFIRM"] = "進捗をクリアしてもよろしいですか？",
["POPUP_LABEL_MAIN_MENU"] = "メインメニュー",
["POPUP_SETTINGS_LANGUAGE"] = "言語",
["POPUP_SETTINGS_MUSIC"] = "音楽",
["POPUP_SETTINGS_SFX"] = "効果音",
["POPUP_label_error_msg"] = "おっと！何らかの問題が発生しました。",
["POPUP_label_error_msg2"] = "おっと！何らかの問題が発生しました。",
["POPUP_label_purchasing"] = "リクエストを処理中",
["POPUP_label_title_options"] = "オプション",
["POPUP_label_version"] = "バージョン0.0.8b",
["POWER_SUMMON_DESCRIPTION"] = "戦場で支援してくれる部隊を呼び出す。",
["POWER_SUMMON_LARGE_DESCRIPTION"] = "戦場で支援してくれる部隊を呼び出す。\n\n援軍は無料であり、15秒に1回呼ぶことができる。",
["POWER_SUMMON_NAME"] = "援軍要請",
["PRICE_FREE"] = "Free",
["PRIVACY_POLICY_ASK_AGE"] = "生年月日は何ですか？",
["PRIVACY_POLICY_BUTTON_LINK"] = "プライバシーポリシー",
["PRIVACY_POLICY_CONSENT_SHORT"] = "このゲームをプレイする前に、あなた自身（あなたがまだ未成年の場合は保護者）がプライバシーポリシーの内容を読み、確認するようにしてください。",
["PRIVACY_POLICY_LINK"] = "プライバシーポリシー",
["PRIVACY_POLICY_WELCOME"] = "ようこそ！",
["PROCESSING YOUR REQUEST"] = "リクエストを処理中",
["Produced by %s"] = "製作： %s",
["QUIT"] = "終了",
["Quit"] = "終了",
["RESTORE_PURCHASES"] = "購入を復元",
["Reset"] = "リセット",
["Restart"] = "再スタート",
["Resume"] = "再開",
["SECONDS_ABBREVIATION"] = "秒",
["SETTINGS_DISPLAY"] = "ディスプレイ",
["SETTINGS_FRAMES_PER_SECOND"] = "FPS",
["SETTINGS_FULLSCREEN"] = "全画面表示",
["SETTINGS_FULLSCREEN_BORDERLESS"] = "フチなしフルスクリーン",
["SETTINGS_IMAGE_QUALITY"] = "画質",
["SETTINGS_LANGUAGE"] = "言語",
["SETTINGS_LARGE_MOUSE_POINTER"] = "大きなマウスポインタ",
["SETTINGS_LOW_IMAGE_QUALITY_LINK"] = "低画質ですか？ここをクリック！",
["SETTINGS_RETINA_DISPLAY"] = "レティナディスプレイ (Mac)",
["SETTINGS_SCREEN_RESOLUTION"] = "画面の解像度",
["SETTINGS_SUPPORT"] = "サポート",
["SETTINGS_VSYNC"] = "Vsync",
["SFX"] = "効果音",
["SHOP_DESKTOP_GET_DLC_BUTTON"] = "それを手に入れろ",
["SHOP_DESKTOP_TITLE"] = "ショップ",
["SHOP_ROOM_BEST_VALUE_TITLE"] = "最高の\n価値",
["SHOP_ROOM_DLC_1_DESCRIPTION"] = "この新たな壮大な冒険に乗り出そう",
["SHOP_ROOM_DLC_1_TITLE"] = "巨大なドワーフ戦争キャンペーン",
["SHOP_ROOM_DLC_1_TOOLTIP_DESCRIPTION"] = "新たに5つのステージ\n新しいタワー\n新しいヒーロー\n10以上の新しい敵\n2回のミニボス戦\n壮大なボス戦\nさらに多くの要素が追加...",
["SHOP_ROOM_DLC_1_TOOLTIP_TITLE"] = "巨大なドワーフ戦争キャンペーン",
["SHOP_ROOM_DLC_2_DESCRIPTION"] = "この新たな壮大な冒険に出発しよう",
["SHOP_ROOM_DLC_2_TITLE"] = "悟空の旅キャンペーン",
["SHOP_ROOM_MOST_POPULAR_TITLE"] = "最も\n人気がある",
["SLOT_CLOUD_DOWNLOADING"] = "ダウンロード中…",
["SLOT_CLOUD_DOWNLOAD_FAILED"] = "iCloud からのセーブデータダウンロードに失敗。後ほどまたお試しください。",
["SLOT_CLOUD_DOWNLOAD_SUCCESSFUL"] = "ダウンロード成功。",
["SLOT_CLOUD_UPLOADING"] = "アップロード中…",
["SLOT_CLOUD_UPLOAD_FAILED"] = "セーブデータの iCloud へのアップロードに失敗。後ほどまたお試しください。",
["SLOT_CLOUD_UPLOAD_ICLOUD_NOT_CONFIGURED"] = "お使いの端末で iCloud が設定されていません。",
["SLOT_CLOUD_UPLOAD_SUCCESSFUL"] = "アップロード成功。",
["SLOT_DELETE_SLOT"] = "スロットを削除しますか？",
["SLOT_NAME"] = "スロット",
["SLOT_NEW_GAME"] = "ニューゲーム",
["SOLDIER_ARBOREAN_BARRACK_NAME"] = "アルボリアンの兵士",
["SOLDIER_ARBOREAN_SENTINELS_1_NAME"] = "バルー",
["SOLDIER_ARBOREAN_SENTINELS_2_NAME"] = "ヴィラ",
["SOLDIER_ARBOREAN_SENTINELS_3_NAME"] = "イッコン",
["SOLDIER_ARBOREAN_SENTINELS_4_NAME"] = "ハーヴィ",
["SOLDIER_ARBOREAN_SENTINELS_5_NAME"] = "プルック",
["SOLDIER_ARBOREAN_SENTINELS_6_NAME"] = "ガルッド",
["SOLDIER_ARBOREAN_SENTINELS_7_NAME"] = "ティーナ",
["SOLDIER_ARBOREAN_SENTINELS_8_NAME"] = "ウーズキー",
["SOLDIER_ARBOREAN_SENTINELS_9_NAME"] = "デルー",
["SOLDIER_DRAGON_BONE_ULTIMATE_DOG_NAME"] = "ボーンドレイク",
["SOLDIER_EARTH_HOLDER_NAME"] = "ストーン・ウォリアー",
["SOLDIER_GHOST_TOWER_NAME"] = "レイス",
["SOLDIER_HERO_BUILDER_WORKER_1_NAME"] = "ヘンマー",
["SOLDIER_HERO_BUILDER_WORKER_2_NAME"] = "オツール",
["SOLDIER_HERO_BUILDER_WORKER_3_NAME"] = "クルーズ",
["SOLDIER_HERO_BUILDER_WORKER_4_NAME"] = "バーク",
["SOLDIER_HERO_BUILDER_WORKER_5_NAME"] = "ラウク",
["SOLDIER_HERO_BUILDER_WORKER_6_NAME"] = "オネイル",
["SOLDIER_HERO_BUILDER_WORKER_7_NAME"] = "ホベルズ",
["SOLDIER_HERO_BUILDER_WORKER_8_NAME"] = "ウッディ",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL1_NAME"] = "アーボリアンの守護者",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL2_NAME"] = "アーボリアンの守護者",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL3_NAME"] = "アーボリアンの守護者",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL1_NAME"] = "アーボリアンの模範",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL2_NAME"] = "アーボリアンの模範",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL3_NAME"] = "アーボリアンの模範",
["SOLDIER_HERO_SPIDER_ULTIMATE_NAME"] = "スパイダーリング",
["SOLDIER_HERO_WITCH_CAT_1_NAME"] = "コナン",
["SOLDIER_HERO_WITCH_CAT_2_NAME"] = "アルファホール",
["SOLDIER_HERO_WITCH_CAT_3_NAME"] = "バビエカ",
["SOLDIER_HERO_WITCH_CAT_4_NAME"] = "ペルチェ",
["SOLDIER_HERO_WITCH_CAT_5_NAME"] = "ピパ",
["SOLDIER_HERO_WITCH_CAT_6_NAME"] = "ワトソン",
["SOLDIER_HERO_WITCH_CAT_7_NAME"] = "チミ",
["SOLDIER_HERO_WITCH_CAT_8_NAME"] = "パントゥフラ",
["SOLDIER_HERO_WITCH_DECOY_NAME"] = "トラピート",
["SOLDIER_HERO_WUKONG_HAIR_CLONES_1_NAME"] = "サン・ウィクン",
["SOLDIER_HERO_WUKONG_HAIR_CLONES_2_NAME"] = "ソン・ウォケン",
["SOLDIER_ITEM_SUMMON_BLACKBURN_NAME"] = "ロード・ブラックバーン",
["SOLDIER_PALADINS_10_NAME"] = "サー・ヨアキム",
["SOLDIER_PALADINS_11_NAME"] = "サー・アンドレ",
["SOLDIER_PALADINS_12_NAME"] = "サー・サメット",
["SOLDIER_PALADINS_13_NAME"] = "サー・ウド",
["SOLDIER_PALADINS_14_NAME"] = "サー・エリック",
["SOLDIER_PALADINS_15_NAME"] = "サー・ブルース",
["SOLDIER_PALADINS_16_NAME"] = "サー・ロブ",
["SOLDIER_PALADINS_17_NAME"] = "サー・ビフ",
["SOLDIER_PALADINS_18_NAME"] = "サー・ボウズ",
["SOLDIER_PALADINS_1_NAME"] = "サー・カイ",
["SOLDIER_PALADINS_2_NAME"] = "サー・ハンジ",
["SOLDIER_PALADINS_3_NAME"] = "サー・ルカ",
["SOLDIER_PALADINS_4_NAME"] = "サー・ティモ",
["SOLDIER_PALADINS_5_NAME"] = "サー・ラルフ",
["SOLDIER_PALADINS_6_NAME"] = "サー・トビアス",
["SOLDIER_PALADINS_7_NAME"] = "サー・デリス",
["SOLDIER_PALADINS_8_NAME"] = "サー・キスケ",
["SOLDIER_PALADINS_9_NAME"] = "サー・ペッシュ",
["SOLDIER_PRIESTS_BARRACK_1_NAME"] = "ウィリー",
["SOLDIER_PRIESTS_BARRACK_2_NAME"] = "ヘンリー",
["SOLDIER_PRIESTS_BARRACK_3_NAME"] = "ジェフリー",
["SOLDIER_PRIESTS_BARRACK_4_NAME"] = "ニコラス",
["SOLDIER_PRIESTS_BARRACK_5_NAME"] = "エド",
["SOLDIER_PRIESTS_BARRACK_6_NAME"] = "ホブ",
["SOLDIER_PRIESTS_BARRACK_7_NAME"] = "オド",
["SOLDIER_PRIESTS_BARRACK_8_NAME"] = "セドリック",
["SOLDIER_PRIESTS_BARRACK_9_NAME"] = "ハル",
["SOLDIER_RANDOM_10_NAME"] = "アルヴス",
["SOLDIER_RANDOM_11_NAME"] = "ボーリン",
["SOLDIER_RANDOM_12_NAME"] = "ハドリアン",
["SOLDIER_RANDOM_13_NAME"] = "トーマス",
["SOLDIER_RANDOM_14_NAME"] = "ヘンリー",
["SOLDIER_RANDOM_15_NAME"] = "ブライス",
["SOLDIER_RANDOM_16_NAME"] = "ルルフ",
["SOLDIER_RANDOM_17_NAME"] = "アリスター",
["SOLDIER_RANDOM_18_NAME"] = "アルタイル",
["SOLDIER_RANDOM_19_NAME"] = "サイモン",
["SOLDIER_RANDOM_1_NAME"] = "ダグラス",
["SOLDIER_RANDOM_20_NAME"] = "エグバート",
["SOLDIER_RANDOM_21_NAME"] = "エルドン",
["SOLDIER_RANDOM_22_NAME"] = "ギャレット",
["SOLDIER_RANDOM_23_NAME"] = "ゴドウィン",
["SOLDIER_RANDOM_24_NAME"] = "ゴードン",
["SOLDIER_RANDOM_25_NAME"] = "ジェラルド",
["SOLDIER_RANDOM_26_NAME"] = "ケルビン",
["SOLDIER_RANDOM_27_NAME"] = "ランド",
["SOLDIER_RANDOM_28_NAME"] = "マドックス",
["SOLDIER_RANDOM_29_NAME"] = "ペイトン",
["SOLDIER_RANDOM_2_NAME"] = "ダン・マッキル",
["SOLDIER_RANDOM_30_NAME"] = "ラムジー",
["SOLDIER_RANDOM_31_NAME"] = "レイモンド",
["SOLDIER_RANDOM_32_NAME"] = "ロバート",
["SOLDIER_RANDOM_33_NAME"] = "ソーヤー",
["SOLDIER_RANDOM_34_NAME"] = "サイラス",
["SOLDIER_RANDOM_35_NAME"] = "スチュアート",
["SOLDIER_RANDOM_36_NAME"] = "タンナー",
["SOLDIER_RANDOM_37_NAME"] = "アッシャー",
["SOLDIER_RANDOM_38_NAME"] = "ウォレス",
["SOLDIER_RANDOM_39_NAME"] = "ウェスリー",
["SOLDIER_RANDOM_3_NAME"] = "ジェームズ・リー",
["SOLDIER_RANDOM_40_NAME"] = "ウィラード",
["SOLDIER_RANDOM_4_NAME"] = "ジャー・ジョンソン",
["SOLDIER_RANDOM_5_NAME"] = "フィル",
["SOLDIER_RANDOM_6_NAME"] = "ロビン",
["SOLDIER_RANDOM_7_NAME"] = "ウィリアム",
["SOLDIER_RANDOM_8_NAME"] = "マーティン",
["SOLDIER_RANDOM_9_NAME"] = "アーサー",
["SOLDIER_REINFORCEMENTS_F_1_NAME"] = "アタイナ",
["SOLDIER_REINFORCEMENTS_F_2_NAME"] = "マウシル",
["SOLDIER_REINFORCEMENTS_F_3_NAME"] = "グリカ",
["SOLDIER_REINFORCEMENTS_F_4_NAME"] = "ロガス",
["SOLDIER_REINFORCEMENTS_M_10_NAME"] = "ポジー",
["SOLDIER_REINFORCEMENTS_M_1_NAME"] = "ガビニ",
["SOLDIER_REINFORCEMENTS_M_2_NAME"] = "オベル",
["SOLDIER_REINFORCEMENTS_M_3_NAME"] = "ケント",
["SOLDIER_REINFORCEMENTS_M_4_NAME"] = "ジェンダーズ",
["SOLDIER_REINFORCEMENTS_M_5_NAME"] = "ジャルロスク",
["SOLDIER_REINFORCEMENTS_M_6_NAME"] = "アストング",
["SOLDIER_REINFORCEMENTS_M_7_NAME"] = "ブイゲル",
["SOLDIER_REINFORCEMENTS_M_8_NAME"] = "クレイン",
["SOLDIER_REINFORCEMENTS_M_9_NAME"] = "マグス",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_1_NAME"] = "デンチ",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_2_NAME"] = "スミス",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_3_NAME"] = "アンドリュース",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_4_NAME"] = "トンプソン",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_5_NAME"] = "テイラー",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_1_NAME"] = "マッカートニー",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_2_NAME"] = "マッケラン",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_3_NAME"] = "ホプキンス",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_4_NAME"] = "ケイン",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_5_NAME"] = "キングスリー",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_10_NAME"] = "バイパー",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_1_NAME"] = "ファン",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_2_NAME"] = "ブレード",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_3_NAME"] = "クロー",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_4_NAME"] = "タロン",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_5_NAME"] = "エッジー",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_6_NAME"] = "シヴ",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_7_NAME"] = "サイズ",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_8_NAME"] = "ダガー",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_9_NAME"] = "スティング",
["SOLDIER_REINFORCEMENTS_SPECIAL_DARK_ARMY_1_NAME"] = "シャドウ・クロウコーラー",
["SOLDIER_REINFORCEMENTS_SPECIAL_LINIREA_1_NAME"] = "パラゴンナイト",
["SOLDIER_STAGE_10_YMCA_BIKER_NAME"] = "グレン",
["SOLDIER_STAGE_10_YMCA_CONSTRUCTOR_NAME"] = "デイビッド",
["SOLDIER_STAGE_10_YMCA_INDIO_NAME"] = "フェリペ",
["SOLDIER_STAGE_10_YMCA_POLICIA_NAME"] = "ヴィクター",
["SOLDIER_STAGE_15_DENAS_NAME"] = "キング・デナス",
["SOLDIER_TOWER_DARK_ELF_1_NAME"] = "フィルライン",
["SOLDIER_TOWER_DARK_ELF_2_NAME"] = "フェイリル",
["SOLDIER_TOWER_DARK_ELF_3_NAME"] = "グリーナ",
["SOLDIER_TOWER_DARK_ELF_4_NAME"] = "ジャラス",
["SOLDIER_TOWER_DARK_ELF_5_NAME"] = "ソレンザール",
["SOLDIER_TOWER_DARK_ELF_6_NAME"] = "テブリン",
["SOLDIER_TOWER_DARK_ELF_7_NAME"] = "ヴィエルナ",
["SOLDIER_TOWER_DARK_ELF_8_NAME"] = "ズィン",
["SOLDIER_TOWER_DARK_ELF_9_NAME"] = "エレラ",
["SOLDIER_TOWER_DWARF_10_NAME"] = "バッビ",
["SOLDIER_TOWER_DWARF_1_NAME"] = "ピッピ",
["SOLDIER_TOWER_DWARF_2_NAME"] = "ギンニ",
["SOLDIER_TOWER_DWARF_3_NAME"] = "メリ",
["SOLDIER_TOWER_DWARF_4_NAME"] = "ロリ",
["SOLDIER_TOWER_DWARF_5_NAME"] = "タリ",
["SOLDIER_TOWER_DWARF_6_NAME"] = "ダンニ",
["SOLDIER_TOWER_DWARF_7_NAME"] = "ゲッティ",
["SOLDIER_TOWER_DWARF_8_NAME"] = "ダッフィ",
["SOLDIER_TOWER_DWARF_9_NAME"] = "ビッビ",
["SOLDIER_TOWER_ELVEN_BARRACK_1_NAME"] = "エランディル",
["SOLDIER_TOWER_ELVEN_BARRACK_2_NAME"] = "パック",
["SOLDIER_TOWER_ELVEN_BARRACK_3_NAME"] = "サス",
["SOLDIER_TOWER_ELVEN_BARRACK_4_NAME"] = "カストール",
["SOLDIER_TOWER_ELVEN_BARRACK_5_NAME"] = "エルリック",
["SOLDIER_TOWER_ELVEN_BARRACK_6_NAME"] = "エレイス",
["SOLDIER_TOWER_NECROMANCER_SKELETON_GOLEM_NAME"] = "ボーンゴーレム",
["SOLDIER_TOWER_NECROMANCER_SKELETON_NAME"] = "スケルトン",
["SOLDIER_TOWER_PANDAS_FEMALE_1_NAME"] = "イエン",
["SOLDIER_TOWER_PANDAS_FEMALE_2_NAME"] = "チンジャオ",
["SOLDIER_TOWER_PANDAS_FEMALE_3_NAME"] = "フイ",
["SOLDIER_TOWER_PANDAS_FEMALE_4_NAME"] = "アイリン",
["SOLDIER_TOWER_PANDAS_MALE_1_NAME"] = "ツー",
["SOLDIER_TOWER_PANDAS_MALE_2_NAME"] = "チエン",
["SOLDIER_TOWER_PANDAS_MALE_3_NAME"] = "シュエチン",
["SOLDIER_TOWER_PANDAS_MALE_4_NAME"] = "ナイアン",
["SOLDIER_TOWER_PANDAS_MALE_5_NAME"] = "シュン",
["SOLDIER_TOWER_PANDAS_MALE_6_NAME"] = "シンジエン",
["SOLDIER_TOWER_PANDAS_MALE_7_NAME"] = "ウェイ",
["SOLDIER_TOWER_PANDAS_MALE_8_NAME"] = "チェン",
["SOLDIER_TOWER_ROCKET_GUNNERS_10_NAME"] = "フォーティス",
["SOLDIER_TOWER_ROCKET_GUNNERS_1_NAME"] = "アクセル",
["SOLDIER_TOWER_ROCKET_GUNNERS_2_NAME"] = "ローズ",
["SOLDIER_TOWER_ROCKET_GUNNERS_3_NAME"] = "スラッシュ",
["SOLDIER_TOWER_ROCKET_GUNNERS_4_NAME"] = "ハドソン",
["SOLDIER_TOWER_ROCKET_GUNNERS_5_NAME"] = "イジー",
["SOLDIER_TOWER_ROCKET_GUNNERS_6_NAME"] = "ダフ",
["SOLDIER_TOWER_ROCKET_GUNNERS_7_NAME"] = "アドラー",
["SOLDIER_TOWER_ROCKET_GUNNERS_8_NAME"] = "ディジー",
["SOLDIER_TOWER_ROCKET_GUNNERS_9_NAME"] = "フェレール",
["SOLDIER_ZHU_APPRENTICE_NAME"] = "Zhu Bajie",
["SPECIAL_ARBOREAN_BARRACK_DESCRIPTION"] = "彼らの道にいる敵と戦う3人のアルボリアン兵士を召喚する。",
["SPECIAL_ARBOREAN_BARRACK_NAME"] = "アルボリアンの市民",
["SPECIAL_ARBOREAN_HONEY_DESCRIPTION"] = "養蜂家が彼の地位を取り、その蜂たちに粘着性の蜂蜜で敵を遅くし、ダメージを与えるよう命じます！",
["SPECIAL_ARBOREAN_HONEY_NAME"] = "アルボリアンの養蜂家",
["SPECIAL_ARBOREAN_OLDTREE_DESCRIPTION"] = "不機嫌なやつが巨大な丸太を解き放ち、その道のりで敵を押しつぶす。",
["SPECIAL_ARBOREAN_OLDTREE_NAME"] = "古い木",
["SPECIAL_ARBOREAN_SENTINELS_SPEARMEN_DESCRIPTION"] = "森の敏しょうな守り手たち。",
["SPECIAL_ARBOREAN_SENTINELS_SPEARMEN_NAME"] = "アーボリアン・ソーンスピア",
["SPECIAL_PRIESTS_SOLDIERS_DESCRIPTION"] = "贖われた狂信者たちは戦場にその魔術をもたらし、死ぬと異形へと変貌する。",
["SPECIAL_PRIESTS_SOLDIERS_NAME"] = "盲目の狂信者",
["SPECIAL_REPAIR_HOLDER_DRAGON_DESCRIPTION"] = "炎を消してタワーを即座に解放しろ。",
["SPECIAL_REPAIR_HOLDER_DRAGON_NAME"] = "炎に包まれている",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_EARTH_DESCRIPTION"] = "タワーのユニットの体力を増加。\n最大3体のストーンウォリアーを出現させる。",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_EARTH_NAME"] = "Elemental Holder: Earth",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_FIRE_DESCRIPTION"] = "建設されたタワーのダメージが増加する。\n時折、敵を即座に倒す。",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_FIRE_NAME"] = "Elemental Holder: Fire",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_METAL_DESCRIPTION"] = "建設コストを削減。\n敵からゴールドを生成する。",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_METAL_NAME"] = "Elemental Holder: Metal",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WATER_DESCRIPTION"] = "近くの味方ユニットを継続的に回復する。\n敵を道の後方へテレポートさせる。",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WATER_NAME"] = "Elemental Holder: Water",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WOOD_DESCRIPTION"] = "建設されたタワーの射程が延びる。\n時折、敵を遅くする根が短時間出現する。",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WOOD_NAME"] = "Elemental Holder: Wood",
["SPECIAL_REPAIR_HOLDER_SEA_OF_TREES_DESCRIPTION"] = "がれきを片付けて、この戦略的な位置を有効にしてください。 ",
["SPECIAL_REPAIR_HOLDER_SEA_OF_TREES_NAME"] = "瓦礫 ",
["SPECIAL_REPAIR_HOLDER_SPIDERS_DESCRIPTION"] = "ホルダーをクモの巣から解放し、この戦略拠点を利用可能にしよう。",
["SPECIAL_REPAIR_HOLDER_SPIDERS_NAME"] = "絡まったホルダー",
["SPECIAL_REPAIR_OVERSEER_DESCRIPTION"] = "触手を退けて、この戦略的な位置を解除してください。",
["SPECIAL_REPAIR_OVERSEER_NAME"] = "触手",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_1_DESCRIPTION"] = "エルフの傭兵を雇い、戦いを支援させる。10秒ごとに再出現します。",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_1_NAME"] = "エルフの傭兵 I",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_2_DESCRIPTION"] = "最大2体のエルフの傭兵を雇い、戦いを支援させる。10秒ごとに再出現します。",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_2_NAME"] = "エルフの傭兵 II",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_3_DESCRIPTION"] = "最大3体のエルフの傭兵を雇い、戦いを支援させる。10秒ごとに再出現します。",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_3_NAME"] = "エルフの傭兵 III",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_1"] = "ミドリアスの幻想を破壊し、数秒間新たに作り出すことを防ぐ魔法の雷を放つ。",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_2"] = "道を歩いて敵と戦う2体のデーモンガードを召喚します。",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_3"] = "デナスを罠にかけ、動きと攻撃を阻止します。",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_1"] = "ソウルインパクト",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_2"] = "インフェルナルスポーン",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_3"] = "マジックシャックル",
["START"] = "スタート",
["START BATTLE!"] = "バトルスタート！",
["START HERE!"] = "ここでスタート！",
["STRATEGY BASICS!"] = "戦略の基礎！",
["Select"] = "選択",
["Select and train abilities"] = "アビリティを選んで訓練",
["Select by clicking on the portrait or hero unit. Hotkey: space bar"] = "ポートレートまたは英雄ユニットをクリックして選択。ホットキー：スペースキー",
["Select hero"] = "英雄を選択",
["Selected"] = "選択済み",
["Sell Tower"] = "タワーを売却",
["Sell this tower and get a %s GP refund."] = "このタワーを売却して%s GPの払い戻しを受け取ろう。",
["Short"] = "ショート",
["Shows level, health and experience."] = "レベル、体力、経験値を表示。",
["Skills"] = "スキル",
["Skip this!"] = "これをスキップ！",
["Slow"] = "遅い",
["Special abilities"] = "スペシャルアビリティ",
["Support your soldiers with ranged towers!"] = "遠距離攻撃可能なタワーで兵士をサポートしよう！",
["Survival mode!"] = "サバイバルモード！",
["TAP_TO_START"] = "タップして開始",
["TAUNT_BOSS_PIG_FROM_POOL_0001"] = "あなたを悲鳴させてやる！",
["TAUNT_BOSS_PIG_FROM_POOL_0002"] = "もう一度「ベーコン」と言ってみなさい。二重に挑戦だ！",
["TAUNT_BOSS_PIG_FROM_POOL_0003"] = "人間がまたメニューに戻ってきたぞ、みんな！",
["TAUNT_BOSS_PIG_FROM_POOL_0004"] = "急いで！お腹が空いた。",
["TAUNT_BOSS_PIG_FROM_POOL_0005"] = "あなたが死ぬのを見るのを楽しむことにする。",
["TAUNT_BOSS_PIG_FROM_POOL_0006"] = "知ってる、僕が最悪だ。",
["TAUNT_LVL30_BOSS_ABILITY_01"] = "喰らえ、我が子らよ！",
["TAUNT_LVL30_BOSS_ABILITY_02"] = "しがみついていろ！ムワハハハ！",
["TAUNT_LVL30_BOSS_ABILITY_03"] = "教団のために！",
["TAUNT_LVL30_BOSS_ABILITY_04"] = "皆に美味なご馳走を！",
["TAUNT_LVL30_BOSS_ABILITY_05"] = "私のクモの勘が騒いでいる！",
["TAUNT_LVL30_BOSS_ABILITY_06"] = "ひざまずけ、同盟よ！",
["TAUNT_LVL30_BOSS_ABILITY_07"] = "ここは私の縄張り、私の掟だ！",
["TAUNT_LVL30_BOSS_ABILITY_08"] = "誰も私の巣から逃れられん！",
["TAUNT_LVL30_BOSS_ABILITY_09"] = "死ね、人型の疫病め！",
["TAUNT_LVL30_BOSS_ABILITY_10"] = "貴様の糸を引いてやる！",
["TAUNT_LVL30_BOSS_ABILITY_11"] = "皆殺しにしろ！",
["TAUNT_LVL30_BOSS_INTRO_01"] = "ついに！我が姉妹を殺めた者どもが姿を現したか…",
["TAUNT_LVL30_BOSS_INTRO_02"] = "サレルガズとマクタンス、私の兄弟姉妹の思い出に…",
["TAUNT_LVL30_BOSS_INTRO_03"] = "お前たちは皆、ひざまずき、私を崇めることになるのだ！",
["TAUNT_LVL30_BOSS_PREFIGHT_01"] = "もううんざりだ…",
["TAUNT_LVL30_BOSS_PREFIGHT_02"] = "貴様らなど取るに足らぬ虫けらだ…",
["TAUNT_LVL30_BOSS_PREFIGHT_03"] = "女王の巣に捕らわれたな！",
["TAUNT_LVL32_BOSS_ABILITY_01"] = "愚か者ども！俺は神の炎、三昧火を操る！",
["TAUNT_LVL32_BOSS_ABILITY_02"] = "灼熱の炎が天からほとばしる！",
["TAUNT_LVL32_BOSS_ABILITY_03"] = "純粋な本物の炎を恐れろ！",
["TAUNT_LVL32_BOSS_ABILITY_04"] = "肉も魂も同じように焼かれる！",
["TAUNT_LVL32_BOSS_FIGHT_01"] = "俺の中の炎は決して消えない！",
["TAUNT_LVL32_BOSS_FINAL_01"] = "俺の炎は消えかけているが…\nまだドラゴンがいる…",
["TAUNT_LVL32_BOSS_INTRO_01"] = "軍隊があるのか？",
["TAUNT_LVL32_BOSS_INTRO_02"] = "俺にはドラゴンがいる！はははは！",
["TAUNT_LVL32_BOSS_PREFIGHT_01"] = "もういい！ここからが俺の勝ちだ！",
["TAUNT_LVL32_BOSS_PREFIGHT_02"] = "俺の真の姿を拝むがいい！",
["TAUNT_LVL34_BOSS_BOSSFIGHT_01"] = "よし、それじゃあ必要なものはわかってる。もっと僕を。僕、僕、僕……",
["TAUNT_LVL34_BOSS_DEATH_01"] = "こんなことが…関係ない、私の夫があなたたちに報いを与えるでしょう…",
["TAUNT_LVL34_BOSS_INTRO_01"] = "このサルどもめ！よくも息子にあんなことをしておいてここに来られたな？",
["TAUNT_LVL34_BOSS_WAVES_01"] = "我が力を味わうがいい、無礼者ども！",
["TAUNT_LVL34_BOSS_WAVES_02"] = "終わりが近い！",
["TAUNT_LVL35_BOSS_DEATH_01"] = "そしてこうして私の支配は終わる…血の中で。",
["TAUNT_LVL35_BOSS_DESTROY_HOUSE_01"] = "うーん、高くついたな。火力を見せる時だ！",
["TAUNT_LVL35_BOSS_DESTROY_HOUSE_02"] = "ああ、忍耐の音だ。お水の時間ですよ、奥様！",
["TAUNT_LVL35_BOSS_DESTROY_HOUSE_03"] = "グルル！俺の下品な力の誇示に屈服しろ！",
["TAUNT_LVL35_BOSS_INTRO_01"] = "取るに足らぬ人間どもよ、生きているうちに喜ぶがよい。",
["TAUNT_LVL35_BOSS_INTRO_02"] = "新たな秩序の時だ。",
["TAUNT_LVL35_BOSS_INTRO_03"] = "アーッ、復讐を叫んでやる！",
["TAUNT_LVL35_BOSS_PREFIGHT_01"] = "よし、それじゃあ殺しが俺の商売ってことを見せてやる！",
["TAUNT_STAGE02_RAELYN_0001"] = "やろう。",
["TAUNT_STAGE02_VEZNAN_0001"] = "彼らが来た。私はあなたの取るに足らない部隊を助けるでしょう...",
["TAUNT_STAGE02_VEZNAN_0002"] = "...つまり、私の一番の兵士がやってくれるだろう。ハ！",
["TAUNT_STAGE02_VEZNAN_0003"] = "ハハハ！",
["TAUNT_STAGE06_BOSS_PIG_PREBATTLE_0001"] = "いいよ... 自分でやる。",
["TAUNT_STAGE06_BOSS_PIG_RESPONSE_0001"] = "落ち着いて、全てがコントロール下にあります。",
["TAUNT_STAGE06_CULTIST_GREETING_0001"] = "そこでとても快適に過ごしているようですね...",
["TAUNT_STAGE06_CULTIST_GREETING_0002"] = "...約束を守った方がいいですよ。",
["TAUNT_STAGE11_CULTIST_LEADER_0001"] = "ここまで来たのは良かった...",
["TAUNT_STAGE11_CULTIST_LEADER_0002"] = "...しかし、避けられないものは止められない！",
["TAUNT_STAGE11_CULTIST_LEADER_0003"] = "もう十分だ!!!",
["TAUNT_STAGE11_CULTIST_LEADER_0004"] = "私たちの前で跪く時間だ！",
["TAUNT_STAGE11_CULTIST_LEADER_0005"] = "グルル... これが終わりじゃない！",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0001"] = "新しい世界が私たちを待っている。",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0002"] = "私の力を侮っている。",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0003"] = "オクルス・ポクルス！",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0004"] = "避けられないものの音を聞け！",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0005"] = "私は悪ですか？はい、そうです！",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0006"] = "全てを見る者が私たちを祝福する！",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0001"] = "お前の終わりが近い！",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0002"] = "私の目が開かれた！",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0003"] = "私の虚無の友達に「こんにちは」と言って！",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0004"] = "オクルス・ポクルス！",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0005"] = "哀れな弱いクズ！",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0006"] = "全てを見る者が私たちを祝福する！",
["TAUNT_STAGE11_VEZNAN_0001"] = "デナス、友よ。久しぶり！",
["TAUNT_STAGE15_CULTIST_0001"] = "近い... 目覚めるのを感じる！",
["TAUNT_STAGE15_CULTIST_0002"] = "新たな時代が近づいている。あなたの努力は無駄になる！",
["TAUNT_STAGE15_CULTIST_0003"] = "グルル…あなたの同盟は強力だ。",
["TAUNT_STAGE15_CULTIST_0004"] = "しかし、真の力とは何かを見せてやろう！",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0001"] = " 馬鹿たち！死にに来たのか。 ",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0002"] = "その視線の前で降参しなさい！",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0003"] = "真の信者になるでしょう。",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0004"] = "同盟かどうか、あなたは運命づけられている！",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0005"] = "虚無には生命はない。死だけがある",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0006"] = "私の時間を無駄にするのをやめて！",
["TAUNT_STAGE15_DENAS_0001"] = "決着をつけるべき点がある。この戦いは見逃さない!",
["TAUNT_STAGE16_DENAS_AFTER_BOSSFIGHT_0001"] = "来るとは思わなかったでしょう？",
["TAUNT_STAGE18_ERIDAN_FIGHT_0001"] = "今夜、血が流された。",
["TAUNT_STAGE18_ERIDAN_FIGHT_0002"] = "エリニーを信頼している。",
["TAUNT_STAGE18_ERIDAN_FIGHT_0003"] = "グニルルはエディノリを話す！",
["TAUNT_STAGE18_ERIDAN_FIGHT_0004"] = "どうしても外せないようだ。",
["TAUNT_STAGE18_ERIDAN_FIGHT_0005"] = "アレディエルは勝利する！",
["TAUNT_STAGE18_ERIDAN_FIGHT_0006"] = "彼らはただのレンジャーではない！",
["TAUNT_STAGE18_ERIDAN_FIGHT_0007"] = "スコアをつけているか？",
["TAUNT_STAGE18_ERIDAN_FIGHT_0008"] = "来させろ！",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0001"] = "私の弓を託す！",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0002"] = "迅速に行動せよ！",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0003"] = "配置につけ！",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0004"] = "目を開けておけ！",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0001"] = "これでウォームアップは十分だ！",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0002"] = "あなたは厄介な存在であることを証明しましたね...",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0003"] = "本当の戦いを始めましょう！",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0001"] = "すべての魂を屈服させる！",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0002"] = "エルフたちは再び立ち上がるだろう。",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0003"] = "私は死者さえも持ち上げる！",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0004"] = "古代の邪悪な力によって！",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0005"] = "墓場の私の子供たちを恐れよ！",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0006"] = "私の民に栄光を取り戻します。",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0001"] = "ああ、強大な同盟が訪れましたね。",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0002"] = "ベールを取り払う時が来ました！",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0003"] = "死の力を見せてあげましょう！",
["TAUNT_STAGE22_BOSS_CROCS_BEFORE_BOSSFIGHT_0001"] = "ようやく食べ尽くす自由が...",
["TAUNT_STAGE22_BOSS_CROCS_BEFORE_BOSSFIGHT_0002"] = "すべて！！！！！",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0001"] = "これ以上の干渉は許さん！",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0002"] = "グリムビアードが礼儀を教えてやる。",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0003"] = "全員乗船、アハハハ！",
["TAUNT_STAGE25_BOSS_MACHINIST_END_0001"] = "お前たち生意気な馬鹿ども！",
["TAUNT_STAGE25_BOSS_MACHINIST_END_0002"] = "私を捕まえることなど永遠にできん、ハハハ！",
["TAUNT_STAGE26_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0001"] = "いや！まだ終わってない…",
["TAUNT_STAGE26_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0002"] = "グレート・スコット！！！",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0001"] = "この軍隊に勝てるわけがない！",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0002"] = "グリムビアードは危険ではない。",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0003"] = "グリムビアードが危険そのものだ！",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0004"] = "狂人がこんなことをできるか？",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0005"] = "世界はグリムビアードにひれ伏す！",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0001"] = "グリムビアードの我慢も限界だ。",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0002"] = "本当に凄いものを見せてやる！",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0003"] = "グリムビアードは自分自身だけで十分だ！",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0004"] = "早くしないか？！",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0001"] = "お前たちとその忌々しいお節介な同盟め！",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0002"] = "もう手を出さないよう教えてやる…",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0003"] = "…「本物のドワーフ」に手を出すな！",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0001"] = "好きなだけレプリカを踏みつけるがいい、私はさらに作るだけだ。",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0002"] = "物事を完璧にしたいなら、自分でやるしかない。",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0003"] = "おお、グリムビアード、お前は天才だ！",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0004"] = "これでは逃げられんぞ！",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0005"] = "本気でやっているのか？",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0006"] = "私の創造物を超えられると思っているのか？",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0001"] = "やっぱり私に夢中だったようだな…",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0002"] = "…そして今度は「究極のドワーフ」に挑戦したいと？",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0003"] = "どうぞ試してみるがいい。",
["TAUNT_TUTORIAL_ARBOREAN_ALL_0001"] = "頑張って！ 君を信じてるよ。",
["TAUNT_TUTORIAL_ARBOREAN_BARRACK_0001"] = "ここで、兵舎を建ててください！",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_1_NAME"] = "リムブリアム",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_2_NAME"] = "触手のヘンリー",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_3_NAME"] = "触手のジェフリー",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_4_NAME"] = "テンタクラス",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_5_NAME"] = "テドタクル",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_6_NAME"] = "ホリム",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_7_NAME"] = "テントド",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_8_NAME"] = "リムドリック",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_9_NAME"] = "ハリム",
["TERMS_OF_SERVICE_LINK"] = "利用規約",
["TIP!"] = "ヒント！",
["TIP_1"] = "敵をタップすると、対処法や弱点などの詳しい情報が表示されるぞ。",
["TIP_10"] = "飛行系の敵はブロックできない、そして砲撃タワーの多くは飛行系を無視する。",
["TIP_11"] = "バトルで使用する強力アイテムの数々はショップで購入できる。アイテムを活用すれば、一気に形勢逆転も可能だ！",
["TIP_2"] = "魔法ダメージは重装甲の敵に特に効果的だ。",
["TIP_3"] = "兵舎をアップグレードすると、元気な兵士の一団がすぐに出てきて、疲れ果てた同胞たちと交代する。",
["TIP_4"] = "敵部隊を早く呼び込むと、ボーナスゴールドに加えて呪文クールダウン時間が少し短縮される。",
["TIP_5"] = "兵舎の集結地を調整することで、戦略的に有利となる地点へと兵団を移動させることができるぞ。",
["TIP_6"] = "敵部隊フラッグからの情報を活用しよう。一度だけタップすると次に来る敵部隊の情報が表示されるぞ。万全の態勢で迎え撃て！",
["TIP_7"] = "飛行系の敵には範囲攻撃が有効だ。厳密に狙わなくともダメージを与えることができるぞ。",
["TIP_8"] = "効果的でない場所に配置されているタワーがある場合、売却して資金を確保することが難局を打破する手段になりうることもある。",
["TIP_9"] = "タワーのアップグレードは、同じタワーをもうひとつ建造するよりも効果が高い。",
["TIP_ALERT_ICON"] = "ヒント",
["TIP_TITLE"] = "ヒント：",
["TOWER_ARBOREAN_EMISSARY_1_DESCRIPTION"] = "アルボリアンは、強力な自然魔法を使って敵をより脆弱にする",
["TOWER_ARBOREAN_EMISSARY_1_NAME"] = "アルボレアの使者I",
["TOWER_ARBOREAN_EMISSARY_2_DESCRIPTION"] = "アルボリアンは強力な自然魔法を使って敵をより脆弱にする。 ",
["TOWER_ARBOREAN_EMISSARY_2_NAME"] = "アルボレアの使者II",
["TOWER_ARBOREAN_EMISSARY_3_DESCRIPTION"] = "アルボリアンズは、強力な自然魔法を使って敵をより弱くします。 ",
["TOWER_ARBOREAN_EMISSARY_3_NAME"] = "アルボレアの使者III",
["TOWER_ARBOREAN_EMISSARY_4_DESCRIPTION"] = "アルボレアンは、強力な自然魔法を使って敵をより脆弱にする",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_1_DESCRIPTION"] = "味方を範囲内で治癒するウィスプを召喚し、%$towers.arborean_emissary.gift_of_nature.s_heal[1]%$の健康を秒間%$towers.arborean_emissary.gift_of_nature.duration[1]%$秒間回復させる",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_1_NAME"] = "自然の贈り物",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_2_DESCRIPTION"] = "仲間をエリア内で%$towers.arborean_emissary.gift_of_nature.duration[2]%$秒間、%$towers.arborean_emissary.gift_of_nature.s_heal[2]%$の健康を回復するウィスプを召喚します。",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_2_NAME"] = "自然の贈り物",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_3_DESCRIPTION"] = "仲間をエリア内で%$towers.arborean_emissary.gift_of_nature.duration[3]%$秒間、%$towers.arborean_emissary.gift_of_nature.s_heal[3]%$の健康を回復するウィスプを召喚します。",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_3_NAME"] = "自然の贈り物",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_NAME"] = "自然の贈り物",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_NOTE"] = "緑に干渉するな。",
["TOWER_ARBOREAN_EMISSARY_4_NAME"] = "アーボレアンの使節 IV",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_1_DESCRIPTION"] = "経路に沿って %$towers.arborean_emissary.wave_of_roots.max_targets[1]%$ の根を成長させ、%$towers.arborean_emissary.wave_of_roots.s_damage[1]%$ の真のダメージを与え、敵を %$towers.arborean_emissary.wave_of_roots.mod_duration[1]%$ 秒間スタンさせます。",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_1_NAME"] = "ブランブルグラスプ",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_2_DESCRIPTION"] = "道に沿って %$towers.arborean_emissary.wave_of_roots.max_targets[2]%$ の根を生やし、%$towers.arborean_emissary.wave_of_roots.s_damage[2]%$ の真実のダメージを与え、敵を %$towers.arborean_emissary.wave_of_roots.mod_duration[2]%$ 秒間スタンさせます。",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_2_NAME"] = "ブランブルグラスプ",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_3_DESCRIPTION"] = "道に沿って %$towers.arborean_emissary.wave_of_roots.max_targets[3]%$ の根を生やし、%$towers.arborean_emissary.wave_of_roots.s_damage[3]%$ の真実のダメージを与え、敵を %$towers.arborean_emissary.wave_of_roots.mod_duration[3]%$ 秒間スタンさせます。",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_3_NAME"] = "ブランブルグラスプ",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_NAME"] = "ブランブルグラスプ",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_NOTE"] = "足元に気をつけてください。",
["TOWER_ARBOREAN_EMISSARY_DESC"] = "挑衅されたとき、アーボレアンズは敵をマークし、弱らせるために彼らの魔法を使うことで知られています。",
["TOWER_ARBOREAN_EMISSARY_NAME"] = "アルボレアの使者",
["TOWER_ARBOREAN_SENTINELS_DESCRIPTION"] = "森の敏しょうな守り手たち。",
["TOWER_ARBOREAN_SENTINELS_NAME"] = "アーボリアン・ソーンスピア",
["TOWER_ARCANE_WIZARD_1_DESCRIPTION"] = "魔法の芸術に精通しているこれらの魔法使いは、常に戦いに備えています。 ",
["TOWER_ARCANE_WIZARD_1_NAME"] = "アーケインウィザード I ",
["TOWER_ARCANE_WIZARD_2_DESCRIPTION"] = "魔法の芸術に精通しているこれらの魔法使いは、常に戦いに備えています。 ",
["TOWER_ARCANE_WIZARD_2_NAME"] = "アーケインウィザード II ",
["TOWER_ARCANE_WIZARD_3_DESCRIPTION"] = "魔法の芸術に精通しているこれらの魔法使いは、常に戦いに備えています。 ",
["TOWER_ARCANE_WIZARD_3_NAME"] = "アーケインウィザード III ",
["TOWER_ARCANE_WIZARD_4_DESCRIPTION"] = "魔法の芸術に精通しているこれらの魔法使いは、常に戦いに備えています。 ",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_1_DESCRIPTION"] = "ターゲットを即座に倒す光線を放つ。ボスとミニボスには代わりに%$towers.arcane_wizard.disintegrate.boss_damage[1]%$の魔法ダメージを与える。",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_1_NAME"] = "分解する ",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_2_DESCRIPTION"] = "クールダウンを%$towers.arcane_wizard.disintegrate.cooldown[2]%$秒に短縮します。ボスおよびミニボスへのダメージは現在%$towers.arcane_wizard.disintegrate.boss_damage[2]%$です。",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_2_NAME"] = "分解する ",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_3_DESCRIPTION"] = "%$towers.arcane_wizard.disintegrate.cooldown[3]%$秒にクールダウンを短縮します。ボスとミニボスへのダメージが%$towers.arcane_wizard.disintegrate.boss_damage[3]%$になります。",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_3_NAME"] = "分解する ",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_NAME"] = "分解する ",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_NOTE"] = "塵は塵へ。 ",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_1_DESCRIPTION"] = "周囲のタワーのダメージを%$towers.arcane_wizard.empowerment.s_damage_factor[1]%$%増加させる。",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_1_NAME"] = "強化",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_2_DESCRIPTION"] = "近くの塔のダメージを%$towers.arcane_wizard.empowerment.s_damage_factor[2]%$%増加させます。",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_2_NAME"] = "強化",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_3_DESCRIPTION"] = "近くの塔のダメージを%$towers.arcane_wizard.empowerment.s_damage_factor[3]%$%増加させます。",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_3_NAME"] = "強化",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_NAME"] = "強化",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_NOTE"] = "無限の力。",
["TOWER_ARCANE_WIZARD_4_NAME"] = "アーケインウィザード IV ",
["TOWER_ARCANE_WIZARD_DESC"] = "純粋な魔法を引き出し、リニアンの魔法使いは敵を完全に破壊するのに十分な力を持っています。 ",
["TOWER_ARCANE_WIZARD_NAME"] = "アーケインウィザード ",
["TOWER_BALLISTA_1_DESCRIPTION"] = "グリーンスキンの戦争に素晴らしい追加で、まだ壊れていないのは奇跡です。",
["TOWER_BALLISTA_1_NAME"] = "弩砲の前哨基地 I",
["TOWER_BALLISTA_2_DESCRIPTION"] = "グリーンスキンの戦争に素晴らしい追加で、まだ壊れていないのは奇跡です。",
["TOWER_BALLISTA_2_NAME"] = "弩砲の前哨基地 II",
["TOWER_BALLISTA_3_DESCRIPTION"] = "グリーンスキンの戦争に素晴らしい追加で、まだ壊れていないのは奇跡です。",
["TOWER_BALLISTA_3_NAME"] = "弩砲の前哨基地 III",
["TOWER_BALLISTA_4_DESCRIPTION"] = "グリーンスキンの戦争に素晴らしい追加で、まだ壊れていないのは奇跡です。",
["TOWER_BALLISTA_4_NAME"] = "弩砲の前哨基地 IV",
["TOWER_BALLISTA_4_SKILL_BOMB_1_DESCRIPTION"] = "長距離からスクラップで作った爆弾を発射し、%$towers.ballista.skill_bomb.damage_min[1]%$-%$towers.ballista.skill_bomb.damage_max[1]%$の物理ダメージを与える。敵を%$towers.ballista.skill_bomb.duration[1]%$秒間遅くする。",
["TOWER_BALLISTA_4_SKILL_BOMB_1_NAME"] = "スクラップボム",
["TOWER_BALLISTA_4_SKILL_BOMB_2_DESCRIPTION"] = "スクラップボムは%$towers.ballista.skill_bomb.damage_min[2]%$-%$towers.ballista.skill_bomb.damage_max[2]%$の物理ダメージを与えます。敵を%$towers.ballista.skill_bomb.duration[1]%$秒間遅くします。",
["TOWER_BALLISTA_4_SKILL_BOMB_2_NAME"] = "スクラップボム",
["TOWER_BALLISTA_4_SKILL_BOMB_3_DESCRIPTION"] = "スクラップボムは%$towers.ballista.skill_bomb.damage_min[3]%$-%$towers.ballista.skill_bomb.damage_max[3]%$の物理ダメージを与えます。敵を%$towers.ballista.skill_bomb.duration[1]%$秒間遅くします。",
["TOWER_BALLISTA_4_SKILL_BOMB_3_NAME"] = "スクラップボム",
["TOWER_BALLISTA_4_SKILL_BOMB_NOTE"] = "フォア！",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_1_DESCRIPTION"] = "塔の最後の一撃は、%$towers.ballista.skill_final_shot.s_damage_factor[1]%$%の追加ダメージを与え、対象を%$towers.ballista.skill_final_shot.s_stun%$秒間スタンさせます。",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_1_NAME"] = "最後の釘",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_2_DESCRIPTION"] = "最後の一撃は、%$towers.ballista.skill_final_shot.s_damage_factor[2]%$%の追加ダメージを与え、対象を%$towers.ballista.skill_final_shot.s_stun%$秒間スタンさせます。",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_2_NAME"] = "最後の釘",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_3_DESCRIPTION"] = "最後の一撃は、%$towers.ballista.skill_final_shot.s_damage_factor[3]%$%の追加ダメージを与え、対象を%$towers.ballista.skill_final_shot.s_stun%$秒間スタンさせます。",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_3_NAME"] = "最後の釘",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_NOTE"] = "それは百万分の一だった、キッド！",
["TOWER_BALLISTA_DESC"] = "戦争に熱中しているゴブリンたちは、もう弓を使うことがないように、余計な努力をしました。",
["TOWER_BALLISTA_NAME"] = "弩砲の前哨基地",
["TOWER_BARREL_1_DESCRIPTION"] = "北方人のポーションの箱は、敵の大群に対する強力な武器です。",
["TOWER_BARREL_1_NAME"] = "バトルブリューマスターズI",
["TOWER_BARREL_2_DESCRIPTION"] = "北方人のポーションの箱は、敵の大群に対する強力な武器です。",
["TOWER_BARREL_2_NAME"] = "バトルブリューマスターズII",
["TOWER_BARREL_3_DESCRIPTION"] = "北方人のポーションの箱は、敵の大群に対する強力な武器です。",
["TOWER_BARREL_3_NAME"] = "バトルブリューマスターズIII",
["TOWER_BARREL_4_DESCRIPTION"] = "北方人のポーションの箱は、敵の大群に対する強力な武器です。",
["TOWER_BARREL_4_NAME"] = "バトルブリューマスターズIV",
["TOWER_BARREL_4_SKILL_BARREL_1_DESCRIPTION"] = "毒性のバレルを投げて、%$towers.barrel.skill_barrel.explosion.damage_min[1]%$から%$towers.barrel.skill_barrel.explosion.damage_max[1]%$の物理ダメージを与えます。バレルは毒を残し、%$towers.barrel.skill_barrel.poison.s_damage%$の真ダメージを毎秒%$towers.barrel.skill_barrel.poison.duration%$秒間与えます。 ",
["TOWER_BARREL_4_SKILL_BARREL_1_NAME"] = "バッドバッチ",
["TOWER_BARREL_4_SKILL_BARREL_2_DESCRIPTION"] = "毒バレルの爆発は%$towers.barrel.skill_barrel.explosion.damage_min[2]%$から%$towers.barrel.skill_barrel.explosion.damage_max[2]%$の物理ダメージを与えます。バレルの毒は%$towers.barrel.skill_barrel.poison.duration%$秒間にわたり、毎秒%$towers.barrel.skill_barrel.poison.s_damage%$の真実のダメージを与えます。",
["TOWER_BARREL_4_SKILL_BARREL_2_NAME"] = "バッドバッチ",
["TOWER_BARREL_4_SKILL_BARREL_3_DESCRIPTION"] = "毒バレルの爆発は%$towers.barrel.skill_barrel.explosion.damage_min[3]%$から%$towers.barrel.skill_barrel.explosion.damage_max[3]%$の物理ダメージを与えます。バレルの毒は、%$towers.barrel.skill_barrel.poison.duration%$秒にわたり毎秒%$towers.barrel.skill_barrel.poison.s_damage%$の真実のダメージを与えます。",
["TOWER_BARREL_4_SKILL_BARREL_3_NAME"] = "バッドバッチ",
["TOWER_BARREL_4_SKILL_BARREL_NOTE"] = "勇者のみぞ入る！",
["TOWER_BARREL_4_SKILL_WARRIOR_1_DESCRIPTION"] = "召喚一個強化戰士在路徑中戰鬥。它有 %$towers.barrel.skill_warrior.entity.hp_max[1]%$ 的健康值並且造成 %$towers.barrel.skill_warrior.entity.damage_min[1]%$-%$towers.barrel.skill_warrior.entity.damage_max[1]%$ 的物理傷害。",
["TOWER_BARREL_4_SKILL_WARRIOR_1_NAME"] = "力のエリクサー",
["TOWER_BARREL_4_SKILL_WARRIOR_2_DESCRIPTION"] = "戦士は%$towers.barrel.skill_warrior.entity.hp_max[2]%$の体力を持ち、%$towers.barrel.skill_warrior.entity.damage_min[2]%$-%$towers.barrel.skill_warrior.entity.damage_max[2]%$の物理ダメージを与えます。",
["TOWER_BARREL_4_SKILL_WARRIOR_2_NAME"] = "力のエリクサー",
["TOWER_BARREL_4_SKILL_WARRIOR_3_DESCRIPTION"] = "戦士は%$towers.barrel.skill_warrior.entity.hp_max[3]%$の体力を持ち、%$towers.barrel.skill_warrior.entity.damage_min[3]%$-%$towers.barrel.skill_warrior.entity.damage_max[3]%$の物理ダメージを与えます。",
["TOWER_BARREL_4_SKILL_WARRIOR_3_NAME"] = "力のエリクサー",
["TOWER_BARREL_4_SKILL_WARRIOR_NOTE"] = "勝利の味だ！",
["TOWER_BARREL_DESC"] = "北国の人々は薬草作りの技術に長けており、戦いで敵と闘うために自分たちの飲み物を使います。",
["TOWER_BARREL_NAME"] = "バトルブリューマスターズ",
["TOWER_BARREL_WARRIOR_NAME"] = "ハルフダン・ザ・ブラント",
["TOWER_BROKEN_DESCRIPTION"] = "この塔は損傷しています。ゴールドを使って修理してください。",
["TOWER_BROKEN_NAME"] = "損傷した塔",
["TOWER_CROCS_EATEN_DESCRIPTION"] = "魔法で塔を元の形に再建します。",
["TOWER_CROCS_EATEN_NAME"] = "タワーの残骸",
["TOWER_DARK_ELF_1_DESCRIPTION"] = "距離や敵の強さに関係なく、彼らの狙いは常に正確です。",
["TOWER_DARK_ELF_1_NAME"] = "黄昏のロングボウ I",
["TOWER_DARK_ELF_2_DESCRIPTION"] = "敵の距離や強さに関係なく、彼らの狙いは常に正確です。",
["TOWER_DARK_ELF_2_NAME"] = "黄昏のロングボウ II",
["TOWER_DARK_ELF_3_DESCRIPTION"] = "敵の距離や強さに関係なく、彼らの狙いは常に正確です。",
["TOWER_DARK_ELF_3_NAME"] = "黄昏のロングボウ III",
["TOWER_DARK_ELF_4_DESCRIPTION"] = "敵の距離や強さに関係なく、彼らの狙いは常に正確です。",
["TOWER_DARK_ELF_4_NAME"] = "黄昏のロングボウ IV",
["TOWER_DARK_ELF_4_SKILL_BUFF_1_DESCRIPTION"] = "敵を倒すたびに、タワーの攻撃力が%$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$増加します。",
["TOWER_DARK_ELF_4_SKILL_BUFF_1_NAME"] = "狩りのスリル",
["TOWER_DARK_ELF_4_SKILL_BUFF_2_DESCRIPTION"] = "敵を倒すたびに、タワーの攻撃力が%$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$増加します。",
["TOWER_DARK_ELF_4_SKILL_BUFF_2_NAME"] = "狩りのスリル",
["TOWER_DARK_ELF_4_SKILL_BUFF_3_DESCRIPTION"] = "敵を倒すたびに、タワーの攻撃力が%$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$増加します。",
["TOWER_DARK_ELF_4_SKILL_BUFF_3_NAME"] = "狩りのスリル",
["TOWER_DARK_ELF_4_SKILL_BUFF_NOTE"] = "タリー・ホー！",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_1_DESCRIPTION"] = "2体のトワイライトハラスターを召喚します。彼らは%$towers.dark_elf.soldier.hp[1]%$の体力を持ち、%$towers.dark_elf.soldier.basic_attack.damage_min[1]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[1]%$の物理ダメージを与えます。",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_1_NAME"] = "サポートブレード",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_2_DESCRIPTION"] = "トワイライトハラスターの体力が%$towers.dark_elf.soldier.hp[2]%$に増加し、%$towers.dark_elf.soldier.basic_attack.damage_min[2]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[2]%$の物理ダメージを与えます。",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_2_NAME"] = "サポートブレード",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_3_DESCRIPTION"] = "トワイライトハラスターの体力が%$towers.dark_elf.soldier.hp[3]%$に増加し、%$towers.dark_elf.soldier.basic_attack.damage_min[3]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[3]%$の物理ダメージを与えます。",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_3_NAME"] = "サポートブレード",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_NOTE"] = "彼らが遊びに降りてくる。",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_DESCRIPTION"] = "タワーのターゲットを出口に最も近い敵に変更します。",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_NAME"] = "敵フォーカス：最前列",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_NOTE"] = "通さないで！",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_DESCRIPTION"] = "タワーのターゲットを最も体力の高い敵に変更します。",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_NAME"] = "敵フォーカス：最大HP",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_NOTE"] = "大物を狙え！",
["TOWER_DARK_ELF_DESC"] = "強力な敵を遠くから狩ることに特化した弓兵たちが、暗黒エネルギーで矢を強化する。",
["TOWER_DARK_ELF_NAME"] = "黄昏のロングボウ",
["TOWER_DEMON_PIT_1_DESCRIPTION"] = "いたずら好きで危険な、これらの悪魔はいつもトラブルを探しています。",
["TOWER_DEMON_PIT_1_NAME"] = "デーモンの穴 I",
["TOWER_DEMON_PIT_2_DESCRIPTION"] = "いたずら好きで危険な、これらの悪魔はいつもトラブルを探しています。",
["TOWER_DEMON_PIT_2_NAME"] = "デーモンの穴 II",
["TOWER_DEMON_PIT_3_DESCRIPTION"] = "いたずら好きで危険な、これらの悪魔はいつもトラブルを探しています。",
["TOWER_DEMON_PIT_3_NAME"] = "デーモンの穴 III",
["TOWER_DEMON_PIT_4_BIG_DEMON_1_DESCRIPTION"] = "巨大なインプを召喚し、%$towers.demon_pit.big_guy.hp_max[1]%$の体力で%$towers.demon_pit.big_guy.melee_attack.damage_min[1]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[1]%$の物理ダメージを与えます。爆発時には%$towers.demon_pit.big_guy.explosion_damage[1]%$のダメージを与えます。",
["TOWER_DEMON_PIT_4_BIG_DEMON_1_NAME"] = "ビッグボス",
["TOWER_DEMON_PIT_4_BIG_DEMON_2_DESCRIPTION"] = "ビッグインプは%$towers.demon_pit.big_guy.hp_max[2]%$の体力を持ち、%$towers.demon_pit.big_guy.melee_attack.damage_min[2]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[2]%$の物理ダメージを与えます。爆発は%$towers.demon_pit.big_guy.explosion_damage[2]%$のダメージを与えます。",
["TOWER_DEMON_PIT_4_BIG_DEMON_2_NAME"] = "ビッグボス",
["TOWER_DEMON_PIT_4_BIG_DEMON_3_DESCRIPTION"] = "ビッグインプは%$towers.demon_pit.big_guy.hp_max[3]%$の体力を持ち、%$towers.demon_pit.big_guy.melee_attack.damage_min[3]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[3]%$の物理ダメージを与えます。爆発は%$towers.demon_pit.big_guy.explosion_damage[3]%$のダメージを与えます。",
["TOWER_DEMON_PIT_4_BIG_DEMON_3_NAME"] = "ビッグボス",
["TOWER_DEMON_PIT_4_BIG_DEMON_NAME"] = "ビッグボス",
["TOWER_DEMON_PIT_4_BIG_DEMON_NOTE"] = "ちょっとリラックスしようとしています。",
["TOWER_DEMON_PIT_4_DESCRIPTION"] = "いたずら好きで危険な、これらの悪魔はいつもトラブルを探しています。",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_1_DESCRIPTION"] = "インプの爆発は、現在 %$towers.demon_pit.master_exploders.s_damage_increase[1]%$% 以上のダメージを与え、敵に燃焼ダメージを与え、%$towers.demon_pit.master_exploders.s_total_burning_damage_min[1]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[1]%$ の真実のダメージを毎秒、%$towers.demon_pit.master_exploders.s_burning_duration[1]%$ 秒間与えます。 ",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_1_NAME"] = "マスター・エクスプローダーズ",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_2_DESCRIPTION"] = "インプの爆発は%$towers.demon_pit.master_exploders.s_damage_increase[2]%$%増加し、燃焼は%$towers.demon_pit.master_exploders.s_total_burning_damage_min[2]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[2]%$の真実のダメージを毎秒%$towers.demon_pit.master_exploders.s_burning_duration[2]%$秒間与えます。",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_2_NAME"] = "マスター・エクスプローダーズ",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_3_DESCRIPTION"] = "インプの爆発は%$towers.demon_pit.master_exploders.s_damage_increase[3]%$%増加し、燃焼は%$towers.demon_pit.master_exploders.s_total_burning_damage_min[3]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[3]%$の真実のダメージを毎秒%$towers.demon_pit.master_exploders.s_burning_duration[3]%$秒間与えます。",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_3_NAME"] = "マスター・エクスプローダーズ",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_NAME"] = "マスター・エクスプローダーズ",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_NOTE"] = "この仕事をするのはバカだけだ。",
["TOWER_DEMON_PIT_4_NAME"] = "デーモンの穴 IV",
["TOWER_DEMON_PIT_DESC"] = "溶岩の深淵から現れるこれらのインプは、敵の道に自らを投げ込むことを躊躇しません。",
["TOWER_DEMON_PIT_NAME"] = "デーモンの穴",
["TOWER_DEMON_PIT_SOLDIER_BIG_GUY_NAME"] = "大きな男",
["TOWER_DEMON_PIT_SOLDIER_NAME"] = "デーモンインプ",
["TOWER_DWARF_1_DESCRIPTION"] = "彼らの導火線と同じくらい短くても、生きてそのラインを突破するものはいない。",
["TOWER_DWARF_1_NAME"] = "カノニア隊 I",
["TOWER_DWARF_2_DESCRIPTION"] = "彼らの導火線と同じくらい短くても、生きてそのラインを突破するものはいない。",
["TOWER_DWARF_2_NAME"] = "カノニア隊 II",
["TOWER_DWARF_3_DESCRIPTION"] = "彼らの導火線と同じくらい短くても、生きてそのラインを突破するものはいない。",
["TOWER_DWARF_3_NAME"] = "カノニア隊 III",
["TOWER_DWARF_4_DESCRIPTION"] = "彼らの導火線と同じくらい短くても、生きてそのラインを突破するものはいない。",
["TOWER_DWARF_4_FORMATION_1_DESCRIPTION"] = "隊に3人目のカノニアを追加する。",
["TOWER_DWARF_4_FORMATION_1_NAME"] = "ランクアップ",
["TOWER_DWARF_4_FORMATION_2_DESCRIPTION"] = "隊に4人目のカノニアを追加する。",
["TOWER_DWARF_4_FORMATION_2_NAME"] = "ランクアップ",
["TOWER_DWARF_4_FORMATION_3_DESCRIPTION"] = "隊に5人目のカノニアを追加する。",
["TOWER_DWARF_4_FORMATION_3_NAME"] = "ランクアップ",
["TOWER_DWARF_4_FORMATION_NOTE"] = "女の子だって銃が欲しいのよ。",
["TOWER_DWARF_4_INCENDIARY_AMMO_1_DESCRIPTION"] = "%$towers.dwarf.incendiary_ammo.damages_min[1]%$ - %$towers.dwarf.incendiary_ammo.damages_max[1]%$ のダメージを与える爆発物を発射し、%$towers.dwarf.incendiary_ammo.burn.s_damage[1]%$ のダメージを%$towers.dwarf.incendiary_ammo.burn.duration%$秒間、範囲内の敵を燃やす。",
["TOWER_DWARF_4_INCENDIARY_AMMO_1_NAME"] = "焼夷弾",
["TOWER_DWARF_4_INCENDIARY_AMMO_2_DESCRIPTION"] = "%$towers.dwarf.incendiary_ammo.damages_min[2]%$ - %$towers.dwarf.incendiary_ammo.damages_max[2]%$ のダメージを与える爆発物を発射し、%$towers.dwarf.incendiary_ammo.burn.s_damage[2]%$ のダメージを%$towers.dwarf.incendiary_ammo.burn.duration%$秒間、範囲内の敵を燃やす。",
["TOWER_DWARF_4_INCENDIARY_AMMO_2_NAME"] = "焼夷弾",
["TOWER_DWARF_4_INCENDIARY_AMMO_3_DESCRIPTION"] = "%$towers.dwarf.incendiary_ammo.damages_min[3]%$ - %$towers.dwarf.incendiary_ammo.damages_max[3]%$ のダメージを与える爆発物を発射し、%$towers.dwarf.incendiary_ammo.burn.s_damage[3]%$ のダメージを%$towers.dwarf.incendiary_ammo.burn.duration%$秒間、範囲内の敵を燃やす。",
["TOWER_DWARF_4_INCENDIARY_AMMO_3_NAME"] = "焼夷弾",
["TOWER_DWARF_4_INCENDIARY_AMMO_NOTE"] = "熱くいくぜ！",
["TOWER_DWARF_4_NAME"] = "カノニア隊 IV",
["TOWER_DWARF_DESC"] = "卓越したチームスピリットを持つ熟練の射手たち。技術の誤用を制御するために北から派遣された。",
["TOWER_DWARF_NAME"] = "カノニア隊",
["TOWER_ELVEN_STARGAZERS_DESC"] = "宇宙のエネルギーを呼び起こし、エルフの星詠みは同時に多くの敵と戦うことができます。",
["TOWER_ELVEN_STARGAZERS_NAME"] = "エルフの星詠み",
["TOWER_FLAMESPITTER_1_DESCRIPTION"] = "その火はドラゴンの火と簡単に比較でき、悪者たちの間に恐怖を広げます。 ",
["TOWER_FLAMESPITTER_1_NAME"] = "ドワーフの火炎放射器I ",
["TOWER_FLAMESPITTER_2_DESCRIPTION"] = "その火はドラゴンの火と簡単に比較でき、悪者たちの間に恐怖を広げます。 ",
["TOWER_FLAMESPITTER_2_NAME"] = "ドワーフの火炎放射器II ",
["TOWER_FLAMESPITTER_3_DESCRIPTION"] = "その火はドラゴンの火と簡単に比較でき、悪者たちの間に恐怖を広げます。 ",
["TOWER_FLAMESPITTER_3_NAME"] = "ドワーフの火炎放射器III ",
["TOWER_FLAMESPITTER_4_DESCRIPTION"] = "その火はドラゴンの火と簡単に比較でき、悪者たちの間に恐怖を広げます。 ",
["TOWER_FLAMESPITTER_4_NAME"] = "ドワーフの火炎放射器IV ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_1_DESCRIPTION"] = "炎の爆弾を発射し、%$towers.flamespitter.skill_bomb.s_damage[1]%$の物理ダメージを与え、%$towers.flamespitter.skill_bomb.burning.s_damage%$の真実のダメージを秒間%$towers.flamespitter.skill_bomb.burning.duration%$秒間敵に与える。",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_1_NAME"] = "燃える小道 ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_2_DESCRIPTION"] = " 炎の爆弾は%$towers.flamespitter.skill_bomb.s_damage[2]%$の物理ダメージを与えます。燃焼は%$towers.flamespitter.skill_bomb.burning.duration%$秒間にわたり、秒間に%$towers.flamespitter.skill_bomb.burning.s_damage%$の真実のダメージを与えます。 ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_2_NAME"] = "燃える小道 ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_3_DESCRIPTION"] = " 炎の爆弾は%$towers.flamespitter.skill_bomb.s_damage[3]%$の物理ダメージを与えます。燃焼は%$towers.flamespitter.skill_bomb.burning.duration%$秒間にわたり、秒間に%$towers.flamespitter.skill_bomb.burning.s_damage%$の真実のダメージを与えます。 ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_3_NAME"] = "燃える小道 ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_NOTE"] = "野性のように燃えろ。 ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_1_DESCRIPTION"] = "道から炎の柱が噴出し、%$towers.flamespitter.skill_columns.s_damage_out[1]%$-%$towers.flamespitter.skill_columns.s_damage_in[1]%$の物理ダメージを与え、敵を%$towers.flamespitter.skill_columns.s_stun%$秒間スタンさせます。 ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_1_NAME"] = "灼熱のトーチ ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_2_DESCRIPTION"] = " 火柱は%$towers.flamespitter.skill_columns.s_damage_out[2]%$-%$towers.flamespitter.skill_columns.s_damage_in[2]%$の物理ダメージを与え、%$towers.flamespitter.skill_columns.s_stun%$秒間敵を気絶させます。 ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_2_NAME"] = "灼熱のトーチ ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_3_DESCRIPTION"] = " 火柱は%$towers.flamespitter.skill_columns.s_damage_out[3]%$-%$towers.flamespitter.skill_columns.s_damage_in[3]%$の物理ダメージを与え、%$towers.flamespitter.skill_columns.s_stun%$秒間敵を気絶させます。 ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_3_NAME"] = "灼熱のトーチ ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_NOTE"] = "足元に注意してください！ ",
["TOWER_FLAMESPITTER_DESC"] = " 鍛冶の熱を戦いに持ち込み、ドワーフはその燃えるような決意を同盟に貸し出します。 ",
["TOWER_FLAMESPITTER_NAME"] = "ドワーフの火炎放射器 ",
["TOWER_GHOST_1_DESCRIPTION"] = "今見える、そして見えない。そして、あなたは死んだ。",
["TOWER_GHOST_1_NAME"] = "グリムレイスI",
["TOWER_GHOST_2_DESCRIPTION"] = "今見える、そして見えない。そして、あなたは死んだ。",
["TOWER_GHOST_2_NAME"] = "グリムレイスII",
["TOWER_GHOST_3_DESCRIPTION"] = "今見える、そして見えない。そして、あなたは死んだ。",
["TOWER_GHOST_3_NAME"] = "グリムレイスIII",
["TOWER_GHOST_4_DESCRIPTION"] = "今見える、そして見えない。そして、あなたは死んだ。",
["TOWER_GHOST_4_EXTRA_DAMAGE_1_DESCRIPTION"] = "霊は戦闘中に%$towers.ghost.extra_damage.cooldown_start%$秒後に%$towers.ghost.extra_damage.s_damage[1]%$%の追加ダメージを与える。",
["TOWER_GHOST_4_EXTRA_DAMAGE_1_NAME"] = "ソウルサイフォニング",
["TOWER_GHOST_4_EXTRA_DAMAGE_2_DESCRIPTION"] = "霊は戦闘中に%$towers.ghost.extra_damage.cooldown_start%$秒後に%$towers.ghost.extra_damage.s_damage[2]%$%の追加ダメージを与える。",
["TOWER_GHOST_4_EXTRA_DAMAGE_2_NAME"] = "ソウルサイフォニング",
["TOWER_GHOST_4_EXTRA_DAMAGE_3_DESCRIPTION"] = "霊は戦闘中に%$towers.ghost.extra_damage.cooldown_start%$秒後に%$towers.ghost.extra_damage.s_damage[3]%$%の追加ダメージを与える。",
["TOWER_GHOST_4_EXTRA_DAMAGE_3_NAME"] = "ソウルサイフォニング",
["TOWER_GHOST_4_EXTRA_DAMAGE_NOTE"] = "露出はお勧めできません。",
["TOWER_GHOST_4_NAME"] = "グリムレイスIV",
["TOWER_GHOST_4_SOUL_ATTACK_1_DESCRIPTION"] = "敗れたレイスが近くの敵に向かって自らを投げつけ、%$towers.ghost.soul_attack.s_damage[1]%$の真実のダメージを与え、そのスピードを落とし、攻撃ダメージを半減させます。",
["TOWER_GHOST_4_SOUL_ATTACK_1_NAME"] = "НЕУМИРАЮЩИЙ УЖАС",
["TOWER_GHOST_4_SOUL_ATTACK_2_DESCRIPTION"] = "敗れたレイスが近くの敵に自らを投げつけ、%$towers.ghost.soul_attack.s_damage[2]%$の実ダメージを与え、そのスピードを落とし、攻撃ダメージを半減させます。",
["TOWER_GHOST_4_SOUL_ATTACK_2_NAME"] = "不滅の恐怖",
["TOWER_GHOST_4_SOUL_ATTACK_3_DESCRIPTION"] = "敗れたレイスが近くの敵に向かって自らを投げつけ、%$towers.ghost.soul_attack.s_damage[3]%$の実際のダメージを与え、そのスピードを落とし、攻撃ダメージを半分に減らします。",
["TOWER_GHOST_4_SOUL_ATTACK_3_NAME"] = "不滅の恐怖",
["TOWER_GHOST_4_SOUL_ATTACK_NOTE"] = "私たちと一緒に来てください！",
["TOWER_GHOST_DESC"] = "死後も戦うスペクター。その力は影を通り抜けて敵を驚かせることができる。",
["TOWER_GHOST_NAME"] = "グリムレイス",
["TOWER_HERMIT_TOAD_1_DESCRIPTION"] = "少しの魔法、少しの腕力、厄介な侵入者を排除するために必要なことは何でも。",
["TOWER_HERMIT_TOAD_1_NAME"] = "沼地の隠者 I",
["TOWER_HERMIT_TOAD_2_DESCRIPTION"] = "少しの魔法、少しの腕力、厄介な侵入者を排除するために必要なものは何でも。",
["TOWER_HERMIT_TOAD_2_NAME"] = "沼地の隠者 II",
["TOWER_HERMIT_TOAD_3_DESCRIPTION"] = "少しの魔法、少しの腕力、厄介な侵入者を排除するために必要なことは何でも。",
["TOWER_HERMIT_TOAD_3_NAME"] = "沼地の隠者 III",
["TOWER_HERMIT_TOAD_4_DESCRIPTION"] = "少しの魔法、少しの腕力、厄介な侵入者を排除するために必要なものは何でも。",
["TOWER_HERMIT_TOAD_4_INSTAKILL_1_DESCRIPTION"] = "%$towers.hermit_toad.power_instakill.cooldown[1]%$秒ごとにその舌を使って敵を捕食します。",
["TOWER_HERMIT_TOAD_4_INSTAKILL_1_NAME"] = "ネバネバ舌",
["TOWER_HERMIT_TOAD_4_JUMP_1_DESCRIPTION"] = "%$towers.hermit_toad.power_jump.cooldown[1]%$秒ごとに隠者は高く空に跳び、敵に落下し、%$towers.hermit_toad.power_jump.damage_min[1]%$のダメージを与え、着地時に%$towers.hermit_toad.power_jump.stun_duration[1]%$秒間スタンさせます。",
["TOWER_HERMIT_TOAD_4_JUMP_1_NAME"] = "グラウンドパウンダー",
["TOWER_HERMIT_TOAD_4_NAME"] = "沼地の隠者 IV",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_1_DESCRIPTION"] = "%$towers.hermit_toad.power_instakill.cooldown[1]%$秒ごとに、彼は自分の舌を使って敵を飲み込みます。",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_1_NAME"] = "ネバネバ舌 I",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_NOTE"] = "ねばねばした事態。",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_1_DESCRIPTION"] = "%$towers.hermit_toad.power_jump.cooldown[1]%$秒ごとに隠者は空高く跳び上がり、敵に衝突して%$towers.hermit_toad.power_jump.damage_min[1]%$のダメージを与え、着地時に%$towers.hermit_toad.power_jump.stun_duration[1]%$秒間スタンさせます。",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_1_NAME"] = "グラウンドパウンダー I",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_2_DESCRIPTION"] = "%$towers.hermit_toad.power_jump.cooldown[2]%$秒ごとに隠者は空高く跳び上がり、敵に衝突して%$towers.hermit_toad.power_jump.damage_min[2]%$のダメージを与え、着地時に%$towers.hermit_toad.power_jump.stun_duration[2]%$秒間スタンさせます。",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_2_NAME"] = "グラウンドパウンダー II",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_3_DESCRIPTION"] = "%$towers.hermit_toad.power_jump.cooldown[3]%$秒ごとに隠者は空高く跳び上がり、敵に衝突して%$towers.hermit_toad.power_jump.damage_min[3]%$のダメージを与え、着地時に%$towers.hermit_toad.power_jump.stun_duration[3]%$秒間スタンさせます。",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_3_NAME"] = "グラウンドパウンダー III",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_NOTE"] = "沼地バレーボールチームの準備ができています",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_DESCRIPTION"] = "隠者は物理的な構えに変わります。",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_NAME"] = "普通の沼地",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_NOTE"] = "泥んこになる！",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_DESCRIPTION"] = "隠者は魔法の構えに変わります。",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_NAME"] = "魔法の池",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_NOTE"] = "無限の力!!",
["TOWER_HERMIT_TOAD_DESC"] = "巨大なヒキガエルの魔法使いで、粘液の玉を吐くのが得意です。彼が望むのは、池のお風呂での平和と静けさだけです。邪魔しないでください。",
["TOWER_HERMIT_TOAD_NAME"] = "沼地の隠者",
["TOWER_NECROMANCER_1_DESCRIPTION"] = "死を操る術に長けているネクロマンサーは、戦場で蒔いた混乱を刈り取ります。",
["TOWER_NECROMANCER_1_NAME"] = "ネクロマンサーI",
["TOWER_NECROMANCER_2_DESCRIPTION"] = "死を操る術に長けているネクロマンサーは、戦場で蒔いた混乱を刈り取ります。",
["TOWER_NECROMANCER_2_NAME"] = "ネクロマンサーII",
["TOWER_NECROMANCER_3_DESCRIPTION"] = "死を操る術に長けているネクロマンサーは、戦場で蒔いた混乱を刈り取ります。",
["TOWER_NECROMANCER_3_NAME"] = "ネクロマンサーIII",
["TOWER_NECROMANCER_4_DESCRIPTION"] = "死を操る術に長けているネクロマンサーは、戦場で蒔いた混乱を刈り取ります。",
["TOWER_NECROMANCER_4_NAME"] = "ネクロマンサーIV",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_1_DESCRIPTION"] = "%$towers.necromancer.skill_debuff.aura_duration[1]%$秒間持続するトーテムを設置し、敵に呪いをかけ、スケルトンに%$towers.necromancer.skill_debuff.s_damage_factor[1]%$%の追加攻撃ダメージを与えます。",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_1_NAME"] = "カタカタと鳴る灯台",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_2_DESCRIPTION"] = "トーテムはスケルトンに%$towers.necromancer.skill_debuff.s_damage_factor[2]%$%の追加攻撃ダメージを与えます。クールダウンは%$towers.necromancer.skill_debuff.cooldown[2]%$秒に短縮されます。",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_2_NAME"] = "ガタガタと鳴るビーコン",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_3_DESCRIPTION"] = "トーテムはスケルトンに%$towers.necromancer.skill_debuff.s_damage_factor[3]%$%の追加攻撃ダメージを与えます。クールダウンは%$towers.necromancer.skill_debuff.cooldown[3]%$秒に短縮されます。",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_3_NAME"] = "ガタガタと鳴るビーコン",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_NOTE"] = "頑丈な骨の軍隊！",
["TOWER_NECROMANCER_4_SKILL_RIDER_1_DESCRIPTION"] = "道に死の騎士を召喚し、通過するすべての敵に対して%$towers.necromancer.skill_rider.s_damage[1]%$の真ダメージを与えます。",
["TOWER_NECROMANCER_4_SKILL_RIDER_1_NAME"] = "デスライダー",
["TOWER_NECROMANCER_4_SKILL_RIDER_2_DESCRIPTION"] = "デスライダーは%$towers.necromancer.skill_rider.s_damage[2]%$の実際のダメージを与えます。",
["TOWER_NECROMANCER_4_SKILL_RIDER_2_NAME"] = "デスライダー",
["TOWER_NECROMANCER_4_SKILL_RIDER_3_DESCRIPTION"] = "デスライダーは%$towers.necromancer.skill_rider.s_damage[3]%$の実際のダメージを与えます。",
["TOWER_NECROMANCER_4_SKILL_RIDER_3_NAME"] = "デスライダー",
["TOWER_NECROMANCER_4_SKILL_RIDER_NOTE"] = "片道切符...",
["TOWER_NECROMANCER_DESC"] = "最も暗い魔法を操るネクロマンサーは、敵を果てしない軍隊の隊列の一部として使用します。",
["TOWER_NECROMANCER_NAME"] = "ネクロマンサー",
["TOWER_PALADIN_COVENANT_1_DESCRIPTION"] = "猛烈で献身的な、パラディンは王国を危険から守るために一生懸命働いています。",
["TOWER_PALADIN_COVENANT_1_NAME"] = "パラディンの契約 I",
["TOWER_PALADIN_COVENANT_2_DESCRIPTION"] = "猛烈で献身的な、パラディンは王国を危険から守るために一生懸命働いています。",
["TOWER_PALADIN_COVENANT_2_NAME"] = "パラディンの契約 II",
["TOWER_PALADIN_COVENANT_3_DESCRIPTION"] = "猛烈で献身的な、パラディンは王国を危険から守るために一生懸命働いています。",
["TOWER_PALADIN_COVENANT_3_NAME"] = "パラディンの契約 III",
["TOWER_PALADIN_COVENANT_4_DESCRIPTION"] = "猛烈で献身的な、パラディンは王国を危険から守るために一生懸命働いています。",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_1_DESCRIPTION"] = "兵士の体力が%$towers.paladin_covenant.healing_prayer.health_trigger_factor[1]%$%に達すると、彼らは無敵になり、%$towers.paladin_covenant.healing_prayer.duration%$秒間にわたって秒間%$towers.paladin_covenant.healing_prayer.s_healing[1]%$の体力を回復します。",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_1_NAME"] = "癒しの祈り",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_2_DESCRIPTION"] = "回復量が秒間に%$towers.paladin_covenant.healing_prayer.s_healing[2]%$の体力に増加し、%$towers.paladin_covenant.healing_prayer.duration%$秒間持続します。",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_2_NAME"] = "癒しの祈り",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_3_DESCRIPTION"] = "秒間に%$towers.paladin_covenant.healing_prayer.s_healing[3]%$の健康増加し、%$towers.paladin_covenant.healing_prayer.duration%$秒間持続します。",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_3_NAME"] = "癒しの祈り",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_NAME"] = "癒しの祈り",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_NOTE"] = "死ぬまでの義務。",
["TOWER_PALADIN_COVENANT_4_LEAD_1_DESCRIPTION"] = "近くの味方に%$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$%の攻撃ダメージ増加を与えるガードのベテランに、パラディンの一人を置き換えます。",
["TOWER_PALADIN_COVENANT_4_LEAD_1_NAME"] = "模範を示す",
["TOWER_PALADIN_COVENANT_4_LEAD_2_DESCRIPTION"] = "近くの味方に%$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$%の攻撃ダメージ増加を与えるガードのベテランに、パラディンの一人を置き換えます。",
["TOWER_PALADIN_COVENANT_4_LEAD_2_NAME"] = "模範を示す",
["TOWER_PALADIN_COVENANT_4_LEAD_3_DESCRIPTION"] = "近くの味方に%$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$%の攻撃ダメージ増加を与えるガードのベテランに、パラディンの一人を置き換えます。",
["TOWER_PALADIN_COVENANT_4_LEAD_3_NAME"] = "模範を示す",
["TOWER_PALADIN_COVENANT_4_LEAD_NAME"] = "模範を示す",
["TOWER_PALADIN_COVENANT_4_LEAD_NOTE"] = "王のために、土地のために、山のために。",
["TOWER_PALADIN_COVENANT_4_NAME"] = "パラディンの契約 IV",
["TOWER_PALADIN_COVENANT_DESC"] = "リニレアの精鋭部隊の中核をなすパラディンは、戦いの中で自らを守り、癒すためにその神聖な力を使用します。",
["TOWER_PALADIN_COVENANT_NAME"] = "パラディンの契約",
["TOWER_PANDAS_1_DESCRIPTION"] = "元素の極意と揺るぎない決意を持つマスターたちは、世界の自然の均衡を守るために戦い続けます。",
["TOWER_PANDAS_1_NAME"] = "バンブーマスター I",
["TOWER_PANDAS_2_DESCRIPTION"] = "元素の極意と揺るぎない決意を持つマスターたちは、世界の自然の均衡を守るために戦い続けます。",
["TOWER_PANDAS_2_NAME"] = "バンブーマスター II",
["TOWER_PANDAS_3_DESCRIPTION"] = "元素の極意と揺るぎない決意を持つマスターたちは、世界の自然の均衡を守るために戦い続けます。",
["TOWER_PANDAS_3_NAME"] = "バンブーマスター III",
["TOWER_PANDAS_4_DESCRIPTION"] = "元素の極意と揺るぎない決意を持つマスターたちは、世界の自然の均衡を守るために戦い続けます。",
["TOWER_PANDAS_4_FIERY"] = "カウーシュ",
["TOWER_PANDAS_4_FIERY_1_DESCRIPTION"] = "%$towers.pandas.soldier.teleport.damage_min[1]%$〜%$towers.pandas.soldier.teleport.damage_max[1]%$の確定ダメージを与え、命中した敵を進路上で後方にテレポートさせる火の玉を放つ。",
["TOWER_PANDAS_4_FIERY_1_NAME"] = "ネザーフレイム",
["TOWER_PANDAS_4_FIERY_2_DESCRIPTION"] = "%$towers.pandas.soldier.teleport.damage_min[2]%$〜%$towers.pandas.soldier.teleport.damage_max[2]%$の確定ダメージを与えるファイアボルトを放ち、命中した敵を進行ルート上で後方にテレポートさせる。",
["TOWER_PANDAS_4_FIERY_2_NAME"] = "ネザーフレイム",
["TOWER_PANDAS_4_HAT"] = "すべてを打ち砕く帽子",
["TOWER_PANDAS_4_HAT_1_DESCRIPTION"] = "鋭く尖った帽子を敵に投げつけ、敵の間を跳ね返って%$towers.pandas.soldier.hat.damage_levels[1].min%$-%$towers.pandas.soldier.hat.damage_levels[1].max%$のダメージを与える。",
["TOWER_PANDAS_4_HAT_1_NAME"] = "ハットトリック",
["TOWER_PANDAS_4_HAT_2_DESCRIPTION"] = "鋭く尖った帽子を敵に投げつけ、敵の間を跳ね返って%$towers.pandas.soldier.hat.damage_levels[2].min%$-%$towers.pandas.soldier.hat.damage_levels[2].max%$のダメージを与える。",
["TOWER_PANDAS_4_HAT_2_NAME"] = "ハットトリック",
["TOWER_PANDAS_4_NAME"] = "バンブーマスター IV",
["TOWER_PANDAS_4_THUNDER"] = "パンダ・コンバット",
["TOWER_PANDAS_4_THUNDER_1_DESCRIPTION"] = "小範囲に雷を落とし、%$towers.pandas.soldier.thunder.damage_min[1]%$〜%$towers.pandas.soldier.thunder.damage_max[1]%$の範囲ダメージを与え、敵を短時間気絶させる。",
["TOWER_PANDAS_4_THUNDER_1_NAME"] = "雷撃の過負荷",
["TOWER_PANDAS_4_THUNDER_2_DESCRIPTION"] = "小範囲に雷を落とし、%$towers.pandas.soldier.thunder.damage_min[2]%$〜%$towers.pandas.soldier.thunder.damage_max[2]%$の範囲ダメージを与え、敵を短時間気絶させる。",
["TOWER_PANDAS_4_THUNDER_2_NAME"] = "ライトニングオーバーロード",
["TOWER_PANDAS_DESC"] = "武術と元素の親和性を兼ね備えたこのパンダの三人組は、敵を引き裂き、倒されたように見えても脅威であり続けます。",
["TOWER_PANDAS_NAME"] = "バンブーマスター",
["TOWER_PANDAS_RETREAT_DESCRIPTION"] = "8秒間、立っているパンダを避難所に退避させる。",
["TOWER_PANDAS_RETREAT_NAME"] = "戦術的撤退",
["TOWER_PANDAS_RETREAT_NOTE"] = "勇気の最たるものは慎重さだ。",
["TOWER_RAY_1_DESCRIPTION"] = "邪悪な魔法使いが悪意ある目的を追求するのを、危険で汚染された魔法の形態が止めたことはありません。",
["TOWER_RAY_1_NAME"] = "異界のチャネラー I",
["TOWER_RAY_2_DESCRIPTION"] = "邪悪な魔法使いが悪意ある目的を追求するのを、危険で汚染された魔法の形態が止めたことはありません。",
["TOWER_RAY_2_NAME"] = "異界のチャネラー II",
["TOWER_RAY_3_DESCRIPTION"] = "邪悪な魔法使いが悪意ある目的を追求するのを、危険で汚染された魔法の形態が止めたことはありません。",
["TOWER_RAY_3_NAME"] = "異界のチャネラー III",
["TOWER_RAY_4_CHAIN_1_DESCRIPTION"] = "魔法の光線は、%$towers.ray.skill_chain.s_max_enemies%$追加の敵に広がり、それらを遅くし、各ターゲットに対総魔法ダメージの%$towers.ray.skill_chain.damage_mult[1]%$%を与えます。",
["TOWER_RAY_4_CHAIN_1_NAME"] = "パワーオーバーフロー",
["TOWER_RAY_4_CHAIN_2_DESCRIPTION"] = "魔法の光線は、%$towers.ray.skill_chain.s_max_enemies%$追加の敵に広がり、それらを遅くし、各ターゲットに対総魔法ダメージの%$towers.ray.skill_chain.damage_mult[2]%$%を与えます。",
["TOWER_RAY_4_CHAIN_2_NAME"] = "パワーオーバーフロー",
["TOWER_RAY_4_CHAIN_3_DESCRIPTION"] = "魔法の光線は、%$towers.ray.skill_chain.s_max_enemies%$追加の敵に拡大し、それらを遅くし、各対象に総魔法ダメージの%$towers.ray.skill_chain.damage_mult[3]%$%を与えます。",
["TOWER_RAY_4_CHAIN_3_NAME"] = "パワーオーバーフロー",
["TOWER_RAY_4_CHAIN_NOTE"] = "すべての人には十分な痛みがあります。",
["TOWER_RAY_4_DESCRIPTION"] = "邪悪な魔法使いが悪意ある目的を追求するのを、危険で汚染された魔法の形態が止めたことはありません。",
["TOWER_RAY_4_NAME"] = "異界のチャネラー IV",
["TOWER_RAY_4_SHEEP_1_DESCRIPTION"] = "近くの敵を無力な羊に変えます。羊は目標の体力の%$towers.ray.skill_sheep.sheep.hp_mult%$%を持っています。",
["TOWER_RAY_4_SHEEP_1_NAME"] = "変異の呪文",
["TOWER_RAY_4_SHEEP_2_DESCRIPTION"] = "近くの敵を無力な羊に変えます。羊は対象の健康の%$towers.ray.skill_sheep.sheep.hp_mult%$%を持っています。",
["TOWER_RAY_4_SHEEP_2_NAME"] = "変異ヘックス",
["TOWER_RAY_4_SHEEP_3_DESCRIPTION"] = "近くの敵を無力な羊に変えます。羊は対象の健康の%$towers.ray.skill_sheep.sheep.hp_mult%$%を持っています。",
["TOWER_RAY_4_SHEEP_3_NAME"] = "変異ヘックス",
["TOWER_RAY_4_SHEEP_NOTE"] = "正直に言って、あなたは今、より良く見えます。",
["TOWER_RAY_DESC"] = "ヴェズナンの弟子たちは、敵に苦しみの暗い光線を投げかけるために、彼らの堕落した力を使います。",
["TOWER_RAY_NAME"] = "異界のチャネラー",
["TOWER_ROCKET_GUNNERS_1_DESCRIPTION"] = "ダークアーミーの最新技術を装備し、砲手たちは空を巡る。",
["TOWER_ROCKET_GUNNERS_1_NAME"] = "ロケットガンナー I",
["TOWER_ROCKET_GUNNERS_2_DESCRIPTION"] = "ダークアーミーの最新技術を装備し、砲手たちは空を巡る。",
["TOWER_ROCKET_GUNNERS_2_NAME"] = "ロケットガンナー II",
["TOWER_ROCKET_GUNNERS_3_DESCRIPTION"] = "ダークアーミーの最新技術を装備し、砲手たちは空を巡る。",
["TOWER_ROCKET_GUNNERS_3_NAME"] = "ロケットガンナー III",
["TOWER_ROCKET_GUNNERS_4_DESCRIPTION"] = "ダークアーミーの最新技術を装備し、砲手たちは空を巡る。",
["TOWER_ROCKET_GUNNERS_4_NAME"] = "ロケットガンナー IV",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_1_DESCRIPTION"] = "各攻撃が敵の装甲を %$towers.rocket_gunners.soldier.phosphoric.armor_reduction[1]%$% 破壊し、範囲ダメージを与える。",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_1_NAME"] = "リン酸塩コーティング",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_2_DESCRIPTION"] = "各攻撃が敵の装甲を %$towers.rocket_gunners.soldier.phosphoric.armor_reduction[2]%$% 破壊し、%$towers.rocket_gunners.soldier.phosphoric.damage_area_min[2]%$-%$towers.rocket_gunners.soldier.phosphoric.damage_area_max[2]%$ の範囲ダメージを与える。",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_2_NAME"] = "リン酸塩コーティング",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_3_DESCRIPTION"] = "各攻撃が敵の装甲を %$towers.rocket_gunners.soldier.phosphoric.armor_reduction[3]%$% 破壊し、%$towers.rocket_gunners.soldier.phosphoric.damage_area_min[3]%$-%$towers.rocket_gunners.soldier.phosphoric.damage_area_max[3]%$ の範囲ダメージを与える。",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_3_NAME"] = "リン酸塩コーティング",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_NOTE"] = "邪悪に味付けされた弾丸。",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_1_DESCRIPTION"] = "目標の体力が%$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[1]%$以下の場合、即座に目標を殺害するミサイルを発射します。",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_1_NAME"] = "スティングミサイル",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_2_DESCRIPTION"] = "%$towers.rocket_gunners.sting_missiles.cooldown[2]%$秒にクールダウンを減少させます。最大で%$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[2]%$の健康を持つ敵をターゲットにできるようになります。",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_2_NAME"] = "スティングミサイル",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_3_DESCRIPTION"] = "%$towers.rocket_gunners.sting_missiles.cooldown[3]%$秒にクールダウンを減少させます。これで、最大%$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[3]%$の健康を持つ敵をターゲットにできます。",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_3_NAME"] = "スティングミサイル",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_NOTE"] = "これを避けろ！",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_DESCRIPTION"] = "ロケットガンナーは離陸し、敵をブロックすることはできません。",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_NAME"] = "離陸",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_NOTE"] = "無限の彼方へ！",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_DESCRIPTION"] = "ロケットガンナーは地上に着陸し、敵をブロックできます。",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_NAME"] = "着陸する",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_NOTE"] = "鷲が着陸しました！",
["TOWER_ROCKET_GUNNERS_DESC"] = "これらの特殊部隊は、地上でも空中でも自分たちの立場を保ち、不意をついた敵に対して先進的な武器を繰り出すことができる。",
["TOWER_ROCKET_GUNNERS_NAME"] = "ロケットガンナー",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_1"] = "このタワーは巨大な脅威キャンペーンに含まれています",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_2"] = "このタワーは「悟空の旅」キャンペーンに含まれています。",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_1"] = "巨大な脅威キャンペーン",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_2"] = "悟空の旅キャンペーン",
["TOWER_ROOM_EQUIPPED_TOWERS_TITLE"] = "装備されたタワー",
["TOWER_ROOM_GET_DLC"] = "それを手に入れろ",
["TOWER_ROOM_LABEL_ROSTER_THUMB_NEW"] = "新しい！",
["TOWER_ROOM_SKILLS_TITLE"] = "スキル",
["TOWER_ROYAL_ARCHERS_1_DESCRIPTION"] = "最後まで忠実に、王立射手は遠くからリニレアン軍を守ります。 ",
["TOWER_ROYAL_ARCHERS_1_NAME"] = "王立射手 I ",
["TOWER_ROYAL_ARCHERS_2_DESCRIPTION"] = "最後まで忠実に、王立射手は遠くからリニレアン軍を守ります。 ",
["TOWER_ROYAL_ARCHERS_2_NAME"] = "王立射手 II ",
["TOWER_ROYAL_ARCHERS_3_DESCRIPTION"] = "最後まで忠実に、王立射手は遠くからリニレアン軍を守ります。 ",
["TOWER_ROYAL_ARCHERS_3_NAME"] = "王立射手 III ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_1_DESCRIPTION"] = "敵のアーマーを%$towers.royal_archers.armor_piercer.armor_penetration[1]%$%無視して、%$towers.royal_archers.armor_piercer.damage_min[1]%$-%$towers.royal_archers.armor_piercer.damage_max[1]%$の物理ダメージを与える強化された矢を3本撃ちます。 ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_1_NAME"] = "アーマーピアサー ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_2_DESCRIPTION"] = "強化された矢を3本放ち、%$towers.royal_archers.armor_piercer.damage_min[2]%$から%$towers.royal_archers.armor_piercer.damage_max[2]%$の物理ダメージを与え、敵の防御の%$towers.royal_archers.armor_piercer.armor_penetration[2]%$%を無視します。 ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_2_NAME"] = "アーマーピアサー ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_3_DESCRIPTION"] = "強化された矢を3本放ち、%$towers.royal_archers.armor_piercer.damage_min[3]%$から%$towers.royal_archers.armor_piercer.damage_max[3]%$の物理ダメージを与え、敵の防御の%$towers.royal_archers.armor_piercer.armor_penetration[3]%$%を無視します。 ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_3_NAME"] = "アーマーピアサー ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_NAME"] = "アーマーピアサー ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_NOTE"] = "あなたを捕らえました。 ",
["TOWER_ROYAL_ARCHERS_4_DESCRIPTION"] = "最後まで忠実に、王立射手は遠くからリニレアン軍を守ります。 ",
["TOWER_ROYAL_ARCHERS_4_NAME"] = "王立射手 IV ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_1_DESCRIPTION"] = "敵を攻撃する鷲を召喚し、%$towers.royal_archers.rapacious_hunter.damage_min[1]%$-%$towers.royal_archers.rapacious_hunter.damage_max[1]%$の物理ダメージを与えます。",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_1_NAME"] = "強欲なハンター ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_2_DESCRIPTION"] = "鷲は%$towers.royal_archers.rapacious_hunter.damage_min[2]%$から%$towers.royal_archers.rapacious_hunter.damage_max[2]%$の物理ダメージを与えます。 ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_2_NAME"] = "強欲なハンター ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_3_DESCRIPTION"] = "鷲は%$towers.royal_archers.rapacious_hunter.damage_min[3]%$から%$towers.royal_archers.rapacious_hunter.damage_max[3]%$の物理ダメージを与えます。 ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_3_NAME"] = "強欲なハンター ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_NAME"] = "強欲なハンター ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_NOTE"] = "鷲の目は悲劇的な何かを隠している。 ",
["TOWER_ROYAL_ARCHERS_DESC"] = "王国で最も強力な射手であり、戦争の鷲によって支援されていることでも知られています。 ",
["TOWER_ROYAL_ARCHERS_NAME"] = "王立射手 ",
["TOWER_SAND_1_DESCRIPTION"] = "投げられた刃の技術は、自分に満ち溢れているどんな傭兵も怖がらせるに足ります。",
["TOWER_SAND_1_NAME"] = "砂丘のセンチネルI",
["TOWER_SAND_2_DESCRIPTION"] = "投げられた刃の技術は、自分に満ち溢れているどんな傭兵も怖がらせるに足ります。",
["TOWER_SAND_2_NAME"] = "砂丘のセンチネルII",
["TOWER_SAND_3_DESCRIPTION"] = "彼らの投げナイフの技術は、自分に満ちているあまりにも多くの傭兵を怖がらせるのに十分です。",
["TOWER_SAND_3_NAME"] = "砂丘の番人III",
["TOWER_SAND_4_DESCRIPTION"] = "彼らの投げナイフの技術は、自分に満ちているあまりにも多くの傭兵を怖がらせるのに十分です。",
["TOWER_SAND_4_NAME"] = "砂丘の番人IV",
["TOWER_SAND_4_SKILL_BIG_BLADE_1_DESCRIPTION"] = "パスに渦巻く刃を発射し、%$towers.sand.skill_big_blade.s_damage_min[1]%$-%$towers.sand.skill_big_blade.s_damage_max[1]%$の物理ダメージを%$towers.sand.skill_big_blade.duration[1]%$秒間毎秒与えます。",
["TOWER_SAND_4_SKILL_BIG_BLADE_1_NAME"] = "渦巻く破滅",
["TOWER_SAND_4_SKILL_BIG_BLADE_2_DESCRIPTION"] = "回転する刃は、%$towers.sand.skill_big_blade.duration[2]%$秒間にわたり、秒間に%$towers.sand.skill_big_blade.s_damage_min[2]%$-%$towers.sand.skill_big_blade.s_damage_max[2]%$の物理ダメージを与えます。",
["TOWER_SAND_4_SKILL_BIG_BLADE_2_NAME"] = "渦巻く滅び",
["TOWER_SAND_4_SKILL_BIG_BLADE_3_DESCRIPTION"] = "回転する刃は、%$towers.sand.skill_big_blade.duration[3]%$秒間にわたって秒間あたり%$towers.sand.skill_big_blade.s_damage_min[3]%$から%$towers.sand.skill_big_blade.s_damage_max[3]%$の物理ダメージを与えます。",
["TOWER_SAND_4_SKILL_BIG_BLADE_3_NAME"] = "渦巻く滅び",
["TOWER_SAND_4_SKILL_BIG_BLADE_NOTE"] = "あなたは私をグルグル回す、赤ちゃんよ。",
["TOWER_SAND_4_SKILL_GOLD_1_DESCRIPTION"] = "対象の敵に %$towers.sand.skill_gold.s_damage[1]%$ の物理ダメージを与える跳ね返るブレードを投げます。 ブレードによって倒された任意の対象からは、%$towers.sand.skill_gold.gold_extra[1]%$ のボーナスゴールドが得られます。 ",
["TOWER_SAND_4_SKILL_GOLD_1_NAME"] = "賞金稼ぎ",
["TOWER_SAND_4_SKILL_GOLD_2_DESCRIPTION"] = "刃は %$towers.sand.skill_gold.s_damage[2]%$ の物理ダメージを与える。キルで %$towers.sand.skill_gold.gold_extra[2]%$ のボーナスゴールドを獲得。 ",
["TOWER_SAND_4_SKILL_GOLD_2_NAME"] = "賞金稼ぎ",
["TOWER_SAND_4_SKILL_GOLD_3_DESCRIPTION"] = "刃が %$towers.sand.skill_gold.s_damage[3]%$ の物理的なダメージを与えます。キルで %$towers.sand.skill_gold.gold_extra[3]%$ のボーナスゴールドを獲得します。",
["TOWER_SAND_4_SKILL_GOLD_3_NAME"] = "賞金稼ぎ",
["TOWER_SAND_4_SKILL_GOLD_NOTE"] = "チラシには死んだか生きているかと書いてある。",
["TOWER_SAND_DESC"] = "ハンマーホールドから来た砂丘のセンチネルは、砂漠の住人の中で最も致命的かもしれません。",
["TOWER_SAND_NAME"] = "砂丘のセンチネル",
["TOWER_SELL"] = "タワーを売却",
["TOWER_SPARKING_GEODE_1_DESCRIPTION"] = "嵐を召喚し、混沌をもたらす存在。エネルギー消費に注意せよ。",
["TOWER_SPARKING_GEODE_1_NAME"] = "雷鳴の巨像 I",
["TOWER_SPARKING_GEODE_2_DESCRIPTION"] = "雷鳴の巨像 III",
["TOWER_SPARKING_GEODE_2_NAME"] = "雷鳴の巨像 II",
["TOWER_SPARKING_GEODE_3_DESCRIPTION"] = "結晶化 I",
["TOWER_SPARKING_GEODE_3_NAME"] = "雷鳴の巨像 IV",
["TOWER_SPARKING_GEODE_4_CRISTALIZE"] = "サンダーボルト！",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_1_DESCRIPTION"] = "%$towers.sparking_geode.crystalize.cooldown[1]%$秒ごとに、範囲内の%$towers.sparking_geode.crystalize.max_targets[1]%$体の敵を結晶化させ、気絶させた上で受けるダメージを%$towers.sparking_geode.crystalize.s_received_damage_factor[1]%$%増加させる。",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_1_NAME"] = "結晶化",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_2_DESCRIPTION"] = "%$towers.sparking_geode.crystalize.cooldown[2]%$秒ごとに、範囲内の%$towers.sparking_geode.crystalize.max_targets[2]%$体の敵を結晶化し、スタンさせ、%$towers.sparking_geode.crystalize.s_received_damage_factor[2]%$%の追加ダメージを与える。",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_2_NAME"] = "結晶化",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_3_DESCRIPTION"] = "%$towers.sparking_geode.crystalize.cooldown[3]%$秒ごとに、範囲内の%$towers.sparking_geode.crystalize.max_targets[3]%$体の敵を結晶化し、スタンさせ、%$towers.sparking_geode.crystalize.s_received_damage_factor[3]%$%の追加ダメージを与える。",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_3_NAME"] = "結晶化",
["TOWER_SPARKING_GEODE_4_CRYSTALIZE_1_DESCRIPTION"] = "%$towers.sparking_geode.crystalize.cooldown[1]%$秒ごとに、範囲内の%$towers.sparking_geode.crystalize.max_targets[1]%$体の敵を結晶化させ、スタンさせ、さらに%$towers.sparking_geode.crystalize.s_received_damage_factor[1]%$%の追加ダメージを与える。",
["TOWER_SPARKING_GEODE_4_CRYSTALIZE_1_NAME"] = "結晶化",
["TOWER_SPARKING_GEODE_4_DESCRIPTION"] = "嵐を呼ぶ者にして、混沌の担い手。その消費エネルギーには注意が必要だ。",
["TOWER_SPARKING_GEODE_4_NAME"] = "サージ・コロッサス IV",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST"] = "より強く、より良く、より速く、より強靭に。",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_1_DESCRIPTION"] = "%$towers.sparking_geode.spike_burst.cooldown[1]%$ 秒ごとに、コロッサスは電場を発生させ、周囲の敵にダメージを与え、%$towers.sparking_geode.spike_burst.duration[1]%$ 秒間鈍足効果を与える。",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_1_NAME"] = "エレクトリカルサージ",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_2_DESCRIPTION"] = "%$towers.sparking_geode.spike_burst.cooldown[2]%$ 秒ごとに、コロッサスは電場を発生させ、周囲の敵にダメージを与え、%$towers.sparking_geode.spike_burst.duration[2]%$ 秒間鈍足効果を与える。",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_2_NAME"] = "エレクトリカルサージ",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_3_DESCRIPTION"] = "%$towers.sparking_geode.spike_burst.cooldown[3]%$ 秒ごとに、コロッサスは電場を発生させ、周囲の敵にダメージを与え、%$towers.sparking_geode.spike_burst.duration[3]%$ 秒間鈍足効果を与える。",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_3_NAME"] = "エレクトリカルサージ",
["TOWER_SPARKING_GEODE_DESC"] = "古の平和な種族に由来するこの強大な存在は、防衛本能に従い、雷の力を駆使して同盟のために戦い、嵐のごとき怒りで敵を討つ。",
["TOWER_SPARKING_GEODE_NAME"] = "雷鳴の巨像",
["TOWER_STAGE_13_SUNRAY_NAME"] = "ダークレイタワー",
["TOWER_STAGE_13_SUNRAY_REPAIR_DESCRIPTION"] = "塔を修理して、その破壊力を利用してください。",
["TOWER_STAGE_13_SUNRAY_REPAIR_NAME"] = "修理",
["TOWER_STAGE_17_WEIRDWOOD_NAME"] = "ウィアードウッド",
["TOWER_STAGE_18_ELVEN_BARRACK_DESCRIPTION"] = "最後まで戦うために雇われたエルフたち。",
["TOWER_STAGE_18_ELVEN_BARRACK_NAME"] = "エルフの傭兵",
["TOWER_STAGE_20_ARBOREAN_BARRACK_DESCRIPTION"] = "アルボリアンの人々を戦いに呼び出す。",
["TOWER_STAGE_20_ARBOREAN_BARRACK_NAME"] = "アルボリアンの市民",
["TOWER_STAGE_20_ARBOREAN_HONEY_DESCRIPTION"] = "蜂の偉大な指揮官を召喚する。",
["TOWER_STAGE_20_ARBOREAN_HONEY_NAME"] = "アルボリアンの養蜂家",
["TOWER_STAGE_20_ARBOREAN_OLDTREE_DESCRIPTION"] = "古い木に助けを求めてください。",
["TOWER_STAGE_20_ARBOREAN_OLDTREE_NAME"] = "古い木",
["TOWER_STAGE_22_ARBOREAN_MAGES_NAME"] = "アルボリアンの魔法使い",
["TOWER_STAGE_28_PRIESTS_BARRACK_DESCRIPTION"] = "贖われた狂信者たちは戦場にその魔術をもたらし、死ぬと異形へと変貌する。",
["TOWER_STAGE_28_PRIESTS_BARRACK_NAME"] = "盲目の狂信者",
["TOWER_STARGAZER_1_DESCRIPTION"] = "星詠みは、地上の領域を超えた強力な魔法を取り込むことを目指しています。",
["TOWER_STARGAZER_1_NAME"] = "エルフの星詠み I",
["TOWER_STARGAZER_2_DESCRIPTION"] = "星詠みは、地上の領域を超えた強力な魔法を取り込むことを目指しています。",
["TOWER_STARGAZER_2_NAME"] = "エルフの星詠み II",
["TOWER_STARGAZER_3_DESCRIPTION"] = "星詠みは、地上の領域を超えた強力な魔法を取り込むことを目指しています。",
["TOWER_STARGAZER_3_NAME"] = "エルフの星詠み III",
["TOWER_STARGAZER_4_DESCRIPTION"] = "星詠みは、地上の領域を超えた強力な魔法を取り込むことを目指しています。",
["TOWER_STARGAZER_4_EVENT_HORIZON_1_DESCRIPTION"] = "パスを逆に最大%$towers.elven_stargazers.teleport.max_targets[1]%$敵をテレポートさせます。",
["TOWER_STARGAZER_4_EVENT_HORIZON_1_NAME"] = "イベントホライズン",
["TOWER_STARGAZER_4_EVENT_HORIZON_2_DESCRIPTION"] = "道の後方へと最大で%$towers.elven_stargazers.teleport.max_targets[2]%$敵をテレポートさせます。 ",
["TOWER_STARGAZER_4_EVENT_HORIZON_2_NAME"] = "イベントホライズン",
["TOWER_STARGAZER_4_EVENT_HORIZON_3_DESCRIPTION"] = "%$towers.elven_stargazers.teleport.max_targets[3]%$敵をさらに道の後方へテレポートします。",
["TOWER_STARGAZER_4_EVENT_HORIZON_3_NAME"] = "イベントホライズン",
["TOWER_STARGAZER_4_EVENT_HORIZON_NAME"] = "イベントホライズン",
["TOWER_STARGAZER_4_EVENT_HORIZON_NOTE"] = "フェーズアウト、フェーズイン。",
["TOWER_STARGAZER_4_NAME"] = "エルフの星詠み IV",
["TOWER_STARGAZER_4_RISING_STAR_1_DESCRIPTION"] = "タワーに倒された敵は、%$towers.elven_stargazers.stars_death.stars[1]%$個の星の爆発で爆発し、敵に%$towers.elven_stargazers.stars_death.damage_min[1]%$～%$towers.elven_stargazers.stars_death.damage_max[1]%$の魔法ダメージを与えます。",
["TOWER_STARGAZER_4_RISING_STAR_1_NAME"] = "新星",
["TOWER_STARGAZER_4_RISING_STAR_2_DESCRIPTION"] = "星の数が%$towers.elven_stargazers.stars_death.stars[2]%$に増加します。星は%$towers.elven_stargazers.stars_death.damage_min[2]%$-%$towers.elven_stargazers.stars_death.damage_max[2]%$の魔法ダメージを与えます。",
["TOWER_STARGAZER_4_RISING_STAR_2_NAME"] = "新星",
["TOWER_STARGAZER_4_RISING_STAR_3_DESCRIPTION"] = "星の数が%$towers.elven_stargazers.stars_death.stars[3]%$に増加します。星は%$towers.elven_stargazers.stars_death.damage_min[3]%$-%$towers.elven_stargazers.stars_death.damage_max[3]%$の魔法ダメージを与えます。",
["TOWER_STARGAZER_4_RISING_STAR_3_NAME"] = "新星",
["TOWER_STARGAZER_4_RISING_STAR_NAME"] = "新星",
["TOWER_STARGAZER_4_RISING_STAR_NOTE"] = "それはスターダストの革命です！",
["TOWER_TRICANNON_1_DESCRIPTION"] = "戦争への壊滅的な愛の歌で、敵にも味方にも恐ろしい光景だ。",
["TOWER_TRICANNON_1_NAME"] = "トリキャノン I",
["TOWER_TRICANNON_2_DESCRIPTION"] = "戦争への壊滅的な愛の歌で、敵にも味方にも恐ろしい光景だ。",
["TOWER_TRICANNON_2_NAME"] = "トリキャノン II",
["TOWER_TRICANNON_3_DESCRIPTION"] = "戦争への壊滅的な愛の歌で、敵にも味方にも恐ろしい光景だ。",
["TOWER_TRICANNON_3_NAME"] = "トリキャノン III",
["TOWER_TRICANNON_4_BOMBARDMENT_1_DESCRIPTION"] = "広い範囲に速やかに爆弾を発射し、それぞれが%$towers.tricannon.bombardment.damage_min[1]%$-%$towers.tricannon.bombardment.damage_max[1]%$の物理ダメージを与える。",
["TOWER_TRICANNON_4_BOMBARDMENT_1_NAME"] = "爆撃",
["TOWER_TRICANNON_4_BOMBARDMENT_2_DESCRIPTION"] = "より広い範囲にもっと多くの爆弾を撃ち、それぞれが%$towers.tricannon.bombardment.damage_min[2]%$-%$towers.tricannon.bombardment.damage_max[2]%$の物理ダメージを与える",
["TOWER_TRICANNON_4_BOMBARDMENT_2_NAME"] = "爆撃",
["TOWER_TRICANNON_4_BOMBARDMENT_3_DESCRIPTION"] = "さらに多くの爆弾をより広い範囲で撃ち、それぞれが%$towers.tricannon.bombardment.damage_min[3]%$-%$towers.tricannon.bombardment.damage_max[3]%$の物理ダメージを与える",
["TOWER_TRICANNON_4_BOMBARDMENT_3_NAME"] = "爆撃",
["TOWER_TRICANNON_4_BOMBARDMENT_NAME"] = "爆撃",
["TOWER_TRICANNON_4_BOMBARDMENT_NOTE"] = "スケーラビリティについて話しましょう。",
["TOWER_TRICANNON_4_DESCRIPTION"] = "戦争への壊滅的な愛の歌で、敵にも味方にも恐ろしい光景だ。",
["TOWER_TRICANNON_4_NAME"] = "トリキャノン IV",
["TOWER_TRICANNON_4_OVERHEAT_1_DESCRIPTION"] = "トライキャノンのバレルが%$towers.tricannon.overheat.duration[1]%$秒間赤熱化し、爆弾が地面を焦がして敵に毎秒%$towers.tricannon.overheat.decal.effect.s_damage[1]%$の真のダメージを与えます。",
["TOWER_TRICANNON_4_OVERHEAT_1_NAME"] = "オーバーヒート",
["TOWER_TRICANNON_4_OVERHEAT_2_DESCRIPTION"] = "各焼け焦げエリアは、1秒間に%$towers.tricannon.overheat.decal.effect.s_damage[2]%$の実際のダメージを与えます。持続時間は%$towers.tricannon.overheat.duration[2]%$秒に延長されます。",
["TOWER_TRICANNON_4_OVERHEAT_2_NAME"] = "オーバーヒート",
["TOWER_TRICANNON_4_OVERHEAT_3_DESCRIPTION"] = "各焼け焦げエリアは、毎秒%$towers.tricannon.overheat.decal.effect.s_damage[3]%$の実際のダメージを与える。持続時間は%$towers.tricannon.overheat.duration[3]%$秒に延長されます。",
["TOWER_TRICANNON_4_OVERHEAT_3_NAME"] = "オーバーヒート",
["TOWER_TRICANNON_4_OVERHEAT_NAME"] = "オーバーヒート",
["TOWER_TRICANNON_4_OVERHEAT_NOTE"] = "私たちは真っ赤に熱いです。",
["TOWER_TRICANNON_DESC"] = "ダークアーミーは、複数の大砲により火と破壊を降らせることで、現代戦の新たな定義を提示します。",
["TOWER_TRICANNON_NAME"] = "トリキャノン",
["TUTORIAL_hero_room_hero_points_desc"] = "戦闘で各ヒーローをレベルアップさせることでヒーローポイントを獲得します。",
["TUTORIAL_hero_room_hero_points_title"] = "ヒーローポイント",
["TUTORIAL_hero_room_power_desc"] = "ヒーローポイントを使用して、あなたのヒーローのパワーを購入し、改善します。",
["TUTORIAL_hero_room_power_title"] = "ヒーローパワー",
["TUTORIAL_hero_room_tutorial_navigate_desc"] = "異なるヒーローをナビゲートします。",
["TUTORIAL_hero_room_tutorial_select_desc"] = "戦場で使用したいヒーローを装備してください。",
["TUTORIAL_item_room_buy_desc"] = "宝石を使って、戦場であなたを助けるアイテムを購入してください。",
["TUTORIAL_item_room_buy_title"] = "アイテム購入",
["TUTORIAL_item_room_tutorial_equip_desc"] = "各スロットを使用してアイテムを装備します。順番を変更するにはドラッグしてください！",
["TUTORIAL_item_room_tutorial_navigate_desc"] = "利用可能な様々なアイテムをナビゲートしてください。",
["TUTORIAL_tower_room_power_desc"] = "これらのスキルは、タワーがレベルIVに到達すると利用可能になります。",
["TUTORIAL_tower_room_power_title"] = "レベルIVスキル",
["TUTORIAL_tower_room_tutorial_equip_desc"] = "新しいタワーを装備して、さまざまな組み合わせを試してみてください。",
["TUTORIAL_tower_room_tutorial_navigate_desc"] = "異なるタワーをナビゲートします。",
["TUTORIAL_tower_room_tutorial_slots_desc"] = "各スロットを使用してタワーを装備します。順番を変更するにはドラッグしてください！",
["TUTORIAL_upgrade_room_tooltip_buy_desc"] = "ポイントを使って、あなたのパワー、タワー、ヒーローのアップグレードを購入してください。",
["TUTORIAL_upgrade_room_tooltip_souls_desc"] = "キャンペーンステージを完了することでアップグレードポイントを獲得します。",
["TUTORIAL_upgrade_room_tooltip_souls_title"] = "アップグレードポイント",
["Tap the road!"] = "道をタップ！",
["Tip"] = "ヒント",
["Tower construction"] = "タワー建設",
["Towers"] = "タワー",
["Try again"] = "再トライ",
["Typography"] = "タイポグラフィ",
["UPDATE_POPUP"] = "更新する",
["UPDATING_CLOUDSAVE_MESSAGE"] = "クラウドに保存されたゲームを更新しています...",
["UPGRADES"] = "アップグレード",
["UPGRADES AND HEROES RESTRICTIONS!"] = "アップグレードと英雄に関する制限！",
["UPGRADE_LEVEL"] = "アップグレードレベル",
["Undo"] = "取消",
["Unlocks at Level"] = "アンロックレベル：",
["Upgrades"] = "アップグレード",
["Use the earned hero points to train your hero!"] = "獲得した英雄ポイントを使って英雄を訓練しよう！",
["Use the earned stars to improve your towers and powers!"] = "獲得した星を使ってタワーとパワーを強化しよう！",
["VICTORY"] = "勝利",
["Very fast"] = "非常に速い",
["Very slow"] = "非常に遅い",
["Veteran"] = "ベテラン",
["Victory!"] = "勝利！",
["Voice Talent"] = "ボイスアクター",
["WARNING"] = "警告",
["WAVE_TOOLTIP_TAP_AGAIN"] = "クリックして早く呼び込む",
["WAVE_TOOLTIP_TITLE"] = "次の敵部隊",
["We would like to thank"] = "スペシャルサンクス",
["Yes"] = "はい",
["You can always change the difficulty in the options menu."] = "難易度はオプションメニューでいつでも変更可能です。",
["_manually_included_characters"] = "$ ¥ ￥ ƒ ₩ € ™ × $ zł ¢ £ ¤ ¥ ƒ ден дин лв. ؋ ৳ ฿ ლ ₡ ₣ ₤ ₥ ₦ ₨ ₩ ₪ ₫ € ₭ ₮ ₱ ₲ ₴ ₵ ₹ ₺ ₽ ﷼",
["alliance_close_to_home_DESCRIPTION"] = "ステージ開始時に追加のゴールドを付与します。",
["alliance_close_to_home_NAME"] = "共有の蓄え",
["alliance_corageous_stand_DESCRIPTION"] = "リニアンの塔が建つごとに、ヒーローのヘルスポイントが増加します。",
["alliance_corageous_stand_NAME"] = "勇敢なスタンド",
["alliance_display_of_true_might_dark_DESCRIPTION"] = "ダークアーミーのヒーロースペルは、今や画面上のすべての敵を遅くします。",
["alliance_display_of_true_might_dark_NAME"] = "不吉な呪い",
["alliance_display_of_true_might_linirea_DESCRIPTION"] = "リニレアンの英雄の呪文は今、同盟軍の全ユニットを癒し、再生させる。",
["alliance_display_of_true_might_linirea_NAME"] = "活力の祝福",
["alliance_flux_altering_coils_DESCRIPTION"] = "すべての出口フラグを、近くの敵をテレポートで戻すアルケインピラーに置き換えます。",
["alliance_flux_altering_coils_NAME"] = "アルケイン ピラー",
["alliance_friends_of_the_crown_DESCRIPTION"] = "装備されたリニリアンのヒーローごとに、塔の建築およびアップグレードコストが削減されます。",
["alliance_friends_of_the_crown_NAME"] = "王冠の友",
["alliance_merciless_DESCRIPTION"] = "ダークアーミーの塔を建てるごとに、ヒーローの攻撃ダメージが増加します。",
["alliance_merciless_NAME"] = "冷酷な防御",
["alliance_seal_of_punishment_DESCRIPTION"] = "防御ポイントを、上を通過する敵にダメージを与える魔法の印に置き換えます。",
["alliance_seal_of_punishment_NAME"] = "罰の印",
["alliance_shady_company_DESCRIPTION"] = "装備されたダークアーミーのヒーローごとに、塔の攻撃ダメージが増加します。",
["alliance_shady_company_NAME"] = "怪しい会社",
["alliance_shared_reserves_DESCRIPTION"] = "ステージ開始時に追加のゴールドを付与します。",
["alliance_shared_reserves_NAME"] = "共有の蓄え",
["build defensive towers along the road to stop them."] = "道に沿って防衛タワーを建設して食い止めよう。",
["build towers to defend the road."] = "タワーを建設して道を防衛しよう。",
["check the stage description to see:"] = "ステージ説明を確認して見る：",
["click these!"] = "これらをクリックしよう！",
["click to continue..."] = "クリックして続ける…",
["deals area damage"] = "範囲ダメージを与える",
["don't let enemies past this point."] = "敵にこの地点を突破させないようにしよう。",
["earn gold by killing enemies."] = "敵を倒してゴールドを獲得しよう。",
["good rate of fire"] = "高い発射速度を誇る",
["heroes_desperate_effort_DESCRIPTION"] = "英雄の攻撃は敵の抵抗の10%を無視します。",
["heroes_desperate_effort_NAME"] = "敵を知れ",
["heroes_lethal_focus_DESCRIPTION"] = "ヒーローは攻撃の20%でクリティカルダメージを与えます。",
["heroes_lethal_focus_NAME"] = "致命的な集中",
["heroes_limit_pushing_DESCRIPTION"] = "各ヒーロースペルを5回使用すると、そのクールダウンが即座にリセットされます。 ",
["heroes_limit_pushing_NAME"] = "限界を押し広げる",
["heroes_lone_wolves_DESCRIPTION"] = "ヒーローたちはお互いから離れているときにより多くの経験を積む。",
["heroes_lone_wolves_NAME"] = "一匹狼",
["heroes_nimble_physique_DESCRIPTION"] = "ヒーローは敵の攻撃の20%を回避します。",
["heroes_nimble_physique_NAME"] = "敏捷な体型",
["heroes_unlimited_vigor_DESCRIPTION"] = "すべてのヒーロースペルのクールダウンを短縮します。",
["heroes_unlimited_vigor_NAME"] = "無制限の活力",
["heroes_visual_learning_DESCRIPTION"] = "互いに近くにいるとき、ヒーローは10％追加のアーマーを持っています。 ",
["heroes_visual_learning_NAME"] = "助ける手",
["high damage, armor piercing"] = "高い攻撃力、装甲貫通",
["iron and heroic challenges may have restrictions on upgrades!"] = "鉄と英雄的なチャレンジではアップグレードが制限される可能性がある！",
["max lvl allowed"] = "最大レベル",
["multi-shot, armor piercing"] = "マルチショット、装甲貫通",
["no heroes"] = "英雄なし",
["protect your lands from the enemy attacks."] = "敵の攻撃から国を守ろう。",
["rally range"] = "集結距離",
["ready for action!"] = "行動準備完了！",
["reinforcements_intense_workout_DESCRIPTION"] = "補強部隊の健康と持続時間を向上させる。",
["reinforcements_intense_workout_NAME"] = "激しいワークアウト",
["reinforcements_master_blacksmiths_DESCRIPTION"] = "補強部隊の攻撃力と防御力を向上させる。",
["reinforcements_master_blacksmiths_NAME"] = "名工鍛冶師",
["reinforcements_night_veil_DESCRIPTION"] = "影の射手は射程距離と攻撃速度が向上しています。",
["reinforcements_night_veil_NAME"] = "灰燼の弓",
["reinforcements_power_trio_DESCRIPTION"] = "援軍を呼ぶことで、今ではパラゴンナイトも召喚されます。",
["reinforcements_power_trio_NAME"] = "リニリアンの模範",
["reinforcements_power_trio_dark_DESCRIPTION"] = "援軍を呼ぶことでシャドウ・クロウコーラーも召喚されます。",
["reinforcements_power_trio_dark_NAME"] = "シャドウ・クロウコーラー",
["reinforcements_rebel_militia_DESCRIPTION"] = "補強はリニレアンの反乱者に置き換えられ、重厚な鎧を着た耐久性のある戦士です。",
["reinforcements_rebel_militia_NAME"] = "リニリアン民兵",
["reinforcements_shadow_archer_DESCRIPTION"] = "補強兵は影の射手に置き換えられ、遠方から攻撃し、飛行ユニットを狙います。",
["reinforcements_shadow_archer_NAME"] = "影の秩序",
["reinforcements_thorny_armor_DESCRIPTION"] = "リニリアンの反逆者たちは敵の近接攻撃の一部のダメージを反射します。",
["reinforcements_thorny_armor_NAME"] = "スパイクアーマー",
["resist damage from"] = "は以下の攻撃を防御：",
["resists damage from"] = "は以下の 攻撃を防御：",
["select the rally point control"] = "集結地操作を選択",
["select the tower you want to build!"] = "建設したいタワーを選択！",
["select where you want to move your soldiers"] = "兵士を移動させたい場所を選択",
["soldiers block enemies"] = "兵士は敵をブロックする",
["some enemies enjoy different levels of magic resistance that protects them against magical attacks."] = "敵はそれぞれ様々なレベルの魔法防御を持ち、当然高い魔法防御を誇る敵ほど魔法攻撃に強い。",
["some enemies wear armor of different strengths that protects them against non-magical attacks."] = "様々な強度の装甲を装備している敵も存在する。当然、強度が高いほど 魔法以外の攻撃に強い。",
["tap these!"] = "これらをタップしよう！",
["this is a strategic point."] = "ここは戦略地点だ。",
["towers_favorite_customer_DESCRIPTION"] = "スキルの最終レベルを購入すると、コストが50％減少します。",
["towers_favorite_customer_NAME"] = "お気に入りの顧客",
["towers_golden_time_DESCRIPTION"] = "波を早期に呼び出すためのボーナスゴールドを増加させます。",
["towers_golden_time_NAME"] = "ゴールデンタイム",
["towers_improved_formulas_DESCRIPTION"] = "タワーからのすべての爆発のダメージを最大化し、その効果範囲を拡大します。",
["towers_improved_formulas_NAME"] = "改良された公式",
["towers_keen_accuracy_DESCRIPTION"] = "全てのタワースキルのクールダウンを20%短縮します。",
["towers_keen_accuracy_NAME"] = "戦闘の熱情",
["towers_royal_training_DESCRIPTION"] = "タワーユニットのスポーン時間および増援のクールダウン時間を短縮します。",
["towers_royal_training_NAME"] = "アクションへの呼びかけ",
["towers_scoping_mechanism_DESCRIPTION"] = "すべてのタワーの攻撃範囲を10%増加させます。",
["towers_scoping_mechanism_NAME"] = "スコーピングメカニズム",
["towers_war_rations_DESCRIPTION"] = "すべてのタワーユニットの体力を10％増加させます。",
["towers_war_rations_NAME"] = "戦時配給",
["towers_wise_investment_DESCRIPTION"] = "タワーは売却時にコストの90%を返金します。",
["towers_wise_investment_NAME"] = "賢明な投資",
["wOOt!"] = "おおおっと！",
["you can adjust your soldiers rally point to make them defend a different area."] = "兵士たちの集結地を調整することで、異なるエリアを防衛させられる。",
}
