-- ------------------------------------------------
-- -- WARNING: DO NOT EDIT BY HAND                 
-- -- Generated by kr-i18n/tools/strings-export.lua
-- ------------------------------------------------
return {
["!!!COMMENT_LOCALIZATION_SOURCE"] = "Keywords",
["%d Life"] = "%d Leben",
["%d Lives"] = "%d Leben",
["%i sec."] = "%i Sek.",
["- if heroes are allowed"] = "- Wenn Helden erlaubt sind",
["- max upgrade level allowed"] = "- Maximaler Verbesserungslevel erlaubt",
["A good challenge!"] = "Eine tolle Herausforderung!",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_1_NAME"] = "Abscheulicher <PERSON>",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_2_NAME"] = "Abscheulicher Henry",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_3_NAME"] = "Abscheulicher Geoffrey",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_4_NAME"] = "Abscheulicher Nicholas",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_5_NAME"] = "Abscheulicher Ed",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_6_NAME"] = "Abscheulicher Hob",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_7_NAME"] = "Abscheulicher Odo",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_8_NAME"] = "Abscheulicher Cedric",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_9_NAME"] = "Abscheulicher Hal",
["ACHIEVEMENT"] = "LEISTUNG",
["ACHIEVEMENTS"] = "LEISTUNGEN",
["ACHIEVEMENTS_TITLE"] = "ERFOLGE",
["ACHIEVEMENT_AGE_OF_HEROES_DESCRIPTION"] = "Gewinne alle Herausforderungen der Heldenhaft-Modus-Kampagne.",
["ACHIEVEMENT_AGE_OF_HEROES_NAME"] = "Zeitalter der Helden",
["ACHIEVEMENT_ALL_THE_SMALL_THINGS_DESCRIPTION"] = "Eliminiere 182 Blinker.",
["ACHIEVEMENT_ALL_THE_SMALL_THINGS_NAME"] = "Alle kleinen Dinge",
["ACHIEVEMENT_ARACHNED_DESCRIPTION"] = "Besiege Mygale, die Spinnenkönigin.",
["ACHIEVEMENT_ARACHNED_NAME"] = "Ausgesponnen",
["ACHIEVEMENT_A_COON_OF_SURPRISES_DESCRIPTION"] = "Hilf Fredo zu entkommen.",
["ACHIEVEMENT_A_COON_OF_SURPRISES_NAME"] = "Eine Kokon voller Überraschungen",
["ACHIEVEMENT_A_TEST_OF_PROWESS_DESCRIPTION"] = "Gewinne eine Stufe mit 3 Sternen.",
["ACHIEVEMENT_A_TEST_OF_PROWESS_NAME"] = "Ein Test der Fertigkeit",
["ACHIEVEMENT_BREAKER_OF_CHAINS_DESCRIPTION"] = "Rette die vier Elfen in den Karminminen.",
["ACHIEVEMENT_BREAKER_OF_CHAINS_NAME"] = "Kettenbrecher",
["ACHIEVEMENT_BUTTERTENTACLES_DESCRIPTION"] = "Schließe den Schandfleck-Turm ab, während du verhinderst, dass Mydrias deine Einheiten fängt.",
["ACHIEVEMENT_BUTTERTENTACLES_NAME"] = "Rutschige Soldaten",
["ACHIEVEMENT_BYE_BYE_BEAUTIFUL_DESCRIPTION"] = "Besiege Seherin Mydrias.",
["ACHIEVEMENT_BYE_BYE_BEAUTIFUL_NAME"] = "Auf Wiedersehen, Auf Wiedersehen, Schöne",
["ACHIEVEMENT_CIRCLE_OF_LIFE_DESCRIPTION"] = "Besucht die Vorstellung des neugeborenen Arborean.",
["ACHIEVEMENT_CIRCLE_OF_LIFE_NAME"] = "Kreis des Lebens",
["ACHIEVEMENT_CLEANSE_THE_KING_DESCRIPTION"] = "Rette den Linireanischen König.",
["ACHIEVEMENT_CLEANSE_THE_KING_NAME"] = "Ruhm dem König",
["ACHIEVEMENT_CLEANUP_IS_OPTIONAL_DESCRIPTION"] = "Schließe die Verwüsteten Außenbezirke ab, ohne den Schutt von den strategischen Punkten zu räumen.",
["ACHIEVEMENT_CLEANUP_IS_OPTIONAL_NAME"] = "Aufräumen ist optional",
["ACHIEVEMENT_CONJUNTIVICTORY_DESCRIPTION"] = "Besiege den Aufseher.",
["ACHIEVEMENT_CONJUNTIVICTORY_NAME"] = "Konjunktivsieger",
["ACHIEVEMENT_CONQUEROR_OF_THE_VOID_DESCRIPTION"] = "Erreiche 3 Sterne in jeder Stufe des Jenseits der Leere.",
["ACHIEVEMENT_CONQUEROR_OF_THE_VOID_NAME"] = "Eroberer des Nichts",
["ACHIEVEMENT_CRAFTING_IN_THE_MINES_DESCRIPTION"] = "Sammle alle drei Schweinekoteletts im Wildtierbau.",
["ACHIEVEMENT_CRAFTING_IN_THE_MINES_NAME"] = "Handwerk in den Minen",
["ACHIEVEMENT_CROWD_CONTROL_DESCRIPTION"] = "Schließe das Tal der Verderbnis ab, ohne dass ein Fleisch-Behemoth aus der Grube auftaucht.",
["ACHIEVEMENT_CROWD_CONTROL_NAME"] = "Kontrolle über Menschenmassen",
["ACHIEVEMENT_CROW_SCARER_DESCRIPTION"] = "Verscheuche alle Krähen im Düsteren Tal.",
["ACHIEVEMENT_CROW_SCARER_NAME"] = "Vogelscheuche",
["ACHIEVEMENT_CRYSTAL_CLEAR_DESCRIPTION"] = "Erhalte in jeder Phase des Verlassenen Canyons 3 Sterne.",
["ACHIEVEMENT_CRYSTAL_CLEAR_NAME"] = "Kristallklar",
["ACHIEVEMENT_DARK_LIEUTENANT_DESCRIPTION"] = "Erreiche Level 10 mit Raelyn.",
["ACHIEVEMENT_DARK_LIEUTENANT_NAME"] = "Dunkler Leutnant",
["ACHIEVEMENT_DARK_RUTHLESSNESS_DESCRIPTION"] = "Gewinne eine Stufe, indem du nur Dunkle Armee Türme und Helden verwendest.",
["ACHIEVEMENT_DARK_RUTHLESSNESS_NAME"] = "Dunkle Rücksichtslosigkeit",
["ACHIEVEMENT_DISTURBING_THE_PEACE_DESCRIPTION"] = "Unterbreche die Mittagspause der Arbeiter in der Herrschaftskuppel",
["ACHIEVEMENT_DISTURBING_THE_PEACE_NAME"] = "Störung des Friedens",
["ACHIEVEMENT_DLC1_WIN_BOSS_DESCRIPTION"] = "Besiege Grymbeard und stoppe den Bau der Kriegsmaschine.",
["ACHIEVEMENT_DLC1_WIN_BOSS_NAME"] = "Selbstarbeitslosigkeit",
["ACHIEVEMENT_DLC2_GATHER_ENVELOPS_DESCRIPTION"] = "Sammle 8 Hongbaos auf der Sturminsel.",
["ACHIEVEMENT_DLC2_GATHER_ENVELOPS_NAME"] = "Wir wünschen Ihnen Reichtum und Wohlstand.",
["ACHIEVEMENT_DLC2_WIN_BOSS_KING_DESCRIPTION"] = "Besiegt den Stierdämonenkönig in seiner Festung.",
["ACHIEVEMENT_DLC2_WIN_BOSS_KING_NAME"] = "Die Rückkehr des Affenkönigs",
["ACHIEVEMENT_DLC2_WIN_BOSS_PRINCESS_DESCRIPTION"] = "Besiege Prinzessin Eisenfächer und ihre Wasserarmee.",
["ACHIEVEMENT_DLC2_WIN_BOSS_PRINCESS_NAME"] = "Ein böser Wind kommt auf",
["ACHIEVEMENT_DLC2_WIN_BOSS_REDBOY_DESCRIPTION"] = "Besiege den Roten Jungen und seine Feuerarmee.",
["ACHIEVEMENT_DLC2_WIN_BOSS_REDBOY_NAME"] = "Alles hat sich verändert...",
["ACHIEVEMENT_DOMO_ARIGATO_DESCRIPTION"] = "Lass 20 Feinde von der riesigen Faust im Kolossalen Kern zerquetschen.",
["ACHIEVEMENT_DOMO_ARIGATO_NAME"] = "Domo Arigato",
["ACHIEVEMENT_FACTORY_STRIKE_DESCRIPTION"] = "Schließe die Hektische Fertigung ab, während du verhinderst das Grymbeard das Laufband aktiviert.",
["ACHIEVEMENT_FACTORY_STRIKE_NAME"] = "Fabrikstreik",
["ACHIEVEMENT_FIELD_TRIP_RUINER_DESCRIPTION"] = "Lösche das Feuer des Campers.",
["ACHIEVEMENT_FIELD_TRIP_RUINER_NAME"] = "Ausflugsverderber",
["ACHIEVEMENT_FOREST_PROTECTOR_DESCRIPTION"] = "Erreiche Level 10 mit Nyru.",
["ACHIEVEMENT_FOREST_PROTECTOR_NAME"] = "Waldschützer",
["ACHIEVEMENT_GARBAGE_DISPOSAL_DESCRIPTION"] = "Eliminiere 10 Verrückte Bastler, bevor sie Schrott-Drohnen erschaffen können.",
["ACHIEVEMENT_GARBAGE_DISPOSAL_NAME"] = "Müllentsorgung",
["ACHIEVEMENT_GEM_SPILLER_DESCRIPTION"] = "Zerbreche alle Edelsteinkörbe.",
["ACHIEVEMENT_GEM_SPILLER_NAME"] = "Edelsteinverschwender",
["ACHIEVEMENT_GET_THE_PARTY_STARTED_DESCRIPTION"] = "Löse das Rätsel und beschwöre die Band.",
["ACHIEVEMENT_GET_THE_PARTY_STARTED_NAME"] = "Starte die Party",
["ACHIEVEMENT_GIFT_OF_LIFE_DESCRIPTION"] = "Befreie das Klon-Experiment in der Replikationskammer.",
["ACHIEVEMENT_GIFT_OF_LIFE_NAME"] = "Das Geschenk des Lebens",
["ACHIEVEMENT_GREENLIT_ALLIES_DESCRIPTION"] = "Beschwöre 10 Arboreanische Dornenspeere.",
["ACHIEVEMENT_GREENLIT_ALLIES_NAME"] = "Grüne Verbündete",
["ACHIEVEMENT_HAIL_TO_THE_K_BABY_DESCRIPTION"] = "Finde den Krokodilkönig.",
["ACHIEVEMENT_HAIL_TO_THE_K_BABY_NAME"] = "Heil K, Baby!",
["ACHIEVEMENT_HEARTLESS_VICTORY_DESCRIPTION"] = "Vollende das Herz des Waldes, ohne die Kraft des Herzens der Arboreer zu nutzen.",
["ACHIEVEMENT_HEARTLESS_VICTORY_NAME"] = "Herzloser Sieg",
["ACHIEVEMENT_INTO_THE_OGREVERSE_DESCRIPTION"] = "Entdecke die Geheimnisse der mysteriösen Spinnenperson.",
["ACHIEVEMENT_INTO_THE_OGREVERSE_NAME"] = "Unfreundlicher Nachbar",
["ACHIEVEMENT_IRONCLAD_DESCRIPTION"] = "Gewinne alle Herausforderungen der Eisen-Modus-Kampagne.",
["ACHIEVEMENT_IRONCLAD_NAME"] = "Gepanzert",
["ACHIEVEMENT_ITS_A_SECRET_TO_EVERYONE_DESCRIPTION"] = "Hilf Lank, 5 Rupien zu fischen.",
["ACHIEVEMENT_ITS_A_SECRET_TO_EVERYONE_NAME"] = "Es ist ein Geheimnis für alle",
["ACHIEVEMENT_KEPT_YOU_WAITING_DESCRIPTION"] = "Finde den listigen Soldaten im Kolossalen Kern.",
["ACHIEVEMENT_KEPT_YOU_WAITING_NAME"] = "Hast lange gewartet, nicht?",
["ACHIEVEMENT_LEARNING_THE_ROPES_DESCRIPTION"] = "Beenden Sie das Tutorial mit 3 Sternen.",
["ACHIEVEMENT_LEARNING_THE_ROPES_NAME"] = "Die Seile lernen",
["ACHIEVEMENT_LINIREAN_RESISTANCE_DESCRIPTION"] = "Gewinne eine Stufe, indem du nur Linireaner Türme und Helden verwendest.",
["ACHIEVEMENT_LINIREAN_RESISTANCE_NAME"] = "Linireanischer Widerstand",
["ACHIEVEMENT_LUCAS_SPIDER_DESCRIPTION"] = "Spiele mit Lucus, bis er glücklich ist.",
["ACHIEVEMENT_LUCAS_SPIDER_NAME"] = "Lucus die Spinne",
["ACHIEVEMENT_MASTER_TACTICIAN_DESCRIPTION"] = "Schließe die Kampagne auf Unmöglicher Schwierigkeit ab.",
["ACHIEVEMENT_MASTER_TACTICIAN_NAME"] = "Meistertaktiker",
["ACHIEVEMENT_MECHANICAL_BURNOUT_DESCRIPTION"] = "Überfüttere die Maschine in den Dunkelstahl-Toren.",
["ACHIEVEMENT_MECHANICAL_BURNOUT_NAME"] = "Mechanische Erschöpfung",
["ACHIEVEMENT_MIGHTY_III_DESCRIPTION"] = "Töte 10000 Feinde.",
["ACHIEVEMENT_MIGHTY_III_NAME"] = "Am mächtigsten",
["ACHIEVEMENT_MIGHTY_II_DESCRIPTION"] = "Töte 3000 Feinde.",
["ACHIEVEMENT_MIGHTY_II_NAME"] = "Mächtiger",
["ACHIEVEMENT_MIGHTY_I_DESCRIPTION"] = "Töte 500 Feinde.",
["ACHIEVEMENT_MIGHTY_I_NAME"] = "Mächtig",
["ACHIEVEMENT_MOST_DELICIOUS_DESCRIPTION"] = "Gib Biggie dem Arboreaner etwas Honig.",
["ACHIEVEMENT_MOST_DELICIOUS_NAME"] = "Am Leckersten",
["ACHIEVEMENT_NATURES_WRATH_DESCRIPTION"] = "Töte 30 Feinde mit dem Herzen der Arboreaner.",
["ACHIEVEMENT_NATURES_WRATH_NAME"] = "Zorn der Natur",
["ACHIEVEMENT_NONE_SHALL_PASS_DESCRIPTION"] = "Vollende den Wildtierbau, ohne dass zusätzliche Feinde durch die Tür gehen.",
["ACHIEVEMENT_NONE_SHALL_PASS_NAME"] = "Niemand wird passieren!",
["ACHIEVEMENT_NOT_A_MOMENT_TO_WASTE_DESCRIPTION"] = "Rufe 15 Wellen früher.",
["ACHIEVEMENT_NOT_A_MOMENT_TO_WASTE_NAME"] = "Keinen Moment zu verlieren",
["ACHIEVEMENT_NO_FLY_ZONE_DESCRIPTION"] = "Töte 50 Ballonspinnen.",
["ACHIEVEMENT_NO_FLY_ZONE_NAME"] = "Flugverbotszone",
["ACHIEVEMENT_OBLITERATE_DESCRIPTION"] = "Finde die Teile des verbotenen Roboters in jeder Stufe der Kolossalen Bedrohung.",
["ACHIEVEMENT_OBLITERATE_NAME"] = "Vernichte!",
["ACHIEVEMENT_ONE_SHOT_TOWER_DESCRIPTION"] = "Eliminiere 10 Feinde mit einem einzigen Strahl des Dunkelstrahl-Turms.",
["ACHIEVEMENT_ONE_SHOT_TOWER_NAME"] = "Ein Schuss auf Ruhm",
["ACHIEVEMENT_OUTBACK_BARBEQUICK_DESCRIPTION"] = "Besiege Goregrind, bevor du auf Unmögliche Schwierigkeit springst.",
["ACHIEVEMENT_OUTBACK_BARBEQUICK_NAME"] = "Verwickelt",
["ACHIEVEMENT_OVER_THE_EDGE_DESCRIPTION"] = "Stoße die Arboreaner von den Baumwipfeln.",
["ACHIEVEMENT_OVER_THE_EDGE_NAME"] = "Spiel vorbei",
["ACHIEVEMENT_OVINE_JOURNALISM_DESCRIPTION"] = "Finde Sheepy in jedem Kampagnengebiet.",
["ACHIEVEMENT_OVINE_JOURNALISM_NAME"] = "Schaf-Journalismus",
["ACHIEVEMENT_PEST_CONTROL_DESCRIPTION"] = "Töte 300 Blicklein.",
["ACHIEVEMENT_PEST_CONTROL_NAME"] = "Schädlingsbekämpfung",
["ACHIEVEMENT_PLAYFUL_FRIENDS_DESCRIPTION"] = "Spiele \"vergraben\" mit allen Arboreanern im Herzen des Waldes.",
["ACHIEVEMENT_PLAYFUL_FRIENDS_NAME"] = "Verspielte Freunde",
["ACHIEVEMENT_PORKS_OFF_THE_MENU_DESCRIPTION"] = "Besiege Goregrind.",
["ACHIEVEMENT_PORKS_OFF_THE_MENU_NAME"] = "Schweinefleisch ist nicht mehr im Angebot",
["ACHIEVEMENT_PROMOTION_DENIED_DESCRIPTION"] = "Töte 30 Kultpriester, bevor sie sich in Abscheulichkeiten verwandeln.",
["ACHIEVEMENT_PROMOTION_DENIED_NAME"] = "Beförderung Verweigert",
["ACHIEVEMENT_ROCK_BEATS_ROCK_DESCRIPTION"] = "Lass die Statue gegen sich selbst gewinnen.",
["ACHIEVEMENT_ROCK_BEATS_ROCK_NAME"] = "Stein schlägt... Stein?",
["ACHIEVEMENT_ROOM_achievement_claim"] = "Belohnung einfordern!",
["ACHIEVEMENT_ROYAL_CAPTAIN_DESCRIPTION"] = "Erreiche Level 10 mit Vesper.",
["ACHIEVEMENT_ROYAL_CAPTAIN_NAME"] = "Königlicher Kapitän",
["ACHIEVEMENT_RUNEQUEST_DESCRIPTION"] = "Aktiviere alle sechs Runen im ganzen Everadiant Wald.",
["ACHIEVEMENT_RUNEQUEST_NAME"] = "Runequest",
["ACHIEVEMENT_RUST_IN_PEACE_DESCRIPTION"] = "Schließe eine Stufe ab, ohne dass eine Lebendige Rüstung wiederbelebt wird.",
["ACHIEVEMENT_RUST_IN_PEACE_NAME"] = "Roste in Frieden",
["ACHIEVEMENT_SAVIOUR_OF_THE_FOREST_DESCRIPTION"] = "Gewinne die Stufe, ohne irgendwelche arborischen Blumen zu verlieren.",
["ACHIEVEMENT_SAVIOUR_OF_THE_FOREST_NAME"] = "Retter des Waldes",
["ACHIEVEMENT_SAVIOUR_OF_THE_GREEN_DESCRIPTION"] = "Erhalte 3 Sterne in jeder Phase des Everadiant Waldes.",
["ACHIEVEMENT_SAVIOUR_OF_THE_GREEN_NAME"] = "Retter des Grünen",
["ACHIEVEMENT_SCRAMBLED_EGGS_DESCRIPTION"] = "Töte 50 Krokinder, bevor sie schlüpfen.",
["ACHIEVEMENT_SCRAMBLED_EGGS_NAME"] = "Rühreier",
["ACHIEVEMENT_SEASONED_GENERAL_DESCRIPTION"] = "Schließe die Kampagne auf Veteran-Schwierigkeit ab.",
["ACHIEVEMENT_SEASONED_GENERAL_NAME"] = "Erfahrener General",
["ACHIEVEMENT_SEE_YA_LATER_ALLIGATOR_DESCRIPTION"] = "Besiege Abominor, den Verschlinger.",
["ACHIEVEMENT_SEE_YA_LATER_ALLIGATOR_NAME"] = "Bis später, Alligator",
["ACHIEVEMENT_SHUT_YOUR_MOUTH_DESCRIPTION"] = "Schließe die Herrschaftskuppel ab, während du verhinderst, dass Grymbeard deine Türme in Brand setzt.",
["ACHIEVEMENT_SHUT_YOUR_MOUTH_NAME"] = "Halt den Mund!",
["ACHIEVEMENT_SIGNATURE_TECHNIQUES_DESCRIPTION"] = "Benutze Heldenkräfte 500 Mal.",
["ACHIEVEMENT_SIGNATURE_TECHNIQUES_NAME"] = "Signaturtechniken",
["ACHIEVEMENT_SILVER_FOR_MONSTERS_DESCRIPTION"] = "Hilf Gerhart, das Baummonster zu töten.",
["ACHIEVEMENT_SILVER_FOR_MONSTERS_NAME"] = "Silber für Monster",
["ACHIEVEMENT_SMOOTH_OPER_GATOR_DESCRIPTION"] = "Hilf dem freundlichen Alligator, sein Boot zu starten.",
["ACHIEVEMENT_SMOOTH_OPER_GATOR_NAME"] = "Glatter Oper-Gator",
["ACHIEVEMENT_SPECTRAL_FURY_DESCRIPTION"] = "Besiege Navira und stoppe die Invasion der Wiederkehrer.",
["ACHIEVEMENT_SPECTRAL_FURY_NAME"] = "Spektraler Zorn",
["ACHIEVEMENT_STARLIGHT_DESCRIPTION"] = "Hilf Fredo und Sammy, der Riesenspinne zu entkommen.",
["ACHIEVEMENT_STARLIGHT_NAME"] = "Sternenlicht",
["ACHIEVEMENT_TAKE_ME_HOME_DESCRIPTION"] = "Schicke Riff, den Goblin, zurück in seine Heimatdimension.",
["ACHIEVEMENT_TAKE_ME_HOME_NAME"] = "Nimm mich",
["ACHIEVEMENT_THE_CAVALRY_IS_HERE_DESCRIPTION"] = "Rufe 1000 Verstärkungen herbei.",
["ACHIEVEMENT_THE_CAVALRY_IS_HERE_NAME"] = "Die Kavallerie ist da!",
["ACHIEVEMENT_TIPPING_THE_SCALES_DESCRIPTION"] = "Wirf Robin Wood in den Fluss.",
["ACHIEVEMENT_TIPPING_THE_SCALES_NAME"] = "Die Waage kippen",
["ACHIEVEMENT_TREE_HUGGER_DESCRIPTION"] = "Schließe die Nebelruinen ab, während mindestens eine Wirrwurzel noch steht.",
["ACHIEVEMENT_TREE_HUGGER_NAME"] = "Baumfreund",
["ACHIEVEMENT_TURN_A_BLIND_EYE_DESCRIPTION"] = "Töte 100 Korruptionsschösslinge, während sie unter dem Einfluss des Blickes stehen.",
["ACHIEVEMENT_TURN_A_BLIND_EYE_NAME"] = "Ein Auge Zudrücken",
["ACHIEVEMENT_UNBOUND_VICTORY_DESCRIPTION"] = "Beende die Verdammte Kreuzung, ohne dass sich irgendein Albtraum in einen Gebundener Albtraum verwandelt.",
["ACHIEVEMENT_UNBOUND_VICTORY_NAME"] = "Ungebundener Sieg",
["ACHIEVEMENT_UNENDING_RICHES_DESCRIPTION"] = "Sammle insgesamt 150000 Gold.",
["ACHIEVEMENT_UNENDING_RICHES_NAME"] = "Unendlicher Reichtum",
["ACHIEVEMENT_UNTAMED_BEAST_DESCRIPTION"] = "Erreiche Level 10 mit Grimson.",
["ACHIEVEMENT_UNTAMED_BEAST_NAME"] = "Ungezähmtes Biest",
["ACHIEVEMENT_WAR_MASONRY_DESCRIPTION"] = "Baue 100 Türme.",
["ACHIEVEMENT_WAR_MASONRY_NAME"] = "Kriegsmauerwerk",
["ACHIEVEMENT_WEIRDER_THINGS_DESCRIPTION"] = "Hilf Ernie und Daston, die Blinker in den Verheerten Ackerländer abzuwehren.",
["ACHIEVEMENT_WEIRDER_THINGS_NAME"] = "Seltsamere Dinge",
["ACHIEVEMENT_WE_ARE_ALL_MAD_HERE_DESCRIPTION"] = "Finde die schwer fassbare Katze in jeder Stufe der „Unsterblicher Zorn“-Kampagne.",
["ACHIEVEMENT_WE_ARE_ALL_MAD_HERE_NAME"] = "Wir sind alle verrückt hier",
["ACHIEVEMENT_WE_RE_NOT_GONNA_TAKE_IT_DESCRIPTION"] = "Töte 15 Verdorbene Schwestern, bevor sie einen Albtraum beschwören können.",
["ACHIEVEMENT_WE_RE_NOT_GONNA_TAKE_IT_NAME"] = "Wir werden es nicht hinnehmen",
["ACHIEVEMENT_WOBBA_LUBBA_DUB_DUB_DESCRIPTION"] = "Repariere Nick und Martys Portalpistole.",
["ACHIEVEMENT_WOBBA_LUBBA_DUB_DUB_NAME"] = "Wobba-Lubba-Dub-Dub!",
["ACHIEVEMENT_YOU_SHALL_NOT_CAST_DESCRIPTION"] = "Besiege den verdorbenen Denas, ohne dass Seherin Mydrias in Unmöglicher Schwierigkeit Projektionen beschwört.",
["ACHIEVEMENT_YOU_SHALL_NOT_CAST_NAME"] = "Du Sollst Nicht Zaubern!",
["ADS_MESSAGE_OK"] = "O.K.",
["ADS_MESSAGE_TITLE"] = "MEHR JUWELEN",
["ADS_NO_REWARD_VIDEO_AVAILABLE"] = "Momentan ist kein Belohnungsvideo verfügbar. Bitte versuche es später erneut.",
["ADS_REWARD_EARNED"] = "Du erhältst %i Juwelen für das Ansehen des Videos",
["ADVANCED TOWERS"] = "VERBESSERTE TÜRME",
["ALERT_VERSION"] = "Eine neuere Version des Spiels ist verfügbar. Bitte lade sie im Store herunter.",
["ALL FOR"] = "ALLES FÜR",
["ARCHER TOWER"] = "SCHÜTZEN-TURM",
["ARE YOU SURE YOU WANT TO QUIT?"] = "MÖCHTEST DU DAS SPIEL WIRKLICH BEENDEN?",
["ARMORED ENEMIES!"] = "Gepanzerte Feinde",
["ARTILLERY"] = "ARTILLERIE",
["Achievements"] = "Erfolge",
["BARRACKS"] = "KASERNE",
["BASIC TOWERS"] = "NORMALE TÜRME",
["BEST VALUE"] = "BESTER WERT",
["BOSS_BULL_KING_DESCRIPTION"] = "Ein rücksichtsloser und autoritärer Anführer, Kriegsveteran und pragmatischer Stratege. Berühmt für seine enorme Stärke, seinen nachtragenden Charakter und seine kriegerische Tüchtigkeit.",
["BOSS_BULL_KING_EXTRA"] = "-Hohe Rüstung und Magieresistenz\n- Großer Flächenbetäubungseffekt auf Einheiten und Türme",
["BOSS_BULL_KING_NAME"] = "Stierdämonenkönig",
["BOSS_CORRUPTED_DENAS_DESCRIPTION"] = "Der besiegte König von Linirea, nun verwandelt in eine gewaltige Abscheulichkeit durch die dunklen Mächte des Kultes des Aufsehers.",
["BOSS_CORRUPTED_DENAS_EXTRA"] = "- Erschafft Blickleine",
["BOSS_CORRUPTED_DENAS_NAME"] = "Verdorbener Denas",
["BOSS_CROCS_DESCRIPTION"] = "Der personifizierte Hunger, Ein uraltes Wesen, das in der Lage ist, die Welt selbst zu verschlingen, wenn es nicht in Schach gehalten wird.",
["BOSS_CROCS_EXTRA"] = "- Frisst Türme\n- Entwickelt sich weiter, nachdem sein Hunger gestillt ist\n- Beschwört Krokinder",
["BOSS_CROCS_LVL1_DESCRIPTION"] = "Der personifizierte Hunger, Ein uraltes Wesen, das in der Lage ist, die Welt selbst zu verschlingen, wenn es nicht in Schach gehalten wird.",
["BOSS_CROCS_LVL1_EXTRA"] = "- Frisst Türme\n- Entwickelt sich weiter, nachdem sein Hunger gestillt ist\n- Beschwört Krokinder",
["BOSS_CROCS_LVL1_NAME"] = "Abominor",
["BOSS_CROCS_LVL2_DESCRIPTION"] = "Der personifizierte Hunger, Ein uraltes Wesen, das in der Lage ist, die Welt selbst zu verschlingen, wenn es nicht in Schach gehalten wird.",
["BOSS_CROCS_LVL2_EXTRA"] = "- Frisst Türme\n- Entwickelt sich weiter, nachdem sein Hunger gestillt ist\n- Beschwört Krokinder",
["BOSS_CROCS_LVL2_NAME"] = "Abominor",
["BOSS_CROCS_LVL3_DESCRIPTION"] = "Der personifizierte Hunger, Ein uraltes Wesen, das in der Lage ist, die Welt selbst zu verschlingen, wenn es nicht in Schach gehalten wird.",
["BOSS_CROCS_LVL3_EXTRA"] = "- Frisst Türme\n- Entwickelt sich weiter, nachdem sein Hunger gestillt ist\n- Beschwört Krokinder",
["BOSS_CROCS_LVL3_NAME"] = "Abominor",
["BOSS_CROCS_LVL4_DESCRIPTION"] = "Der personifizierte Hunger, Ein uraltes Wesen, das in der Lage ist, die Welt selbst zu verschlingen, wenn es nicht in Schach gehalten wird.",
["BOSS_CROCS_LVL4_EXTRA"] = "- Frisst Türme\n- Entwickelt sich weiter, nachdem sein Hunger gestillt ist\n- Beschwört Krokinder",
["BOSS_CROCS_LVL4_NAME"] = "Abominor",
["BOSS_CROCS_LVL5_DESCRIPTION"] = "Der personifizierte Hunger, Ein uraltes Wesen, das in der Lage ist, die Welt selbst zu verschlingen, wenn es nicht in Schach gehalten wird.",
["BOSS_CROCS_LVL5_EXTRA"] = "- Frisst Türme\n- Entwickelt sich weiter, nachdem sein Hunger gestillt ist\n- Beschwört Krokinder",
["BOSS_CROCS_LVL5_NAME"] = "Abominor",
["BOSS_CROCS_NAME"] = "Abominor",
["BOSS_CULT_LEADER_DESCRIPTION"] = "Der aktuelle Anführer des Kultes, Mydrias, agiert als die Hand des Aufsehers und orchestriert die Invasion der Welten.",
["BOSS_CULT_LEADER_EXTRA"] = "- Hohe Rüstung und Magieresistenz, solange sie nicht blockiert ist\n - Hoher Flächenschaden",
["BOSS_CULT_LEADER_NAME"] = "Seherin Mydrias",
["BOSS_GRYMBEARD_DESCRIPTION"] = "Ein egomanischer Zwerg mit Größenwahn, der ebenso gefährlich wie verrückt ist.",
["BOSS_GRYMBEARD_EXTRA"] = "- Feuert eine Raketenfaust auf Spielereinheiten.",
["BOSS_GRYMBEARD_NAME"] = "Grymbeard",
["BOSS_MACHINIST_DESCRIPTION"] = "Auf seiner neuesten Erfindung jagt Grymbeard seine Feinde und lässt Feuer und Metall regnen.",
["BOSS_MACHINIST_EXTRA"] = "- Fliegend\n- Feuert Schrott auf Einheiten",
["BOSS_MACHINIST_NAME"] = "Grymbeard",
["BOSS_NAVIRA_DESCRIPTION"] = "Vom Pfad der Gnade abgefallen und die verbotenen Kräfte der Todesmagie anzapfend, sucht Navira danach, den Elfen ihren Ruhm zurückzubringen.",
["BOSS_NAVIRA_EXTRA"] = "- Blockiert Türme mit Feuerbällen\n- Verwandelt sich in einen unblockbaren Tornado",
["BOSS_NAVIRA_NAME"] = "Navira",
["BOSS_PIG_DESCRIPTION"] = "Der einzig wahre selbsternannte König der Wildbestien verwendet einen riesigen Morgenstern, um seine Feinde zu zermalmen.",
["BOSS_PIG_EXTRA"] = "- Springt große Distanzen über Pfade",
["BOSS_PIG_NAME"] = "Goregrind",
["BOSS_PRINCESS_IRON_FAN_DESCRIPTION"] = "Elegant und doch tödlich, Prinzessin Eisenfächer führt den legendären Eisenfächer, der Flammen löschen und Stürme verursachen kann.",
["BOSS_PRINCESS_IRON_FAN_EXTRA"] = "- Klont sich selbst\n- Sperrt Helden in eine Flasche\n- Verwandelt Türme in Feind-Erzeuger",
["BOSS_PRINCESS_IRON_FAN_NAME"] = "Princess Iron Fan",
["BOSS_REDBOY_TEEN_DESCRIPTION"] = "Der feurige und stolze junge Dämonenprinz. Hitzköpfig, überheblich und unerbittlich ehrgeizig. Meister des Samadhi-Feuers und geübter Speerkämpfer.",
["BOSS_REDBOY_TEEN_EXTRA"] = "- Großer Flächenschaden-Angriff\n- Befiehlt seinem Drachen, Türme zu betäuben",
["BOSS_REDBOY_TEEN_NAME"] = "Red Boy",
["BOSS_SPIDER_QUEEN_DESCRIPTION"] = "Eine uralte Spinnenkönigin, eine primordiale Kraft, erwacht aus ihrem Schlummer, um sich zurück zu holen, was rechtmäßig ihr gehört.",
["BOSS_SPIDER_QUEEN_EXTRA"] = "- Betäubt Türme\n- Entzieht Leben von Feinden in der Nähe\n- Beschwört Lebensraubspinnen\n- Wirft Netze in deine Augen",
["BOSS_SPIDER_QUEEN_NAME"] = "Mygale",
["BRIEFING_LEVEL_WARNING"] = "Neue Kampagne!",
["BUTTON_BUG_CRASH"] = "SPIELDEFEKT",
["BUTTON_BUG_OTHER"] = "ANDERER",
["BUTTON_BUG_REPORT"] = "FEHLER",
["BUTTON_BUY"] = "KAUFEN",
["BUTTON_BUY_UPGRADE"] = "VERBESSERUNG KAUFEN",
["BUTTON_CLOSE"] = "SCHLIESSEN",
["BUTTON_CONFIRM"] = "BESTÄTIGEN",
["BUTTON_CONTINUE"] = "WEITER",
["BUTTON_DISABLE"] = "Deaktivieren",
["BUTTON_DONE"] = "FERTIG",
["BUTTON_ENDLESS_QUIT"] = "BEENDEN",
["BUTTON_ENDLESS_TRYAGAIN"] = "ERNEUT VERSUCHEN",
["BUTTON_GET_GEMS"] = "GEGENSTÄNDE HOLEN",
["BUTTON_LEVEL_SELECT_FIGHT"] = "KÄMPFE!",
["BUTTON_LOST_CONTENT"] = "VERLORENER INHALT",
["BUTTON_MAIN_MENU"] = "HAUPTMENÜ",
["BUTTON_NO"] = "NEIN",
["BUTTON_OK"] = "O.K.!",
["BUTTON_QUIT"] = "BEENDEN",
["BUTTON_RESET"] = "VERLERNEN",
["BUTTON_RESTART"] = "NEUSTART",
["BUTTON_UNDO"] = "RÜCKGÄNGIG",
["BUTTON_YES"] = "JA",
["BUY UPGRADES!"] = "KAUF VERBESSERUNGEN!",
["Basic Tower Types"] = "Turm-Grundarten",
["CARD_REWARDS_CAMPAIGN"] = "Neue Kampagne!",
["CARD_REWARDS_DLC_1"] = "Kolossale Bedrohung",
["CARD_REWARDS_DLC_2"] = "Wukongs Reise",
["CARD_REWARDS_HERO"] = "NEUER HELD!",
["CARD_REWARDS_TOWER"] = "NEUER TURM!",
["CARD_REWARDS_TOWER_LEVEL"] = "NEUE TURMSTUFE!",
["CARD_REWARDS_TOWER_LEVEL_PREFIX"] = "LVL.",
["CARD_REWARDS_UPDATE_01"] = "Unsterblicher Zorn",
["CARD_REWARDS_UPDATE_02"] = "Uralter Hunger",
["CARD_REWARDS_UPDATE_03"] = "Arachnophobie",
["CARD_REWARDS_UPGRADES"] = "UPGRADE-PUNKTE!",
["CArmor0"] = "Keine",
["CArmor1"] = "Niedrig",
["CArmor2"] = "Mittel",
["CArmor3"] = "Hoch",
["CArmor4"] = "Großartig",
["CArmor9"] = "Immun",
["CArmorSmall0"] = "Kei.",
["CArmorSmall1"] = "Nie.",
["CArmorSmall2"] = "Mi.",
["CArmorSmall3"] = "Hoc.",
["CArmorSmall4"] = "Gro.",
["CArmorSmall9"] = "Imm.",
["CHALLENGE_RULE_DIFFICULTY_CASUAL"] = "Einfach",
["CHALLENGE_RULE_DIFFICULTY_IMPOSSIBLE"] = "Unmöglich",
["CHALLENGE_RULE_DIFFICULTY_NORMAL"] = "Normal",
["CHALLENGE_RULE_DIFFICULTY_VETERAN"] = "Veteran",
["CHANGE_LANGUAGE_QUESTION"] = "Möchtest du die Spracheinstellungen wirklich ändern?",
["CINEMATICS_TAP_TO_CONTINUE"] = "Zum Fortfahren tippen ...",
["CINEMATICS_TAP_TO_CONTINUE_KR1"] = "Zum Fortfahren tippen ...",
["CINEMATICS_TAP_TO_CONTINUE_KR2"] = "Zum Fortfahren tippen ...",
["CINEMATICS_TAP_TO_CONTINUE_KR3"] = "Zum Fortfahren tippen ...",
["CINEMATICS_TAP_TO_CONTINUE_KR5"] = "Zum Fortfahren tippen ...",
["CLAIM_GIFT"] = "Geschenk einlösen",
["CLOUDSYNC_PLEASE_WAIT"] = "Cloud-Speicher wird aktualisiert ...",
["CLOUD_DIALOG_NO"] = "Nein",
["CLOUD_DIALOG_OK"] = "O.K.",
["CLOUD_DIALOG_YES"] = "Ja",
["CLOUD_DOWNLOAD_QUESTION"] = "Gespeichertes Spiel aus iCloud laden?",
["CLOUD_DOWNLOAD_TITLE"] = "Aus iCloud laden",
["CLOUD_SAVE"] = "Cloud-Speicher",
["CLOUD_SAVE_DISABLE_EXTRA"] = "Hinweis: Du könntest beim Deinstallieren des Spiels deinen Spielfortschritt verlieren.",
["CLOUD_SAVE_DISABLE_GENERIC_DESCRIPTION"] = "Möchtest du das Speichern deines Spielfortschritts in der Cloud wirklich deaktivieren?",
["CLOUD_SAVE_OFF"] = "Cloud aus",
["CLOUD_SAVE_ON"] = "Cloud an",
["CLOUD_UPLOAD_QUESTION"] = "Gespeichertes Spiel in iCloud hochladen?",
["CLOUD_UPLOAD_TITLE"] = "In iCloud hochladen",
["COMIC_10_1_KR5_KR5"] = "Lasst mich frei! Ich tue, was das Beste für das Königreich ist!",
["COMIC_10_2_KR5_KR5"] = "Hör auf mit dieser Blasphemie, Bruder. Das ist nicht der Weg der Elfen.",
["COMIC_10_3_KR5_KR5"] = "Danke, mein alter Lehrling. Wir übernehmen ab hier.",
["COMIC_10_4_KR5_KR5"] = "Später, im Lager...",
["COMIC_10_5_KR5_KR5"] = "Also... du bist dir sicher, dass Vez'nan vertrauenswürdig ist?",
["COMIC_10_6_KR5_KR5"] = "Wir haben ein Auge auf ihn...",
["COMIC_10_7_KR5_KR5"] = "...aber er scheint sich im Moment gut zu verhalten.",
["COMIC_10_8_KR5_KR5"] = "Heh. Im Moment...",
["COMIC_11_1_KR5_KR5"] = "Der Sumpf scheint erwacht zu sein...",
["COMIC_11_2_KR5_KR5"] = "...als ob er uns beobachten würde...",
["COMIC_11_3_KR5_KR5"] = "...voranschreitend und lauernd...",
["COMIC_11_4_KR5_KR5"] = "...bereit, uns zu verschlingen.",
["COMIC_11_5_KR5_KR5"] = "Sei vorsichtig!",
["COMIC_11_6_KR5_KR5"] = "Wir werden angegriffen!",
["COMIC_11_7_KR5_KR5"] = "Geh, kleines Irrlicht! Unsere Sicherheit liegt in deiner Eile!",
["COMIC_12_1_KR5_KR5"] = "Dich einfach einzusperren, war ein Fehler. Einer, den ich nicht wiederholen werde.",
["COMIC_12_2_KR5_KR5"] = "NEIIIIIN!!!",
["COMIC_12_3_KR5_KR5"] = "Ich verbanne dich für immer!!",
["COMIC_12_4_KR5_KR5"] = "Hust !",
["COMIC_12_5_KR5_KR5"] = "Hust, Hust !",
["COMIC_12_6_KR5_KR5"] = "Äh-Ich nehme an, ich bin aus der Übung.",
["COMIC_13_1_KR5_KR5"] = "Sie sagten, es sei Wahnsinn.",
["COMIC_13_2_KR5_KR5"] = "Dass eine solche Waffe unmöglich sei.",
["COMIC_13_3_KR5_KR5"] = "Doch bald werden sie wissen, wie sehr sie sich geirrt haben...",
["COMIC_13_4_KR5_KR5"] = "...und sich vor Grymbeards Genie ergeben!",
["COMIC_14_1_KR5_KR5"] = "Was sollen wir mit ihnen machen?",
["COMIC_14_2_KR5_KR5"] = "Lass das mal meine Sorge sein!",
["COMIC_14_3_KR5_KR5"] = "Ich kenne genau den richtigen Ort.",
["COMIC_14_4_KR5_KR5"] = "Also das war's?",
["COMIC_14_5_KR5_KR5"] = "Lass Grymbeard in einer Zelle verrotten?!",
["COMIC_14_6_KR5_KR5"] = "Ganz im Gegenteil, mein kleiner Freund...",
["COMIC_14_7_KR5_KR5"] = "...ich habe große Pläne für dein großes Gehirn!",
["COMIC_15_10_KR5_KR5"] = "…aber in keinen guten Zustand.",
["COMIC_15_1_KR5_KR5"] = "Irgendwo in den Bergen.",
["COMIC_15_2_KR5_KR5"] = "Hey, Goblin!",
["COMIC_15_3_KR5_KR5"] = "An die Arbeit!",
["COMIC_15_4_KR5_KR5"] = "Du musst eine Nachricht überbringen.",
["COMIC_15_5_KR5_KR5"] = "Wir sollten mehr Kundschafter aussenden. Wir können uns nicht entspannen, solange diese Kultisten umherstreifen.",
["COMIC_15_6_KR5_KR5"] = "Wir könnten einige Irrlichter schicken, um zu helfen; sie...",
["COMIC_15_7_KR5_KR5"] = "Dunkler Herr! Dringende Neuigkeiten!",
["COMIC_15_8_KR5_KR5"] = "Nun...",
["COMIC_15_9_KR5_KR5"] = "Wir haben unsere Späher gefunden...",
["COMIC_16_1_KR5_KR5"] = "Ich werde gerächt!",
["COMIC_16_2_KR5_KR5"] = "Meine Schwester...waaas?",
["COMIC_17_10_KR5_KR5"] = "Wenn wir sie nicht aufhalten, werden sie alle Königreiche auslöschen!",
["COMIC_17_11_KR5_KR5"] = "Wir müssen ihm helfen!",
["COMIC_17_12_KR5_KR5"] = "Oh, sicher doch.",
["COMIC_17_13_KR5_KR5"] = "Ja, ja…",
["COMIC_17_1_KR5_KR5"] = "Schöner Nachmittag, nicht wahr?",
["COMIC_17_2_KR5_KR5"] = "Ich könnte mich an diesen Frieden gewöhnen.",
["COMIC_17_3_KR5_KR5"] = "Besser nicht.",
["COMIC_17_4_KR5_KR5"] = "Sonne, bist du das?! Du hättest auch einfach winken können, weißt du…",
["COMIC_17_5_KR5_KR5"] = "Freunde, etwas Schreckliches ist passiert...",
["COMIC_17_6_KR5_KR5"] = "Ich meditierte friedlich in meiner Schildkröte, als...",
["COMIC_17_7_KR5_KR5"] = "Die Drei Dämonenkönige sind aus dem Nichts erschienen!",
["COMIC_17_8_KR5_KR5"] = "Selbstverständlich habe ich tapfer gekämpft, aber...",
["COMIC_17_9_KR5_KR5"] = "Sie haben mir auf unehrenhafte Weise meine himmlischen Sphären gestohlen!",
["COMIC_18_1_KR5_KR5"] = "In der Nähe der Küsten des Verstecks des Stierdämonenkönigs...",
["COMIC_18_2_KR5_KR5"] = "Ziel erfasst!",
["COMIC_18_3_KR5_KR5"] = "Lass uns diese Festung in die Luft jagen!",
["COMIC_18_4_KR5_KR5"] = "Meine Mauern lachen über eure Kieselsteine!",
["COMIC_18_5_KR5_KR5"] = "Für Linirea!",
["COMIC_18_6_KR5_KR5"] = "Zurück, Jungs! Wir brauchen hier eine Lücke!",
["COMIC_19_1_KR5_KR5"] = "Die Himmelskugeln können nicht in eurer Obhut bleiben, das ist absurd!",
["COMIC_19_2_KR5_KR5"] = "Ja, sei vorsichtig damit, Kumpel.",
["COMIC_19_3_KR5_KR5"] = "Du warst sehr weise, edler Affe!",
["COMIC_19_4_KR5_KR5"] = "Was soll ich nur mit euch dreien machen?",
["COMIC_1_1_KR5"] = "Es ist ein Monat vergangen, seit wir in diesem Land angekommen sind, auf der Suche nach unserem verlorenen König...",
["COMIC_1_2B_KR5"] = "...Nachdem er von Vez'nan, dem dunklen Zauberer, verbannt wurde.",
["COMIC_1_4_KR5"] = "Wir fanden einen Ort und richteten ein Lager ein, um unsere Kräfte wiederherzustellen...",
["COMIC_1_5_KR5"] = "...in Frieden...",
["COMIC_1_8_KR5"] = "...Aber es scheint, dass das jetzt vorbei ist.",
["COMIC_2_1_KR5"] = "Hurra!",
["COMIC_2_3_KR5"] = "Vez'nan?!",
["COMIC_2_4a_KR5"] = "Ruhig jetzt... Ich komme, um etwas vorzuschlagen...",
["COMIC_2_4b_KR5"] = "...einen Deal.",
["COMIC_2_5_KR5"] = "Nach dem, was du unserem Königreich angetan hast?!",
["COMIC_2_6_KR5"] = "König Denas' Augen mussten geöffnet werden.",
["COMIC_2_7_KR5"] = "Er weigerte sich, die Gefahr zu sehen, die das Königreich befiel.",
["COMIC_2_8_1_KR5"] = "Aber lassen Sie uns Ihren König finden...",
["COMIC_2_8_2_KR5"] = "...und dieser Bedrohung ein Ende setzen.",
["COMIC_2_8b_KR5"] = "...zusammen.",
["COMIC_3_1_KR5"] = "Oh weh! Was haben wir denn hier...?",
["COMIC_3_2_KR5"] = "Das mächtige Schwert von Elynie!",
["COMIC_3_3_KR5"] = "Autsch!",
["COMIC_3_4a_KR5"] = "Natürlich...",
["COMIC_3_4b_KR5"] = "Hör auf, Zeit zu verschwenden!",
["COMIC_3_5a_KR5"] = "Ah... aber er ist näher, als du denkst.",
["COMIC_3_5b_KR5"] = "Unser König wird immer noch vermisst.",
["COMIC_3_6_KR5"] = "Es könnte allerdings ein harter Kampf werden.",
["COMIC_4_10a_KR5"] = "Ha! Das hatte ich immer.",
["COMIC_4_10b_KR5"] = "Also... was passiert jetzt?",
["COMIC_4_11_KR5"] = "Wir mögen unsere Unterschiede haben...",
["COMIC_4_12_KR5"] = "...aber wir haben eine größere Bedrohung gemeinsam.",
["COMIC_4_1_KR5"] = "Elynie...",
["COMIC_4_2_KR5"] = "...gib ihm Kraft!",
["COMIC_4_4_KR5"] = "Aaurrrgh!",
["COMIC_4_7a_KR5"] = "Ich sehe, dein 'Urlaub' hat dir Wunder getan!",
["COMIC_4_7b_KR5"] = "DU!!!",
["COMIC_4_8_KR5"] = "Du solltest für deine Streiche bezahlen!",
["COMIC_4_9_KR5"] = "Aber du hattest recht.",
["COMIC_5_1_KR2"] = "Sieg!",
["COMIC_5_1_KR5_KR5"] = "Ihr Würmer könnt sie nicht aufhalten...",
["COMIC_5_2_KR2"] = "Sieg!",
["COMIC_5_2_KR5_KR5"] = "DIE NEUE WELT!",
["COMIC_5_6_KR5_KR5"] = "Es ist erwacht!",
["COMIC_5_7a_KR5_KR5"] = "Also das ist es...",
["COMIC_5_7b_KR5_KR5"] = "die letzte Auseinandersetzung.",
["COMIC_6_1a_KR5_KR5"] = "Du bist mutig, mich herauszufordern...",
["COMIC_6_1b_KR5_KR5"] = "aber das hat hier keinen Platz!",
["COMIC_6_4_KR5_KR5"] = "Hey!",
["COMIC_6_5_KR5_KR5"] = "Du, kosmischer Schleimer...",
["COMIC_6_6_KR5_KR5"] = "...unterschätzt MEINE Macht!!!",
["COMIC_6_8_KR5_KR5"] = "Mach dich bereit. Ich kann es nicht lange halten!",
["COMIC_7_1_KR5_KR5"] = "NEIN! Das... kann nicht sein!!!",
["COMIC_7_3_KR5_KR5"] = "Also... was jetzt?",
["COMIC_7_4a_KR5_KR5"] = "Nun, meine Mission ist erfüllt...",
["COMIC_7_4b_KR5_KR5"] = "...und ich denke, sie brauchen ihren König.",
["COMIC_7_5_2_KR2"] = "Nö.",
["COMIC_7_6_KR5_KR5"] = "Bis zum nächsten Mal, lieber Feind.",
["COMIC_7_7_KR5_KR5"] = "Später, im Everadiant Wald...",
["COMIC_8_1_KR5_KR5"] = "Ah, endlich!",
["COMIC_8_2_KR5_KR5"] = "Diese Macht ist, einmal mehr...",
["COMIC_8_4_KR5_KR5"] = "... MEINS!",
["COMIC_8_5_KR5_KR5"] = "MUA HA HA HA HA!",
["COMIC_9_1_KR5_KR5"] = "Vor nicht allzu langer Zeit wurden wir Elfen für unsere Magie und Anmut verehrt...",
["COMIC_9_2_KR5_KR5"] = "...bis unser heiliges Relikt verdorben wurde und wir zu einem Schatten dessen wurden, was wir einst waren.",
["COMIC_9_3_KR5_KR5"] = "Aber mit dieser Armee werde ich unseren Ruhm wiederherstellen...",
["COMIC_9_4_KR5_KR5"] = "...und ich werde eine neue Welt anführen, die von den Elfen regiert wird!!!",
["COMIC_BALLOON_0002_KR1"] = "Sieg!",
["COMIC_BALLOON_02_KR1"] = "Sieg!",
["COMIC_balloon_0002_KR1"] = "Sieg!",
["COMMAND YOUR TROOPS!"] = "BEFEHLIGE DEINE TRUPPEN!",
["CONFIRM_EXIT"] = "Verlassen?",
["CONFIRM_RESTART"] = "Neustart?",
["CONTROLLER_STAGE_16_OVERSEER_DESCRIPTION"] = "Eine extradimensionale Monstrosität, die andere Welten überfällt und erobert, um ihre Energie zu absorbieren. Muss um jeden Preis gestoppt werden.",
["CONTROLLER_STAGE_16_OVERSEER_EXTRA"] = "- Tauscht die Türme des Spielers aus\n- Erschafft Blendlinge\n- Zerstört strategische Positionen",
["CONTROLLER_STAGE_16_OVERSEER_NAME"] = "Der Aufseher",
["CREDITS_COPYRIGHT"] = "© 2014 Ironhide Game Studio. Alle Rechte vorbehalten.",
["CREDITS_POWERED_BY"] = "Powered by",
["CREDITS_SUBTITLE_01"] = "(alphabetisch sortiert)",
["CREDITS_SUBTITLE_07"] = "(alphabetisch sortiert)",
["CREDITS_SUBTITLE_09"] = "(alphabetisch sortiert)",
["CREDITS_SUBTITLE_16"] = "(alphabetisch sortiert)",
["CREDITS_TEXT_18"] = "Unser Dank gilt unseren Familien, Freunden und der Community",
["CREDITS_TEXT_18_2"] = "für die Unterstützung in den vergangenen Jahren.",
["CREDITS_TITLE_01"] = "Creative Directors & Executive Producers",
["CREDITS_TITLE_01_CREATIVE_DIRECTORS"] = "Kreative Direktoren",
["CREDITS_TITLE_01_EXECUTIVE_PRODUCERS"] = "Ausführende Produzenten",
["CREDITS_TITLE_02"] = "Lead Game Designer",
["CREDITS_TITLE_02_LEAD_GAME_DESIGNERS"] = "Leitende Spieldesigner",
["CREDITS_TITLE_03"] = "Game Designers",
["CREDITS_TITLE_03_GAME_DESIGNER"] = "Spieldesigner",
["CREDITS_TITLE_04"] = "Story Writer",
["CREDITS_TITLE_04_STORY_WRITERS"] = "Geschichtenschreiber",
["CREDITS_TITLE_05"] = "Text Writers",
["CREDITS_TITLE_06"] = "Lead Programmer",
["CREDITS_TITLE_06_LEAD_PROGRAMMERS"] = "Leitende Programmierer",
["CREDITS_TITLE_07"] = "Programmers",
["CREDITS_TITLE_08"] = "Lead Artist",
["CREDITS_TITLE_09"] = "Artists",
["CREDITS_TITLE_10"] = "Comic Artist",
["CREDITS_TITLE_11"] = "Comic Writer",
["CREDITS_TITLE_12"] = "Technical Artist",
["CREDITS_TITLE_13"] = "Sound FX",
["CREDITS_TITLE_14"] = "Original music by",
["CREDITS_TITLE_15"] = "Voice Talent",
["CREDITS_TITLE_16"] = "Q&A & Testing",
["CREDITS_TITLE_17"] = "Beta Testing",
["CREDITS_TITLE_18"] = "Besonderer Dank an",
["CREDITS_TITLE_19_PMO"] = "Projektmanagementbüro",
["CREDITS_TITLE_20_PRODUCER"] = "Produzent",
["CREDITS_TITLE_21_MARKETING"] = "Marketing",
["CREDITS_TITLE_22_SPECIAL_COLLAB"] = "Spezielle Mitarbeiter",
["CREDITS_TITLE_ANCIENT_HUNGER_UPDATE"] = "Uralter Hunger / Arachnophobie / Wukongs Reise",
["CREDITS_TITLE_GAME_ENGINE_PROGRAMMER"] = "Game-Engine-Programmierer",
["CREDITS_TITLE_LOCALIZATION"] = "Lokalisierung",
["CREDITS_TITLE_LOGO"] = "EIN SPIEL VON",
["CRange0"] = "Kurz",
["CRange1"] = "Mittel",
["CRange2"] = "Weit",
["CRange3"] = "Großartig",
["CRange4"] = "Extrem",
["CReload0"] = "Sehr langsam",
["CReload1"] = "Langsam",
["CReload2"] = "Mittel",
["CReload3"] = "Schnell",
["CReload4"] = "Sehr schnell",
["CSpeed0"] = "Langsam",
["CSpeed1"] = "Mittel",
["CSpeed2"] = "Schnell",
["C_DIFFICULTY_EASY"] = "Gelegenheitsspieler abgeschlossen",
["C_DIFFICULTY_HARD"] = "Veteran abgeschlossen",
["C_DIFFICULTY_IMPOSSIBLE"] = "Unmöglich abgeschlossen",
["C_DIFFICULTY_NORMAL"] = "Normal abgeschlossen",
["C_REWARD"] = "Belohnung:",
["Campaign"] = "Kampagne",
["Cancel"] = "Stornieren",
["Casual"] = "Einfach",
["Challenge Rules"] = "Herausforderungsregeln",
["Clear_progress"] = "Fortschritt löschen",
["Community Manager"] = "Community Manager",
["Credits"] = "Mitwirkende",
["DAYS_ABBREVIATION"] = "d",
["DELETE SLOT?"] = "Platz löschen?",
["DIFFICULTY_SELECTION_EASY_DESCRIPTION"] = "Für Einsteiger ins Strategiespiel-Genre!",
["DIFFICULTY_SELECTION_HARD_DESCRIPTION"] = "Hardcore! Spielen auf eigene Gefahr!",
["DIFFICULTY_SELECTION_IMPOSSIBLE_DESCRIPTION"] = "Nur die Stärksten haben eine Chance!",
["DIFFICULTY_SELECTION_IMPOSSIBLE_LOCKED_DESCRIPTION"] = "Schließe die Kampagne ab, um diesen Modus freizuschalten.",
["DIFFICULTY_SELECTION_NORMAL_DESCRIPTION"] = "Eine tolle Herausforderung!",
["DIFFICULTY_SELECTION_NOTE"] = "Du kannst den Schwierigkeitsgrad jederzeit bei der Wahl eines Abschnitts ändern.",
["DIFFICULTY_SELECTION_TITLE"] = "Wähle den Schwierigkeitsgrad!",
["DISCOUNT"] = "RABATT",
["DLC_OWNED"] = "GEKAUFT",
["Difficulty Level"] = "Schwierigkeitsgrad",
["ELITE STAGE!"] = "ELITEABSCHNITT!",
["ENEMY_ACOLYTE_DESCRIPTION"] = "Klein und schwach, die Acolythen lassen ihre Anzahl in der Schlacht zählen.",
["ENEMY_ACOLYTE_EXTRA"] = "- Erzeugt bei Tod einen Tentakel",
["ENEMY_ACOLYTE_NAME"] = "Kultakolyth",
["ENEMY_ACOLYTE_SPECIAL"] = "Erzeugt beim Tod einen Tentakel",
["ENEMY_ACOLYTE_TENTACLE_DESCRIPTION"] = "Als letzten Ausweg opfern Akolythen ihr Leben dem Aufseher, indem sie tödliche Tentakel erzeugen.",
["ENEMY_ACOLYTE_TENTACLE_EXTRA"] = "- Entsteht aus toten Akolythen",
["ENEMY_ACOLYTE_TENTACLE_NAME"] = "Akolythententakel",
["ENEMY_AMALGAM_DESCRIPTION"] = "Monstrositäten, die sowohl aus Fleisch als auch aus dem Boden des Jenseitigen Nichts bestehen. Die Behemoths verbreiten Angst, trotz ihrer Langsamkeit, das Schlachtfeld zu durchqueren.",
["ENEMY_AMALGAM_EXTRA"] = "- Mini-Boss\n- Explodiert, wenn es stirbt",
["ENEMY_AMALGAM_NAME"] = "Fleisch-Behemoth",
["ENEMY_ANIMATED_ARMOR_DESCRIPTION"] = "Abgenutze Relikte aus vergangenen Kriegen, besessen von Geistern, die sie jetzt in eine neue Schlacht bringen.",
["ENEMY_ANIMATED_ARMOR_EXTRA"] = "- Kann nach dem Tod von einem Geist wiederbelebt werden",
["ENEMY_ANIMATED_ARMOR_NAME"] = "Lebendige Rüstung",
["ENEMY_ARMORED_NIGHTMARE_DESCRIPTION"] = "Dank der Kultmagie in Rüstungen gekleidet, stürzen sich diese Albträume kopfüber in die Schlacht",
["ENEMY_ARMORED_NIGHTMARE_EXTRA"] = "- Hohe Rüstung\n- Verwandelt sich in einen Albtraum, wenn besiegt",
["ENEMY_ARMORED_NIGHTMARE_NAME"] = "Gebundener Albtraum",
["ENEMY_ARMORED_NIGHTMARE_SPECIAL"] = "Verwandelt sich in einen Albtraum, wenn besiegt.",
["ENEMY_ASH_SPIRIT_DESCRIPTION"] = "Mächtige Geister verwandelten sich in furchterregende Monster, geboren aus Lava, Asche und Leid.",
["ENEMY_ASH_SPIRIT_EXTRA"] = "- Hohe Gesundheit\n- Hohe Rüstung\n- Regeneriert Gesundheit auf brennendem Boden",
["ENEMY_ASH_SPIRIT_NAME"] = "Ash Spirit",
["ENEMY_BALLOONING_SPIDER_DESCRIPTION"] = "Schnelle und hinterlistige Spinnen, die Ärger geschickt aus dem Weg gehen.",
["ENEMY_BALLOONING_SPIDER_EXTRA"] = "- Beginnt zu fliegen, wenn in die Enge getrieben\n- Mittlere Rüstung",
["ENEMY_BALLOONING_SPIDER_FLYER_DESCRIPTION"] = "Schnelle und hinterlistige Spinnen, die Ärger geschickt aus dem Weg gehen.",
["ENEMY_BALLOONING_SPIDER_FLYER_EXTRA"] = "- Beginnt zu fliegen, wenn in die Enge getrieben\n- Mittlere Rüstung",
["ENEMY_BALLOONING_SPIDER_FLYER_NAME"] = "Ballonspinne",
["ENEMY_BALLOONING_SPIDER_NAME"] = "Ballonspinne",
["ENEMY_BANE_WOLF_DESCRIPTION"] = "Verdorbene Wölfe, die Jag auf jene machen, die zu langsam sind um sie kommen zu sehen.",
["ENEMY_BANE_WOLF_EXTRA"] = "- Bewegt sich schneller, jedes mal wenn er Schaden erleidet",
["ENEMY_BANE_WOLF_NAME"] = "Unheilswolf",
["ENEMY_BEAR_VANGUARD_DESCRIPTION"] = "Groß, breit und böse, zerreissen sie ihre Feinde dutzendweise.",
["ENEMY_BEAR_VANGUARD_EXTRA"] = "- Hohe Rüstung\n- Wird wütend, wenn ein Bär in der Nähe stirbt.",
["ENEMY_BEAR_VANGUARD_NAME"] = "Bärenvanguard",
["ENEMY_BEAR_VANGUARD_SPECIAL"] = "Gerät in einen Rasereizustand, wenn ein weiterer Bär in der Nähe stirbt.",
["ENEMY_BEAR_WOODCUTTER_DESCRIPTION"] = "Neigt dazu, im Dienst einzuschlafen, aber wenn es aufwacht, wird es ernst.",
["ENEMY_BEAR_WOODCUTTER_EXTRA"] = "- Hohe Rüstung\n- Wird wütend, wenn in der Nähe ein Bär stirbt",
["ENEMY_BEAR_WOODCUTTER_NAME"] = "Bärenholzfäller",
["ENEMY_BIG_TERRACOTA_DESCRIPTION"] = "Eine anthropomorphe Schlammmasse, geboren aus der Verschmelzung mehrerer von Mordlust getriebener Seelen.",
["ENEMY_BIG_TERRACOTA_EXTRA"] = "- Nahkampf",
["ENEMY_BIG_TERRACOTA_NAME"] = "Illusorischer Monstertäuschkörper",
["ENEMY_BLAZE_RAIDER_DESCRIPTION"] = "Stolze und stämmige Hauptmänner, Eingeweihte des Pfades des Feuers, die Schlangenspeere führen, um Feinde zu überlisten.",
["ENEMY_BLAZE_RAIDER_EXTRA"] = "- Schwache Rüstung\n- Spezialangriff auf brennendem Boden",
["ENEMY_BLAZE_RAIDER_NAME"] = "Flammenplünderer",
["ENEMY_BLINKER_DESCRIPTION"] = "Mit ihrem bedrohlichen Starren und fledermausähnlichen Flügeln jagen die Blinker ahnungslose Feinde.",
["ENEMY_BLINKER_EXTRA"] = "- Betäubt Spielereinheiten",
["ENEMY_BLINKER_NAME"] = "Blinker des Nichts",
["ENEMY_BLINKER_SPECIAL"] = "Betäubt Spielereinheiten",
["ENEMY_BOSS_BULL_KING_NAME"] = "Bull Demon King",
["ENEMY_BOSS_CORRUPTED_DENAS_NAME"] = "Verdorbener Denas",
["ENEMY_BOSS_CROCS_2_NAME"] = "Abominor Gift",
["ENEMY_BOSS_CROCS_3_NAME"] = "Abominor Feuer",
["ENEMY_BOSS_CROCS_NAME"] = "Abominor",
["ENEMY_BOSS_CULT_LEADER_NAME"] = "Seherin Mydrias",
["ENEMY_BOSS_DEFORMED_GRYMBEARD_NAME"] = "Deformierter Grymbeard",
["ENEMY_BOSS_GRYMBEARD_NAME"] = "Grymbeard",
["ENEMY_BOSS_MACHINIST_NAME"] = "Grymbeard",
["ENEMY_BOSS_NAVIRA_NAME"] = "Navira",
["ENEMY_BOSS_OVERSEER_NAME"] = "Der Aufseher",
["ENEMY_BOSS_PIG_NAME"] = "Goregrind",
["ENEMY_BOSS_PRINCESS_IRON_FAN_CLONE_NAME"] = "Prinzessin Eisenfächer-Klon",
["ENEMY_BOSS_PRINCESS_IRON_FAN_NAME"] = "Princess Iron Fan",
["ENEMY_BOSS_REDBOY_TEEN_NAME"] = "Roter Junge",
["ENEMY_BOSS_SPIDER_QUEEN_NAME"] = "Mygale",
["ENEMY_BRUTE_WELDER_DESCRIPTION"] = "Diese Arbeiter nutzen ihre Brenner ohne Provokation gegen Feinde.",
["ENEMY_BRUTE_WELDER_EXTRA"] = "- Blockiert einen Turm, wenn getötet",
["ENEMY_BRUTE_WELDER_NAME"] = "Brutaler Schweißer",
["ENEMY_BURNING_TREANT_DESCRIPTION"] = "Hölzerne Kreaturen mit bösen Absichten, geboren inmitten eines brennenden Waldes.",
["ENEMY_BURNING_TREANT_EXTRA"] = "- Flächenschaden\n- Hinterlässt beim Angriff brennenden Boden",
["ENEMY_BURNING_TREANT_NAME"] = "Burning Treant",
["ENEMY_CITIZEN_1_DESCRIPTION"] = "Finstere Fischer, die der Prinzessin dienen und sich durch den Schwarzmarkt schmuggeln.",
["ENEMY_CITIZEN_1_EXTRA"] = "- Schwach",
["ENEMY_CITIZEN_1_NAME"] = "Alter Fischhändler",
["ENEMY_CITIZEN_2_DESCRIPTION"] = "Düstere Fischer im Dienst der Prinzessin, die sich ihren Weg durch den Schwarzmarkt schmuggeln.",
["ENEMY_CITIZEN_2_EXTRA"] = "- Schwach",
["ENEMY_CITIZEN_2_NAME"] = "Blackwater-Fischer",
["ENEMY_CITIZEN_3_DESCRIPTION"] = "Finstere Fischer, die der Prinzessin dienen und sich durch den Schwarzmarkt schmuggeln.",
["ENEMY_CITIZEN_3_EXTRA"] = "- Schwach",
["ENEMY_CITIZEN_3_NAME"] = "Tintenschmuggler",
["ENEMY_CITIZEN_4_DESCRIPTION"] = "Finstere Fischer, die der Prinzessin dienen und sich durch den Schwarzmarkt schmuggeln.",
["ENEMY_CITIZEN_4_EXTRA"] = "- Schwach",
["ENEMY_CITIZEN_4_NAME"] = "Gezeitenräuber",
["ENEMY_COMMON_CLONE_DESCRIPTION"] = "Nicht bemerkenswert, nicht besonders, ganz wie das Original.",
["ENEMY_COMMON_CLONE_EXTRA"] = "- Läuft gedankenlos vorwärts",
["ENEMY_COMMON_CLONE_NAME"] = "Klon",
["ENEMY_CORRUPTED_ELF_DESCRIPTION"] = "Wiederbelebte Elfen, die ihre Feinde aus der Ferne jagen. Selbst im Tod bleiben sie äußerst effektiv.",
["ENEMY_CORRUPTED_ELF_EXTRA"] = "- Erzäugt ein Geist beim Tod",
["ENEMY_CORRUPTED_ELF_NAME"] = "Wiederkehrender Waldhüter",
["ENEMY_CORRUPTED_STALKER_DESCRIPTION"] = "Wolkenpirscher, gezähmt von den Akolythen, dienen nun als Reittiere für den Kult.",
["ENEMY_CORRUPTED_STALKER_EXTRA"] = "- Fliegend",
["ENEMY_CORRUPTED_STALKER_NAME"] = "Gezähmter Wolkenpirscher",
["ENEMY_CORRUPTED_STALKER_SPECIAL"] = "Fliegend",
["ENEMY_CROCS_BASIC_DESCRIPTION"] = "Stolzer Krok-Krieger, noch früh im Leben und nur wenige Kalorien davon entfernt, sich in die Tötungsmaschine zu verwandeln, die er zu sein weiß. ",
["ENEMY_CROCS_BASIC_EGG_DESCRIPTION"] = "Neu geboren und auf ihren Füßen unaufhaltsam, „sie wachsen so schnell“ war ein Ausdruck, der erfunden wurde dank dieser Kleinen die voller Überraschungen stecken. ",
["ENEMY_CROCS_BASIC_EGG_EXTRA"] = "- Unblockbar\n- Geringe Rüstung\n- Verwandelt sich nach einigen Sekunden in einen Gator ",
["ENEMY_CROCS_BASIC_EGG_NAME"] = "Krokinder ",
["ENEMY_CROCS_BASIC_EXTRA"] = "- Nahkampf ",
["ENEMY_CROCS_BASIC_NAME"] = "Gator",
["ENEMY_CROCS_EGG_SPAWNER_DESCRIPTION"] = "Dieses Krok besitzt ein Nest voller Ärger! Alle paar Schritte legt es Eier, aus denen eine Herde von Krokindern schlüpft. Es ist wie eine mobile Kinderstube, aber mit wesentlich mehr Biss!",
["ENEMY_CROCS_EGG_SPAWNER_EXTRA"] = "- Erschafft Krokinders auf dem Weg",
["ENEMY_CROCS_EGG_SPAWNER_NAME"] = "Nistender Gator",
["ENEMY_CROCS_FLIER_DESCRIPTION"] = "Listige Kroks, die in ihrer Verachtung für die natürliche Evolution ihre eigenen Flügel schufen, um einen Luftvorteil zu erlangen.",
["ENEMY_CROCS_FLIER_EXTRA"] = "- Fliegend",
["ENEMY_CROCS_FLIER_NAME"] = "Geflügelter Krok",
["ENEMY_CROCS_HYDRA_DESCRIPTION"] = "Zwei Köpfe sind besser als einer und Hydras beweisen es. Es gibt einen alten Mythos über ein dreiköpfiges Biest wie dieses, aber es ist wahrscheinlich eine Lüge.",
["ENEMY_CROCS_HYDRA_EXTRA"] = "- beim sterben wächst ein dritter Kopf\n- spuckt gift auf den Boden",
["ENEMY_CROCS_HYDRA_NAME"] = "Hydra",
["ENEMY_CROCS_QUICKFEET_GATOR_NAME"] = "Schnellfüße",
["ENEMY_CROCS_RANGED_DESCRIPTION"] = "Schnell und flinke Jägerechsen, die sich mit ihren Feinden mittels Langstreckenschleudern auseinandersetzen. ",
["ENEMY_CROCS_RANGED_EXTRA"] = "- Schnell\n- Fernkampf ",
["ENEMY_CROCS_RANGED_NAME"] = "Echsenschuss",
["ENEMY_CROCS_SHAMAN_DESCRIPTION"] = "Magische Wesen von großer Bedeutung für die Croks. Schließlich ist für eine kaltblütige Rasse die Fähigkeit, die Launen des Himmels vorherzusehen, eine Frage von Leben und Tod.",
["ENEMY_CROCS_SHAMAN_EXTRA"] = "- Fernmagieschaden\n- Hohe Magieresistenz\n- Heilt andere Croks\n- Betäubt Türme\n- Verstärkt Betäubungseffekt auf schon betäubten Türmen",
["ENEMY_CROCS_SHAMAN_NAME"] = "Weiser Krok",
["ENEMY_CROCS_TANK_DESCRIPTION"] = "Die Eckpfeiler der Crok Armee, mit der Mentalität, dass \"eine gute Verteidigung der beste Angriff ist\", haben sie einige Panzer gestohlen und begannen, sie zu verwenden, wie sie es für den besten Weg hielten.",
["ENEMY_CROCS_TANK_EXTRA"] = "- Hohe Gesundheit\n- Hohe Rüstung\n -Wirbelt wenn blockiert",
["ENEMY_CROCS_TANK_NAME"] = "Panzerdil",
["ENEMY_CRYSTAL_GOLEM_DESCRIPTION"] = "Mit außerweltlicher Magie aus ihren Kristallen durchtränkt, sind diese steinernen Bildnisse nahezu unaufhaltsam.",
["ENEMY_CRYSTAL_GOLEM_EXTRA"] = "- Mini-Boss\n- Sehr hohe Rüstung",
["ENEMY_CRYSTAL_GOLEM_NAME"] = "Kristall Golem",
["ENEMY_CULTBROOD_DESCRIPTION"] = "Halb Spinne, halb fanatische Abscheulichkeit, stürzen sie sich furchtlos und gnadenlos in die Schlacht.",
["ENEMY_CULTBROOD_EXTRA"] = "- Schnell \n- Giftangriff \n- Stirbt ein Feind durch Gift, entsteht eine neue Kultbrut",
["ENEMY_CULTBROOD_NAME"] = "Kultbrut",
["ENEMY_CUTTHROAT_RAT_DESCRIPTION"] = "Von Natur aus listig und hinterhältig, sind die Ratten geschickte Attentäter und Infiltratoren.",
["ENEMY_CUTTHROAT_RAT_EXTRA"] = "- Schnelle Geschwindigkeit\n- Wird unsichtbar nach dem Treffen eines Feindes.",
["ENEMY_CUTTHROAT_RAT_NAME"] = "Kehlenschneider Ratte",
["ENEMY_CUTTHROAT_RAT_SPECIAL"] = "Wird unsichtbar nach dem Treffen eines Feindes.",
["ENEMY_DARKSTEEL_ANVIL_DESCRIPTION"] = "Die zwergische Antwort auf Kriegstrommeln. Je schwerer sie aussehen, desto lauter ertönen sie.",
["ENEMY_DARKSTEEL_ANVIL_EXTRA"] = "- Gibt Rüstungs- und Geschwindigkeitsboni an Feinde",
["ENEMY_DARKSTEEL_ANVIL_NAME"] = "Dunkelstahl-Amboss",
["ENEMY_DARKSTEEL_FIST_DESCRIPTION"] = "Mechanisch verbessert, um Metall zu biegen, schlägt stattdessen auf andere ein.",
["ENEMY_DARKSTEEL_FIST_EXTRA"] = "- Spezialangriff betäubt Spieler-Einheiten",
["ENEMY_DARKSTEEL_FIST_NAME"] = "Dunkelstahl-Faust",
["ENEMY_DARKSTEEL_GUARDIAN_DESCRIPTION"] = "Robuste Kampfrüstungen, die von Zwergenkriegern betrieben und von feurigen Motoren angetrieben werden. Perfekt ausgerüstet zum Töten.",
["ENEMY_DARKSTEEL_GUARDIAN_EXTRA"] = "- Mini-Boss\n- Wird bei geringer Gesundheit rasend",
["ENEMY_DARKSTEEL_GUARDIAN_NAME"] = "Dunkelstahl-Wächter",
["ENEMY_DARKSTEEL_HAMMERER_DESCRIPTION"] = "Krieger, so grob wie ihre bevorzugte Waffe.",
["ENEMY_DARKSTEEL_HAMMERER_EXTRA"] = " ",
["ENEMY_DARKSTEEL_HAMMERER_NAME"] = "Dunkelstahl-Hämmerer",
["ENEMY_DARKSTEEL_HULK_DESCRIPTION"] = "Griesgrämig und mit flüssigem Stahl in den Adern ist dies die schwerste Form, die Zwerge erreichen können.",
["ENEMY_DARKSTEEL_HULK_EXTRA"] = "- Mini-Boss\n- Bei geringer Gesundheit stürmt er den Pfad entlang und fügt Schaden zu",
["ENEMY_DARKSTEEL_HULK_NAME"] = "Dunkelstahl-Koloss",
["ENEMY_DARKSTEEL_SHIELDER_DESCRIPTION"] = "Geschützt durch riesige Schilde, drängen sie Feinde beiseite, während sie voranschreiten.",
["ENEMY_DARKSTEEL_SHIELDER_EXTRA"] = "- Verwandelt sich in einen Hämmerer, wenn besiegt",
["ENEMY_DARKSTEEL_SHIELDER_NAME"] = "Dunkelstahl-Schildträger",
["ENEMY_DEATHWOOD_DESCRIPTION"] = "Wirrwurzeln, korrumpiert von den dunklen Geistern die nun durch den Wald irren, verbreiten Chaos.",
["ENEMY_DEATHWOOD_EXTRA"] = "- Mini-Boss\n- Wirft eine verfluchte Eichel die Flächenschaden verursacht",
["ENEMY_DEATHWOOD_NAME"] = "Todeswurzel",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_DESCRIPTION"] = "Das Ergebnis von Grymbeards zügelloser Arroganz. Seine geistige Stärke wird nur von seiner Abscheulichkeit übertroffen.",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_EXTRA"] = "- Fliegend\n- Magieresistenter Schild",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_NAME"] = "Deformierter Klon",
["ENEMY_DEMON_MINOTAUR_DESCRIPTION"] = "Halb Mensch, halb Stier – hybride Dämonen mit einem verheerenden Sturmangriff. Sie kennen keine Gnade.",
["ENEMY_DEMON_MINOTAUR_EXTRA"] = "- Sturmangriff\n- Kann nicht sofort getötet werden",
["ENEMY_DEMON_MINOTAUR_NAME"] = "Demon Minotaur",
["ENEMY_DOOM_BRINGER_DESCRIPTION"] = "Furchteinflößende Krieger, die um jeden Preis Verderben bringen.",
["ENEMY_DOOM_BRINGER_EXTRA"] = "- Betäubt Türme",
["ENEMY_DOOM_BRINGER_NAME"] = "Doombringer",
["ENEMY_DRAINBROOD_DESCRIPTION"] = "Eine uralte Spinne mit einem tödlichen Biss. Einige vermuten, dass sie die Hauptverantwortliche für die Kristallisierung der anderen Spinnen ist.",
["ENEMY_DRAINBROOD_EXTRA"] = "- Kristallisiert Feinde und entzieht ihnen gleichzeitig das Leben",
["ENEMY_DRAINBROOD_NAME"] = "Lebensraubspinne",
["ENEMY_DREADEYE_VIPER_DESCRIPTION"] = "Ihre Pfeile mit ihrem eigenen Gift beschichtend, sind sie aus der Ferne tödliche Feinde.",
["ENEMY_DREADEYE_VIPER_EXTRA"] = "- Niedrige Magieresistenz\n- Gift Angriffe",
["ENEMY_DREADEYE_VIPER_NAME"] = "Schreckensaugen-Viper",
["ENEMY_DREADEYE_VIPER_SPECIAL"] = "Pfeile tragen Gift auf das Ziel auf.",
["ENEMY_DUST_CRYPTID_DESCRIPTION"] = "Früher, ein wundervoller Anblick, jetzt, eine qälender Sicht für alle die zu weit wandern.",
["ENEMY_DUST_CRYPTID_EXTRA"] = "- Fliegend\n- Hinterlässt eine Pollenwolke beim Tod, die andere Feinde unverwundbar macht",
["ENEMY_DUST_CRYPTID_NAME"] = "Pollen Kryptid",
["ENEMY_EVOLVING_SCOURGE_DESCRIPTION"] = "Auf den ersten Blick wirken sie vielleicht fast kuschelig, aber wenn die Geißel sich von gefallener Beute ernährt, wird es schnell gruselig.",
["ENEMY_EVOLVING_SCOURGE_EXTRA"] = "- Frisst gefallene Einheiten, um sich zu einer stärkeren Form zu entwickeln\n - Entwickelt sich sofort zu seiner Endform, wenn vom Blick betroffen",
["ENEMY_EVOLVING_SCOURGE_NAME"] = "Evolvierende Geißel",
["ENEMY_FAN_GUARD_DESCRIPTION"] = "Starke und äußerst vielseitige Kriegerinnen, die sowohl Schmerz zufügen als auch sich mit ihren magischen Fächern schützen können.",
["ENEMY_FAN_GUARD_EXTRA"] = "- Hat mittlere Rüstung und Magieresistenz, solange er nicht blockiert ist.",
["ENEMY_FAN_GUARD_NAME"] = "Fan Guard",
["ENEMY_FIRE_FOX_DESCRIPTION"] = "Scheue und niedliche Füchse, geboren aus Feuer. Zu schnell und ungestüm, um gezähmt zu werden.",
["ENEMY_FIRE_FOX_EXTRA"] = "- Niedriger Magiewiderstand\n- Schneller auf brennendem Boden\n- Hinterlässt einen brennenden Boden beim Tod",
["ENEMY_FIRE_FOX_NAME"] = "Renard de Feu",
["ENEMY_FIRE_PHOENIX_DESCRIPTION"] = "Mythische fliegende Kreaturen, die sich vom Feuer selbst ernähren. Sie leben und sterben in lodernden Flammen.",
["ENEMY_FIRE_PHOENIX_EXTRA"] = "- Fliegend\n- Hinterlässt einen brennenden Boden beim Tod",
["ENEMY_FIRE_PHOENIX_NAME"] = "Zhuque",
["ENEMY_FLAME_GUARD_DESCRIPTION"] = "Im Streben nach der Anerkennung ihrer Meister brillieren diese niedrigstufigen Jünger mit kleinen Klingen.",
["ENEMY_FLAME_GUARD_EXTRA"] = "- Spezialangriff auf brennendem Boden",
["ENEMY_FLAME_GUARD_NAME"] = "Flammenwache",
["ENEMY_GALE_WARRIOR_DESCRIPTION"] = "Anmutig und stilvoll – diese Kriegerinnen wurden von ihrer Prinzessin auserwählt und würden für sie sterben.",
["ENEMY_GALE_WARRIOR_EXTRA"] = "- Mittlere Rüstung\n- Verursacht alle 3 Angriffe Blutung",
["ENEMY_GALE_WARRIOR_NAME"] = "Gale Warrior",
["ENEMY_GLAREBROOD_CRYSTAL_NAME"] = "Starrerbrut-Kristall",
["ENEMY_GLARELING_DESCRIPTION"] = "Wenn sie nicht in Schach gehalten werden, können diese bescheidenen Kreaturen selbst die stärksten Armeen überrennen.",
["ENEMY_GLARELING_EXTRA"] = "- Hohe Geschwindigkeit",
["ENEMY_GLARELING_NAME"] = "Blicklein",
["ENEMY_GLARENWARDEN_DESCRIPTION"] = "Diese abscheulichen Spinnen sind das Produkt der Verschmelzung von Starrerbruten, wodurch sie stärker und robuster denn je werden.",
["ENEMY_GLARENWARDEN_EXTRA"] = "- Hohe Rüstung\n- Lebensraub beim Angriff",
["ENEMY_GLARENWARDEN_NAME"] = "Starrerwächter",
["ENEMY_GOLDEN_EYED_DESCRIPTION"] = "Eine kolossale Bestie, deren Gebrüll Angst in die Herzen ihrer Feinde jagt.",
["ENEMY_GOLDEN_EYED_EXTRA"] = "- Miniboss\n- Erhöht die Bewegungsgeschwindigkeit von Verbündeten",
["ENEMY_GOLDEN_EYED_NAME"] = "Golden-Eyed Beast",
["ENEMY_HARDENED_HORROR_DESCRIPTION"] = "Diese Horror-Rasse besitzt geschärfte Klingen als Hände und wird sich einen Weg durch ihre Feinde bahnen.",
["ENEMY_HARDENED_HORROR_EXTRA"] = "- Rollt mit hoher Geschwindigkeit und kann nicht blockiert werden, wenn vom Blick betroffen.",
["ENEMY_HARDENED_HORROR_NAME"] = "Klingenklaue-Horror",
["ENEMY_HELLFIRE_WARLOCK_DESCRIPTION"] = "Äußerst gefährliche Hexenmeister, Experten im Herbeirufen von Kreaturen und Flammen aus den Tiefen der Hölle.",
["ENEMY_HELLFIRE_WARLOCK_EXTRA"] = "- Wirft Feuerbälle\n- Beschwört einen Neunschwänzigen Fuchs",
["ENEMY_HELLFIRE_WARLOCK_NAME"] = "Hellfire Warlock",
["ENEMY_HOG_INVADER_DESCRIPTION"] = "Schmutzig und unorganisiert Störenfriede. Der Großteil der Wildtierarmee.",
["ENEMY_HOG_INVADER_EXTRA"] = "- Niedrige HP",
["ENEMY_HOG_INVADER_NAME"] = "Schwein-Eindringling",
["ENEMY_HYENA5_DESCRIPTION"] = "Grausame Kämpfer, die eine Vorliebe dafür haben, sich an ihren gefallenen Feinden zu laben.",
["ENEMY_HYENA5_EXTRA"] = "- Mittlere Rüstung\n- Heilt sich, indem es gefallene Spielereinheiten frisst",
["ENEMY_HYENA5_NAME"] = "Faulzahnhyäne",
["ENEMY_HYENA5_SPECIAL"] = "Heilt sich, indem es getötete blockierende Feinde frisst.",
["ENEMY_KILLERTILE_DESCRIPTION"] = "Mächtige Zerstörer, jahrelange Kampferfahrung (oder ein Huhn) haben ihnen einen starken und tödlichen Biss gegeben. ",
["ENEMY_KILLERTILE_EXTRA"] = "- Hohe Gesundheit\n- Hoher Schaden",
["ENEMY_KILLERTILE_NAME"] = "Killertil",
["ENEMY_LESSER_EYE_DESCRIPTION"] = "Böse Augen, die über das Schlachtfeld schweben und als Späher der Niederträchtigen Spawner fungieren.",
["ENEMY_LESSER_EYE_EXTRA"] = "- Fliegend",
["ENEMY_LESSER_EYE_NAME"] = "Kleineres Auge",
["ENEMY_LESSER_SISTER_DESCRIPTION"] = "Mit ihrer bösartigen Magie erleichtern die Verdorbenen Schwestern den Albträumen den Zugang zur physischen Welt. ",
["ENEMY_LESSER_SISTER_EXTRA"] = "- Hohe Magieresistenz\n- Beschwört Albträume ",
["ENEMY_LESSER_SISTER_NAME"] = "Verdorbene Schwester",
["ENEMY_LESSER_SISTER_NIGHTMARE_DESCRIPTION"] = "Ätherische Schatten, gewoben aus dem Buch der Gesänge der Schwestern des Kultes",
["ENEMY_LESSER_SISTER_NIGHTMARE_EXTRA"] = "- Kann nicht anvisiert werden, es sei denn, es wird von Nahkampfeinheiten blockiert",
["ENEMY_LESSER_SISTER_NIGHTMARE_NAME"] = "Albtraum",
["ENEMY_LESSER_SISTER_SPECIAL"] = "Beschwört Alpträume",
["ENEMY_MACHINIST_DESCRIPTION"] = "Besessen von Zahnrädern und Motoren, lebt dieser Zwerg für industrielle Automatisierung und Kriegsführung.",
["ENEMY_MACHINIST_EXTRA"] = "- Betreibt eine Montagelinie, die Wachen erzeugt",
["ENEMY_MACHINIST_NAME"] = "Grymbeard",
["ENEMY_MAD_TINKERER_DESCRIPTION"] = "Bastler kümmern sich um nichts anderes, als aus Schrott Dinge zu bauen.",
["ENEMY_MAD_TINKERER_EXTRA"] = "- Erschafft Drohnen aus Schrott, der von anderen Einheiten hinterlassen wird",
["ENEMY_MAD_TINKERER_NAME"] = "Verrückter Bastler",
["ENEMY_MINDLESS_HUSK_DESCRIPTION"] = "Aufgrund ihres Aussehens wirken Hüllen wie schwache Gegner, doch jeder von ihnen bringt eine Überraschung auf das Schlachtfeld.",
["ENEMY_MINDLESS_HUSK_EXTRA"] = "- Erzeugt beim Tod ein Blicklein",
["ENEMY_MINDLESS_HUSK_NAME"] = "Hirnlose Hülle",
["ENEMY_NINE_TAILED_FOX_DESCRIPTION"] = "Geheimnisvolle Kreaturen, schön und mächtig zugleich. Sie fegen durch Feinde wie ein loderndes Feuer.",
["ENEMY_NINE_TAILED_FOX_EXTRA"] = "- Mittlere Magieresistenz\n- Teleportiert sich nach vorne und betäubt Feinde bei Ankunft\n- Flächenschaden",
["ENEMY_NINE_TAILED_FOX_NAME"] = "Neunschwänziger Fuchs",
["ENEMY_NOXIOUS_HORROR_DESCRIPTION"] = "Amphibisch aussehende Kreaturen, die giftige Galle auf ihre Beute spucken. Auch aus der Nähe gefährlich.",
["ENEMY_NOXIOUS_HORROR_EXTRA"] = "- Erhält Magieresistenz und strahlt eine giftige Aura aus, wenn vom Blick betroffen.",
["ENEMY_NOXIOUS_HORROR_NAME"] = "Giftspeier",
["ENEMY_PALACE_GUARD_DESCRIPTION"] = "Wenig talentierte Rekruten, deren einzige Motivation es ist, die Wünsche ihrer Prinzessin zu erfüllen.",
["ENEMY_PALACE_GUARD_EXTRA"] = "- Nahkampf\n- Schwache Rüstung",
["ENEMY_PALACE_GUARD_NAME"] = "Palastwache",
["ENEMY_PUMPKIN_WITCH_DESCRIPTION"] = "Feind in Kürbling verwandelt. Leicht zu zertrampeln.",
["ENEMY_PUMPKIN_WITCH_EXTRA"] = "- Unblockbar",
["ENEMY_PUMPKIN_WITCH_FLYING_DESCRIPTION"] = "Feind in Kürbling verwandelt. Leicht zu zertrampeln.",
["ENEMY_PUMPKIN_WITCH_FLYING_EXTRA"] = "- Unblockbar",
["ENEMY_PUMPKIN_WITCH_FLYING_NAME"] = "Kürbling",
["ENEMY_PUMPKIN_WITCH_NAME"] = "Kürbling",
["ENEMY_QIONGQI_DESCRIPTION"] = "Wilde fliegende Löwen, die mit der Macht des Blitzes angreifen. Die Könige des Sturms.",
["ENEMY_QIONGQI_EXTRA"] = "- Fliegend\n- Sehr hoher Schaden\n- Mittlere Magieresistenz",
["ENEMY_QIONGQI_NAME"] = "Qiongqi",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_DESCRIPTION"] = "Nach Jahren, in denen sie Hühner an ihre Brüder geliefert haben, sind sie so schnell geworden, dass sie manchmal vergessen, das Huhn überhaupt mitzubringen.",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_EXTRA"] = "- Schnell\n- Fernkampf\n- Wirft ein Hähnchenbein zu einem Gator, was ihn weiterentwickelt",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_NAME"] = "Schnellfüße",
["ENEMY_QUICKFEET_GATOR_DESCRIPTION"] = "Nach Jahren, in denen sie Hühner an ihre Brüder geliefert haben, sind sie so schnell geworden, dass sie manchmal vergessen, das Huhn überhaupt mitzubringen.",
["ENEMY_QUICKFEET_GATOR_EXTRA"] = "- Schnell\n- Fernkampf\n- Vorsicht! Es kann ein Hähnchenbein zu einem Gator werfen, was ihn weiterentwickelt",
["ENEMY_QUICKFEET_GATOR_NAME"] = "Flinkfüße",
["ENEMY_REVENANT_HARVESTER_DESCRIPTION"] = "Priesterinnen früherer Tage, irren nun durch den Wald und nutzen die Geister um ihren Einfluss zu verbreiten.",
["ENEMY_REVENANT_HARVESTER_EXTRA"] = "- Verwandelt Geister in ihrer Nähe zu Wiederkehrenden Sensen",
["ENEMY_REVENANT_HARVESTER_NAME"] = "Wiederkehrende Sensen",
["ENEMY_REVENANT_SOULCALLER_DESCRIPTION"] = "Elfenmagier, die den Einfluss von Todesmagie erlitten und von Ihren Gräbern auferstanden sind, um die Geister der Gefallenen zu beschwören.",
["ENEMY_REVENANT_SOULCALLER_EXTRA"] = "- Deaktiviert Türme\n- Beschwört Geister",
["ENEMY_REVENANT_SOULCALLER_NAME"] = "Wiederkehrender Seelenrufer",
["ENEMY_RHINO_DESCRIPTION"] = "Ein lebender Rammbock, der rücksichtslos durch das Schlachtfeld trampelt.",
["ENEMY_RHINO_EXTRA"] = "- Mini-Boss\n- Stürmt auf Feinde zu",
["ENEMY_RHINO_NAME"] = "Verwüstender Rhino",
["ENEMY_RHINO_SPECIAL"] = "Stürmt auf Feinde zu.",
["ENEMY_ROLLING_SENTRY_DESCRIPTION"] = "Sobald sie abgeschossen sind, jagen sie weiterhin am Boden.",
["ENEMY_ROLLING_SENTRY_EXTRA"] = "- Verwandelt sich in Schrott, wenn zerstört\n- Fernkampf",
["ENEMY_ROLLING_SENTRY_NAME"] = "Rollende Wache",
["ENEMY_SCRAP_DRONE_DESCRIPTION"] = "Grob zusammengebaut mit einem einzigen Ziel, die Truppen zu belästigen.",
["ENEMY_SCRAP_DRONE_EXTRA"] = "- Fliegend",
["ENEMY_SCRAP_DRONE_NAME"] = "Schrott-Drohne",
["ENEMY_SCRAP_SPEEDSTER_DESCRIPTION"] = "Laut und nervig, mit einem unbändigen Drang nach Geschwindigkeit.",
["ENEMY_SCRAP_SPEEDSTER_EXTRA"] = "- Verwandelt sich in Schrott, wenn zerstört",
["ENEMY_SCRAP_SPEEDSTER_NAME"] = "Schrott-Sprinter",
["ENEMY_SKUNK_BOMBARDIER_DESCRIPTION"] = "Ihre natürlichen Toxine auf ein neues Level bringend, verbreiten Stinktiere Unordnung in den feindlichen Linien.",
["ENEMY_SKUNK_BOMBARDIER_EXTRA"] = "- Niedrige Geschwindigkeit\n- Mittlere Magieresistenz\n- Angriffe schwächen Spielereinheiten\n- Explodiert beim Tod",
["ENEMY_SKUNK_BOMBARDIER_NAME"] = "Stinktier-Bombardier",
["ENEMY_SKUNK_BOMBARDIER_SPECIAL"] = "Angriffe schwächen Spielereinheiten. Explodiert beim Tod und verursacht Schaden.",
["ENEMY_SMALL_STALKER_DESCRIPTION"] = "Durch die Magie des Kultes verdorben, teleportieren sich diese Wolkenpirscher über das Schlachtfeld und säen Chaos.",
["ENEMY_SMALL_STALKER_EXTRA"] = "- Teleportiert sich nach vorne, wenn angegriffen",
["ENEMY_SMALL_STALKER_NAME"] = "Verdorbener Wolkenpirscher",
["ENEMY_SMALL_STALKER_SPECIAL"] = "Teleportiert sich über eine kurze Distanz, um Angriffen auszuweichen.",
["ENEMY_SPECTER_DESCRIPTION"] = "Versklavt, weit über ihren Zerfall hinaus, verpflichted die Lebenden heimzusuchen.",
["ENEMY_SPECTER_EXTRA"] = "- Kann mit anderen Feinden und Elementen interagieren",
["ENEMY_SPECTER_NAME"] = "Geist",
["ENEMY_SPIDEAD_DESCRIPTION"] = "Direkte Nachkommen der Spinnenkönigin Mygale, diese Spinnen finden immer einen Weg lästig zu sein—selbst nach ihrem Tod",
["ENEMY_SPIDEAD_EXTRA"] = "- Magieresistenz\n- Erzeugt ein Spinnennetz beim Tod",
["ENEMY_SPIDEAD_NAME"] = "Todesbrut",
["ENEMY_SPIDERLING_DESCRIPTION"] = "Von der Magie des Kultes verstärkte Spinnen. Schnell und wütend. Werden beißen.",
["ENEMY_SPIDERLING_EXTRA"] = "- Schnelle Geschwindigkeit\n- Geringe Magieresistenz",
["ENEMY_SPIDERLING_NAME"] = "Starrerbrut",
["ENEMY_SPIDER_PRIEST_DESCRIPTION"] = "Verwoben von ihrem neuen Gott, betreten die Priester das Schlachtfeld, um ihre dunkle Magie auszuüben.",
["ENEMY_SPIDER_PRIEST_EXTRA"] = "- Hoher Magiewiderstand\n- Wird bei Todesnähe zu einem Starrerwächter",
["ENEMY_SPIDER_PRIEST_NAME"] = "Priester des Netzes",
["ENEMY_SPIDER_SISTER_DESCRIPTION"] = "Als treue Anhängerinnen der Spinnenkönigin nutzen sie ihre Magie, um ihre Brut herbeizurufen.",
["ENEMY_SPIDER_SISTER_EXTRA"] = "- Magieresistenz\n- Beschwört Starrerbrut",
["ENEMY_SPIDER_SISTER_NAME"] = "Spinnenschwester",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_DESCRIPTION"] = "Schattendoppelgänger, die Mydrias benutzt, um in die Schlacht einzugreifen.",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_EXTRA"] = "- Schützt Feinde vor Schäden\n- Fängt Türme mit dunklen Tentakeln ein",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_NAME"] = "Mydrias' Illusion",
["ENEMY_STORM_ELEMENTAL_DESCRIPTION"] = "Mächtige Elementare, geboren aus Taifunen, Blitzen und Zorn. Ein entfernter Verwandter des Aschegeistes.",
["ENEMY_STORM_ELEMENTAL_EXTRA"] = "- Hohe Rüstung\n- Fernkampf\n- Betäubt einen nahen Turm beim Tod",
["ENEMY_STORM_ELEMENTAL_NAME"] = "Sturmgeist",
["ENEMY_STORM_SPIRIT_DESCRIPTION"] = "Kleine Drachen springen durch Sturmwolken und weichen geschickt Gefahren und Feinden aus.",
["ENEMY_STORM_SPIRIT_EXTRA"] = "- Fliegend\n- Geringe Magieresistenz\n- Stürmt vorwärts, wenn verletzt",
["ENEMY_STORM_SPIRIT_NAME"] = "Sturm-Dräkelchen",
["ENEMY_SURVEILLANCE_SENTRY_DESCRIPTION"] = "Von Zwergen entwickelt, um Feinde aus der Luft zu beobachten.",
["ENEMY_SURVEILLANCE_SENTRY_EXTRA"] = "- Fliegend\n- Verwandelt sich in Rollende Wache wenn zerstört",
["ENEMY_SURVEILLANCE_SENTRY_NAME"] = "Fliegende Wache",
["ENEMY_SURVEYOR_HARPY_DESCRIPTION"] = "Auf der Suche nach Aas, verfolgen Geier die Wildtiere überall.",
["ENEMY_SURVEYOR_HARPY_EXTRA"] = "- Fliegend",
["ENEMY_SURVEYOR_HARPY_NAME"] = "Patrouillierender Geier",
["ENEMY_SURVEYOR_HARPY_SPECIAL"] = "Fliegend.",
["ENEMY_TERRACOTA_DESCRIPTION"] = "Manifestierte Schatten, die als Ablenkung dienen.",
["ENEMY_TERRACOTA_EXTRA"] = "- Nahkampf",
["ENEMY_TERRACOTA_NAME"] = "Illusorisches Lockbild",
["ENEMY_TOWER_RAY_SHEEP_DESCRIPTION"] = "Bääääää.",
["ENEMY_TOWER_RAY_SHEEP_EXTRA"] = "- Unblockbar",
["ENEMY_TOWER_RAY_SHEEP_FLYING_DESCRIPTION"] = "Bääääää.",
["ENEMY_TOWER_RAY_SHEEP_FLYING_EXTRA"] = "- Fliegend",
["ENEMY_TOWER_RAY_SHEEP_FLYING_NAME"] = "Fliegendes Schaf",
["ENEMY_TOWER_RAY_SHEEP_NAME"] = "Schaf",
["ENEMY_TURTLE_SHAMAN_DESCRIPTION"] = "Friedlich aussehend, aber bösartig, halten die Schamanen die Wildtiere geheilt und kampfbereit.",
["ENEMY_TURTLE_SHAMAN_EXTRA"] = "- Langsame Geschwindigkeit\n- Hohe HP\n- Hohe Magieresistenz\n- Heilt feindliche Einheiten",
["ENEMY_TURTLE_SHAMAN_NAME"] = "Schildkröten-Schamane",
["ENEMY_TURTLE_SHAMAN_SPECIAL"] = "Heilt feindliche Einheiten.",
["ENEMY_TUSKED_BRAWLER_DESCRIPTION"] = "Hartnäckiger als die Eindringlinge und ausgestattet mit klappriger Rüstung. Immer bereit zum Kampf.",
["ENEMY_TUSKED_BRAWLER_EXTRA"] = "- Niedrige Rüstung",
["ENEMY_TUSKED_BRAWLER_NAME"] = "Hauer-Schläger",
["ENEMY_UNBLINDED_ABOMINATION_DESCRIPTION"] = "Vollständig korrumpierte Priester des Kultes, bekannt für ihre Grausamkeit im Kampf.",
["ENEMY_UNBLINDED_ABOMINATION_EXTRA"] = "- Verschlingt Einheiten mit niedriger Gesundheit",
["ENEMY_UNBLINDED_ABOMINATION_NAME"] = "Kultabscheulichkeit",
["ENEMY_UNBLINDED_ABOMINATION_SPECIAL"] = "Verschlingt gelegentlich eine Einheit mit niedriger Gesundheit",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_DESCRIPTION"] = "Nachdem die Elfen versklavt wurden, wurden einige Abscheuligkeiten ernannt, um sicherzustellen, dass die Arbeit in den Minen reibungslos verläuft.",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_EXTRA"] = "-  Muss getötet werden, um die Elfen zu befreien",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_NAME"] = "Vorarbeiter Abscheulichkeit",
["ENEMY_UNBLINDED_PRIEST_DESCRIPTION"] = "Zwischen Gebeten und dem Okkult, ziehen die Priester mit dunkler Magie in den Kampf.",
["ENEMY_UNBLINDED_PRIEST_EXTRA"] = "- Hohe Magieresistenz\n- Verwandelt sich in eine Abscheulichkeit, wenn der Tod naht",
["ENEMY_UNBLINDED_PRIEST_NAME"] = "Kultpriester",
["ENEMY_UNBLINDED_PRIEST_SPECIAL"] = "Bei geringer Gesundheit verwandeln sie sich in eine Abscheulichkeit.",
["ENEMY_UNBLINDED_SHACKLER_DESCRIPTION"] = "Leiten verdorbene Magie durch die in ihren Armen eingebetteten Kristalle, wodurch Fessler gefürchtete Gegner im Nahkampf werden",
["ENEMY_UNBLINDED_SHACKLER_EXTRA"] = "- Mittlere Magieresistenz\n- Deaktiviert Türme bei geringer Gesundheit",
["ENEMY_UNBLINDED_SHACKLER_NAME"] = "Fessler",
["ENEMY_UNBLINDED_SHACKLER_SPECIAL"] = "Verkettet Türme und hindert diese daran anzugreifen",
["ENEMY_VILE_SPAWNER_DESCRIPTION"] = "Ihre vielen fliegenden Augen über die Feinde werfend, beobachten die Niederträchtigen Spawner immer in jede Richtung.",
["ENEMY_VILE_SPAWNER_EXTRA"] = "- Erschafft Kleinere Augen.",
["ENEMY_VILE_SPAWNER_NAME"] = "Niederträchtiger Spawner",
["ENEMY_WATER_SORCERESS_DESCRIPTION"] = "Altgediente Elementarmagier, die die Macht des Wassers nutzen, um Verbündete zu heilen und Feinde aus der Ferne zu bezwingen.",
["ENEMY_WATER_SORCERESS_EXTRA"] = "- Fernkampf\n- Mittlere Magieresistenz\n- Heilt Verbündete",
["ENEMY_WATER_SORCERESS_NAME"] = "Water Master",
["ENEMY_WATER_SPIRIT_DESCRIPTION"] = "Seelenlose Wasserwesen, die in unerbittlichen Wellen heranstürmen und mit Wut die Küsten verwüsten.",
["ENEMY_WATER_SPIRIT_EXTRA"] = "- Niedrige Magieresistenz\n- Kann aus Wasser entstehen",
["ENEMY_WATER_SPIRIT_NAME"] = "Water Spirit",
["ENEMY_WATER_SPIRIT_SPAWNLESS_DESCRIPTION"] = "Seelenlose Wasserwesen, die in unerbittlichen Wellen heranstürmen und mit Wut die Küsten verwüsten.",
["ENEMY_WATER_SPIRIT_SPAWNLESS_EXTRA"] = "- Niedrige Magieresistenz\n- Kann aus Wasser entstehen",
["ENEMY_WATER_SPIRIT_SPAWNLESS_NAME"] = "Water Spirit",
["ENEMY_WUXIAN_DESCRIPTION"] = "Mächtige und widerstandsfähige Zauberer, die ihre Feinde mit Magie vernichten.",
["ENEMY_WUXIAN_EXTRA"] = "- Fernkampf\n- Mittlere Rüstung\n- Spezialangriff auf brennendem Boden",
["ENEMY_WUXIAN_NAME"] = "Wuxian",
["ERROR_MESSAGE_GENERIC"] = "Uups! Etwas ist schiefgelaufen.",
["Earn huge bonus points and gold by calling waves earlier!"] = "Verdiene etliche Bonuspunkte und Gold, indem du Wellen früher auslöst!",
["FIRST_WEEK_PACK"] = "Geschenk",
["FULLADS_BONUS_REWARDS_TITLE"] = "BONUS BELOHNUNGEN!",
["FULLADS_BUTTON_BUY"] = "KAUFEN",
["FULLADS_BUTTON_CLAIM"] = "SIEH DIR FÜR DIESE BELOHNUNGEN WERBUNG AN!",
["FULLADS_BUTTON_CLAIM_SHORT"] = "ANSPRUCH!",
["FULLADS_BUTTON_HIRE"] = "MIETEN",
["FULLADS_BUTTON_INFO"] = "INFO",
["FULLADS_BUTTON_PLAY"] = "SIEH DIR FÜR DIESE BELOHNUNGEN WERBUNG AN!",
["FULLADS_BUTTON_PLAY_SHORT"] = "ABSPIELEN!",
["FULLADS_BUTTON_SPIN"] = "SIEH DIR FÜR DAS DREHEN DES RADS WERBUNG AN!",
["FULLADS_BUTTON_SPIN_SHORT"] = "DREHEN",
["FULLADS_BUTTON_UNLOCK"] = "BEFREIEN",
["FULLADS_DEFEAT_ENDLESS_REWARDS_TITLE"] = "BESIEGE BELOHNUNGEN",
["FULLADS_DEFEAT_REWARDS_TITLE"] = "BESIEGE BELOHNUNGEN",
["FULLADS_GNOME_REWARDS_TITLE"] = "GNOME BELOHNUNGEN!",
["FULLADS_MAP_CROWNS_DESCRIPTION"] = "Benutze Kronen, um einen Helden für einen Tag anzuheuern.",
["FULLADS_MAP_GEMS_DESCRIPTION"] = "Benutze Juwelen, um Gegenstände zu kaufen und Helden dauerhaft freizuschalten.",
["FULLADS_MAP_HEROROOM_HELP_CROWNS"] = "Mieten Sie für einen Tag",
["FULLADS_MAP_HEROROOM_HELP_GEMS"] = "Für immer kaufen",
["FULLADS_MAP_STARS_DESCRIPTION"] = "Benutze Sterne, um Verbesserungen zu kaufen.",
["FULLADS_VICTORY_CLAIM_BONUS"] = "SCHAU DIR ZUM ABHOLEN DES BONUS (%sX) WERBUNG AN",
["FULLADS_VICTORY_REWARDS_TITLE"] = "SIEG BELOHNUNGEN!",
["FULLADS_WHEEL_PROBABILITIES_TITLE"] = "Belohnungswahrscheinlichkeiten",
["FULLADS_WHEEL_REWARDS_TITLE"] = "GLÜCKSRAD",
["FULLADS_YOUR_REWARDS_TITLE"] = "DEINE BELOHNUNG!",
["FULLADS_YOUR_REWARD_TITLE"] = "DEINE BELOHNUNG!",
["Face an endless unrelenting enemy force and try to defeat as many as possible to comete for the best score!"] = "Kämpfe um die höchste Punktezahl gegen unendlich erbarmungslose Truppen!",
["Face an endless unrelenting enemy force and try to defeat as many as possible to compete for the best score!"] = "Kämpfe um die höchste Punktezahl gegen unendlich erbarmungslose Truppen!",
["Failed to load Rewarded Video, first session !"] = "Das Belohnungsvideo konnte nicht geladen werden.",
["Failed to load Rewarded Video, internal error !"] = "Das Belohnungsvideo konnte nicht geladen werden.",
["Failed to load Rewarded Video, missing location parameter !"] = "Das Belohnungsvideo konnte nicht geladen werden.",
["Failed to load Rewarded Video, network error !"] = "Das Belohnungsvideo konnte nicht geladen werden.",
["Failed to load Rewarded Video, no Internet connection !"] = "Das Belohnungsvideo konnte nicht geladen werden.",
["Failed to load Rewarded Video, no ad found !"] = "Das Belohnungsvideo konnte nicht geladen werden.",
["Failed to load Rewarded Video, session not started !"] = "Das Belohnungsvideo konnte nicht geladen werden.",
["Failed to load Rewarded Video, too many connections !"] = "Das Belohnungsvideo konnte nicht geladen werden.",
["Failed to load Rewarded Video, unknown error !"] = "Das Belohnungsvideo konnte nicht geladen werden.",
["Failed to load Rewarded Video, wrong orientation !"] = "Das Belohnungsvideo konnte nicht geladen werden.",
["GAME PAUSED"] = "SPIEL PAUSIERT",
["GAME_TITLE_KR5"] = "Kingdom Rush 5: Alliance",
["GEMS_BARREL_NAME"] = "FASS VOLLER EDELSTEINE",
["GEMS_CHEST_NAME"] = "KISTE MIT EDELSTEINEN",
["GEMS_HANDFUL_NAME"] = "HANDVOLL EDELSTEINE",
["GEMS_MOUNTAIN_NAME"] = "BERG VON EDELSTEINEN",
["GEMS_POUCH_NAME"] = "BEUTEL MIT EDELSTEINEN",
["GEMS_WAGON_NAME"] = "WAGEN VOLLER EDELSTEINE",
["GET_ALL_AWESOME_HEROES"] = "HOL DIR ALLE DIESE FANTASTISCHEN HELDEN",
["GET_THIS_AWESOME"] = "HOL DIR DIESEN\nFANTASTISCHEN HELDEN",
["GET_THIS_AWESOME_2"] = "HOL DIR DIESE\nFANTASTISCHEN HELDEN",
["GET_THIS_AWESOME_3"] = "HOL DIR DIESE\nFANTASTISCHEN HELDEN",
["GIFT_CLAIMED"] = "Geschenk eingelöst!",
["GOOGLE_PLAY"] = "GOOGLE PLAY",
["Got it!"] = "Kapiert!",
["HERO LEVEL UP!"] = "LEVELAUFSTIEG DES HELDEN!",
["HERO ROOM"] = "HELDEN",
["HERO UNLOCKED!"] = "HELD FREIGESCHALTET!",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_1"] = "Beschwört Greife, die %$heroes.hero_bird.ultimate.bird.duration[2]%$ Sekunden lang über dem Gebiet fliegen und bei jedem Schlag %$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[2]%$ Schaden an Feinden verursachen.",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_2"] = "Beschwört Greife, die %$heroes.hero_bird.ultimate.bird.duration[3]%$ Sekunden lang über dem Gebiet fliegen und bei jedem Schlag %$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[3]%$ Schaden an Feinden verursachen.",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_3"] = "Beschwört Greife, die %$heroes.hero_bird.ultimate.bird.duration[4]%$ Sekunden lang über dem Gebiet fliegen und bei jedem Schlag %$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[4]%$ Schaden an Feinden verursachen.",
["HERO_BIRD_BIRDS_OF_PREY_MENUBOTTOM_DESCRIPTION"] = "Beschwört Greife, die über dem Gebiet fliegen und Feinde angreifen.",
["HERO_BIRD_BIRDS_OF_PREY_MENUBOTTOM_NAME"] = "Kampfvögel",
["HERO_BIRD_BIRDS_OF_PREY_TITLE"] = "VÖGEL DES GEFECHTS",
["HERO_BIRD_CLASS"] = "Der Assreiter",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_1"] = "Wirft einen Sprengkörper, der sich über die Feinde verteilt, verursacht %$heroes.hero_bird.cluster_bomb.explosion_damage_min[1]%$ Schaden bei jedem und setzt den Boden für %$heroes.hero_bird.cluster_bomb.fire_duration[1]%$ Sekunden in Brand, verbrennt Feinde für %$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$ Schaden über 3 Sekunden.",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_2"] = "Wirft einen Sprengkörper, der sich über die Feinde verteilt, verursacht %$heroes.hero_bird.cluster_bomb.explosion_damage_min[2]%$ Schaden bei jedem und setzt den Boden für %$heroes.hero_bird.cluster_bomb.fire_duration[2]%$ Sekunden in Brand, verbrennt Feinde für %$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$ Schaden über 3 Sekunden.",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_3"] = "Wirft einen Sprengkörper, der sich über die Feinde verteilt, verursacht %$heroes.hero_bird.cluster_bomb.explosion_damage_min[3]%$ Schaden bei jedem und setzt den Boden für %$heroes.hero_bird.cluster_bomb.fire_duration[3]%$ Sekunden in Brand, verbrennt Feinde für %$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$ Schaden über 3 Sekunden.",
["HERO_BIRD_CLUSTER_BOMB_TITLE"] = "TEPPICHBOMBARDEMENT",
["HERO_BIRD_DESC"] = "Der mutige Greifenreiter fliegt mit einem Arsenal aus Stahl und Feuer in die Schlacht. Obwohl er nur widerwillig der Allianz beitrat, seit die Dunkle Armee sein Zuhause überfallen hat, stimmte Broden zu, Zerstörung über den Kult zu bringen, als Mittel, um den Status quo in Linirea wiederherzustellen.",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_1"] = "Der Greif stürzt sich zu Boden, um einen Feind mit bis zu %$heroes.hero_bird.eat_instakill.hp_max[1]%$ Gesundheit zu verschlingen.",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_2"] = "Der Greif stürzt sich zu Boden, um einen Feind mit bis zu %$heroes.hero_bird.eat_instakill.hp_max[2]%$ Gesundheit zu verschlingen.",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_3"] = "Der Greif stürzt sich zu Boden, um einen Feind mit bis zu %$heroes.hero_bird.eat_instakill.hp_max[3]%$ Gesundheit zu verschlingen.",
["HERO_BIRD_EAT_INSTAKILL_TITLE"] = "JAGDSTURZFLUG",
["HERO_BIRD_GATTLING_DESCRIPTION_1"] = "Lässt Kugeln auf einen Feind regnen, verursacht %$heroes.hero_bird.gattling.s_damage_min[1]%$-%$heroes.hero_bird.gattling.s_damage_max[1]%$ physischen Schaden.",
["HERO_BIRD_GATTLING_DESCRIPTION_2"] = "Lässt Kugeln auf einen Feind regnen, verursacht %$heroes.hero_bird.gattling.s_damage_min[2]%$-%$heroes.hero_bird.gattling.s_damage_max[2]%$ physischen Schaden.",
["HERO_BIRD_GATTLING_DESCRIPTION_3"] = "Lässt Kugeln auf einen Feind regnen, verursacht %$heroes.hero_bird.gattling.s_damage_min[3]%$-%$heroes.hero_bird.gattling.s_damage_max[3]%$ physischen Schaden.",
["HERO_BIRD_GATTLING_TITLE"] = "BEISPIELHAFTE FÜHRUNG",
["HERO_BIRD_NAME"] = "Broden",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_1"] = "Der Greif erzeugt einen ohrenbetäubenden Schrei, der Feinde für %$heroes.hero_bird.shout_stun.stun_duration[1]%$ Sekunde betäubt und sie danach für %$heroes.hero_bird.shout_stun.slow_duration[1]%$ Sekunden verlangsamt.",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_2"] = "Der Greif erzeugt einen ohrenbetäubenden Schrei, der Feinde für %$heroes.hero_bird.shout_stun.stun_duration[2]%$ Sekunde betäubt und sie danach für %$heroes.hero_bird.shout_stun.slow_duration[2]%$ Sekunden verlangsamt.",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_3"] = "Der Greif erzeugt einen ohrenbetäubenden Schrei, der Feinde für %$heroes.hero_bird.shout_stun.stun_duration[3]%$ Sekunde betäubt und sie danach für %$heroes.hero_bird.shout_stun.slow_duration[3]%$ Sekunden verlangsamt.",
["HERO_BIRD_SHOUT_STUN_TITLE"] = "SCHRECKENSSCHREI",
["HERO_BUILDER_CLASS"] = "Bauleiter",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_1"] = "Baut einen provisorischen Turm, der vorbeikommende Feinde %$heroes.hero_builder.defensive_turret.duration[1]%$ Sekunden lang angreift und pro Angriff %$heroes.hero_builder.defensive_turret.attack.damage_min[1]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[1]%$ physischen Schaden verursacht.",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_2"] = "Baut einen provisorischen Turm, der vorbeikommende Feinde %$heroes.hero_builder.defensive_turret.duration[2]%$ Sekunden lang angreift und pro Angriff %$heroes.hero_builder.defensive_turret.attack.damage_min[2]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[2]%$ physischen Schaden verursacht.",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_3"] = "Baut einen provisorischen Turm, der vorbeikommende Feinde %$heroes.hero_builder.defensive_turret.duration[3]%$ Sekunden lang angreift und pro Angriff %$heroes.hero_builder.defensive_turret.attack.damage_min[3]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[3]%$ physischen Schaden verursacht.",
["HERO_BUILDER_DEFENSIVE_TURRET_TITLE"] = "VERTEIDIGUNGSTURM",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_1"] = "Dreht schnell seinen Holzbalken, und fügt Feinden in seiner Umgebung %$heroes.hero_builder.demolition_man.s_damage_min[1]%$-%$heroes.hero_builder.demolition_man.s_damage_max[1]%$ physischen Schaden zu.",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_2"] = "Dreht schnell seinen Holzbalken, und fügt Feinden in seiner Umgebung %$heroes.hero_builder.demolition_man.s_damage_min[2]%$-%$heroes.hero_builder.demolition_man.s_damage_max[2]%$ physischen Schaden zu.",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_3"] = "Dreht schnell seinen Holzbalken, und fügt Feinden in seiner Umgebung %$heroes.hero_builder.demolition_man.s_damage_min[3]%$-%$heroes.hero_builder.demolition_man.s_damage_max[3]%$ physischen Schaden zu.",
["HERO_BUILDER_DEMOLITION_MAN_TITLE"] = "ABRISSMANN",
["HERO_BUILDER_DESC"] = "Jahre in der Verantwortung für den Bau der Linireaner Verteidigungsanlagen haben Torres einiges über den Kampf gelehrt. Jetzt, da das ganze Königreich in Gefahr ist (und es leid ist, von der Seitenlinie zuzusehen), setzt er all sein Werkzeug und Wissen in der Schlacht ein.",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_1"] = "Torres hört auf zu kämpfen, um einen Snack zu essen, und heilt sich selbst um %$heroes.hero_builder.lunch_break.heal_hp[1]%$ Gesundheitspunkte.",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_2"] = "Torres hört auf zu kämpfen, um einen Snack zu essen, und heilt sich selbst um %$heroes.hero_builder.lunch_break.heal_hp[2]%$ Gesundheitspunkte.",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_3"] = "Torres hört auf zu kämpfen, um einen Snack zu essen, und heilt sich selbst um %$heroes.hero_builder.lunch_break.heal_hp[3]%$ Gesundheitspunkte.",
["HERO_BUILDER_LUNCH_BREAK_TITLE"] = "MITTAGSPAUSE",
["HERO_BUILDER_NAME"] = "Torres",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_1"] = "Ruft zwei Bauarbeiter, die %$heroes.hero_builder.overtime_work.soldier.duration%$ Sekunden lang an seiner Seite kämpfen.",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_2"] = "Die Bauarbeiter haben %$heroes.hero_builder.overtime_work.soldier.hp_max[2]%$ Gesundheit und verursachen %$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_max[2]%$ physischen Schaden. Sie kämpfen für %$heroes.hero_builder.overtime_work.soldier.duration%$ Sekunden.",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_3"] = "Die Baumeister haben %$heroes.hero_builder.overtime_work.soldier.hp_max[3]%$ Gesundheit und verursachen %$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_max[3]%$ körperlichen Schaden. Sie kämpfen für %$heroes.hero_builder.overtime_work.soldier.duration%$ Sekunden. ",
["HERO_BUILDER_OVERTIME_WORK_TITLE"] = "MÄNNER BEI DER ARBEIT",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_1"] = "Lässt eine riesige Stahlkugel auf den Weg fallen, die %$heroes.hero_builder.ultimate.damage[2]%$ physischen Schaden verursacht und Gegner für %$heroes.hero_builder.ultimate.stun_duration[2]%$ Sekunden betäubt.",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_2"] = "Lässt eine riesige Stahlkugel auf den Weg fallen, die %$heroes.hero_builder.ultimate.damage[3]%$ physischen Schaden verursacht und Gegner für %$heroes.hero_builder.ultimate.stun_duration[3]%$ Sekunden betäubt.",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_3"] = "Lässt eine riesige Stahlkugel auf den Weg fallen, die %$heroes.hero_builder.ultimate.damage[4]%$ physischen Schaden verursacht und Gegner für %$heroes.hero_builder.ultimate.stun_duration[4]%$ Sekunden betäubt.",
["HERO_BUILDER_WRECKING_BALL_MENUBOTTOM_DESCRIPTION"] = "Lässt eine Abrisskugel auf den Weg fallen, die Feinde beschädigt.",
["HERO_BUILDER_WRECKING_BALL_MENUBOTTOM_NAME"] = "Abrisskugel",
["HERO_BUILDER_WRECKING_BALL_TITLE"] = "ABRISSKUGEL",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_1"] = "Sylvara entfesselt ihre wahre Form für %$heroes.hero_dragon_arb.ultimate.duration[2]%$ Sekunden, in denen sie %$heroes.hero_dragon_arb.ultimate.s_bonuses[2]%$% Schaden, Geschwindigkeit, Widerstandsfähigkeiten erhält und einige ihrer Kräfte weiterentwickelt.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_2"] = "Sylvara entfesselt ihre wahre Form für %$heroes.hero_dragon_arb.ultimate.duration[3]%$ Sekunden, in denen sie %$heroes.hero_dragon_arb.ultimate.s_bonuses[3]%$% Schaden, Geschwindigkeit, Widerstandsfähigkeiten erhält und einige ihrer Kräfte weiterentwickelt.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_3"] = "Sylvara entfesselt ihre wahre Form für %$heroes.hero_dragon_arb.ultimate.duration[4]%$ Sekunden, in denen sie %$heroes.hero_dragon_arb.ultimate.s_bonuses[4]%$% Schaden, Geschwindigkeit, Widerstandsfähigkeiten erhält und einige ihrer Kräfte weiterentwickelt.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_MENUBOTTOM_DESCRIPTION"] = "Entfessle Sylvaras wahre Form.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_MENUBOTTOM_NAME"] = "Innere Natur",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_TITLE"] = "Innere Natur",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_1"] = "Verwandelt grüne Flächen in Arboreaner, die für %$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[1]%$ Sekunden kämpfen, während Innerer Natur ruft es stärkere Arboreaner herbei.",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_2"] = "Verwandelt grüne Flächen in Arboreaner, die für %$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[2]%$ Sekunden kämpfen, während Innerer Natur ruft es stärkere Arboreaner herbei.",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_3"] = "Verwandelt grüne Flächen in Arboreaner, die für %$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[3]%$ Sekunden kämpfen, während Innerer Natur ruft es stärkere Arboreaner herbei.",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_TITLE"] = "Ruf des Waldes",
["HERO_DRAGON_ARB_CLASS"] = "Naturkraft",
["HERO_DRAGON_ARB_DESC"] = "Der Naturdrache und Beschützer der Arboreaner, sie webt Wälder mit ihrem Atem und lässt den Wind mit ihren Flügeln tanzen. Wie die Natur selbst kann sie sowohl fürsorglich als auch strafend sein. Achte darauf, keinen Müll zu hinterlassen!",
["HERO_DRAGON_ARB_NAME"] = "Sylvara",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_1"] = "Alle %$heroes.hero_dragon_arb.thorn_bleed.cooldown[1]%$ Sekunden verstärkt Sylvara ihren nächsten Atemzug, um Gegner abhängig von ihrer Geschwindigkeit zu schädigen, während Innerer Natur hat sie eine %$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[1]%$% Chance auf eine sofortige Tötung.",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_2"] = "Alle %$heroes.hero_dragon_arb.thorn_bleed.cooldown[2]%$ Sekunden verstärkt Sylvara ihren nächsten Atemzug, um Gegner abhängig von ihrer Geschwindigkeit zu schädigen, während Innerer Natur hat sie eine %$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[2]%$% Chance auf eine sofortige Tötung.",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_3"] = "Alle %$heroes.hero_dragon_arb.thorn_bleed.cooldown[3]%$ Sekunden verstärkt Sylvara ihren nächsten Atemzug, um Gegner abhängig von ihrer Geschwindigkeit zu schädigen, während Innere Natur hat sie eine %$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[3]%$% Chance auf eine sofortige Tötung.",
["HERO_DRAGON_ARB_THORN BLEED_TITLE"] = "Dorniger Atem",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_1"] = "Erhöht den Schaden benachbarter Türme um %$heroes.hero_dragon_arb.tower_runes.s_damage_factor[1]%$% für %$heroes.hero_dragon_arb.tower_runes.duration[1]%$ Sekunden.",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_2"] = "Erhöht den Schaden benachbarter Türme um %$heroes.hero_dragon_arb.tower_runes.s_damage_factor[2]%$% für %$heroes.hero_dragon_arb.tower_runes.duration[2]%$ Sekunden.",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_3"] = "Erhöht den Schaden benachbarter Türme um %$heroes.hero_dragon_arb.tower_runes.s_damage_factor[3]%$% für %$heroes.hero_dragon_arb.tower_runes.duration[3]%$ Sekunden.",
["HERO_DRAGON_ARB_TOWER RUNES_TITLE"] = "Tiefe Wurzeln",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_1"] = "Beschwört Pflanzen in der Nähe von Türmen, die %$heroes.hero_dragon_arb.tower_plants.duration[1]%$ Sekunden anhalten. Je nach ihrer Zugehörigkeit werden sie zu giftigen Pflanzen, die Schaden anrichten und verlangsamen, oder zu heilenden Pflanzen, die Verbündete heilen.",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_2"] = "Beschwört Pflanzen in der Nähe von Türmen, die %$heroes.hero_dragon_arb.tower_plants.duration[2]%$ Sekunden anhalten. Je nach ihrer Zugehörigkeit werden sie zu giftigen Pflanzen, die Schaden anrichten und verlangsamen, oder zu heilenden Pflanzen, die Verbündete heilen.",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_3"] = "Beschwört Pflanzen in der Nähe von Türmen, die %$heroes.hero_dragon_arb.tower_plants.duration[3]%$ Sekunden anhalten. Je nach ihrer Zugehörigkeit werden sie zu giftigen Pflanzen, die Schaden anrichten und verlangsamen, oder zu heilenden Pflanzen, die Verbündete heilen.",
["HERO_DRAGON_ARB_TOWER_PLANTS_TITLE"] = "Lebensbringer",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_1"] = "Schleudert %$heroes.hero_dragon_bone.burst.proj_count[1]%$ magische Projektile in alle Richtungen, die jeweils %$heroes.hero_dragon_bone.burst.damage_min[1]%$-%$heroes.hero_dragon_bone.burst.damage_max[1]%$ echten Schaden verursachen und Feinde infizieren.",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_2"] = "Schleudert %$heroes.hero_dragon_bone.burst.proj_count[2]%$ magische Projektile in alle Richtungen, die jeweils %$heroes.hero_dragon_bone.burst.damage_min[2]%$-%$heroes.hero_dragon_bone.burst.damage_max[2]%$ echten Schaden verursachen und Feinde infizieren.",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_3"] = "Schleudert %$heroes.hero_dragon_bone.burst.proj_count[3]%$ magische Projektile in alle Richtungen, die jeweils %$heroes.hero_dragon_bone.burst.damage_min[3]%$-%$heroes.hero_dragon_bone.burst.damage_max[3]%$ echten Schaden verursachen und Feinde infizieren.",
["HERO_DRAGON_BONE_BURST_TITLE"] = "VERBREITUNGS-AUSBRUCH",
["HERO_DRAGON_BONE_CLASS"] = "Drachenleichnam",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_1"] = "Bedeckt ein Gebiet mit einer pestilenten Wolke, die Feinde mit der Seuche infiziert und sie für %$heroes.hero_dragon_bone.cloud.duration[1]%$ Sekunden verlangsamt.",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_2"] = "Bedeckt ein Gebiet mit einer pestilenten Wolke, die Feinde mit der Seuche infiziert sie für %$heroes.hero_dragon_bone.cloud.duration[2]%$ Sekunden verlangsamt.",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_3"] = "Bedeckt ein Gebiet mit einer pestilenten Wolke, die Feinde mit der Seuche infiziert und sie für %$heroes.hero_dragon_bone.cloud.duration[3]%$ Sekunden verlangsamt.",
["HERO_DRAGON_BONE_CLOUD_TITLE"] = "SEUCHENWOLKE",
["HERO_DRAGON_BONE_DESC"] = "Nachdem er von Vez'nan während dessen Eroberungskampagne befreit wurde, bot Bonehart an, seine Schuld zu begleichen, indem er seine Kräfte einsetzte, um das Land nach Magieanwendern zu durchsuchen, die eine Bedrohung für die Pläne des Dunklen Zauberers darstellen könnten.",
["HERO_DRAGON_BONE_NAME"] = "Bonehart",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_1"] = "Stürzt sich auf den Pfad und verursacht %$heroes.hero_dragon_bone.nova.damage_min[1]%$-%$heroes.hero_dragon_bone.nova.damage_max[1]%$ Explosionsschaden an Feinden und infiziert sie mit der Seuche.",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_2"] = "Stürzt sich auf den Pfad und verursacht %$heroes.hero_dragon_bone.nova.damage_min[2]%$-%$heroes.hero_dragon_bone.nova.damage_max[2]%$ Explosionsschaden an Feinden und infiziert sie mit der Seuche.",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_3"] = "Stürzt sich auf den Pfad und verursacht %$heroes.hero_dragon_bone.nova.damage_min[3]%$-%$heroes.hero_dragon_bone.nova.damage_max[3]%$ Explosionsschaden an Feinden und infiziert sie mit der Seuche.",
["HERO_DRAGON_BONE_NOVA_TITLE"] = "SEUCHEN-NOVA",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_1"] = "Wirft %$heroes.hero_dragon_bone.rain.bones_count[1]%$ Knochendorne auf Feinde, verursacht %$heroes.hero_dragon_bone.rain.damage_min[1]%$-%$heroes.hero_dragon_bone.rain.damage_max[1]%$ echten Schaden und betäubt sie kurzzeitig.",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_2"] = "Wirft %$heroes.hero_dragon_bone.rain.bones_count[2]%$ Knochendorne auf Feinde, verursacht %$heroes.hero_dragon_bone.rain.damage_min[2]%$-%$heroes.hero_dragon_bone.rain.damage_max[2]%$ echten Schaden und betäubt sie kurzzeitig.",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_3"] = "Wirft %$heroes.hero_dragon_bone.rain.bones_count[3]%$ Knochendorne auf Feinde, verursacht %$heroes.hero_dragon_bone.rain.damage_min[3]%$-%$heroes.hero_dragon_bone.rain.damage_max[3]%$ echten Schaden und betäubt sie kurzzeitig.",
["HERO_DRAGON_BONE_RAIN_TITLE"] = "DORNENREGEN",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_1"] = "Beschwört zwei Knochendraken. Jeder Drake hat %$heroes.hero_dragon_bone.ultimate.dog.hp[2]%$ Lebenspunkte und verursacht %$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[2]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[2]%$ physischen Schaden.",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_2"] = "Beschwört zwei Knochendraken. Jeder Drake hat %$heroes.hero_dragon_bone.ultimate.dog.hp[3]%$ Lebenspunkte und verursacht %$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[3]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[3]%$ physischen Schaden.",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_3"] = "Beschwört zwei Knochendraken. Jeder Drake hat %$heroes.hero_dragon_bone.ultimate.dog.hp[4]%$ Lebenspunkte und verursacht %$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[4]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[4]%$ physischen Schaden.",
["HERO_DRAGON_BONE_RAISE_DRAKES_MENUBOTTOM_DESCRIPTION"] = "Beschwört zwei Knochendraken.",
["HERO_DRAGON_BONE_RAISE_DRAKES_MENUBOTTOM_NAME"] = "Draken Beschwören",
["HERO_DRAGON_BONE_RAISE_DRAKES_TITLE"] = "DRAKEN BESCHWÖREN",
["HERO_DRAGON_GEM_CLASS"] = "Ungebrochen",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_1"] = "Hüllt einen Feind für einige Sekunden in einen Kristall. Danach explodiert der Kristall, tötet das Ziel sofort und verursacht %$heroes.hero_dragon_gem.crystal_instakill.s_damage[1]%$ echten Schaden um sich herum.",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_2"] = "Hüllt einen Feind für einige Sekunden in einen Kristall. Danach explodiert der Kristall, tötet das Ziel sofort und verursacht %$heroes.hero_dragon_gem.crystal_instakill.s_damage[2]%$ echten Schaden um sich herum.",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_3"] = "Hüllt einen Feind für einige Sekunden in einen Kristall. Danach explodiert der Kristall, tötet das Ziel sofort und verursacht %$heroes.hero_dragon_gem.crystal_instakill.s_damage[3]%$ echten Schaden um sich herum.",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_TITLE"] = "GRANATGRAB",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_1"] = "Wirft einen Kristall auf den Weg, der die Feindgeschwindigkeit um %$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$% reduziert und alle 1 Sekunden %$heroes.hero_dragon_gem.crystal_totem.s_damage[1]%$ magischen Schaden in der Umgebung verursacht. Hält %$heroes.hero_dragon_gem.crystal_totem.duration[1]%$ Sekunden an.",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_2"] = "Wirft einen Kristall auf den Weg, der die Feindgeschwindigkeit um %$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$% reduziert und alle 1 Sekunden %$heroes.hero_dragon_gem.crystal_totem.s_damage[2]%$ magischen Schaden in der Umgebung verursacht. Hält %$heroes.hero_dragon_gem.crystal_totem.duration[2]%$ Sekunden an.",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_3"] = "Wirft einen Kristall auf den Weg, der die Feindgeschwindigkeit um %$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$% reduziert und alle 1 Sekunden %$heroes.hero_dragon_gem.crystal_totem.s_damage[3]%$ magischen Schaden in der Umgebung verursacht. Hält %$heroes.hero_dragon_gem.crystal_totem.duration[3]%$ Sekunden an.",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_TITLE"] = "ENERGIELEITUNG",
["HERO_DRAGON_GEM_DESC"] = "Das abgeschiedene Leben von Kosmyr wurde unterbrochen, als der Kult seine Operationen im Verlassenen Canyon begann. Um die Eindringlinge loszuwerden, machte der Drache einen Deal mit Vez'nan, um sich der Allianz gegen einen gemeinsamen Feind anzuschließen.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_1"] = "Beschwört %$heroes.hero_dragon_gem.ultimate.max_shards[2]%$ Kristallschauer, die %$heroes.hero_dragon_gem.ultimate.damage_min[2]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[2]%$ echten Schaden bei Feinden im Bereich anrichten.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_2"] = "Beschwört %$heroes.hero_dragon_gem.ultimate.max_shards[3]%$ Kristallschauer, die %$heroes.hero_dragon_gem.ultimate.damage_min[3]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[3]%$ echten Schaden bei Feinden im Bereich anrichten.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_3"] = "Beschwört %$heroes.hero_dragon_gem.ultimate.max_shards[4]%$ Kristallschauer, die %$heroes.hero_dragon_gem.ultimate.damage_min[4]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[4]%$ echten Schaden bei Feinden im Bereich anrichten.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_MENUBOTTOM_DESCRIPTION"] = "Wirft verschiedene Kristall-Salven gegen Feinde.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_MENUBOTTOM_NAME"] = "Kristalllawine",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_TITLE"] = "KRISTALLLAWINE",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_1"] = "Lässt Kristallspitzen auf den Pfaden um sich herum wachsen und fügt jedem getroffenen Feind %$heroes.hero_dragon_gem.floor_impact.damage_min[1]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[1]%$ physischen Schaden zu.",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_2"] = "Lässt Kristallspitzen auf den Pfaden um sich herum wachsen und fügt jedem getroffenen Feind %$heroes.hero_dragon_gem.floor_impact.damage_min[2]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[2]%$ physischen Schaden zu.",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_3"] = "Lässt Kristallspitzen auf den Pfaden um sich herum wachsen und fügt jedem getroffenen Feind %$heroes.hero_dragon_gem.floor_impact.damage_min[3]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[3]%$ physischen Schaden zu.",
["HERO_DRAGON_GEM_FLOOR_IMPACT_TITLE"] = "PRISMATISCHE SCHERBEN",
["HERO_DRAGON_GEM_NAME"] = "Kosmyr",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_1"] = "Kristallisiert eine Gruppe von Feinden und betäubt sie für %$heroes.hero_dragon_gem.stun.duration[1]%$ Sekunden.",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_2"] = "Kristallisiert eine Gruppe von Feinden und betäubt sie für %$heroes.hero_dragon_gem.stun.duration[2]%$ Sekunden.",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_3"] = "Kristallisiert eine Gruppe von Feinden und betäubt sie für %$heroes.hero_dragon_gem.stun.duration[3]%$ Sekunden.",
["HERO_DRAGON_GEM_STUN_TITLE"] = "LÄHMENDER ATEM",
["HERO_HUNTER_BEASTS_DESCRIPTION_1"] = "Beschwört 2 Fledermäuse, die nahegelegene Feinde für %$heroes.hero_hunter.beasts.duration[1]%$ Sekunden angreifen und dabei %$heroes.hero_hunter.beasts.damage_min[1]%$-%$heroes.hero_hunter.beasts.damage_max[1]%$ physischen Schaden verursachen. Jede Fledermaus hat eine Chance, %$heroes.hero_hunter.beasts.gold_to_steal[1]%$ Gold von ihrem Ziel zu stehlen.",
["HERO_HUNTER_BEASTS_DESCRIPTION_2"] = "Beschwört 2 Fledermäuse, die nahegelegene Feinde für %$heroes.hero_hunter.beasts.duration[2]%$ Sekunden angreifen und dabei %$heroes.hero_hunter.beasts.damage_min[2]%$-%$heroes.hero_hunter.beasts.damage_max[2]%$ physischen Schaden verursachen. Jede Fledermaus hat eine Chance, %$heroes.hero_hunter.beasts.gold_to_steal[2]%$ Gold von ihrem Ziel zu stehlen.",
["HERO_HUNTER_BEASTS_DESCRIPTION_3"] = "Beschwört 2 Fledermäuse, die nahegelegene Feinde für %$heroes.hero_hunter.beasts.duration[3]%$ Sekunden angreifen und dabei %$heroes.hero_hunter.beasts.damage_min[3]%$-%$heroes.hero_hunter.beasts.damage_max[3]%$ physischen Schaden verursachen. Jede Fledermaus hat eine Chance, %$heroes.hero_hunter.beasts.gold_to_steal[3]%$ Gold von ihrem Ziel zu stehlen.",
["HERO_HUNTER_BEASTS_TITLE"] = "DÄMMERUNGSBESTIEN",
["HERO_HUNTER_CLASS"] = "Silberne Jägerin",
["HERO_HUNTER_DESC"] = "Geboren aus der Verbindung eines Vampirs und eines berühmten Jägers folgt Anya den Spuren ihres Vaters im Kampf gegen die Bewohner der Dunkelheit. Die unermüdliche Jagd auf Kultisten führte sie schnell in die südlichen Länder und zum Beitritt zur Allianz.",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_1"] = "Jeder 7. Nahkampfangriff verursacht %$heroes.hero_hunter.heal_strike.damage_min[1]%$-%$heroes.hero_hunter.heal_strike.damage_max[1]%$ echten Schaden und heilt Anya um %$heroes.hero_hunter.heal_strike.heal_factor[1]%$% der maximalen Gesundheit ihres Ziels.",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_2"] = "Jeder 7. Nahkampfangriff verursacht %$heroes.hero_hunter.heal_strike.damage_min[2]%$-%$heroes.hero_hunter.heal_strike.damage_max[2]%$ echten Schaden und heilt Anya um %$heroes.hero_hunter.heal_strike.heal_factor[2]%$% der maximalen Gesundheit ihres Ziels.",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_3"] = "Jeder 7. Nahkampfangriff verursacht %$heroes.hero_hunter.heal_strike.damage_min[3]%$-%$heroes.hero_hunter.heal_strike.damage_max[3]%$ echten Schaden und heilt Anya um %$heroes.hero_hunter.heal_strike.heal_factor[3]%$% der maximalen Gesundheit ihres Ziels.",
["HERO_HUNTER_HEAL_STRIKE_TITLE"] = "VAMPIRKLAUEN",
["HERO_HUNTER_NAME"] = "Anya",
["HERO_HUNTER_RICOCHET_DESCRIPTION_1"] = "Anya verwandelt sich in Nebel und springt zwischen %$heroes.hero_hunter.ricochet.s_bounces[1]%$ Feinden hin und her, wobei sie jedem %$heroes.hero_hunter.ricochet.damage_min[1]%$-%$heroes.hero_hunter.ricochet.damage_max[1]%$ physischen Schaden zufügt.",
["HERO_HUNTER_RICOCHET_DESCRIPTION_2"] = "Anya verwandelt sich in Nebel und springt zwischen %$heroes.hero_hunter.ricochet.s_bounces[2]%$ Feinden hin und her, wobei sie jedem %$heroes.hero_hunter.ricochet.damage_min[2]%$-%$heroes.hero_hunter.ricochet.damage_max[2]%$ physischen Schaden zufügt.",
["HERO_HUNTER_RICOCHET_DESCRIPTION_3"] = "Anya verwandelt sich in Nebel und springt zwischen %$heroes.hero_hunter.ricochet.s_bounces[3]%$ Feinden hin und her, wobei sie jedem %$heroes.hero_hunter.ricochet.damage_min[3]%$-%$heroes.hero_hunter.ricochet.damage_max[3]%$ physischen Schaden zufügt.",
["HERO_HUNTER_RICOCHET_TITLE"] = "NEBELSCHRITT",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_1"] = "Schießt auf alle Feinde in ihrer Umgebung und fügt jedem %$heroes.hero_hunter.shoot_around.s_damage_min[1]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[1]%$ echten Schaden zu.",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_2"] = "Schießt auf alle Feinde in ihrer Umgebung und fügt jedem %$heroes.hero_hunter.shoot_around.s_damage_min[2]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[2]%$ echten Schaden zu.",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_3"] = "Schießt auf alle Feinde in ihrer Umgebung und fügt jedem %$heroes.hero_hunter.shoot_around.s_damage_min[2]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[2]%$ echten Schaden zu.",
["HERO_HUNTER_SHOOT_AROUND_TITLE"] = "SILBERSTURM",
["HERO_HUNTER_SPIRIT_DESCRIPTION_1"] = "Beschwört eine Projektion von Dante, die %$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[2]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[2]%$ wahren Schaden pro Sekunde für %$heroes.hero_hunter.ultimate.duration%$ Sekunden verursacht. Belebt Anya wieder, wenn ihr Körper in der Nähe ist.",
["HERO_HUNTER_SPIRIT_DESCRIPTION_2"] = "Beschwört eine Projektion von Dante, die %$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[3]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[3]%$ wahren Schaden pro Sekunde für %$heroes.hero_hunter.ultimate.duration%$ Sekunden verursacht. Belebt Anya wieder, wenn ihr Körper in der Nähe ist.",
["HERO_HUNTER_SPIRIT_DESCRIPTION_3"] = "Beschwört eine Projektion von Dante, die %$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[4]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[4]%$ wahren Schaden pro Sekunde für %$heroes.hero_hunter.ultimate.duration%$ Sekunden verursacht. Belebt Anya wieder, wenn ihr Körper in der Nähe ist.",
["HERO_HUNTER_SPIRIT_MENUBOTTOM_DESCRIPTION"] = "Beschwört eine Projektion von Dante, die Feinde verlangsamt und angreift.",
["HERO_HUNTER_SPIRIT_MENUBOTTOM_NAME"] = "Jägerhilfe",
["HERO_HUNTER_SPIRIT_TITLE"] = "JÄGERHILFE",
["HERO_HUNTER_ULTIMATE_ENTITY_NAME"] = "Dantes Projektion",
["HERO_LAVA_CLASS"] = "Geschmolzene Wut",
["HERO_LAVA_DESC"] = "Ein feuriges und zerstörerisches Wesen mit schlechtem Temperament, das durch Grymbeards Aktivitäten aus einem tiefen Schlaf erwacht ist. Da Dialoge nicht seine Stärke sind, wird Kratoa sich durch die Reihen seiner Feinde prügeln, bis er sich beruhigt und wieder schlafen kann.",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_1"] = "Wirft eine Lava-Kugel, die %$heroes.hero_lava.double_trouble.s_damage[1]%$ explosiven Schaden an Feinden verursacht und eine Magmilbe mit %$heroes.hero_lava.double_trouble.soldier.hp_max[1]%$ Gesundheit beschwört, die für %$heroes.hero_lava.double_trouble.soldier.duration%$ Sekunden kämpft.",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_2"] = "Wirft eine Lava-Kugel, die %$heroes.hero_lava.double_trouble.s_damage[2]%$ explosiven Schaden an Feinden verursacht und eine Magmilbe mit %$heroes.hero_lava.double_trouble.soldier.hp_max[2]%$ Gesundheit beschwört, die für %$heroes.hero_lava.double_trouble.soldier.duration%$ Sekunden kämpft.",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_3"] = "Wirft eine Lava-Kugel, die %$heroes.hero_lava.double_trouble.s_damage[3]%$ explosiven Schaden an Feinden verursacht und eine Magmilbe mit %$heroes.hero_lava.double_trouble.soldier.hp_max[3]%$ Gesundheit beschwört, die für %$heroes.hero_lava.double_trouble.soldier.duration%$ Sekunden kämpft.",
["HERO_LAVA_DOUBLE_TROUBLE_SOLDIER_NAME"] = "Magmilbe",
["HERO_LAVA_DOUBLE_TROUBLE_TITLE"] = "DOPPELTE BELÄSTIGUNG",
["HERO_LAVA_HOTHEADED_DESCRIPTION_1"] = "Wenn Kratoa wiederbelebt, gewährt er benachbarten Türmen einen %$heroes.hero_lava.hotheaded.s_damage_factors[1]%$% Schadensbonus für %$heroes.hero_lava.hotheaded.durations[1]%$ Sekunden.",
["HERO_LAVA_HOTHEADED_DESCRIPTION_2"] = "Wenn Kratoa wiederbelebt, gewährt er benachbarten Türmen einen %$heroes.hero_lava.hotheaded.s_damage_factors[2]%$% Schadensbonus für %$heroes.hero_lava.hotheaded.durations[2]%$ Sekunden.",
["HERO_LAVA_HOTHEADED_DESCRIPTION_3"] = "Wenn Kratoa wiederbelebt, gewährt er benachbarten Türmen einen %$heroes.hero_lava.hotheaded.s_damage_factors[3]%$% Schadensbonus für %$heroes.hero_lava.hotheaded.durations[3]%$ Sekunden.",
["HERO_LAVA_HOTHEADED_TITLE"] = "HITZKOPF",
["HERO_LAVA_NAME"] = "Kratoa",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_1"] = "Schlägt wiederholt auf einen Feind ein, verursacht dabei %$heroes.hero_lava.temper_tantrum.s_damage_min[1]%$-%$heroes.hero_lava.temper_tantrum.s_damage_max[1]%$ physischen Schaden und betäubt das Ziel für %$heroes.hero_lava.temper_tantrum.duration[1]%$ Sekunden.",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_2"] = "Schlägt wiederholt auf einen Feind ein, verursacht dabei %$heroes.hero_lava.temper_tantrum.s_damage_min[2]%$-%$heroes.hero_lava.temper_tantrum.s_damage_max[2]%$ physischen Schaden und betäubt das Ziel für %$heroes.hero_lava.temper_tantrum.duration[2]%$ Sekunden.",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_3"] = "Schlägt wiederholt auf einen Feind ein, verursacht dabei %$heroes.hero_lava.temper_tantrum.s_damage_min[3]%$-%$heroes.hero_lava.temper_tantrum.s_damage_max[3]%$ physischen Schaden und betäubt das Ziel für %$heroes.hero_lava.temper_tantrum.duration[3]%$ Sekunden.",
["HERO_LAVA_TEMPER_TANTRUM_TITLE"] = "WUTANFALL",
["HERO_LAVA_ULTIMATE_DESCRIPTION_1"] = "Wirft %$heroes.hero_lava.ultimate.fireball_count[2]%$ Lava-Tropfen auf den Weg, von denen jeder %$heroes.hero_lava.ultimate.bullet.s_damage[2]%$ echten Schaden an jedem getroffenen Feind verursacht und ihn für %$heroes.hero_lava.ultimate.bullet.scorch.duration%$ Sekunden verbrennt.",
["HERO_LAVA_ULTIMATE_DESCRIPTION_2"] = "Wirft %$heroes.hero_lava.ultimate.fireball_count[3]%$ Lava-Tropfen auf den Weg, von denen jeder %$heroes.hero_lava.ultimate.bullet.s_damage[3]%$ echten Schaden an jedem getroffenen Feind verursacht und ihn für %$heroes.hero_lava.ultimate.bullet.scorch.duration%$ Sekunden verbrennt.",
["HERO_LAVA_ULTIMATE_DESCRIPTION_3"] = "Wirft %$heroes.hero_lava.ultimate.fireball_count[4]%$ Lava-Tropfen auf den Weg, von denen jeder %$heroes.hero_lava.ultimate.bullet.s_damage[4]%$ echten Schaden an jedem getroffenen Feind verursacht und ihn für %$heroes.hero_lava.ultimate.bullet.scorch.duration%$ Sekunden verbrennt.",
["HERO_LAVA_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "Wirft Lava-Tropfen auf den Weg, die den Boden verbrennt.",
["HERO_LAVA_ULTIMATE_MENUBOTTOM_NAME"] = "Wutausbruch",
["HERO_LAVA_ULTIMATE_TITLE"] = "WUTAUSBRUCH",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_1"] = "Versprüht Lava auf Feinde und verursacht dabei %$heroes.hero_lava.wild_eruption.s_damage[1]%$ echten Schaden pro Sekunde und verbrennt die Feinde für %$heroes.hero_lava.wild_eruption.duration[1]%$ Sekunden.",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_2"] = "Versprüht Lava auf Feinde und verursacht dabei %$heroes.hero_lava.wild_eruption.s_damage[2]%$ echten Schaden pro Sekunde und verbrennt die Feinde für %$heroes.hero_lava.wild_eruption.duration[2]%$ Sekunden.",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_3"] = "Versprüht Lava auf Feinde und verursacht dabei %$heroes.hero_lava.wild_eruption.s_damage[3]%$ echten Schaden pro Sekunde und verbrennt die Feinde für %$heroes.hero_lava.wild_eruption.duration[3]%$ Sekunden.",
["HERO_LAVA_WILD_ERUPTION_TITLE"] = "WILDE ERUPTION",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_1"] = "Beschwört %$heroes.hero_lumenir.ultimate.soldier_count[1]%$ Krieger des Lichts, die nahe Feinde kurzzeitig betäuben und %$heroes.hero_lumenir.ultimate.damage_min[1]%$-%$heroes.hero_lumenir.ultimate.damage_max[1]%$ wahren Schaden zufügen.",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_2"] = "Beschwört %$heroes.hero_lumenir.ultimate.soldier_count[2]%$ Krieger des Lichts, die nahe Feinde kurzzeitig betäuben und %$heroes.hero_lumenir.ultimate.damage_min[2]%$-%$heroes.hero_lumenir.ultimate.damage_max[2]%$ wahren Schaden zufügen.",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_3"] = "Beschwört %$heroes.hero_lumenir.ultimate.soldier_count[3]%$ Krieger des Lichts, die nahe Feinde kurzzeitig betäuben und %$heroes.hero_lumenir.ultimate.damage_min[3]%$-%$heroes.hero_lumenir.ultimate.damage_max[3]%$ wahren Schaden zufügen.",
["HERO_LUMENIR_ARROW_STORM_MENUBOTTOM_DESCRIPTION"] = "Beschwört göttliche Krieger, die gegen Feinde kämpfen.",
["HERO_LUMENIR_ARROW_STORM_MENUBOTTOM_NAME"] = "Ruf des Triumphs",
["HERO_LUMENIR_ARROW_STORM_TITLE"] = "RUF DES TRIUMPHS",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_1"] = "Wirft ein göttliches Schwert des Lichts auf den stärksten Feind in der Nähe, fügt %$heroes.hero_lumenir.celestial_judgement.damage[1]%$ echten Schaden zu und betäubt ihn für %$heroes.hero_lumenir.celestial_judgement.stun_duration[1]%$ Sekunden.",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_2"] = "Wirft ein göttliches Schwert des Lichts auf den stärksten Feind in der Nähe, fügt %$heroes.hero_lumenir.celestial_judgement.damage[2]%$ echten Schaden zu und betäubt ihn für %$heroes.hero_lumenir.celestial_judgement.stun_duration[2]%$ Sekunden.",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_3"] = "Wirft ein göttliches Schwert des Lichts auf den stärksten Feind in der Nähe, fügt %$heroes.hero_lumenir.celestial_judgement.damage[3]%$ echten Schaden zu und betäubt ihn für %$heroes.hero_lumenir.celestial_judgement.stun_duration[3]%$ Sekunden.",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_TITLE"] = "HIMMLISCHES URTEIL",
["HERO_LUMENIR_CLASS"] = "Lichtbringer",
["HERO_LUMENIR_DESC"] = "Schwebend zwischen den Reichen, steht Lumenir als der Avatar der Gerechtigkeit und Entschlossenheit. Sie ist die legendäre Lichtbringerin, verehrt von den Paladinen von Linirea, denen sie ihren Segen erteilt, um ihnen große Kräfte im Kampf gegen das Böse zu verleihen.",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_1"] = "Erschafft %$heroes.hero_lumenir.fire_balls.flames_count[1]%$ Kugeln göttlichen Lichts, die sich auf dem Pfad bewegen und Feinden Schaden zufügen. Jede Kugel fügt jedem durchquerten Feind %$heroes.hero_lumenir.fire_balls.flame_damage_min[1]%$-%$heroes.hero_lumenir.fire_balls.flame_damage_max[1]%$ wahren Schaden zu.",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_2"] = "Erschafft %$heroes.hero_lumenir.fire_balls.flames_count[2]%$ Kugeln göttlichen Lichts, die sich auf dem Pfad bewegen und Feinden Schaden zufügen. Jede Kugel fügt jedem durchquerten Feind %$heroes.hero_lumenir.fire_balls.flame_damage_min[2]%$-%$heroes.hero_lumenir.fire_balls.flame_damage_max[2]%$ wahren Schaden zu.",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_3"] = "Erschafft %$heroes.hero_lumenir.fire_balls.flames_count[3]%$ Kugeln göttlichen Lichts, die sich auf dem Pfad bewegen und Feinden Schaden zufügen. Jede Kugel fügt jedem durchquerten Feind %$heroes.hero_lumenir.fire_balls.flame_damage_min[3]%$-%$heroes.hero_lumenir.fire_balls.flame_damage_max[3]%$ wahren Schaden zu.",
["HERO_LUMENIR_FIRE_BALLS_TITLE"] = "LEUCHTENDE WELLE",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_1"] = "Beschwört einen kleinen Lichtdrachen, der dem anderen ausgerüsteten Helden für %$heroes.hero_lumenir.mini_dragon.dragon.duration[1]%$ Sekunden folgt. Der Drache verursacht %$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[1]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[1]%$ physischen Schaden pro Angriff.",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_2"] = "Beschwört einen kleinen Lichtdrachen, der dem anderen ausgerüsteten Helden für %$heroes.hero_lumenir.mini_dragon.dragon.duration[2]%$ Sekunden folgt. Der Drache verursacht %$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[2]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[2]%$ physischen Schaden pro Angriff.",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_3"] = "Beschwört einen kleinen Lichtdrachen, der dem anderen ausgerüsteten Helden für %$heroes.hero_lumenir.mini_dragon.dragon.duration[3]%$ Sekunden folgt. Der Drache verursacht %$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[3]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[3]%$ physischen Schaden pro Angriff.",
["HERO_LUMENIR_MINI_DRAGON_TITLE"] = "LICHTBEGLEITER",
["HERO_LUMENIR_NAME"] = "Lumenir",
["HERO_LUMENIR_SHIELD_DESCRIPTION_1"] = "Verleiht verbündeten Einheiten einen Rüstungsschild von %$heroes.hero_lumenir.shield.armor[1]%$%, der %$heroes.hero_lumenir.shield.spiked_armor[1]%$% des Schadens an die Feinde zurückwirft.",
["HERO_LUMENIR_SHIELD_DESCRIPTION_2"] = "Verleiht verbündeten Einheiten einen Rüstungsschild von %$heroes.hero_lumenir.shield.armor[2]%$%, der %$heroes.hero_lumenir.shield.spiked_armor[2]%$% des Schadens an die Feinde zurückwirft.",
["HERO_LUMENIR_SHIELD_DESCRIPTION_3"] = "Verleiht verbündeten Einheiten einen Rüstungsschild von %$heroes.hero_lumenir.shield.armor[3]%$%, der %$heroes.hero_lumenir.shield.spiked_armor[3]%$% des Schadens an die Feinde zurückwirft.",
["HERO_LUMENIR_SHIELD_TITLE"] = "SEGEN DER VERGELTUNG",
["HERO_MECHA_CLASS"] = "Mobile Bedrohung",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_1"] = "Ruft ein Goblin-Zeppelin herbei, das Feinde in der Nähe des Zielgebiets bombardiert und pro Angriff %$heroes.hero_mecha.ultimate.ranged_attack.damage_min[2]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[2]%$ wahren Flächenschaden verursacht.",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_2"] = "Ruft ein Goblin-Zeppelin herbei, das Feinde in der Nähe des Zielgebiets bombardiert und pro Angriff %$heroes.hero_mecha.ultimate.ranged_attack.damage_min[3]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[3]%$ wahren Flächenschaden verursacht.",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_3"] = "Ruft ein Goblin-Zeppelin herbei, das Feinde in der Nähe des Zielgebiets bombardiert und pro Angriff %$heroes.hero_mecha.ultimate.ranged_attack.damage_min[4]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[4]%$ wahren Flächenschaden verursacht.",
["HERO_MECHA_DEATH_FROM_ABOVE_MENUBOTTOM_DESCRIPTION"] = "Beschwört ein Zeppelin, das Feinde im Gebiet bombardiert.",
["HERO_MECHA_DEATH_FROM_ABOVE_MENUBOTTOM_NAME"] = "Tod von oben",
["HERO_MECHA_DEATH_FROM_ABOVE_TITLE"] = "TOD VON OBEN",
["HERO_MECHA_DESC"] = "Geboren aus dem Geist von zwei verrückten Goblin-Tüftlern und erbaut auf den Fundamenten gestohlener zwergischer Technologie, ist Onagro die ultimative Kriegsmaschine der Grünhäute und ein erschreckender Anblick für die Feinde der Dunklen Armee.",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_1"] = "Beschwört %$heroes.hero_mecha.goblidrones.units%$ Drohnen, die Feinde für %$heroes.hero_mecha.goblidrones.drone.duration[1]%$ Sekunden angreifen und pro Angriff %$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[1]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[1]%$ physischen Schaden verursachen.",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_2"] = "Beschwört %$heroes.hero_mecha.goblidrones.units%$ Drohnen, die Feinde für %$heroes.hero_mecha.goblidrones.drone.duration[2]%$ Sekunden angreifen und pro Angriff %$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[2]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[2]%$ physischen Schaden verursachen.",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_3"] = "Beschwört %$heroes.hero_mecha.goblidrones.units%$ Drohnen, die Feinde für %$heroes.hero_mecha.goblidrones.drone.duration[3]%$ Sekunden angreifen und pro Angriff %$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[3]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[3]%$ physischen Schaden verursachen.",
["HERO_MECHA_GOBLIDRONES_TITLE"] = "GOBLIDRONEN",
["HERO_MECHA_MINE_DROP_DESCRIPTION_1"] = "Während es stillsteht, hinterlässt der Mech periodisch bis zu %$heroes.hero_mecha.mine_drop.max_mines[1]%$ Sprengminen auf dem Pfad. Die Minen explodieren und verursachen jeweils %$heroes.hero_mecha.mine_drop.damage_min[1]%$-%$heroes.hero_mecha.mine_drop.damage_max[1]%$ Sprengschaden.",
["HERO_MECHA_MINE_DROP_DESCRIPTION_2"] = "Während es stillsteht, hinterlässt der Mech periodisch bis zu %$heroes.hero_mecha.mine_drop.max_mines[2]%$ Sprengminen auf dem Pfad. Die Minen explodieren und verursachen jeweils %$heroes.hero_mecha.mine_drop.damage_min[2]%$-%$heroes.hero_mecha.mine_drop.damage_max[2]%$ Sprengschaden.",
["HERO_MECHA_MINE_DROP_DESCRIPTION_3"] = "Während es stillsteht, hinterlässt der Mech periodisch bis zu %$heroes.hero_mecha.mine_drop.max_mines[3]%$ Sprengminen auf dem Pfad. Die Minen explodieren und verursachen jeweils %$heroes.hero_mecha.mine_drop.damage_min[3]%$-%$heroes.hero_mecha.mine_drop.damage_max[3]%$ Sprengschaden.",
["HERO_MECHA_MINE_DROP_TITLE"] = "MINENABWURF",
["HERO_MECHA_NAME"] = "Onager",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_1"] = "Der Mech schlägt auf den Boden, betäubt kurzzeitig und fügt allen nahen Feinden %$heroes.hero_mecha.power_slam.s_damage[1]%$ physischen Schaden zu.",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_2"] = "Der Mech schlägt auf den Boden, betäubt kurzzeitig und fügt allen nahen Feinden %$heroes.hero_mecha.power_slam.s_damage[2]%$ physischen Schaden zu.",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_3"] = "Der Mech schlägt auf den Boden, betäubt kurzzeitig und fügt allen nahen Feinden %$heroes.hero_mecha.power_slam.s_damage[3]%$ physischen Schaden zu.",
["HERO_MECHA_POWER_SLAM_TITLE"] = "KRAFTSCHLAG",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_1"] = "Wirft eine Bombe, die Teer auf den Weg verschüttet und die Feinde um %$heroes.hero_mecha.tar_bomb.slow_factor%$% für %$heroes.hero_mecha.tar_bomb.duration[1]%$ Sekunden verlangsamt.",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_2"] = "Wirft eine Bombe, die Teer auf den Weg verschüttet und die Feinde um %$heroes.hero_mecha.tar_bomb.slow_factor%$% für %$heroes.hero_mecha.tar_bomb.duration[2]%$ Sekunden verlangsamt.",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_3"] = "Wirft eine Bombe, die Teer auf den Weg verschüttet und die Feinde um %$heroes.hero_mecha.tar_bomb.slow_factor%$% für %$heroes.hero_mecha.tar_bomb.duration[3]%$ Sekunden verlangsamt.",
["HERO_MECHA_TAR_BOMB_TITLE"] = "TEERBOMBE",
["HERO_MUYRN_CLASS"] = "Waldwächter",
["HERO_MUYRN_DESC"] = "Trotz seines kindlichen Aussehens schützt der Schelm Nyru seit Hunderten von Jahren den Wald mit seiner Verbindung zu den Kräften der Natur. Er schloss sich der Allianz an, um ein Ende der zunehmenden Wellen von Eindringlingen zu setzen, die seine Heimat bedrohen.",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_1"] = "Bezaubert alle Feinde in einem Bereich und verringert ihren Angriffsschaden um %$heroes.hero_muyrn.faery_dust.s_damage_factor[1]%$% für %$heroes.hero_muyrn.faery_dust.duration[1]%$ Sekunden.",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_2"] = "Bezaubert alle Feinde in einem Bereich und verringert ihren Angriffsschaden um %$heroes.hero_muyrn.faery_dust.s_damage_factor[2]%$% für %$heroes.hero_muyrn.faery_dust.duration[2]%$ Sekunden.",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_3"] = "Bezaubert alle Feinde in einem Bereich und verringert ihren Angriffsschaden um %$heroes.hero_muyrn.faery_dust.s_damage_factor[3]%$% für %$heroes.hero_muyrn.faery_dust.duration[3]%$ Sekunden.",
["HERO_MUYRN_FAERY_DUST_TITLE"] = "Schwächungszauber",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_1"] = "Im Kampf erschafft Nyru einen Blattschild um sich herum. Der Schild fügt %$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[1]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[1]%$ magischen Schaden pro Sekunde zu und heilt Nyru für %$heroes.hero_muyrn.leaf_whirlwind.duration[1]%$ Sekunden.",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_2"] = "Im Kampf erschafft Nyru einen Blattschild um sich herum. Der Schild fügt %$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[2]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[2]%$ magischen Schaden pro Sekunde zu und heilt Nyru für %$heroes.hero_muyrn.leaf_whirlwind.duration[2]%$ Sekunden.",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_3"] = "Im Kampf erschafft Nyru einen Blattschild um sich herum. Der Schild fügt %$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[3]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[3]%$ magischen Schaden pro Sekunde zu und heilt Nyru für %$heroes.hero_muyrn.leaf_whirlwind.duration[3]%$ Sekunden.",
["HERO_MUYRN_LEAF_WHIRLWIND_TITLE"] = "Blattwirbel",
["HERO_MUYRN_NAME"] = "Nyru",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_1"] = "Lässt für %$heroes.hero_muyrn.ultimate.duration[2]%$ Sekunden Wurzeln über einem Bereich sprießen, verlangsamt Feinde und fügt %$heroes.hero_muyrn.ultimate.s_damage_min[2]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[2]%$ echten Schaden pro Sekunde zu.",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_2"] = "Lässt für %$heroes.hero_muyrn.ultimate.duration[3]%$ Sekunden Wurzeln über einem Bereich sprießen, verlangsamt Feinde und fügt %$heroes.hero_muyrn.ultimate.s_damage_min[3]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[3]%$ echten Schaden pro Sekunde zu.",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_3"] = "Lässt für %$heroes.hero_muyrn.ultimate.duration[4]%$ Sekunden Wurzeln über einem Bereich sprießen, verlangsamt Feinde und fügt %$heroes.hero_muyrn.ultimate.s_damage_min[4]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[4]%$ echten Schaden pro Sekunde zu.",
["HERO_MUYRN_ROOT_DEFENDER_MENUBOTTOM_DESCRIPTION"] = "Erschafft Wurzeln, die Feinde verlangsamen und ihnen Schaden zufügen.",
["HERO_MUYRN_ROOT_DEFENDER_MENUBOTTOM_NAME"] = "Wurzelverteidiger",
["HERO_MUYRN_ROOT_DEFENDER_TITLE"] = "Wurzelverteidiger",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_1"] = "Beschwört %$heroes.hero_muyrn.sentinel_wisps.max_summons[1]%$ freundlichen Wisp, der Nyru für %$heroes.hero_muyrn.sentinel_wisps.wisp.duration[1]%$ Sekunden folgt. Der Wisp verursacht %$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[1]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[1]%$ magischen Schaden.",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_2"] = "Beschwört %$heroes.hero_muyrn.sentinel_wisps.max_summons[2]%$ freundliche Wisps, die Nyru für %$heroes.hero_muyrn.sentinel_wisps.wisp.duration[2]%$ Sekunden folgen. Die Wisps verursachen %$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[2]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[2]%$ magischen Schaden.",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_3"] = "Beschwört %$heroes.hero_muyrn.sentinel_wisps.max_summons[3]%$ freundliche Wisps, die Nyru für %$heroes.hero_muyrn.sentinel_wisps.wisp.duration[3]%$ Sekunden folgen. Die Wisps verursachen %$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[3]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[3]%$ magischen Schaden.",
["HERO_MUYRN_SENTINEL_WISPS_TITLE"] = "BEWACHER FEEN",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_1"] = "Feuert eine grüne Energieexplosion auf einen Feind ab, die %$heroes.hero_muyrn.verdant_blast.s_damage[1]%$ magischen Schaden verursacht.",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_2"] = "Feuert eine grüne Energieexplosion auf einen Feind ab, die %$heroes.hero_muyrn.verdant_blast.s_damage[2]%$ magischen Schaden verursacht.",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_3"] = "Feuert eine grüne Energieexplosion auf einen Feind ab, die %$heroes.hero_muyrn.verdant_blast.s_damage[3]%$ magischen Schaden verursacht.",
["HERO_MUYRN_VERDANT_BLAST_TITLE"] = "Grüne Explosion",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_1"] = "Schlägt einen Feind brutal mit ihrem Schwert, verursacht %$heroes.hero_raelyn.brutal_slash.s_damage[1]%$ echten Schaden.",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_2"] = "Schlägt einen Feind brutal mit ihrem Schwert, verursacht %$heroes.hero_raelyn.brutal_slash.s_damage[2]%$ echten Schaden.",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_3"] = "Schlägt einen Feind brutal mit ihrem Schwert, verursacht %$heroes.hero_raelyn.brutal_slash.s_damage[3]%$ echten Schaden.",
["HERO_RAELYN_BRUTAL_SLASH_TITLE"] = "BRUTALER SCHLAG",
["HERO_RAELYN_CLASS"] = "Dunkler Leutnant",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_1"] = "Beschwört einen Schwarzen Ritter, der %$heroes.hero_raelyn.ultimate.entity.hp_max[2]%$ Gesundheit besitzt und %$heroes.hero_raelyn.ultimate.entity.damage_min[2]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[2]%$ echten Schaden verursacht.",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_2"] = "Der Schwarze Ritter hat %$heroes.hero_raelyn.ultimate.entity.hp_max[3]%$ Gesundheit und verursacht %$heroes.hero_raelyn.ultimate.entity.damage_min[3]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[3]%$ echten Schaden.",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_3"] = "Der Schwarze Ritter hat %$heroes.hero_raelyn.ultimate.entity.hp_max[4]%$ Gesundheit und verursacht %$heroes.hero_raelyn.ultimate.entity.damage_min[4]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[4]%$ echten Schaden.",
["HERO_RAELYN_COMMAND_ORDERS_MENUBOTTOM_DESCRIPTION"] = "Beschwört einen Schwarzen Ritter auf dem Schlachtfeld.",
["HERO_RAELYN_COMMAND_ORDERS_MENUBOTTOM_NAME"] = "Kommandobefehle",
["HERO_RAELYN_COMMAND_ORDERS_TITLE"] = "BEFEHL",
["HERO_RAELYN_DESC"] = "Die imposante Raelyn lebt dafür, die Schwarzen Ritter an der Spitze zu führen. Ihre Brutalität und Unnachgiebigkeit brachte ihr die Anerkennung von Vez’nan und die Furcht der Linireaner ein. Immer bereit für einen guten Kampf, war sie die erste Freiwillige, die sich den Reihen des Dunklen Zauberers anschloss.",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_1"] = "Betäubt nahe Feinde für %$heroes.hero_raelyn.inspire_fear.stun_duration[1]%$ Sekunden und reduziert ihren Angriffsschaden um %$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[1]%$% für %$heroes.hero_raelyn.inspire_fear.damage_duration[1]%$ Sekunden.",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_2"] = "Betäubt nahe Feinde für %$heroes.hero_raelyn.inspire_fear.stun_duration[2]%$ Sekunden und reduziert ihren Angriffsschaden um %$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[2]%$% für %$heroes.hero_raelyn.inspire_fear.damage_duration[2]%$ Sekunden.",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_3"] = "Betäubt nahe Feinde für %$heroes.hero_raelyn.inspire_fear.stun_duration[3]%$ Sekunden und reduziert ihren Angriffsschaden um %$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[3]%$% für %$heroes.hero_raelyn.inspire_fear.damage_duration[3]%$ Sekunden.",
["HERO_RAELYN_INSPIRE_FEAR_TITLE"] = "ANGST EINFLÖßEN",
["HERO_RAELYN_NAME"] = "Raelyn",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_1"] = "Für %$heroes.hero_raelyn.onslaught.duration[1]%$ Sekunden greift Raelyn schneller an und verursacht %$heroes.hero_raelyn.onslaught.damage_factor[1]%$% ihres Angriffsschadens in einem kleinen Bereich um das Hauptziel.",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_2"] = "Für %$heroes.hero_raelyn.onslaught.duration[2]%$ Sekunden greift Raelyn schneller an und verursacht %$heroes.hero_raelyn.onslaught.damage_factor[2]%$% ihres Angriffsschadens in einem kleinen Bereich um das Hauptziel.",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_3"] = "Für %$heroes.hero_raelyn.onslaught.duration[3]%$ Sekunden greift Raelyn schneller an und verursacht %$heroes.hero_raelyn.onslaught.damage_factor[3]%$% ihres Angriffsschadens in einem kleinen Bereich um das Hauptziel.",
["HERO_RAELYN_ONSLAUGHT_TITLE"] = "STURM",
["HERO_RAELYN_ULTIMATE_ENTITY_NAME"] = "Dunkler Ritter",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_1"] = "Im Kampf erzeugt Raelyn einen Gesundheitsschild basierend auf der Anzahl der Feinde in ihrer Nähe (%$heroes.hero_raelyn.unbreakable.shield_per_enemy[1]%$% ihrer Gesamtlebenspunkte pro Feind, bis zu %$heroes.hero_raelyn.unbreakable.max_targets%$ Feinde)",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_2"] = "Im Kampf erzeugt Raelyn einen Gesundheitsschild basierend darauf, wie viele Feinde in ihrer Nähe sind (%$heroes.hero_raelyn.unbreakable.shield_per_enemy[2]%$% ihres Lebensgesamts pro jedem von bis zu %$heroes.hero_raelyn.unbreakable.max_targets%$ Feinden)",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_3"] = "Im Kampf erzeugt Raelyn einen Gesundheitsschild basierend darauf, wie viele Feinde in ihrer Nähe sind (%$heroes.hero_raelyn.unbreakable.shield_per_enemy[3]%$% ihres Lebensgesamts pro jedem von bis zu %$heroes.hero_raelyn.unbreakable.max_targets%$ Feinden)",
["HERO_RAELYN_UNBREAKABLE_TITLE"] = "UNZERBRECHLICH",
["HERO_ROBOT_CLASS"] = "Belagerungsgolem",
["HERO_ROBOT_DESC"] = "Die Schmiedemeister der dunklen Armee haben sich selbst übertroffen, indem sie einen Kriegsautomaten erschufen, den sie treffend Warhead nannten. Verstärkt durch feurige Motoren und unberührt von Emotionen, stürzt sich Warhead in den Kampf, ohne zwischen Freund und Feind zu unterscheiden.",
["HERO_ROBOT_EXPLODE_DESCRIPTION_1"] = "Erzeugt eine feurige Explosion, die %$heroes.hero_robot.explode.damage_min[1]%$-%$heroes.hero_robot.explode.damage_max[1]%$ Explosivschaden an Feinden verursacht und sie für %$heroes.hero_robot.explode.burning_duration%$ Sekunden verbrennt. Das Brennen verursacht %$heroes.hero_robot.explode.s_burning_damage[1]%$ Schaden pro Sekunde.",
["HERO_ROBOT_EXPLODE_DESCRIPTION_2"] = "Erzeugt eine feurige Explosion, die %$heroes.hero_robot.explode.damage_min[2]%$-%$heroes.hero_robot.explode.damage_max[2]%$ Explosivschaden an Feinden verursacht und sie für %$heroes.hero_robot.explode.burning_duration%$ Sekunden verbrennt. Das Brennen verursacht %$heroes.hero_robot.explode.s_burning_damage[2]%$ Schaden pro Sekunde.",
["HERO_ROBOT_EXPLODE_DESCRIPTION_3"] = "Erzeugt eine feurige Explosion, die %$heroes.hero_robot.explode.damage_min[3]%$-%$heroes.hero_robot.explode.damage_max[3]%$ Explosivschaden an Feinden verursacht und sie für %$heroes.hero_robot.explode.burning_duration%$ Sekunden verbrennt. Das Brennen verursacht %$heroes.hero_robot.explode.s_burning_damage[3]%$ Schaden pro Sekunde.",
["HERO_ROBOT_EXPLODE_TITLE"] = "SELBSTENTZÜNDUNG",
["HERO_ROBOT_FIRE_DESCRIPTION_1"] = "Feuert eine Kanone voller glühender Kohlen ab, fügt %$heroes.hero_robot.fire.damage_min[1]%$-%$heroes.hero_robot.fire.damage_max[1]%$ physischen Schaden zu und verlangsamt Gegner für %$heroes.hero_robot.fire.s_slow_duration[1]%$ Sekunden.",
["HERO_ROBOT_FIRE_DESCRIPTION_2"] = "Feuert eine Kanone voller glühender Kohlen ab, fügt %$heroes.hero_robot.fire.damage_min[2]%$-%$heroes.hero_robot.fire.damage_max[2]%$ physischen Schaden zu und verlangsamt Gegner für %$heroes.hero_robot.fire.s_slow_duration[1]%$ Sekunden.",
["HERO_ROBOT_FIRE_DESCRIPTION_3"] = "Feuert eine Kanone voller glühender Kohlen ab, fügt %$heroes.hero_robot.fire.damage_min[3]%$-%$heroes.hero_robot.fire.damage_max[3]%$ physischen Schaden zu und verlangsamt Gegner für %$heroes.hero_robot.fire.s_slow_duration[1]%$ Sekunden.",
["HERO_ROBOT_FIRE_TITLE"] = "RAUCHVORHANG",
["HERO_ROBOT_JUMP_DESCRIPTION_1"] = "Springt über einen Feind, betäubt ihn für %$heroes.hero_robot.jump.stun_duration[1]%$ Sekunden und fügt %$heroes.hero_robot.jump.s_damage[1]%$ physischen Schaden in einem Bereich zu.",
["HERO_ROBOT_JUMP_DESCRIPTION_2"] = "Springt über einen Feind, betäubt ihn für %$heroes.hero_robot.jump.stun_duration[2]%$ Sekunden und fügt %$heroes.hero_robot.jump.s_damage[2]%$ physischen Schaden in einem Bereich zu.",
["HERO_ROBOT_JUMP_DESCRIPTION_3"] = "Springt über einen Feind, betäubt ihn für %$heroes.hero_robot.jump.stun_duration[3]%$ Sekunden und fügt %$heroes.hero_robot.jump.s_damage[3]%$ physischen Schaden in einem Bereich zu.",
["HERO_ROBOT_JUMP_TITLE"] = "TIEFER EINSCHLAG",
["HERO_ROBOT_NAME"] = "Sprengkopf",
["HERO_ROBOT_TRAIN_DESCRIPTION_1"] = "Beschwört einen Kriegswagen, der den Pfad entlangfährt und %$heroes.hero_robot.ultimate.s_damage[2]%$ Schaden an Feinden verursacht und sie für %$heroes.hero_robot.ultimate.burning_duration%$ Sekunden verbrennt. Das Brennen verursacht %$heroes.hero_robot.ultimate.s_burning_damage%$ Schaden pro Sekunde.",
["HERO_ROBOT_TRAIN_DESCRIPTION_2"] = "Beschwört einen Kriegswagen, der den Pfad entlangfährt und %$heroes.hero_robot.ultimate.s_damage[3]%$ Schaden an Feinden verursacht und sie für %$heroes.hero_robot.ultimate.burning_duration%$ Sekunden verbrennt. Das Brennen verursacht %$heroes.hero_robot.ultimate.s_burning_damage%$ Schaden pro Sekunde.",
["HERO_ROBOT_TRAIN_DESCRIPTION_3"] = "Beschwört einen Kriegswagen, der den Pfad entlangfährt und %$heroes.hero_robot.ultimate.s_damage[4]%$ Schaden an Feinden verursacht und sie für %$heroes.hero_robot.ultimate.burning_duration%$ Sekunden verbrennt. Das Brennen verursacht %$heroes.hero_robot.ultimate.s_burning_damage%$ Schaden pro Sekunde.",
["HERO_ROBOT_TRAIN_MENUBOTTOM_DESCRIPTION"] = "Beschwört einen Kriegswagen, der Feinde niedertrampelt.",
["HERO_ROBOT_TRAIN_MENUBOTTOM_NAME"] = "Kopf des Motors",
["HERO_ROBOT_TRAIN_TITLE"] = "KOPF DES MOTORS",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_1"] = "Schlägt einen Feind mit weniger als %$heroes.hero_robot.uppercut.s_life_threshold[1]%$% Gesundheit und beendet ihn sofort.",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_2"] = "Schlägt einen Feind mit weniger als %$heroes.hero_robot.uppercut.s_life_threshold[2]%$% Gesundheit und beendet ihn sofort.",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_3"] = "Schlägt einen Feind mit weniger als %$heroes.hero_robot.uppercut.s_life_threshold[3]%$% Gesundheit und beendet ihn sofort.",
["HERO_ROBOT_UPPERCUT_TITLE"] = "EISEN-UPPERCUT",
["HERO_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_1"] = "Dieser Held ist in der Kolossale Bedrohung Kampagne enthalten",
["HERO_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_2"] = "Dieser Held ist in der Kampagne „Wukongs Reise“ enthalten.",
["HERO_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_1"] = "Kolossale Bedrohung Kampagne",
["HERO_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_2"] = "Wukongs Reise-Kampagne",
["HERO_ROOM_EQUIPPED_HEROES"] = "Ausgerüstete Helden",
["HERO_ROOM_GET_DLC"] = "HOL ES DIR",
["HERO_ROOM_LABEL_ROSTER_THUMB_NEW"] = "Neu!",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_1"] = "Beschwört eine magische Reflexion von Therien, die Feinde angreift und %$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[1]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[1]%$ magischen Schaden verursacht.",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_2"] = "Beschwört eine magische Reflexion von Therien, die Feinde angreift und %$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[2]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[2]%$ magischen Schaden verursacht.",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_3"] = "Beschwört eine magische Reflexion von Therien, die Feinde angreift und %$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[3]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[3]%$ magischen Schaden verursacht.",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_ENTITY_NAME"] = "Astrale Reflexion",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_TITLE"] = "Astralreflexion",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_1"] = "Schützt eine verbündete Einheit, indem bis zu %$heroes.hero_space_elf.black_aegis.shield_base[1]%$ Schaden verhindert wird. Der Schild explodiert nach einem Moment und verursacht %$heroes.hero_space_elf.black_aegis.explosion_damage[1]%$ magischen Schaden in einem Bereich.",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_2"] = "Schützt eine verbündete Einheit, indem bis zu %$heroes.hero_space_elf.black_aegis.shield_base[2]%$ Schaden verhindert wird. Der explosive Schild verursacht jetzt %$heroes.hero_space_elf.black_aegis.explosion_damage[2]%$ magischen Schaden in einem Bereich.",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_3"] = "Schützt eine verbündete Einheit, indem bis zu %$heroes.hero_space_elf.black_aegis.shield_base[3]%$ Schaden verhindert wird. Der explosive Schild verursacht jetzt %$heroes.hero_space_elf.black_aegis.explosion_damage[3]%$ magischen Schaden in einem Bereich.",
["HERO_SPACE_ELF_BLACK_AEGIS_TITLE"] = "SCHWARZE OBHUT",
["HERO_SPACE_ELF_CLASS"] = "Leerenmagier",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_1"] = "Fängt eine Gruppe von Feinden im Nichts für %$heroes.hero_space_elf.ultimate.duration[2]%$ Sekunden, verursacht %$heroes.hero_space_elf.ultimate.damage[2]%$ Schaden.",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_2"] = "Fängt eine Gruppe von Feinden im Nichts für %$heroes.hero_space_elf.ultimate.duration[3]%$ Sekunden, verursacht %$heroes.hero_space_elf.ultimate.damage[3]%$ Schaden.",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_3"] = "Fängt eine Gruppe von Feinden im Nichts für %$heroes.hero_space_elf.ultimate.duration[4]%$ Sekunden, verursacht %$heroes.hero_space_elf.ultimate.damage[4]%$ Schaden.",
["HERO_SPACE_ELF_COSMIC_PRISON_MENUBOTTOM_DESCRIPTION"] = "Fängt Feinde in einem Bereich ein und fügt ihnen Schaden zu.",
["HERO_SPACE_ELF_COSMIC_PRISON_MENUBOTTOM_NAME"] = "Kosmisches Gefängnis",
["HERO_SPACE_ELF_COSMIC_PRISON_TITLE"] = "KOSMISCHES GEFÄNGNIS",
["HERO_SPACE_ELF_DESC"] = "Von ihren Gleichgestellten jahrelang gemieden, weil sie sich mit unbekannten und jenseitigen Kräften eingelassen hatte, findet sich die Leerenmagierin Therien nun als eines der größten Vermögenswerte der Allianz wieder, um den Aufseher und jegliche Kräfte jenseits dieser Ebene zu verstehen.",
["HERO_SPACE_ELF_NAME"] = "Therien",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_1"] = "Verzerrt den Raum rund um allen Türmen für %$heroes.hero_space_elf.spatial_distortion.duration[1]%$ Sekunden und erhöht deren Reichweiten um %$heroes.hero_space_elf.spatial_distortion.s_range_factor[1]%$%.",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_2"] = "Verzerrt den Raum rund um allen Türmen für %$heroes.hero_space_elf.spatial_distortion.duration[2]%$ Sekunden und erhöht deren Reichweiten um %$heroes.hero_space_elf.spatial_distortion.s_range_factor[2]%$%.",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_3"] = "Verzerrt den Raum rund um allen Türmen für %$heroes.hero_space_elf.spatial_distortion.duration[3]%$ Sekunden und erhöht deren Reichweiten um %$heroes.hero_space_elf.spatial_distortion.s_range_factor[3]%$%.",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_TITLE"] = "RAUMVERZERRUNG",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_1"] = "Öffnet %$heroes.hero_space_elf.void_rift.cracks_amount[1]%$ Riss auf dem Pfad für %$heroes.hero_space_elf.void_rift.duration[1]%$ Sekunden und verursacht %$heroes.hero_space_elf.void_rift.s_damage_min[1]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[1]%$ Schaden pro Sekunde an jedem Feind, der darauf steht.",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_2"] = "Öffnet %$heroes.hero_space_elf.void_rift.cracks_amount[2]%$ Risse auf dem Pfad für %$heroes.hero_space_elf.void_rift.duration[2]%$ Sekunden und verursacht %$heroes.hero_space_elf.void_rift.s_damage_min[2]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[2]%$ Schaden pro Sekunde an jedem Feind, der darauf steht.",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_3"] = "Öffnet %$heroes.hero_space_elf.void_rift.cracks_amount[3]%$ Risse auf dem Pfad für %$heroes.hero_space_elf.void_rift.duration[3]%$ Sekunden und verursacht %$heroes.hero_space_elf.void_rift.s_damage_min[3]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[3]%$ Schaden pro Sekunde an jedem Feind, der darauf steht.",
["HERO_SPACE_ELF_VOID_RIFT_TITLE"] = "LEERE RISS",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_1"] = "Beschwört %$heroes.hero_spider.ultimate.spawn_amount[2]%$ Spinnen, die %$heroes.hero_spider.ultimate.spider.duration[2]%$ Sekunden lang kämpfen und Feinde beim Treffer betäuben.",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_2"] = "Beschwört %$heroes.hero_spider.ultimate.spawn_amount[3]%$ Spinnen, die %$heroes.hero_spider.ultimate.spider.duration[3]%$ Sekunden lang kämpfen und Feinde beim Treffer betäuben.",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_3"] = "Beschwört %$heroes.hero_spider.ultimate.spawn_amount[4]%$ Spinnen, die %$heroes.hero_spider.ultimate.spider.duration[4]%$ Sekunden lang kämpfen und Feinde beim Treffer betäuben.",
["HERO_SPIDER_ARACNID_SPAWNER_MENUBOTTOM_DESCRIPTION"] = "Beschwört ein Rudel betäubender Spinnen.",
["HERO_SPIDER_ARACNID_SPAWNER_MENUBOTTOM_NAME"] = "Ruf der Jägerin",
["HERO_SPIDER_ARACNID_SPAWNER_TITLE"] = "Ruf der Jägerin",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_1"] = "Alle %$heroes.hero_spider.area_attack.cooldown[1]%$ Sekunden behauptet Spydyr ihre Präsenz und betäubt nahe Feinde für %$heroes.hero_spider.area_attack.s_stun_time[1]%$ Sekunden.",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_2"] = "Alle %$heroes.hero_spider.area_attack.cooldown[2]%$ Sekunden behauptet Spydyr ihre Präsenz und betäubt nahe Feinde für %$heroes.hero_spider.area_attack.s_stun_time[2]%$ Sekunden.",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_3"] = "Alle %$heroes.hero_spider.area_attack.cooldown[3]%$ Sekunden betont Spydyr ihre Präsenz und betäubt nahe Feinde für %$heroes.hero_spider.area_attack.s_stun_time[3]%$ Sekunden.",
["HERO_SPIDER_AREA_ATTACK_TITLE"] = "Überwältigende Präsenz",
["HERO_SPIDER_DESC"] = "Das letzte lebende Mitglied einer Gruppe von Zwielichtelfen, die damit beauftragt wurden, den Kult der Spinnenkönigin zu vernichten. Dank ihrer Schattenmagie und ihrer Jagdfähigkeiten gilt sie als eine der tödlichsten Assassinen in allen Königreichen.",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_1"] = "Alle %$heroes.hero_spider.instakill_melee.cooldown[1]%$ Sekunden kann Spydyr einen betäubten Feind hinrichten, dessen Gesundheit unter %$heroes.hero_spider.instakill_melee.life_threshold[1]%$ liegt.",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_2"] = "Alle %$heroes.hero_spider.instakill_melee.cooldown[2]%$ Sekunden kann Spydyr einen betäubten Feind hinrichten, dessen Gesundheit unter %$heroes.hero_spider.instakill_melee.life_threshold[2]%$ liegt.",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_3"] = "Alle %$heroes.hero_spider.instakill_melee.cooldown[3]%$ Sekunden kann Spydyr einen betäubten Gegner hinrichten, dessen Gesundheit unter %$heroes.hero_spider.instakill_melee.life_threshold[3]%$ liegt.",
["HERO_SPIDER_INSTAKILL_MELEE_TITLE"] = "Griff des Todes",
["HERO_SPIDER_NAME"] = "Spydyr",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_1"] = "Im Bruchteil einer Sekunde teleportiert sich Spydyr zum Gegner mit der höchsten Gesundheit und fügt ihm %$heroes.hero_spider.supreme_hunter.damage_min[1]%$-%$heroes.hero_spider.supreme_hunter.damage_max[1]%$ Schaden zu.",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_2"] = "Im Bruchteil einer Sekunde teleportiert sich Spydyr zum Gegner mit der höchsten Gesundheit und fügt ihm %$heroes.hero_spider.supreme_hunter.damage_min[2]%$-%$heroes.hero_spider.supreme_hunter.damage_max[2]%$ Schaden zu.",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_3"] = "Im Bruchteil einer Sekunde teleportiert sich Spydyr zum Gegner mit der höchsten Gesundheit und fügt ihm %$heroes.hero_spider.supreme_hunter.damage_min[3]%$-%$heroes.hero_spider.supreme_hunter.damage_max[3]%$ Schaden zu.",
["HERO_SPIDER_SUPREME_HUNTER_TITLE"] = "Schattenschritt",
["HERO_SPIDER_TUNNELING_DESCRIPTION_1"] = "Spydyrs Graben verursacht jetzt %$heroes.hero_spider.tunneling.damage_min[1]%$-%$heroes.hero_spider.tunneling.damage_max[1]%$ Schaden beim Wiederauftauchen.",
["HERO_SPIDER_TUNNELING_DESCRIPTION_2"] = "Spydyrs Graben verursacht jetzt %$heroes.hero_spider.tunneling.damage_min[2]%$-%$heroes.hero_spider.tunneling.damage_max[2]%$ Schaden beim Wiederauftauchen.",
["HERO_SPIDER_TUNNELING_DESCRIPTION_3"] = "Spydyrs Graben verursacht jetzt %$heroes.hero_spider.tunneling.damage_min[3]%$-%$heroes.hero_spider.tunneling.damage_max[3]%$ Schaden beim Wiederauftauchen.",
["HERO_SPIDER_TUNNELING_TITLE"] = "Tunnelbau",
["HERO_VENOM_CLASS"] = "Besudelter Schlächter",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_1"] = "Füllt ein Gebiet mit einer klebrigen Substanz, die Feinde verlangsamt und sich nach einem Moment in durchdringende Stacheln verwandelt, die %$heroes.hero_venom.ultimate.s_damage[2]%$ echten Schaden verursachen.",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_2"] = "Füllt ein Gebiet mit einer klebrigen Substanz, die Feinde verlangsamt und sich nach einem Moment in durchdringende Stacheln verwandelt, die %$heroes.hero_venom.ultimate.s_damage[3]%$ echten Schaden verursachen.",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_3"] = "Füllt ein Gebiet mit einer klebrigen Substanz, die Feinde verlangsamt und sich nach einem Moment in durchdringende Stacheln verwandelt, die %$heroes.hero_venom.ultimate.s_damage[4]%$ echten Schaden verursachen.",
["HERO_VENOM_CREEPING_DEATH_MENUBOTTOM_DESCRIPTION"] = "Beschwört eine klebrige Substanz auf dem Pfad, die Feinde verlangsamt und schädigt.",
["HERO_VENOM_CREEPING_DEATH_MENUBOTTOM_NAME"] = "Kriechender Tod",
["HERO_VENOM_CREEPING_DEATH_TITLE"] = "SCHLEICHENDER TOD",
["HERO_VENOM_DESC"] = "Nachdem er es abgelehnt hatte, vom Kult in eine Abscheulichkeit verwandelt zu werden, wurde der Söldner Grimson eingekerkert und zum Verrotten zurückgelassen. Der qualvolle Prozess verlieh Grimson Gestaltwandelkräfte, die er nutzte, um aus dem Kult zu fliehen und schwor, zur Rache zurückzukehren.",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_1"] = "Grimson verschlingt einen Feind mit weniger als %$heroes.hero_venom.eat_enemy.hp_trigger%$% Gesundheit und gewinnt dabei %$heroes.hero_venom.eat_enemy.regen[1]%$% seiner eigenen Gesamtgesundheit zurück.",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_2"] = "Grimson verschlingt einen Feind mit weniger als %$heroes.hero_venom.eat_enemy.hp_trigger%$% Gesundheit und gewinnt dabei %$heroes.hero_venom.eat_enemy.regen[2]%$% seiner eigenen Gesamtgesundheit zurück.",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_3"] = "Grimson verschlingt einen Feind mit weniger als %$heroes.hero_venom.eat_enemy.hp_trigger%$% Gesundheit und gewinnt dabei %$heroes.hero_venom.eat_enemy.regen[3]%$% seiner eigenen Gesamtgesundheit zurück.",
["HERO_VENOM_EAT_ENEMY_TITLE"] = "FLEISCH ERNEUERN",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_1"] = "Breitet stachelige Ranken auf dem Pfad aus, die %$heroes.hero_venom.floor_spikes.s_damage[1]%$ echten Schaden pro Stachel an nahe Feinde zufügen.",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_2"] = "Breitet stachelige Ranken auf dem Pfad aus, die %$heroes.hero_venom.floor_spikes.s_damage[2]%$ echten Schaden pro Stachel an nahe Feinde zufügen.",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_3"] = "Breitet stachelige Ranken auf dem Pfad aus, die %$heroes.hero_venom.floor_spikes.s_damage[3]%$ echten Schaden pro Stachel an nahe Feinde zufügen.",
["HERO_VENOM_FLOOR_SPIKES_TITLE"] = "TÖDLICHE STACHELN",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_1"] = "Wenn seine Gesundheit unter %$heroes.hero_venom.inner_beast.trigger_hp%$% fällt, verwandelt sich Grimson vollständig, erhält %$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[1]%$% zusätzlichen Schaden und heilt sich selbst um %$heroes.hero_venom.inner_beast.basic_melee.regen_health%$% seines gesamten Lebens pro Schlag für %$heroes.hero_venom.inner_beast.duration%$ Sekunden.",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_2"] = "Wenn seine Gesundheit unter %$heroes.hero_venom.inner_beast.trigger_hp%$% fällt, verwandelt sich Grimson vollständig, erhält %$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[2]%$% zusätzlichen Schaden und heilt sich selbst um %$heroes.hero_venom.inner_beast.basic_melee.regen_health%$% seines gesamten Lebens pro Schlag für %$heroes.hero_venom.inner_beast.duration%$ Sekunden.",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_3"] = "Wenn seine Gesundheit unter %$heroes.hero_venom.inner_beast.trigger_hp%$% fällt, verwandelt sich Grimson vollständig, erhält %$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[3]%$% zusätzlichen Schaden und heilt sich selbst um %$heroes.hero_venom.inner_beast.basic_melee.regen_health%$% seines gesamten Lebens pro Schlag für %$heroes.hero_venom.inner_beast.duration%$ Sekunden.",
["HERO_VENOM_INNER_BEAST_TITLE"] = "INNERES BIEST",
["HERO_VENOM_NAME"] = "Grimson",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_1"] = "Schlägt einen entfernten Feind und verursacht %$heroes.hero_venom.ranged_tentacle.s_damage[1]%$ physischen Schaden mit einer %$heroes.hero_venom.ranged_tentacle.bleed_chance[1]%$% Chance auf Blutung. Blutung verursacht %$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$ Schaden pro Sekunde für %$heroes.hero_venom.ranged_tentacle.bleed_duration[1]%$ Sekunden.",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_2"] = "Schlägt einen entfernten Feind und verursacht %$heroes.hero_venom.ranged_tentacle.s_damage[2]%$ physischen Schaden mit einer %$heroes.hero_venom.ranged_tentacle.bleed_chance[2]%$% Chance auf Blutung. Blutung verursacht %$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$ Schaden pro Sekunde für %$heroes.hero_venom.ranged_tentacle.bleed_duration[2]%$ Sekunden.",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_3"] = "Schlägt einen entfernten Feind und verursacht %$heroes.hero_venom.ranged_tentacle.s_damage[3]%$ physischen Schaden mit einer %$heroes.hero_venom.ranged_tentacle.bleed_chance[3]%$% Chance auf Blutung. Blutung verursacht %$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$ Schaden pro Sekunde für %$heroes.hero_venom.ranged_tentacle.bleed_duration[3]%$ Sekunden.",
["HERO_VENOM_RANGED_TENTACLE_TITLE"] = "HERZSUCHER",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_1"] = "Bedekt ein Gebiet mit %$heroes.hero_vesper.ultimate.s_spread[2]%$ Pfeilen, die jeweils %$heroes.hero_vesper.ultimate.damage[2]%$ physischen Schaden an Feinden verursachen.",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_2"] = "Bedekt ein Gebiet mit %$heroes.hero_vesper.ultimate.s_spread[3]%$ Pfeilen, die jeweils %$heroes.hero_vesper.ultimate.damage[3]%$ physischen Schaden an Feinden verursachen.",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_3"] = "Bedekt ein Gebiet mit %$heroes.hero_vesper.ultimate.s_spread[4]%$ Pfeilen, die jeweils %$heroes.hero_vesper.ultimate.damage[4]%$ physischen Schaden an Feinden verursachen.",
["HERO_VESPER_ARROW_STORM_MENUBOTTOM_DESCRIPTION"] = "Beschiesst ein Gebiet mit Pfeilen, fügt Feinden Schaden zu.",
["HERO_VESPER_ARROW_STORM_MENUBOTTOM_NAME"] = "Pfeilsturm",
["HERO_VESPER_ARROW_STORM_TITLE"] = "PFEILSTURM",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_1"] = "Schießt einen Pfeil, der den Feind für %$heroes.hero_vesper.arrow_to_the_knee.stun_duration[1]%$ Sekunden betäubt, und fügt %$heroes.hero_vesper.arrow_to_the_knee.s_damage[1]%$ physischen Schaden zu.",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_2"] = "Schießt einen Pfeil, der den Feind für %$heroes.hero_vesper.arrow_to_the_knee.stun_duration[2]%$ Sekunden betäubt, und fügt %$heroes.hero_vesper.arrow_to_the_knee.s_damage[2]%$ physischen Schaden zu.",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_3"] = "Schießt einen Pfeil, der den Feind für %$heroes.hero_vesper.arrow_to_the_knee.stun_duration[3]%$ Sekunde betäubt, und fügt %$heroes.hero_vesper.arrow_to_the_knee.s_damage[3]%$ physischen Schaden zu.",
["HERO_VESPER_ARROW_TO_THE_KNEE_TITLE"] = "PFEIL IM KNIE",
["HERO_VESPER_CLASS"] = "Königlicher Kapitän",
["HERO_VESPER_DESC"] = "Sowohl mit dem Schwert als auch mit dem Bogen geschickt, erwarb Vesper seinen Platz als Kommandant der Linirean-Streitkräfte. Nachdem Linirea fiel und König Denas verschwand, sammelte er alle Truppen, die er konnte, und startete einen Kreuzzug, um den ehemaligen Herrscher zurückzubringen.",
["HERO_VESPER_DISENGAGE_DESCRIPTION_1"] = "Wenn die Gesundheit unter %$heroes.hero_vesper.disengage.hp_to_trigger%$% fällt, weicht Vesper dem nächsten Nahkampfangriff aus, indem er rückwärts springt. Danach schießt er drei Pfeile, die jeweils %$heroes.hero_vesper.disengage.s_damage[1]%$ physischen Schaden an nahe Feinde verursachen.",
["HERO_VESPER_DISENGAGE_DESCRIPTION_2"] = "Wenn die Gesundheit unter %$heroes.hero_vesper.disengage.hp_to_trigger%$% fällt, weicht Vesper dem nächsten Nahkampfangriff aus, indem er rückwärts springt. Danach schießt er drei Pfeile, die jeweils %$heroes.hero_vesper.disengage.s_damage[2]%$ physischen Schaden an nahe Feinde verursachen.",
["HERO_VESPER_DISENGAGE_DESCRIPTION_3"] = "Wenn die Gesundheit unter %$heroes.hero_vesper.disengage.hp_to_trigger%$% fällt, weicht Vesper dem nächsten Nahkampfangriff aus, indem er rückwärts springt. Danach schießt er drei Pfeile, die jeweils %$heroes.hero_vesper.disengage.s_damage[3]%$ physischen Schaden an nahe Feinde verursachen.",
["HERO_VESPER_DISENGAGE_TITLE"] = "ZURÜCKFALLEN",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_1"] = "Schlägt einen Feind dreimal, verursacht %$heroes.hero_vesper.martial_flourish.s_damage[1]%$ physischen Schaden.",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_2"] = "Schlägt einen Feind dreimal, verursacht %$heroes.hero_vesper.martial_flourish.s_damage[2]%$ physischen Schaden.",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_3"] = "Schlägt einen Feind dreimal, verursacht %$heroes.hero_vesper.martial_flourish.s_damage[3]%$ physischen Schaden.",
["HERO_VESPER_MARTIAL_FLOURISH_TITLE"] = "MARTIALISCHE PRACHT",
["HERO_VESPER_NAME"] = "Vesper",
["HERO_VESPER_RICOCHET_DESCRIPTION_1"] = "Schießt einen Pfeil, der zwischen %$heroes.hero_vesper.ricochet.s_bounces[1]%$ Feinden abprallt und jedes Mal %$heroes.hero_vesper.ricochet.s_damage[1]%$ physischen Schaden verursacht.",
["HERO_VESPER_RICOCHET_DESCRIPTION_2"] = "Schießt einen Pfeil, der zwischen %$heroes.hero_vesper.ricochet.s_bounces[2]%$ Feinden abprallt und jedes Mal %$heroes.hero_vesper.ricochet.s_damage[2]%$ physischen Schaden verursacht.",
["HERO_VESPER_RICOCHET_DESCRIPTION_3"] = "Schießt einen Pfeil, der zwischen %$heroes.hero_vesper.ricochet.s_bounces[3]%$ Feinden abprallt und jedes Mal %$heroes.hero_vesper.ricochet.s_damage[3]%$ physischen Schaden verursacht.",
["HERO_VESPER_RICOCHET_TITLE"] = "ABPRALLENDER PFEIL",
["HERO_WITCH_CLASS"] = "Schabernack Hexe",
["HERO_WITCH_DESC"] = "Obwohl sie es liebt, Fremde, die durch den Feenwald ziehen, mit lustigen und harmlosen Streichen zu überraschen, merken diejenigen, die eine Bedrohung für den Wald oder ihre Mitgnome darstellen, schnell, dass ihr spielerisches Lächeln eine unerbittliche Hexe verbirgt, mit der nicht zu spaßen ist.",
["HERO_WITCH_DISENGAGE_DESCRIPTION_1"] = "Wenn Stregis Gesundheit unter %$heroes.hero_witch.disengage.hp_to_trigger%$% fällt, teleportiert sie sich zurück und lässt eine Attrappe zurück, die an ihrer Stelle kämpft. Die Attrappe hat %$heroes.hero_witch.disengage.decoy.hp_max[1]%$ Lebenspunkte und explodiert, wenn sie zerstört wird, wodurch Feinde für %$heroes.hero_witch.disengage.decoy.explotion.stun_duration[1]%$ Sekunden betäubt werden.",
["HERO_WITCH_DISENGAGE_DESCRIPTION_2"] = "Wenn Stregis Gesundheit unter %$heroes.hero_witch.disengage.hp_to_trigger%$% fällt, teleportiert sie sich zurück und lässt eine Attrappe zurück, die an ihrer Stelle kämpft. Die Attrappe hat %$heroes.hero_witch.disengage.decoy.hp_max[2]%$ Lebenspunkte und explodiert, wenn sie zerstört wird, wodurch Feinde für %$heroes.hero_witch.disengage.decoy.explotion.stun_duration[2]%$ Sekunden betäubt werden.",
["HERO_WITCH_DISENGAGE_DESCRIPTION_3"] = "Wenn Stregis Gesundheit unter %$heroes.hero_witch.disengage.hp_to_trigger%$% fällt, teleportiert sie sich zurück und lässt eine Attrappe zurück, die an ihrer Stelle kämpft. Die Attrappe hat %$heroes.hero_witch.disengage.decoy.hp_max[3]%$ Lebenspunkte und explodiert, wenn sie zerstört wird, wodurch Feinde für %$heroes.hero_witch.disengage.decoy.explotion.stun_duration[3]%$ Sekunden betäubt werden.",
["HERO_WITCH_DISENGAGE_TITLE"] = "GLÄNZENDE ATTRAPPE",
["HERO_WITCH_NAME"] = "Stregi",
["HERO_WITCH_PATH_AOE_DESCRIPTION_1"] = "Wirft einen riesigen Trank auf den Pfad, der %$heroes.hero_witch.skill_path_aoe.s_damage[1]%$ magischen Schaden in einem Bereich verursacht und Feinde für %$heroes.hero_witch.skill_path_aoe.duration[1]%$ Sekunden verlangsamt.",
["HERO_WITCH_PATH_AOE_DESCRIPTION_2"] = "Wirft einen riesigen Trank auf den Pfad, der %$heroes.hero_witch.skill_path_aoe.s_damage[2]%$ magischen Schaden in einem Bereich verursacht und Feinde für %$heroes.hero_witch.skill_path_aoe.duration[2]%$ Sekunden verlangsamt.",
["HERO_WITCH_PATH_AOE_DESCRIPTION_3"] = "Wirft einen riesigen Trank auf den Pfad, der %$heroes.hero_witch.skill_path_aoe.s_damage[3]%$ magischen Schaden in einem Bereich verursacht und Feinde für %$heroes.hero_witch.skill_path_aoe.duration[3]%$ Sekunden verlangsamt.",
["HERO_WITCH_PATH_AOE_TITLE"] = "SWISH 'N' SQUASH",
["HERO_WITCH_POLYMORPH_DESCRIPTION_1"] = "Verwandelt einen Feind für %$heroes.hero_witch.skill_polymorph.duration[1]%$ Sekunden in einen Kürbling. Der Kürbling hat %$heroes.hero_witch.skill_polymorph.pumpkin.hp[1]%$% der Gesundheit des Ziels.",
["HERO_WITCH_POLYMORPH_DESCRIPTION_2"] = "Verwandelt einen Feind für %$heroes.hero_witch.skill_polymorph.duration[2]%$ Sekunden in einen Kürbling. Der Kürbling hat %$heroes.hero_witch.skill_polymorph.pumpkin.hp[2]%$% der Gesundheit des Ziels.",
["HERO_WITCH_POLYMORPH_DESCRIPTION_3"] = "Verwandelt einen Feind für %$heroes.hero_witch.skill_polymorph.duration[3]%$ Sekunden in einen Kürbling. Der Kürbling hat %$heroes.hero_witch.skill_polymorph.pumpkin.hp[3]%$% der Gesundheit des Ziels.",
["HERO_WITCH_POLYMORPH_TITLE"] = "VEGETARISIERT!",
["HERO_WITCH_SOLDIERS_DESCRIPTION_1"] = "Beschwört %$heroes.hero_witch.skill_soldiers.soldiers_amount[1]%$ Katze, die gegen Feinde kämpft. Die Katze hat %$heroes.hero_witch.skill_soldiers.soldier.hp_max[1]%$ Lebenspunkte und verursacht %$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[1]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[1]%$ physischen Schaden.",
["HERO_WITCH_SOLDIERS_DESCRIPTION_2"] = "Beschwört %$heroes.hero_witch.skill_soldiers.soldiers_amount[2]%$ Katzen, die gegen Feinde kämpfen. Die Katzen haben %$heroes.hero_witch.skill_soldiers.soldier.hp_max[2]%$ Lebenspunkte und verursachen %$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[2]%$ physischen Schaden.",
["HERO_WITCH_SOLDIERS_DESCRIPTION_3"] = "Beschwört %$heroes.hero_witch.skill_soldiers.soldiers_amount[3]%$ Katzen, die gegen Feinde kämpfen. Die Katzen haben %$heroes.hero_witch.skill_soldiers.soldier.hp_max[3]%$ Lebenspunkte und verursachen %$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[3]%$ physischen Schaden.",
["HERO_WITCH_SOLDIERS_TITLE"] = "NACHTFURIEN",
["HERO_WITCH_ULTIMATE_DESCRIPTION_1"] = "Teleportiert %$heroes.hero_witch.ultimate.max_targets[2]%$ Feinde rückwärts und lässt sie für %$heroes.hero_witch.ultimate.duration[2]%$ Sekunden einschlafen.",
["HERO_WITCH_ULTIMATE_DESCRIPTION_2"] = "Teleportiert %$heroes.hero_witch.ultimate.max_targets[3]%$ Feinde rückwärts und lässt sie für %$heroes.hero_witch.ultimate.duration[3]%$ Sekunden einschlafen.",
["HERO_WITCH_ULTIMATE_DESCRIPTION_3"] = "Teleportiert %$heroes.hero_witch.ultimate.max_targets[4]%$ Feinde rückwärts und lässt sie für %$heroes.hero_witch.ultimate.duration[4]%$ Sekunden einschlafen.",
["HERO_WITCH_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "Teleportiert Feinde zurück den Pfad entlang und versetzt sie für eine Weile in Schlaf.",
["HERO_WITCH_ULTIMATE_MENUBOTTOM_NAME"] = "Schläfrige Rückkehr",
["HERO_WITCH_ULTIMATE_TITLE"] = "SCHLÄFRIGE RÜCKKEHR",
["HERO_WUKONG_CLASS"] = "Der Affenkönig",
["HERO_WUKONG_DESC"] = "Geboren aus einem himmlischen Stein von Yin und Yang, wurde Sun Wukong mit Stärke, Beweglichkeit und Unsterblichkeit gesegnet. Doch die Dämonenkönige raubten ihm die Kugeln der Macht. Nun erhebt sich der legendäre Trickser, um sie zurückzuholen, bevor es zu spät ist.",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_1"] = "Stürzt sich und vergrößert den Jingu Bang, um einen Feind zu zertrampeln, tötet ihn augenblicklich und verursacht %$heroes.hero_wukong.giant_staff.area_damage.damage_min[1]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[1]%$ Schaden im Umkreis des Ziels.",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_2"] = "Stolpert und vergrößert den Jingu Bang, um einen Feind zu zertrampeln, tötet ihn sofort und verursacht %$heroes.hero_wukong.giant_staff.area_damage.damage_min[2]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[2]%$ Schaden im Umkreis des Ziels.",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_3"] = "Stolpert und vergrößert den Jingu Bang, um einen Feind zu zertrampeln, tötet ihn sofort und verursacht %$heroes.hero_wukong.giant_staff.area_damage.damage_min[3]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[3]%$ Schaden in einem Bereich um das Ziel.",
["HERO_WUKONG_GIANT_STAFF_TITLE"] = "Jingu-Bang-Technik",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_1"] = "Beschwört 2 Haar-Klone von Sun Wukong, die an seiner Seite kämpfen. Sie verursachen %$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[1]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[1]%$ Schaden und bleiben für %$heroes.hero_wukong.hair_clones.soldier.duration[1]%$ Sekunden.",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_2"] = "Beschwört 2 Haar-Klone von Sun Wukong, die an seiner Seite kämpfen. Sie verursachen %$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[2]%$ Schaden und bleiben %$heroes.hero_wukong.hair_clones.soldier.duration[2]%$ Sekunden lang bestehen.",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_3"] = "Beschwört 2 Haar-Klone von Sun Wukong, die an seiner Seite kämpfen. Sie verursachen %$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[3]%$ Schaden und halten %$heroes.hero_wukong.hair_clones.soldier.duration[3]%$ Sekunden lang.",
["HERO_WUKONG_HAIR_CLONES_TITLE"] = "Hair Clones",
["HERO_WUKONG_NAME"] = "Sun Wukong",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_1"] = "Schleudert den Jingu Bang in die Luft, wobei er sich in %$heroes.hero_wukong.pole_ranged.pole_amounts[1]%$ Stäbe vervielfacht, die auf Feinde herabfallen, jeweils %$heroes.hero_wukong.pole_ranged.damage_min[1]%$ Schaden verursachen und Gegner in einem kleinen Bereich betäuben.",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_2"] = "Schleudert den Jingu Bang in die Luft, wobei er sich in %$heroes.hero_wukong.pole_ranged.pole_amounts[2]%$ Stäbe vervielfacht, die auf Feinde herabfallen, jeweils %$heroes.hero_wukong.pole_ranged.damage_min[2]%$ Schaden verursachen und Gegner in einem kleinen Bereich betäuben.",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_3"] = "Schleudert den Jingu Bang in die Luft, wobei er sich in %$heroes.hero_wukong.pole_ranged.pole_amounts[3]%$ Stäbe vervielfacht, die auf Feinde herabfallen, jeweils %$heroes.hero_wukong.pole_ranged.damage_min[3]%$ Schaden verursachen und Gegner in einem kleinen Bereich betäuben.",
["HERO_WUKONG_POLE_RANGED_TITLE"] = "Pfahl-Salve",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_1"] = "Der Weiße Drache schlägt mit gewaltiger Kraft in den Boden ein, verursacht %$heroes.hero_wukong.ultimate.damage_total[2]%$ wahren Schaden und hinterlässt ein verlangsamendes Gebiet.",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_2"] = "Der Weiße Drache schlägt mit gewaltiger Wucht in den Boden ein, verursacht %$heroes.hero_wukong.ultimate.damage_total[3]%$ wahren Schaden und hinterlässt ein verlangsamendes Gebiet.",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_3"] = "Der Weiße Drache schlägt mit gewaltiger Kraft in den Boden ein, verursacht %$heroes.hero_wukong.ultimate.damage_total[4]%$ wahren Schaden und hinterlässt ein verlangsamendes Gebiet.",
["HERO_WUKONG_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "Beschwört den Weißen Drachen.",
["HERO_WUKONG_ULTIMATE_MENUBOTTOM_NAME"] = "Der Weiße Drache",
["HERO_WUKONG_ULTIMATE_TITLE"] = "Der Weiße Drache",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_1"] = "Zhu Bajie, Sun Wukongs treuer Begleiter, folgt ihm überallhin. Verursacht %$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[1]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[1]%$ Schaden und hat eine geringe Chance, einen großen Flächenschaden-Angriff auszuführen.",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_2"] = "Zhu Bajie, Sun Wukongs treuer Begleiter, folgt ihm überallhin. Verursacht %$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[2]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[2]%$ Schaden und hat eine geringe Chance, einen großen Flächenschaden-Angriff auszuführen.",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_3"] = "Zhu Bajie, Sun Wukongs treuer Begleiter, folgt ihm überall hin. Fügt %$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[3]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[3]%$ Schaden zu und hat eine geringe Chance, einen großen Flächenschaden-Angriff auszuführen.",
["HERO_WUKONG_ZHU_APPRENTICE_TITLE"] = "Zhuschüler",
["HOURS_ABBREVIATION"] = "h",
["Hero at your command!"] = "Held steht zur Verfügung!",
["Heroes are elite units that can face strong enemies and support your forces."] = "Helden sind Eliteeinheiten, die es mit starken Feinden aufnehmen können und deine Truppen unterstützen.",
["Heroes gain experience every time they damage an enemy or use an ability."] = "Helden gewinnen an Erfahrung, wenn sie Feinden Schaden zufügen oder eine Fähigkeit nutzen.",
["IAP_CONNECTION_ERROR"] = "Verbindung zu %@ nicht möglich",
["INCOMING NEXT WAVE!"] = "NÄCHSTE WELLE KOMMT!",
["INCOMING WAVE"] = "WELLE KOMMT",
["INGAME_BALLOON_BUILD_HERE"] = "Bauen Sie hier!",
["INGAME_BALLOON_GOAL"] = "Lassen Sie die Feinde nicht über diesen Punkt hinaus",
["INGAME_BALLOON_GOLD"] = "Verdiene Gold, indem du Feinde tötest",
["INGAME_BALLOON_INCOMING"] = "NÄCHSTE WELLE KOMMT!",
["INGAME_BALLOON_NEW_HERO"] = "Neuer Held!",
["INGAME_BALLOON_NEW_POWER"] = "Neue Kraft!",
["INGAME_BALLOON_NOTIFICATION_TAP_HERE"] = "Tippen Sie hier!",
["INGAME_BALLOON_SELECT_HERO"] = "Tippen, um auszuwählen!",
["INGAME_BALLOON_START_BATTLE"] = "SCHLACHT BEGINNEN!",
["INGAME_BALLOON_TAP_HERE"] = "Tippen Sie auf den Weg",
["INGAME_BALLOON_TAP_TO_CALL"] = "TIPPEN, UM ES FRÜH ZU RUFEN",
["INGAME_BALLOON_TAP_TWICE_BUILD"] = "Tippen Sie zweimal, um einen Turm zu bauen",
["INGAME_BALLOON_TAP_TWICE_START"] = "ZWEIMAL TIPPEN, UM DIE SCHLACHT ZU BEGINNEN",
["INGAME_BALLOON_TAP_TWICE_WAVE"] = "Zweimal tippen, um die Welle zu rufen",
["INGAME_TUTORIAL1_HELP1"] = "Lassen Sie die Feinde nicht über diesen Punkt hinaus.",
["INGAME_TUTORIAL1_HELP2"] = "Bau Türme, um den Weg zu verteidigen.",
["INGAME_TUTORIAL1_HELP3"] = "Verdiene Gold, indem du Feinde tötest.",
["INGAME_TUTORIAL1_SUBTITLE1"] = "Schütze deine Länder vor den Angriffen des Feindes.",
["INGAME_TUTORIAL1_SUBTITLE2"] = "Baue Verteidigungstürme entlang des Weges, um sie zu stoppen.",
["INGAME_TUTORIAL1_TITLE"] = "Ziel",
["INGAME_TUTORIAL_GOTCHA_1"] = "Verstanden!",
["INGAME_TUTORIAL_GOTCHA_2"] = "Ich bin bereit, komm schon!",
["INGAME_TUTORIAL_HINT"] = "HINWEIS",
["INGAME_TUTORIAL_INSTRUCTIONS"] = "ANLEITUNGEN",
["INGAME_TUTORIAL_NEW_TIP"] = "NEUER TIPP",
["INGAME_TUTORIAL_NEXT"] = "Nächstes!",
["INGAME_TUTORIAL_OK"] = "Ok!",
["INGAME_TUTORIAL_SKIP"] = "Überspring das!",
["INGAME_TUTORIAL_TIP_CHALLENGE"] = "WARNUNG",
["ITEM_CLUSTER_BOMB_BOTTOM_DESC"] = "Wie Popcorn, aber viel mehr Spaß und weniger schmackhaft.",
["ITEM_CLUSTER_BOMB_BOTTOM_INFO"] = "Eine Bombe, die neue kleinere Bomben erzeugt.",
["ITEM_CLUSTER_BOMB_DESC"] = "Wirf eine Bombe, die Feinde in der Umgebung schädigt und weitere kleinere Bomben um sich herum auslöst.",
["ITEM_CLUSTER_BOMB_NAME"] = "Streubombe",
["ITEM_DEATHS_TOUCH_BOTTOM_DESC"] = "Großartig, wenn du dich wie ein Gott... DES TODES fühlen willst!",
["ITEM_DEATHS_TOUCH_BOTTOM_INFO"] = "Wählen. Tippen Sie auf Ihr Ziel. Töten.",
["ITEM_DEATHS_TOUCH_DESC"] = "Lade dich mit der Macht des Todes auf und tippe auf einen beliebigen Feind, um ihn sofort zu eliminieren. Funktioniert nicht bei Bossen oder Mini-Bossen.",
["ITEM_DEATHS_TOUCH_NAME"] = "Berührung des Todes",
["ITEM_LOOT_BOX_BOTTOM_DESC"] = "Ein paar davon und du hast fürs Leben ausgesorgt.",
["ITEM_LOOT_BOX_BOTTOM_INFO"] = "Lasse eine Kiste auf den Weg fallen, die Feinde beschädigt und sofort Gold erhält.",
["ITEM_LOOT_BOX_DESC"] = "Lasse eine Kiste auf den Weg fallen, die Feinde beschädigt und sofort 300 Gold erhält.",
["ITEM_LOOT_BOX_NAME"] = "Hauptader-Box",
["ITEM_MEDICAL_KIT_BOTTOM_DESC"] = "Alles, was Sie brauchen, um wieder auf die Beine zu kommen, General.",
["ITEM_MEDICAL_KIT_BOTTOM_INFO"] = "Stellt dem Spieler bis zu 3 Herzen wieder her.",
["ITEM_MEDICAL_KIT_DESC"] = "Ein spezielles Kit, das dem Spieler bis zu 3 Herzen wiederherstellt.",
["ITEM_MEDICAL_KIT_NAME"] = "Erste-Hilfe-Set",
["ITEM_PORTABLE_COIL_BOTTOM_DESC"] = "Zip! Zap! Gebraten wie eine Ratte!",
["ITEM_PORTABLE_COIL_BOTTOM_INFO"] = "Stelle eine Falle auf, die Feinden in einem Bereich Schaden zufügt und sie betäubt.",
["ITEM_PORTABLE_COIL_DESC"] = "Richte eine Bereichsfalle ein, die Feinde schädigt und betäubt, die sie auslösen. Ihre Effekte können sich auf nahe Feinde ausweiten.",
["ITEM_PORTABLE_COIL_NAME"] = "Tragbare Spule",
["ITEM_ROOM_EQUIP"] = "Ausrüsten",
["ITEM_ROOM_EQUIPPED"] = "Gerüstet",
["ITEM_ROOM_EQUIPPED_ITEMS"] = "Ausgerüstete Gegenstände",
["ITEM_SCROLL_OF_SPACESHIFT_BOTTOM_DESC"] = "Hast du jemals die Zeit verloren, gegen deine Feinde zu kämpfen? Mach dir keine Sorgen mehr!",
["ITEM_SCROLL_OF_SPACESHIFT_BOTTOM_INFO"] = "Teleportiere eine Gruppe von Feinden zurück auf den Pfad.",
["ITEM_SCROLL_OF_SPACESHIFT_DESC"] = "Teleportiere eine Gruppe von Feinden zurück auf den Pfad.",
["ITEM_SCROLL_OF_SPACESHIFT_NAME"] = "Schriftrolle der Verschiebung",
["ITEM_SECOND_BREATH_BOTTOM_DESC"] = "Erhebe dich aus dem Grab, ohne den Nachteil der Untoten.",
["ITEM_SECOND_BREATH_BOTTOM_INFO"] = "Belebt gefallene Helden wieder, heilt Verletzte und setzt die Abklingzeit der Heldenkräfte zurück.",
["ITEM_SECOND_BREATH_DESC"] = "Ein göttlicher Segen, der gefallene Helden wiederbelebt, Verletzte heilt und die Abklingzeit der Heldenkräfte zurücksetzt.",
["ITEM_SECOND_BREATH_NAME"] = "Zweiter Atem",
["ITEM_SUMMON_BLACKBURN_BOTTOM_DESC"] = "Der Eine. Der Einzige. Der Unnachahmliche.",
["ITEM_SUMMON_BLACKBURN_BOTTOM_INFO"] = "Beschwöre den mächtigen Blackburn, um an deiner Seite zu kämpfen.",
["ITEM_SUMMON_BLACKBURN_DESC"] = "Beschwöre den mächtigen Krieger-Rückkehrer, um deine Feinde zu besiegen.",
["ITEM_SUMMON_BLACKBURN_NAME"] = "Helm von Blackburn",
["ITEM_VEZNAN_WRATH_BOTTOM_DESC"] = "Lasst sie die unendliche Macht des Dunklen Zauberers schmecken!",
["ITEM_VEZNAN_WRATH_BOTTOM_INFO"] = "Vernichtet jeden Feind auf dem Schlachtfeld.",
["ITEM_VEZNAN_WRATH_DESC"] = "Vez'nan wirkt einen mächtigen Zauber, der jeden Feind auf dem Schlachtfeld dezimiert.",
["ITEM_VEZNAN_WRATH_NAME"] = "Vez'nans Zorn",
["ITEM_WINTER_AGE_BOTTOM_DESC"] = "Auch nützlich, wenn du den Sommer einfach WIRKLICH nicht magst.",
["ITEM_WINTER_AGE_BOTTOM_INFO"] = "Friere alle Feinde auf dem Bildschirm ein.",
["ITEM_WINTER_AGE_DESC"] = "Ein mächtiger Zauber, der eisige Winde erzeugt, um alle Feinde für mehrere Sekunden einzufrieren.",
["ITEM_WINTER_AGE_NAME"] = "Winterzeitalter",
["If you enjoy using %@, would you mind taking a moment to rate it? It won't take more than a minute. Thanks for your support!"] = "Sie nutzen %@ gerne? Dann nehmen Sie sich bitte für eine Bewertung einen Moment Zeit! Es dauert nicht länger als eine Minute. Vielen Dank",
["Impossible"] = "UNMÖGLICH",
["Iron Challenge"] = "Eiserne Herausforderung",
["KR5_NO_GEMS"] = "Du hast nicht genug Edelsteine.\nMöchtest du mehr kaufen?",
["KR5_PURCHASE_ERROR"] = "Beim Bearbeiten Ihres Kaufs ist ein Fehler aufgetreten.",
["KR5_RATE_US"] = "Gefällt dir das Spiel? Bewerte uns im Store!",
["LEVEL_10_HEROIC"] = "Heroische Beschreibung 10",
["LEVEL_10_HISTORY"] = "Es stellt sich heraus, dass der Kult die abgebauten Kristalle verwendet, um direkt am Ausgang des Canyons ein unheilvoll aussehendes Artefakt zu bauen. Es summt mit einer seltsamen Energie und die Luft rund um den Ort fühlt sich schwer an. Wir müssen sicherstellen, dass es zerstört wird, bevor wir weitermachen.",
["LEVEL_10_IRON"] = "Eisenbeschreibung 10",
["LEVEL_10_IRON_UNLOCK"] = "Zu definieren",
["LEVEL_10_MODES_UPGRADES"] = "Stufe 5 max",
["LEVEL_10_TITLE"] = "10. Tempelhof",
["LEVEL_11_HEROIC"] = "Heroische Beschreibung 11",
["LEVEL_11_HISTORY"] = "Wir haben endlich die Schluchten hinter uns gelassen, aber der Weg ist noch lang. Jetzt stehen wir vor einem riesigen Portal mit Kristallen, während die Seherin Mydrias ihre Rituale beendet. Was von jenseits kommen wird, wissen wir nicht, aber wir sind bereit. Nehmt euch in Acht!",
["LEVEL_11_IRON"] = "Eisenbeschreibung 11",
["LEVEL_11_IRON_UNLOCK"] = "Zu definieren",
["LEVEL_11_MODES_UPGRADES"] = "Stufe 5 max",
["LEVEL_11_TITLE"] = "11. Canyon-Plateau",
["LEVEL_12_HEROIC"] = "Heroische Beschreibung 12",
["LEVEL_12_HISTORY"] = "Mit Denas wieder an unserer Seite, haben wir das Portal ins Unbekannte durchquert. Diese seltsame Welt sieht aus wie ein verdrehtes Spiegelbild von Linirea, aber eines, das von einer Seuche verschlungen wurde. Pass auf, wohin du trittst, etwas Schlimmeres als der Kult lauert im Dunkeln.",
["LEVEL_12_IRON"] = "Eisenbeschreibung 12",
["LEVEL_12_IRON_UNLOCK"] = "Zu definieren",
["LEVEL_12_MODES_UPGRADES"] = "Stufe 5 max",
["LEVEL_12_TITLE"] = "12. Verheerte Ackerländer",
["LEVEL_13_HEROIC"] = "Heroische Beschreibung 13",
["LEVEL_13_HISTORY"] = "Die vertraute Silhouette des Sturmwolken-Tempels zeichnet sich am Horizont ab. Der Pfad ist klar genug, folge dem Gestank und der Korruption, während sie wachsen und wir werden die Quelle von allem finden. Wir müssen nur die verdrehten Schrecken überleben, die scheinbar aus der Erde selbst hervortreten.",
["LEVEL_13_IRON"] = "Eisenbeschreibung 13",
["LEVEL_13_IRON_UNLOCK"] = "Zu definieren",
["LEVEL_13_MODES_UPGRADES"] = "Stufe 5 max",
["LEVEL_13_TITLE"] = "13. Entweihter Tempel",
["LEVEL_14_HEROIC"] = "Heroische Beschreibung 14",
["LEVEL_14_HISTORY"] = "Diese verfluchten Kreaturen scheinen aus dem Nichts zu kommen! Die Truppen sind unruhig, alles, was wir berühren, scheint lebendig und bereit anzugreifen zu sein, als ob das Land selbst mit aller Macht gegen uns kämpfte. Seherin Mydrias und ihre Schergen müssen in der Nähe sein.",
["LEVEL_14_IRON"] = "Eisenbeschreibung 14",
["LEVEL_14_IRON_UNLOCK"] = "Zu definieren",
["LEVEL_14_MODES_UPGRADES"] = "Stufe 5 max",
["LEVEL_14_TITLE"] = "14. Korruptionstal",
["LEVEL_15_HEROIC"] = "Heldenhafte Beschreibung 15",
["LEVEL_15_HISTORY"] = "Wir sind als Sieger aus dem Tal hervorgegangen und jetzt steht nur noch Mydrias selbst zwischen uns und dem Aufseher. Wir haben gesehen, wozu sie in den Schluchten fähig war, aber hier, unter der Sicht und Macht ihres Meisters, hat sie die Oberhand. Nicht, dass uns die Chancen jemals zuvor aufgehalten hätten. Achtung!",
["LEVEL_15_IRON"] = "Eisenbeschreibung 15",
["LEVEL_15_IRON_UNLOCK"] = "Noch zu bestimmen",
["LEVEL_15_MODES_UPGRADES"] = "Level 5 max",
["LEVEL_15_TITLE"] = "15. Der Schandfleck Turm",
["LEVEL_16_HEROIC"] = "Heldenhafte Beschreibung 16",
["LEVEL_16_HISTORY"] = "Mydrias ist nicht mehr und der Überwacher ist der größere Feind, der bleibt. Dies ist unsere letzte Chance, dem Kult und der Invasion ein Ende zu setzen. Was danach passiert, spielt keine Rolle, wenn wir nicht ein letztes Mal zusammenstehen. Vorwärts!",
["LEVEL_16_IRON"] = "Eisenbeschreibung 16",
["LEVEL_16_IRON_UNLOCK"] = "Zu definieren",
["LEVEL_16_MODES_UPGRADES"] = "Stufe 5 max",
["LEVEL_16_TITLE"] = "16. Hungergipfel",
["LEVEL_17_HISTORY"] = "Die Umgebung des einst wunderlichen Feenwaldes sieht jetzt unfreundlich und grässlich aus. Es heißt, dass Horden gefallener Elfenkrieger und spektraler Wesen nun durch diese Lande ziehen, Reisende angreifen und den Wald selbst mit ihrer Anwesenheit verderben. General, wir müssen die Sache weiter untersuchen.",
["LEVEL_17_TITLE"] = "17. Nebelruinen",
["LEVEL_18_HISTORY"] = "Eine Nachricht erreichte uns vom Dunkellaub Außenposten, wo einige Elfen kaum dem Vormarsch der Wiederkehrerhorde widerstehen. Wir müssen uns beeilen, um ihnen und ihrem Hauptmann, Eridan, zur Hilfe zu kommen, bevor es zu spät ist. Sobald der Außenposten ordnungsgemäß gesichert ist, können wir voranschreiten, um die Wurzel dieser Invasion zu erreichen.",
["LEVEL_18_TITLE"] = "18. Dunkellaub Außenposten",
["LEVEL_19_HISTORY"] = "Ein erschöpfter Eridan wies uns auf den Tempel der Gefallenen hin, von wo aus die Horde auf den Kontinent losgelassen wird, angeführt von einem Magier, der sich Navira, der Seelenbändiger, nennt. Er muss um jeden Preis aufgehalten werden!",
["LEVEL_19_TITLE"] = "19. Tempel der Gefallenen",
["LEVEL_1_HEROIC"] = "Heldenbeschreibung 1",
["LEVEL_1_HISTORY"] = "Wir durchkämmen seit Monaten erfolglos die südlichen Wälder, da König Denas nirgendwo zu finden ist. In der Zwischenzeit haben wir uns mit den Arboreanern, Geistern der Natur angefreundet und ihre kriegerischen Nachbarn, die Wildbestien, getroffen, die uns bei Sicht angreifen.\nLasst uns diesen Kampf beenden, damit wir weiter nach dem König suchen können.",
["LEVEL_1_IRON"] = "Eisenbeschreibung 1",
["LEVEL_1_IRON_UNLOCK"] = "Königliche Bogenschützen\nPaladinsbund",
["LEVEL_1_MODES_UPGRADES"] = "Stufe 1 max",
["LEVEL_1_TITLE"] = "1. Meer der Bäume",
["LEVEL_20_HISTORY"] = "Wir haben ein dringendes Wisp von den Arborianern am Waldrand erhalten, die verzweifelt um Hilfe rufen. Sie werden von den unerbittlichen Kroks angegriffen. Sie werden nicht mehr lange durchhalten können. Seien Sie vorsichtig, General. Die Kroks haben viele Tricks in ihren Schuppen.",
["LEVEL_20_TITLE"] = "20. Arboreanisches Dorf",
["LEVEL_21_HISTORY"] = "Nachdem sie die Sicherheit der Stadt gewährleistet hatten, enthüllten die Arborianer, dass sie kurz vor dem Angriff gespürt hatten, wie ihr alter Siegel zu wanken begann. Mit einer Spur zur plötzlichen Invasion der Croks ausgerüstet, tauchten wir ins Herz des Sumpfes ein. Wir stolperten über einen alten arborianischen Steinkreis, es sieht aus wie ein Versteck... ein Versteck von etwas Riesigem.",
["LEVEL_21_TITLE"] = "21. Die Versunkenen Ruinen",
["LEVEL_22_HISTORY"] = "Bei unserer Ankunft am alten Tempel wurden unsere schlimmsten Befürchtungen bestätigt. Das Siegel, das unsere Welt lange vor Abominor — dem Verschlinger der Welten — geschützt hatte, war nahezu aufgelöst, gehalten nur durch die verzweifelte Bindungsmagie einiger Arboreaner Schamanen. General, stoppen Sie Abominor, oder die Königreiche werden von seinem unersättlichen Maul verschlungen.",
["LEVEL_22_TITLE"] = "22. Hungernde Höhle",
["LEVEL_23_HISTORY"] = "Späher berichteten von unnatürlichen Erdrutschen in den benachbarten Bergen. Weitere Untersuchungen ergaben, dass diese von uns unbekannten Zwergen verursacht werden. Sie bauen einen riesigen Mecha an der Südseite des Berges zusammen. Sie sollten sich das ansehen, General.",
["LEVEL_23_TITLE"] = "23. Dunkelstahl-Tore",
["LEVEL_24_HISTORY"] = "Zwerge waren schon immer als Erfinder bekannt, aber dieser selbsternannte Dunkelstahl-Clan treibt seine Hingabe zum Metall viel zu weit und beschämt sogar Bolgurs Volk, indem sie ihre Schmiede nutzen, um sich in rasanter Geschwindigkeit zu \"verbessern\". Wer steckt hinter diesem Wahnsinn? Das müssen wir herausfinden!",
["LEVEL_24_TITLE"] = "24. Hektische Fertigung ",
["LEVEL_25_HISTORY"] = "Die Situation ist, wie wir befürchtet haben: Nur das Innere eines so großen Berges könnte eine Schmiede beherbergen, die in der Lage ist, diesen Mecha zu erschaffen. Wie viele Zwerge sind hier? Sie widerstehen unserem Vormarsch und schmieden und schweißen dennoch weiter. Und was noch seltsamer ist: Sie sehen alle gleich aus? Irgendetwas stimmt nicht.",
["LEVEL_25_TITLE"] = "25. Kolossaler Kern",
["LEVEL_26_HISTORY"] = "Unser Weg in und aus dem Berg führte uns zu einer Kammer voller Behälter, und sie waren nicht leer. Kein Wunder, dass sie zahlenmäßig stark sind und zudem Handwerkskunst und Aussehen besitzen. Sie sind alle derselbe Zwerg, Grymbeard! Er hat durch üble Wissenschaft Kopien von sich selbst erschaffen. General, das müssen wir stoppen!",
["LEVEL_26_TITLE"] = "26. Replikationskammer",
["LEVEL_27_HISTORY"] = "Es ist uns gelungen, den Großteil der Dunkelstahl-Operation im Berg zu stören, aber alles wird vergebens sein, wenn Grymbeard noch auf freiem Fuß ist. Er arbeitet mit Sicherheit an den letzten Feinheiten des Kopfes des Mecha. General, führen Sie die Truppen zu den Gipfeln, und hoffen wir, dass wir es diesmal mit dem richtigen Zwerg zu tun haben.",
["LEVEL_27_TITLE"] = "27. Herrschaftskuppel",
["LEVEL_28_HISTORY"] = "Den Hinweisen unserer Kundschafter folgend, haben wir eine Spur entdeckt, die zu diesen verfluchten Kultisten führt. Es scheint, als hätten sie eine neue Gottheit zum Anbeten gefunden—eine schreckliche, netzspinnende Abscheulichkeit... Kultisten UND Spinnen? Dabei kann nichts gutes herauskommen.",
["LEVEL_28_TITLE"] = "28. Entweihter Tempel",
["LEVEL_29_HISTORY"] = "Je tiefer wir vordringen, desto klarer wird, dass dieses Grauen sich schon lange unter uns ausbreitet hat, und nur auf den richtigen Moment gewartet hat um anzugreifen. Wenn man die sich verdichtenden Netze um uns, und die Dunkelheit, die uns im Nacken zu sitzen scheint, betrachtet, würde ich wetten, dass wir uns dem Herzen ihres Verstecks nähern.",
["LEVEL_29_TITLE"] = "29. Brutkammer",
["LEVEL_2_HEROIC"] = "Heldenbeschreibung 2",
["LEVEL_2_HISTORY"] = "Seid wachsam, die Nachricht erreichte uns durch ein Wispern! Das Herz des Waldes wird angegriffen! Wir müssen zurückkehren und den Arboreanern helfen. Einige Kräfte der Dunklen Armee werden sich uns auf dem Schlachtfeld anschließen, also haltet die Augen offen. Wir mögen jetzt im selben Boot sitzen, doch das kann sich jederzeit ändern.",
["LEVEL_2_IRON"] = "Eisenbeschreibung 2",
["LEVEL_2_IRON_UNLOCK"] = "Arkaner Magier\nTricannon",
["LEVEL_2_MODES_UPGRADES"] = "Stufe 2 max",
["LEVEL_2_TITLE"] = "2. Das Wächtertor",
["LEVEL_30_HISTORY"] = "Endlich haben wir das Versteck ihrer sogenannten Göttin erreicht—ein verfallener, längst verlassener Tempel, der unter der Last seiner eigenen vergessenen Vergangenheit zusammenbricht. Ein passender Thron für eine verlassene Gottheit. Dieses Mal werden wir sicher gehen nichts und niemand am Leben zulassen, um diese Plage ein für alle Mal auszurotten.",
["LEVEL_30_TITLE"] = "30. Der Vergessene Thron",
["LEVEL_31_HISTORY"] = "Nach all dem Kämpfen und dem Ringen ist endlich Frieden in die Königreiche zurückgekehrt. Jetzt ist das Einzige, was zu tun bleibt, den Wellen beim Brechen zuzuhören und Brettspiele zu spielen, während man auf einen alten Freund wartet. Und doch frage ich mich, selbst wenn alles so ruhig erscheint, wie lange dieser Frieden wohl andauern wird...",
["LEVEL_31_TITLE"] = "31. Himmlischer Affenwald",
["LEVEL_32_HISTORY"] = "Unsere Verfolgung hat uns tief ins Herz des Vulkans geführt, wo einst ein längst vergessenes Tempel den Flammen huldigte.\n\nDoch der Große Feuerdrache, einst ein neutraler Wächter dieser feurigen Tiefen, regt sich nun in unnatürlicher Wut. Alles deutet darauf hin, dass Red Boys Einfluss seinen Willen verdorben hat.",
["LEVEL_32_TITLE"] = "32. Feuerdrachenhöhle",
["LEVEL_33_HISTORY"] = "Nach einem erschöpfenden Kampf mit dem Roten Jungen setzen wir unseren Weg zur Sturminsel fort. Kaum angekommen, brauen sich blitzgeladene Wolken zusammen, und heftige Böen heulen in seltsamen, wirbelnden Mustern. Dennoch haben wir keine Wahl – die Insel beherbergt den einzigen Eingang zum Palast der Prinzessin. Macht euch bereit... ein Sturm zieht auf.",
["LEVEL_33_TITLE"] = "33. Sturminsel",
["LEVEL_34_HISTORY"] = "Wir können der Prinzessin und ihrem Eisernen Fächer für die Schwierigkeiten danken, die wir durchlitten haben. Nachdem wir die Brücke überquert und die heftigsten Stürme durchstanden haben, stehen wir nun im Zentrum des Ganzen. Dieser Ort bleibt unversehrt—trügerisch ruhig und schön. Wir dürfen unsere Wachsamkeit nicht aufgeben. Nicht einmal dämonische Hoheit wird uns aufhalten.",
["LEVEL_34_TITLE"] = "34. Das Auge des Sturms",
["LEVEL_35_HISTORY"] = "Dies ist es. Der Stierdämonenkönig steht hoch auf seiner uneinnehmbaren Festung. Mit dem Rest unserer Truppen stürmen wir frontal mit Kraft und Klugheit. Wir müssen zuschlagen, bevor er die Macht der Kugeln vollständig entfesselt.\nBei allem, was euch auf diesem guten Land heilig ist... Steht fest, Allianz!",
["LEVEL_35_TITLE"] = "35. Festung des Dämonenkönigs",
["LEVEL_3_HEROIC"] = "Heldenbeschreibung 3",
["LEVEL_3_HISTORY"] = "Wir haben es gerade noch rechtzeitig zum Herzen geschafft, aber die Wildtiere kommen bereits durch. Seid wachsam und verstärkt eure Positionen! Schützt das Herz um jeden Preis, oder der Wald und die Arboreans werden sicherlich zugrunde gehen.",
["LEVEL_3_IRON"] = "Eisenbeschreibung 3",
["LEVEL_3_IRON_UNLOCK"] = "Königliche Bogenschützen\nPaladinsbund",
["LEVEL_3_MODES_UPGRADES"] = "Stufe 3 max",
["LEVEL_3_TITLE"] = "3. Das Herz des Waldes",
["LEVEL_4_HEROIC"] = "Heldenbeschreibung 4",
["LEVEL_4_HISTORY"] = "Jetzt, da das Herz des Waldes sicher ist, müssen wir uns neu gruppieren und den Vorteil nutzen. Es ist an der Zeit, den Kampf ins Gebiet der Wildbestien zu tragen. Führe die Truppen in die Baumwipfel des Waldes und suche von oben nach ihrem Lager.",
["LEVEL_4_IRON"] = "Eisenbeschreibung 4",
["LEVEL_4_IRON_UNLOCK"] = "Tricannon\nArborischer Gesandter",
["LEVEL_4_MODES_UPGRADES"] = "Stufe 4 max",
["LEVEL_4_TITLE"] = "4. Smaragdgrüne Baumwipfel",
["LEVEL_5_HEROIC"] = "Heldenbeschreibung 5",
["LEVEL_5_HISTORY"] = "Dank eurer Anstrengungen, die Höhe zu nehmen, haben wir das Lager der Wildbestien in alten Ruinen jenseits der Waldgrenzen ausgemacht. Führe die Streitkräfte in ihr Gebiet und sei wachsam gegenüber ihrer Taktik. Wir haben vielleicht eine weitere Schlacht gewonnen, aber das ist noch lange nicht vorbei.",
["LEVEL_5_IRON"] = "Eisenbeschreibung 5",
["LEVEL_5_IRON_UNLOCK"] = "Arkaner Magier\nPaladin-Bund",
["LEVEL_5_MODES_UPGRADES"] = "Stufe 5 max",
["LEVEL_5_TITLE"] = "5. Verwüstete Außenbezirke",
["LEVEL_6_HEROIC"] = "Heldenbeschreibung 6",
["LEVEL_6_HISTORY"] = "Wir haben vielleicht die Oberhand gegen die Wildbestien, aber wir müssen uns noch ihrem Anführer, Goregrind, stellen. Der selbst ernannte König der Wildbestien ist ein mächtiger Feind, also lasst euch nicht von seinen Streichen täuschen, sonst werdet ihr unter seinen Hauern enden.",
["LEVEL_6_IRON"] = "Eisenbeschreibung 6",
["LEVEL_6_IRON_UNLOCK"] = "Königliche Bogenschützen\nDämonengrube",
["LEVEL_6_MODES_UPGRADES"] = "Stufe 5 max",
["LEVEL_6_TITLE"] = "6. Die Höhle der Wildbestien",
["LEVEL_7_HEROIC"] = "Heldenbeschreibung 7",
["LEVEL_7_HISTORY"] = "Folgend der Spur der Kultisten, die den Wildbestien geholfen haben, einen Teil des Waldes niederzubrennen, kommen wir an einen verlassenen Ort, wo wir vermuten, dass der Kult seine seltsamen Pläne ausführt. Wir müssen vorsichtig sein, denn wir wissen nicht genau, was uns erwartet... aber sie scheinen ein paar Tricks im Ärmel zu haben.",
["LEVEL_7_IRON"] = "Eisenbeschreibung 7",
["LEVEL_7_IRON_UNLOCK"] = "Keine königlichen Bogenschützen",
["LEVEL_7_MODES_UPGRADES"] = "Stufe 5 max",
["LEVEL_7_TITLE"] = "7. Düsteres Tal ",
["LEVEL_8_HEROIC"] = "Heldenbeschreibung 8",
["LEVEL_8_HISTORY"] = "Als wir uns auf das Gebiet der Kultisten begaben, kamen wir in ein Set von riesigen Höhlen voller Kristalle, die mit seltsamer Magie resonieren. Der Kult baut diese Kristalle ab, sicherlich um sie als Energiequelle zu nutzen. Zu welchem Zweck, wissen wir nicht, aber ihre Aktivitäten zu stören, ist ein guter Weg, Chaos in ihren Reihen zu verursachen. ",
["LEVEL_8_IRON"] = "Eisenbeschreibung 8",
["LEVEL_8_IRON_UNLOCK"] = "Tricannon\nPaladinbund",
["LEVEL_8_MODES_UPGRADES"] = "Stufe 5 max",
["LEVEL_8_TITLE"] = "8. Karminminen",
["LEVEL_9_HEROIC"] = "Heroische Beschreibung 9",
["LEVEL_9_HISTORY"] = "Die Verdrehungen und Wendungen dieser Tunnel machen einen wahnsinnig, aber wir wissen, dass wir auf dem richtigen Weg sind, da die Aktivität der Kultisten stetig zunimmt. Je weiter wir vorankommen, desto mehr neuen Arten von Schrecken begegnen wir, was die Frage aufwirft, wie tief die Korruption innerhalb der Reihen des Kults reicht.",
["LEVEL_9_IRON"] = "Eisenbeschreibung 9",
["LEVEL_9_IRON_UNLOCK"] = "Dämonengrube\nArkaner Zauberer",
["LEVEL_9_MODES_UPGRADES"] = "Stufe 5 max",
["LEVEL_9_TITLE"] = "9. Verdammte Kreuzung",
["LEVEL_DEFEAT_ADVICE"] = "BENUTZE JUWELEN, UM SPEZIELLE GEGENSTÄNDE FÜR DIE VERNICHTUNG DEINER FEINDE ZU ERHALTEN!",
["LEVEL_DEFEAT_GEMS_COLLECTED"] = "DU HAST GESAMMELT:",
["LEVEL_DEFEAT_GEMS_COUNT"] = "%iJUWELEN",
["LEVEL_DEFEAT_TITLE"] = "NIEDERLAGE!",
["LEVEL_MODE_CAMPAIGN"] = "Kampagne",
["LEVEL_MODE_HEROIC"] = "Heldenhafte Herausforderung",
["LEVEL_MODE_HEROIC_DESCRIPTION"] = "Stelle dein taktisches Können gegen eine Elitetruppe des Feindes in dieser Herausforderung für wahre Helden auf die Probe!",
["LEVEL_MODE_IRON"] = "Eiserne Herausforderung",
["LEVEL_MODE_IRON_DESCRIPTION"] = "Eine Prüfung für den ultimativen Verteidiger! Die Eiserne Herausforderung wird deine taktischen Fähigkeiten an ihre Grenzen führen.",
["LEVEL_SELECT_AVAILABLE_TOWERS"] = "Verfügbare Türme",
["LEVEL_SELECT_CHALLENGE_ONE_ELITE_WAVE"] = "Elitewellen: 1",
["LEVEL_SELECT_CHALLENGE_ONE_LIFE"] = "Leben: 1",
["LEVEL_SELECT_CHALLENGE_ONE_WAVE"] = "Superwellen: 1",
["LEVEL_SELECT_CHALLENGE_RULES"] = "Herausforderungsregeln",
["LEVEL_SELECT_CHALLENGE_SIX_ELITE_WAVE"] = "Elitewellen: 6",
["LEVEL_SELECT_DIFFICULTY_CASUAL"] = "EINFACH",
["LEVEL_SELECT_DIFFICULTY_IMPOSSIBLE"] = "UNMÖGLICH",
["LEVEL_SELECT_DIFFICULTY_NORMAL"] = "NORMAL",
["LEVEL_SELECT_DIFFICULTY_VETERAN"] = "VETERAN",
["LEVEL_SELECT_GAME_MODE"] = "Spielmodus",
["LEVEL_SELECT_GET_DLC"] = "HOL ES DIR",
["LEVEL_SELECT_HELP1"] = "Wähle hier den Spielmodus!",
["LEVEL_SELECT_HELP2"] = "Wähle den Schwierigkeitsgrad!",
["LEVEL_SELECT_HELP3"] = "Kampf Beginnen!",
["LEVEL_SELECT_MODE_LOCKED1"] = "Modus gesperrt",
["LEVEL_SELECT_MODE_LOCKED2"] = "Schalte diesen Modus frei, indem du diese Stufe abschließt.",
["LEVEL_SELECT_TO_BATTLE"] = "AUF IN\nDEN KAMPF",
["LOADING"] = "WIRD GELADEN",
["LV22_BOSS_BEFORE_FIGHT_EAT_01"] = "Lecker! Ha Ha Ha",
["LV22_BOSS_BEFORE_FIGHT_EAT_02"] = "Ich hasse Pflanzen",
["LV22_BOSS_BEFORE_FIGHT_EAT_03"] = "Du bist, was du isst",
["LV22_BOSS_BEFORE_FIGHT_EAT_04"] = "Dieser Bissen war erfrischend",
["LV22_BOSS_BEFORE_FIGHT_EAT_05"] = "Schon müde?",
["LV22_BOSS_BEFORE_FIGHT_EAT_06"] = "Ich werde nie wieder hungrig sein",
["LV22_BOSS_BEFORE_FIGHT_EAT_07"] = "Das war ein großer Turm, hahaha",
["LV22_BOSS_BEFORE_FIGHT_EAT_08"] = "Es schmeckt nach Freiheit",
["LV22_BOSS_INTRO_01"] = "Bringt Snacks für meine erste Mahlzeit mit.",
["LV22_BOSS_INTRO_02"] = "Die sehen... knusprig aus",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_01"] = "Ihr werdet nur Pflanzen schmecken.",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_02"] = "Grünzeug sind Freunde, kein Futter",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_03"] = "Und du wirst nichts mehr essen!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_04"] = "Kehre zurück in dein Gefängnis, Monster!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_05"] = "Du sollst nicht essen!!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_06"] = "Ich werde das Grüne schützen!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_07"] = "Du wirst am Ende nicht lachen",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_08"] = "Er wird stärker! Hilfe!!",
["LV22_MAGE_INTRO_01"] = "Halt die Klappe!",
["LV22_MAGE_INTRO_02"] = "Beeilung! Ich kann ihn nicht länger aufhalten!",
["Localization Manager"] = "Lokalisierungs-Manager",
["Log in"] = "Einloggen",
["Log out?"] = "Abmelden?",
["Login Required"] = "Einloggen",
["MAGES’ GUILD"] = "MAGIERGILDE",
["MAGIC RESISTANT ENEMIES!"] = "FEINDE MIT MAGIERESISTENZEN!",
["MAP_BALLON_BUY_UPGRADES_DESCRIPTION"] = "Benutze deine Sterne zur Verbesserung deiner Türme und Kräfte!",
["MAP_BALLON_BUY_UPGRADES_TITLE"] = "KAUF VERBESSERUNGEN!",
["MAP_BALLON_HERO_LEVELUP_DESCRIPTION"] = "Benutze deine Heldenpunkte zur Ausbildung deines Helden!",
["MAP_BALLON_HERO_LEVELUP_TITLE"] = "LEVELAUFSTIEG DES HELDEN!",
["MAP_BALLON_HERO_UNLOCKED"] = "HELD FREIGESCHALTET!",
["MAP_BALLON_START_HERE"] = "HIER BEGINNEN!",
["MAP_BUTTON_ACHIEVEMENTS"] = "ERFOLGE",
["MAP_BUTTON_CHALLENGES"] = "HERAUSFORDERUNGEN",
["MAP_BUTTON_ENCYCLOPEDIA"] = "ENZYKLOPÄDIE",
["MAP_BUTTON_HERO_ROOM"] = "HELDEN",
["MAP_BUTTON_ITEMS"] = "GEGENSTÄNDE",
["MAP_BUTTON_SHOP"] = "SHOP",
["MAP_BUTTON_TOWER_ROOM"] = "TÜRME",
["MAP_BUTTON_UPGRADES"] = "VERBESSERUNGEN",
["MAP_ENCYCLOPEDIA_STRATEGY_GUIDE"] = "STRATEGIEBUCH",
["MAP_ENCYCLOPEDIA_TIPS"] = "TIPPS",
["MAP_HEROROOM_HELP1"] = "Wähle und lerne Fähigkeiten!",
["MAP_HEROROOM_HELP2"] = "Zum Auswählen tippen",
["MAP_HEROROOM_HELP3"] = "Verbessere die Heldenkraft!",
["MAP_HERO_ROOM_GET_IT_NOW"] = "JETZT HOLEN!",
["MAP_HERO_ROOM_SELECT"] = "AUSRÜSTEN",
["MAP_HERO_ROOM_SELECTED"] = "GERÜSTET",
["MAP_HERO_ROOM_TRAIN"] = "LERNEN",
["MAP_HERO_ROOM_UNLOCK"] = "FREISCHALTUNG IN ABSCHNITT %d",
["MAP_HERO_ROOM_UNLOCK_10"] = "FREISCHALTUNG IN ABSCHNITT 10",
["MAP_HERO_ROOM_UNLOCK_14"] = "FREISCHALTUNG IN ABSCHNITT 14",
["MAP_HERO_ROOM_UNLOCK_15"] = "FREISCHALTUNG IN ABSCHNITT 15",
["MAP_HERO_ROOM_UNLOCK_4"] = "FREISCHALTUNG IN ABSCHNITT 4",
["MAP_HERO_ROOM_UNLOCK_7"] = "FREISCHALTUNG IN ABSCHNITT 7",
["MAP_HERO_ROOM_UNLOCK_9"] = "FREISCHALTUNG IN ABSCHNITT 9",
["MAP_HERO_ROOM_UNLOCK_AFTER_CAMPAIGN"] = "Wird nach Abschluss des Spiels freigeschaltet",
["MAP_INAPPS_BUBBLE_INFO_1"] = "Spiele das Spiel und sammle Juwelen.",
["MAP_INAPPS_BUBBLE_INFO_2"] = "Benutze Juwelen, um spezielle Gegenstände zu kaufen!",
["MAP_INAPPS_BUBBLE_MORE_GEMS"] = "Du brauchst mehr Juwelen!",
["MAP_INAPPS_BUBBLE_SUCCESSFUL"] = "Kauf\nerfolgreich!",
["MAP_INAPP_GEMS_GEM_SHOP_TITLE"] = "JUWELEN-SHOP",
["MAP_INAPP_GEM_PACK_1"] = "HANDVOLL EDELSTEINE",
["MAP_INAPP_GEM_PACK_2"] = "BEUTEL MIT EDELSTEINEN",
["MAP_INAPP_GEM_PACK_3"] = "FASS VOLLER EDELSTEINE",
["MAP_INAPP_GEM_PACK_4"] = "KISTE MIT EDELSTEINEN",
["MAP_INAPP_GEM_PACK_5"] = "WAGEN VOLLER EDELSTEINE",
["MAP_INAPP_GEM_PACK_6"] = "BERG VON EDELSTEINEN",
["MAP_INAPP_GEM_PACK_BAG"] = "Beutel Juwelen",
["MAP_INAPP_GEM_PACK_BARREL"] = "Fass Juwelen",
["MAP_INAPP_GEM_PACK_CHEST"] = "Kiste Juwelen",
["MAP_INAPP_GEM_PACK_FREE"] = "Gratisjuwelen",
["MAP_INAPP_GEM_PACK_HANDFUL"] = "Handvoll Juwelen",
["MAP_INAPP_GEM_PACK_VAULT"] = "Schatzkammer voll Juwelen",
["MAP_INAPP_GEM_PACK_WAGON"] = "Wagen voll Juwelen",
["MAP_INAPP_MORE_GEMS"] = "MEHR JUWELEN",
["MAP_INAPP_TEXT_1"] = "Handvoll Juwelen",
["MAP_INAPP_TEXT_2"] = "Beutel Juwelen",
["MAP_INAPP_TEXT_3"] = "Kiste Juwelen",
["MAP_INAPP_TEXT_4"] = "Gratisjuwelen",
["MAP_INAPP_TEXT_GEMS"] = "Juwelen",
["MAP_NEW_GAMEMODE_UNLOCKED_DESCRIPTION"] = "Stelle dich endlosen Feindeshorden und kämpfe um die beste Punktzahl!",
["MAP_NEW_GAMEMODE_UNLOCKED_TITLE"] = "NEUE HERAUSFORDERUNG!",
["MAP_NEW_HERO_ALERT"] = "NEUER\nHELD!",
["MAP_NEW_TOWER_ALERT"] = "NEUER\nTURM!",
["MAP_TOWER_ROOM_SELECT"] = "AUSRÜSTEN",
["MAP_TOWER_ROOM_SELECTED"] = "GERÜSTET",
["MEDIUM"] = "MITTEL",
["MENU_HUD_WAVES"] = "%i/%i",
["MINUTES_ABBREVIATION"] = "m",
["MORE_GAMES"] = "Mehr Spiele",
["Magic resistant enemies take less damage from mages."] = "Feinde mit Magieresistenz erleiden weniger Schaden durch Magier.",
["NEW"] = "NEU",
["NEW SPECIAL POWER!"] = "NEUE SPEZIALFÄHIGKEIT!",
["NEW TOWER UNLOCKED"] = "NEUER TURM FREIGESCHALTET",
["NEW TOWER UPGRADES"] = "NEUE TURM-VERBESSERUNGEN",
["NEWS"] = "NEWS",
["NEWS_ERROR"] = "Verbindung nicht möglich. Bitte überprüfe deine Internetverbindung oder versuche es erneut.",
["NOTIFICATION_NEW_ENEMY_TITLE"] = "NEUER FEIND",
["NOTIFICATION_NEW_SPECIAL_TITLE"] = "NEUE SPEZIALKRAFT!",
["NOTIFICATION_NEW_TOWERS_SUB_DESCRIPTION"] = "Du kannst deine Türme jetzt bis zu Level %d aufrüsten.",
["NOTIFICATION_NEW_TOWERS_SUB_TITLE"] = "TÜRME DER STUFE %d VERFÜGBAR",
["NOTIFICATION_NEW_TOWERS_TITLE"] = "NEUE TURMUPGRADES",
["NOTIFICATION_NEW_TOWER_TITLE"] = "NEUER TURM FREIGESCHALTET",
["NOTIFICATION_armored_enemies_desc_body_1"] = "Einige Feinde tragen Rüstungen unterschiedlicher Stärken, die sie gegen nicht-magische Angriffe schützt.",
["NOTIFICATION_armored_enemies_desc_body_2"] = "Widersteht Schaden von",
["NOTIFICATION_armored_enemies_desc_body_3"] = "Gepanzerte Feinde nehmen weniger Schaden von Scharfschützen-, Kaserne- und Artillerietürmen.",
["NOTIFICATION_armored_enemies_desc_title"] = "Gepanzerte Feinde!",
["NOTIFICATION_armored_enemies_enemy_name"] = "Hauer-Schläger",
["NOTIFICATION_bottom_info_desc_body"] = "Du kannst die Feindinformationen jederzeit überprüfen, indem du auf eine Einheit und ihr Porträt tippst.",
["NOTIFICATION_bottom_info_desc_title"] = "FEINDINFORMATIONEN",
["NOTIFICATION_bottom_info_tap_portrait_desc"] = "Hier antippen, um wieder zu öffnen",
["NOTIFICATION_button_ok"] = "Ok",
["NOTIFICATION_glare_desc_body"] = "Der Aufseher starrt auf das Schlachtfeld und stärkt nahe Feinde mit seinem korrumpierten Blick.",
["NOTIFICATION_glare_desc_bullets"] = " - Heilt Feinde, die sich im Bereich befinden\n- Löst die einzigartigen Fähigkeiten der Feinde aus",
["NOTIFICATION_glare_desc_title"] = "Blick des Aufsehers",
["NOTIFICATION_hero_desc"] = "Zeigt Level, Gesundheit und Erfahrung an.",
["NOTIFICATION_hero_desc_baloon_1"] = "Wählen Sie es aus, indem Sie den Helden oder sein Porträt antippen",
["NOTIFICATION_hero_desc_baloon_2"] = "Tippen oder ziehen Sie über den Pfad, um ihn zu bewegen",
["NOTIFICATION_hero_desc_body_1"] = "Helden sind Eliteeinheiten, die mächtigen Feinden gegenübertreten und Ihre Kräfte unterstützen können.",
["NOTIFICATION_hero_desc_body_2"] = "Helden gewinnen Erfahrung, jedes Mal wenn sie einen Feind angreifen oder eine Fähigkeit benutzen.",
["NOTIFICATION_hero_desc_title"] = "Held zu Ihren Diensten!",
["NOTIFICATION_magic_resistant_enemies_desc_body_1"] = "Einige Feinde haben unterschiedliche Stufen der Magieresistenz, was sie gegen magische Angriffe schützt.",
["NOTIFICATION_magic_resistant_enemies_desc_body_2"] = "Widersteht Schaden von",
["NOTIFICATION_magic_resistant_enemies_desc_body_3"] = "Magieresistente Feinde nehmen weniger Schaden von Magiertürmen.",
["NOTIFICATION_magic_resistant_enemies_desc_title"] = "Magieresistente Feinde!",
["NOTIFICATION_magic_resistant_enemies_enemy_name"] = "Schildkröten-Schamane",
["NOTIFICATION_rally_point_desc_body_1"] = "Du kannst den Sammelpunkt deiner Kasernen anpassen, um Einheiten eine andere Gegend verteidigen zu lassen.",
["NOTIFICATION_rally_point_desc_body_2"] = "Wähle die Versammlungspunktsteuerung.",
["NOTIFICATION_rally_point_desc_body_3"] = "Wähle, wohin du deine Soldaten schicken willst.",
["NOTIFICATION_rally_point_desc_subtitle"] = "Sammelbereich",
["NOTIFICATION_rally_point_desc_title"] = "Befehligt eure Truppen!",
["NOTIFICATION_special_desc_body"] = "Du kannst zusätzliche Truppen herbeirufen, um dir auf dem Schlachtfeld zu helfen.",
["NOTIFICATION_special_desc_bullets"] = "Verstärkungen eignen sich hervorragend, um Feinde aufzuhalten.",
["NOTIFICATION_special_desc_title"] = "Verstärkung rufen",
["NOTIFICATION_title_enemy"] = "Feindinfo",
["NOTIFICATION_title_glare"] = "NEUER TIPP!",
["NOTIFICATION_title_hint"] = "HELD FREIGESCHALTET",
["NOTIFICATION_title_new_tip"] = "NEUER TIPP",
["NOTIFICATION_title_special"] = "SPEZIAL FREIGESCHALTET",
["No"] = "Nein",
["No, Thanks"] = "Nein, danke",
["None"] = "Keine",
["Nope"] = "Nö.",
["Normal"] = "Normal",
["OFF!"] = "OFF!",
["OFFERS_END"] = "Angebot endet in:",
["OFFER_GET_IT_NOW"] = "JETZT HOLEN",
["OFFER_GET_THEM_NOW"] = "HOL SIE DIR JETZT",
["OFFER_ICON_BANNER"] = "ANGEBOT",
["OFFER_OFF"] = "AUS",
["OFFER_PACK_DESCRIPTION_ALL_HEROES"] = "HOL DIR JETZT ALLE HELDEN!",
["OFFER_PACK_DESCRIPTION_ALL_TOWERS"] = "HOL DIR JETZT ALLE TÜRME!",
["OFFER_PACK_DESCRIPTION_TEXT_01"] = "Verstärke deine Truppen mit diesem einzigartigen und zeitlich begrenzten Angebot!",
["OFFER_PACK_DESCRIPTION_TEXT_02"] = "Kaufe dieses Angebot!",
["OFFER_PACK_TIMELEFT_TEXT"] = "Angebot endet in:",
["OFFER_PACK_TITLE_01"] = "Halloween-Angebot",
["OFFER_PACK_TITLE_02"] = "Black-Friday-Angebot",
["OFFER_PACK_TITLE_03"] = "Weihnachtsangebot",
["OFFER_PACK_TITLE_04"] = "Neujahrsangebot",
["OFFER_PACK_TITLE_05"] = "Frühlingsfest-Angebot",
["OFFER_PACK_TITLE_06"] = "Sommerschlussverkauf-Angebot",
["OFFER_PACK_TITLE_07"] = "Ironhide-Angebot",
["OFFER_PACK_TITLE_08"] = "Starterpaket-Angebot",
["OFFER_PACK_TITLE_09"] = "Limitiertes Angebot",
["OFFER_PACK_TITLE_ALL_HEROES"] = "MEGA-HELDENPAKET",
["OFFER_PACK_TITLE_ALL_TOWERS"] = "MEGA-TURMPAKET",
["OFFER_PACK_TITLE_STARTER_PACK"] = "Starterpaket-Angebot",
["OFFER_REGULAR"] = "NORMALPREIS",
["ONE_TIME_OFFER"] = "EINMALIGES ANGEBOT!",
["ON_SALE"] = "IM ANGEBOT",
["OPTIONS"] = "EINSTELLUNGEN",
["OPTIONS_PAGE_CONTROLS"] = "TASTENBELEGUNG",
["OPTIONS_PAGE_HELP"] = "HILFE",
["OPTIONS_PAGE_SHORTCUTS"] = "TASTATURHILFE",
["OPTIONS_PAGE_VIDEO"] = "VIDEO",
["Objective"] = "Ziel",
["Over 50 stars are recommended to face this stage."] = "Für diesen Abschnitt sind mehr als 50 Sterne empfohlen.",
["POPUP_CLEAR_PROGRESS_CONFIRM"] = "SIND SIE SICHER, DASS SIE IHREN FORTSCHRITT LÖSCHEN MÖCHTEN?",
["POPUP_LABEL_MAIN_MENU"] = "Hauptmenü",
["POPUP_SETTINGS_LANGUAGE"] = "Sprache",
["POPUP_SETTINGS_MUSIC"] = "MUSIK",
["POPUP_SETTINGS_SFX"] = "Effekte",
["POPUP_label_error_msg"] = "Uups! Etwas ist schiefgelaufen.",
["POPUP_label_error_msg2"] = "Uups! Etwas ist schiefgelaufen.",
["POPUP_label_purchasing"] = "ANFRAGE WIRD BEARBEITET",
["POPUP_label_title_options"] = "Optionen",
["POPUP_label_version"] = "Version 0.0.8b",
["POWER_REINFORCEMENTS_NAME"] = "Verstärkung",
["POWER_SUMMON_DESCRIPTION"] = "Beschwöre Truppen auf das Schlachtfeld.",
["POWER_SUMMON_LARGE_DESCRIPTION"] = "Du kannst Truppen beschwören, die dir auf dem Schlachtfeld helfen.\n\nVerstärkungstruppen sind gratis und du kannst sie alle 15 Sekunden anfordern.",
["POWER_SUMMON_NAME"] = "Verstärkung",
["PRICE_FREE"] = "Kostenlos",
["PRIVACY_POLICY_ASK_AGE"] = "Wann wurdest du geboren?",
["PRIVACY_POLICY_BUTTON_LINK"] = "Datenschutz\nrichtlinie",
["PRIVACY_POLICY_CONSENT_SHORT"] = "Bestätige vor dem Spielen unseres Spiels bitte, dass du (und deine Eltern, falls du ein Kind oder Jugendlicher bist) unsere Datenschutzrichtlinie gelesen habt.",
["PRIVACY_POLICY_LINK"] = "Datenschutz\nrichtlinie",
["PRIVACY_POLICY_WELCOME"] = "Willkommen!",
["PROCESSING ITEMS TO RESTORE"] = "VERARBEITENDE EINZELTEILE",
["PROCESSING YOUR REQUEST"] = "ANFRAGE WIRD BEARBEITET",
["PURCHASE_PENDING_MESSAGE"] = "Der Kauf steht noch aus und wird geliefert, nachdem die Zahlung oder Bearbeitung abgeschlossen ist.",
["PUSH_NOTIFICATIONS_PERMISSION_RATIONALE"] = "Möchten Sie Benachrichtigungen über Produktverkäufe und neue Spiele von Ironhide erhalten?",
["Produced by %s"] = "Produziert von %s",
["QUIT"] = "Beenden",
["Quit"] = "Abbrechen",
["RESTORE"] = "WIEDERHERSTELLEN",
["RESTORE_PURCHASES"] = "Käufe wiederherstellen",
["RESTORE_SLOT_ADD_GEMS_TITLE"] = "Wählen Sie den Steckplatz, um Edelsteine hinzuzufügen",
["RESTORE_SLOT_PROGRESS_MSG"] = "Abrufen von Wiederherstellungsdaten vom Server",
["RESTORE_SLOT_STATS_TITLE"] = "Statistiken",
["RESTORE_SLOT_TITLE"] = "Wählen Sie den zu ersetzenden Steckplatz aus",
["Rate %@"] = "Bewerte %@",
["Remind me later"] = "Später erinnern",
["SALE_SCREEN_MAP_ROOMS"] = "SALE",
["SECONDS_ABBREVIATION"] = "s",
["SETTINGS_LANGUAGE"] = "Sprache",
["SETTINGS_SUPPORT"] = "Unterstützung",
["SHOP_DESKTOP_GET_DLC_BUTTON"] = "HOL ES DIR",
["SHOP_DESKTOP_TITLE"] = "SHOP",
["SHOP_ROOM_BEST_VALUE_TITLE"] = "BESTER WERT",
["SHOP_ROOM_DLC_1_DESCRIPTION"] = "BEGIEB DICH AUF DIESES NEUE EPISCHE ABENTEUER",
["SHOP_ROOM_DLC_1_TITLE"] = "KOLOSSALE BEDROHUNG KAMPAGNE",
["SHOP_ROOM_DLC_1_TOOLTIP_DESCRIPTION"] = "5 Neue Level\nNeuer Turm\nNeuer Held\nÜber 10 Neue Feinde\n2 Mini Bosskämpfe\nEin epischer Bosskampf\nUnd mehr...",
["SHOP_ROOM_DLC_1_TOOLTIP_TITLE"] = "KOLOSSALE BEDROHUNG KAMPAGNE",
["SHOP_ROOM_DLC_2_DESCRIPTION"] = "PACKEN SIE DIESES NEUE EPISCHE ABENTEUER AN",
["SHOP_ROOM_DLC_2_TITLE"] = "WUKONGS REISE-KAMPAGNE",
["SHOP_ROOM_MOST_POPULAR_TITLE"] = "BELIEBT",
["SLOT_CLOUD_DOWNLOADING"] = "Wird heruntergeladen ...",
["SLOT_CLOUD_DOWNLOAD_FAILED"] = "Fehler beim Laden des Spielstands aus iCloud. Versuche es später erneut.",
["SLOT_CLOUD_DOWNLOAD_SUCCESSFUL"] = "Herunterladen erfolgreich.",
["SLOT_CLOUD_UPLOADING"] = "Wird hochgeladen ...",
["SLOT_CLOUD_UPLOAD_FAILED"] = "Fehler beim Hochladen des Spielstands zu iCloud. Versuche es später erneut.",
["SLOT_CLOUD_UPLOAD_ICLOUD_NOT_CONFIGURED"] = "Dein Gerät wurde nicht für iCloud konfiguriert.",
["SLOT_CLOUD_UPLOAD_SUCCESSFUL"] = "Hochladen erfolgreich.",
["SLOT_DELETE_SLOT"] = "Platz löschen?",
["SLOT_NAME"] = "Platz",
["SLOT_NEW_GAME"] = "NEUES SPIEL",
["SOLDIER_ARBOREAN_BARRACK_NAME"] = "Arboreanischer Soldat",
["SOLDIER_ARBOREAN_SENTINELS_1_NAME"] = "Baluu",
["SOLDIER_ARBOREAN_SENTINELS_2_NAME"] = "Vylla",
["SOLDIER_ARBOREAN_SENTINELS_3_NAME"] = "Ykkon",
["SOLDIER_ARBOREAN_SENTINELS_4_NAME"] = "Haavi",
["SOLDIER_ARBOREAN_SENTINELS_5_NAME"] = "Plook",
["SOLDIER_ARBOREAN_SENTINELS_6_NAME"] = "Guldd",
["SOLDIER_ARBOREAN_SENTINELS_7_NAME"] = "Teena",
["SOLDIER_ARBOREAN_SENTINELS_8_NAME"] = "Uuzky",
["SOLDIER_ARBOREAN_SENTINELS_9_NAME"] = "Deluu",
["SOLDIER_DRAGON_BONE_ULTIMATE_DOG_NAME"] = "Knochendrake",
["SOLDIER_EARTH_HOLDER_NAME"] = "Steinkrieger",
["SOLDIER_GHOST_TOWER_NAME"] = "Gespenst",
["SOLDIER_HERO_BUILDER_WORKER_1_NAME"] = "Hemmar",
["SOLDIER_HERO_BUILDER_WORKER_2_NAME"] = "O'Tool",
["SOLDIER_HERO_BUILDER_WORKER_3_NAME"] = "Crews",
["SOLDIER_HERO_BUILDER_WORKER_4_NAME"] = "Birck",
["SOLDIER_HERO_BUILDER_WORKER_5_NAME"] = "Lauck",
["SOLDIER_HERO_BUILDER_WORKER_6_NAME"] = "O'Nail",
["SOLDIER_HERO_BUILDER_WORKER_7_NAME"] = "Hovels",
["SOLDIER_HERO_BUILDER_WORKER_8_NAME"] = "Woody",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL1_NAME"] = "Wächter der Arborealen",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL2_NAME"] = "Wächter der Arborealen",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL3_NAME"] = "Wächter der Arborealen",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL1_NAME"] = "Paragon der Arborealen",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL2_NAME"] = "Paragon der Arborealen",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL3_NAME"] = "Paragon der Arborealen",
["SOLDIER_HERO_SPIDER_ULTIMATE_NAME"] = "Jungspinne",
["SOLDIER_HERO_WITCH_CAT_1_NAME"] = "Conan",
["SOLDIER_HERO_WITCH_CAT_2_NAME"] = "Alfajor",
["SOLDIER_HERO_WITCH_CAT_3_NAME"] = "Babieca",
["SOLDIER_HERO_WITCH_CAT_4_NAME"] = "Peluche",
["SOLDIER_HERO_WITCH_CAT_5_NAME"] = "Pipa",
["SOLDIER_HERO_WITCH_CAT_6_NAME"] = "Watson",
["SOLDIER_HERO_WITCH_CAT_7_NAME"] = "Chimi",
["SOLDIER_HERO_WITCH_CAT_8_NAME"] = "Pantufla",
["SOLDIER_HERO_WITCH_DECOY_NAME"] = "Ragdoll",
["SOLDIER_HERO_WUKONG_HAIR_CLONES_1_NAME"] = "San Wikung",
["SOLDIER_HERO_WUKONG_HAIR_CLONES_2_NAME"] = "Son Wokeng",
["SOLDIER_ITEM_SUMMON_BLACKBURN_NAME"] = "Lord Blackburn",
["SOLDIER_PALADINS_10_NAME"] = "Sir Joacim",
["SOLDIER_PALADINS_11_NAME"] = "Sir Andre",
["SOLDIER_PALADINS_12_NAME"] = "Sir Sammet",
["SOLDIER_PALADINS_13_NAME"] = "Sir Udo",
["SOLDIER_PALADINS_14_NAME"] = "Sir Eric",
["SOLDIER_PALADINS_15_NAME"] = "Sir Bruce",
["SOLDIER_PALADINS_16_NAME"] = "Sir Rob",
["SOLDIER_PALADINS_17_NAME"] = "Sir Biff",
["SOLDIER_PALADINS_18_NAME"] = "Sir Bowes",
["SOLDIER_PALADINS_1_NAME"] = "Sir Kai",
["SOLDIER_PALADINS_2_NAME"] = "Sir Hansi",
["SOLDIER_PALADINS_3_NAME"] = "Sir Luca",
["SOLDIER_PALADINS_4_NAME"] = "Sir Timo",
["SOLDIER_PALADINS_5_NAME"] = "Sir Ralf",
["SOLDIER_PALADINS_6_NAME"] = "Sir Tobias",
["SOLDIER_PALADINS_7_NAME"] = "Sir Deris",
["SOLDIER_PALADINS_8_NAME"] = "Sir Kiske",
["SOLDIER_PALADINS_9_NAME"] = "Sir Pesch",
["SOLDIER_PRIESTS_BARRACK_1_NAME"] = "Willy",
["SOLDIER_PRIESTS_BARRACK_2_NAME"] = "Henry",
["SOLDIER_PRIESTS_BARRACK_3_NAME"] = "Geoffrey",
["SOLDIER_PRIESTS_BARRACK_4_NAME"] = "Nicholas",
["SOLDIER_PRIESTS_BARRACK_5_NAME"] = "Ed",
["SOLDIER_PRIESTS_BARRACK_6_NAME"] = "Hob",
["SOLDIER_PRIESTS_BARRACK_7_NAME"] = "Odo",
["SOLDIER_PRIESTS_BARRACK_8_NAME"] = "Cedric",
["SOLDIER_PRIESTS_BARRACK_9_NAME"] = "Hal",
["SOLDIER_RANDOM_10_NAME"] = "Alvus",
["SOLDIER_RANDOM_11_NAME"] = "Borin",
["SOLDIER_RANDOM_12_NAME"] = "Hadrian",
["SOLDIER_RANDOM_13_NAME"] = "Thomas",
["SOLDIER_RANDOM_14_NAME"] = "Henry",
["SOLDIER_RANDOM_15_NAME"] = "Bryce",
["SOLDIER_RANDOM_16_NAME"] = "Rulf",
["SOLDIER_RANDOM_17_NAME"] = "Allister",
["SOLDIER_RANDOM_18_NAME"] = "Altair",
["SOLDIER_RANDOM_19_NAME"] = "Simon",
["SOLDIER_RANDOM_1_NAME"] = "Douglas",
["SOLDIER_RANDOM_20_NAME"] = "Egbert",
["SOLDIER_RANDOM_21_NAME"] = "Eldon",
["SOLDIER_RANDOM_22_NAME"] = "Garrett",
["SOLDIER_RANDOM_23_NAME"] = "Godwin",
["SOLDIER_RANDOM_24_NAME"] = "Gordon",
["SOLDIER_RANDOM_25_NAME"] = "Jerald",
["SOLDIER_RANDOM_26_NAME"] = "Kelvin",
["SOLDIER_RANDOM_27_NAME"] = "Lando",
["SOLDIER_RANDOM_28_NAME"] = "Maddox",
["SOLDIER_RANDOM_29_NAME"] = "Peyton",
["SOLDIER_RANDOM_2_NAME"] = "Dan McKill",
["SOLDIER_RANDOM_30_NAME"] = "Ramsey",
["SOLDIER_RANDOM_31_NAME"] = "Raymond",
["SOLDIER_RANDOM_32_NAME"] = "Robert",
["SOLDIER_RANDOM_33_NAME"] = "Sawyer",
["SOLDIER_RANDOM_34_NAME"] = "Silas",
["SOLDIER_RANDOM_35_NAME"] = "Stuart",
["SOLDIER_RANDOM_36_NAME"] = "Tanner",
["SOLDIER_RANDOM_37_NAME"] = "Usher",
["SOLDIER_RANDOM_38_NAME"] = "Wallace",
["SOLDIER_RANDOM_39_NAME"] = "Wesley",
["SOLDIER_RANDOM_3_NAME"] = "James Lee",
["SOLDIER_RANDOM_40_NAME"] = "Willard",
["SOLDIER_RANDOM_4_NAME"] = "Jar Johson",
["SOLDIER_RANDOM_5_NAME"] = "Phil",
["SOLDIER_RANDOM_6_NAME"] = "Robin",
["SOLDIER_RANDOM_7_NAME"] = "William",
["SOLDIER_RANDOM_8_NAME"] = "Martin",
["SOLDIER_RANDOM_9_NAME"] = "Arthur",
["SOLDIER_REINFORCEMENTS_F_1_NAME"] = "Ataina",
["SOLDIER_REINFORCEMENTS_F_2_NAME"] = "Maucil",
["SOLDIER_REINFORCEMENTS_F_3_NAME"] = "Gulica",
["SOLDIER_REINFORCEMENTS_F_4_NAME"] = "Rogas",
["SOLDIER_REINFORCEMENTS_M_10_NAME"] = "Podgie",
["SOLDIER_REINFORCEMENTS_M_1_NAME"] = "Gabini",
["SOLDIER_REINFORCEMENTS_M_2_NAME"] = "O'Bell",
["SOLDIER_REINFORCEMENTS_M_3_NAME"] = "Kent",
["SOLDIER_REINFORCEMENTS_M_4_NAME"] = "Jendars",
["SOLDIER_REINFORCEMENTS_M_5_NAME"] = "Jarlosc",
["SOLDIER_REINFORCEMENTS_M_6_NAME"] = "Astong",
["SOLDIER_REINFORCEMENTS_M_7_NAME"] = "Buigell",
["SOLDIER_REINFORCEMENTS_M_8_NAME"] = "Clane",
["SOLDIER_REINFORCEMENTS_M_9_NAME"] = "Magus",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_1_NAME"] = "Dench",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_2_NAME"] = "Smith",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_3_NAME"] = "Andrews",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_4_NAME"] = "Thompson",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_5_NAME"] = "Taylor",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_1_NAME"] = "McCartney",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_2_NAME"] = "McKellen",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_3_NAME"] = "Hopkins",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_4_NAME"] = "Caine",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_5_NAME"] = "Kingsley",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_10_NAME"] = "Viper",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_1_NAME"] = "Fang",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_2_NAME"] = "Blade",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_3_NAME"] = "Claw",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_4_NAME"] = "Talon",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_5_NAME"] = "Edgee",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_6_NAME"] = "Shiv",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_7_NAME"] = "Sense",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_8_NAME"] = "Dolch",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_9_NAME"] = "Stich",
["SOLDIER_REINFORCEMENTS_SPECIAL_DARK_ARMY_1_NAME"] = "Schattenkrähenrufer",
["SOLDIER_REINFORCEMENTS_SPECIAL_LINIREA_1_NAME"] = "Knight Paragon",
["SOLDIER_STAGE_10_YMCA_BIKER_NAME"] = "Glenn",
["SOLDIER_STAGE_10_YMCA_CONSTRUCTOR_NAME"] = "David",
["SOLDIER_STAGE_10_YMCA_INDIO_NAME"] = "Philipp",
["SOLDIER_STAGE_10_YMCA_POLICIA_NAME"] = "Victor",
["SOLDIER_STAGE_15_DENAS_NAME"] = "König Denas",
["SOLDIER_TOWER_DARK_ELF_1_NAME"] = "Filraen",
["SOLDIER_TOWER_DARK_ELF_2_NAME"] = "Faeryl",
["SOLDIER_TOWER_DARK_ELF_3_NAME"] = "Gurina",
["SOLDIER_TOWER_DARK_ELF_4_NAME"] = "Jhalass",
["SOLDIER_TOWER_DARK_ELF_5_NAME"] = "Solenzar",
["SOLDIER_TOWER_DARK_ELF_6_NAME"] = "Tebryn",
["SOLDIER_TOWER_DARK_ELF_7_NAME"] = "Vierna",
["SOLDIER_TOWER_DARK_ELF_8_NAME"] = "Zyn",
["SOLDIER_TOWER_DARK_ELF_9_NAME"] = "Elerra",
["SOLDIER_TOWER_DWARF_10_NAME"] = "Babbi",
["SOLDIER_TOWER_DWARF_1_NAME"] = "Pippi",
["SOLDIER_TOWER_DWARF_2_NAME"] = "Ginni",
["SOLDIER_TOWER_DWARF_3_NAME"] = "Merri",
["SOLDIER_TOWER_DWARF_4_NAME"] = "Lorri",
["SOLDIER_TOWER_DWARF_5_NAME"] = "Talli",
["SOLDIER_TOWER_DWARF_6_NAME"] = "Danni",
["SOLDIER_TOWER_DWARF_7_NAME"] = "Getti",
["SOLDIER_TOWER_DWARF_8_NAME"] = "Daffi",
["SOLDIER_TOWER_DWARF_9_NAME"] = "Bibbi",
["SOLDIER_TOWER_ELVEN_BARRACK_1_NAME"] = "Elandil",
["SOLDIER_TOWER_ELVEN_BARRACK_2_NAME"] = "Puck",
["SOLDIER_TOWER_ELVEN_BARRACK_3_NAME"] = "Thas",
["SOLDIER_TOWER_ELVEN_BARRACK_4_NAME"] = "Kastore",
["SOLDIER_TOWER_ELVEN_BARRACK_5_NAME"] = "Elric",
["SOLDIER_TOWER_ELVEN_BARRACK_6_NAME"] = "Elaith",
["SOLDIER_TOWER_NECROMANCER_SKELETON_GOLEM_NAME"] = "Knochengolem",
["SOLDIER_TOWER_NECROMANCER_SKELETON_NAME"] = "Skelett",
["SOLDIER_TOWER_PANDAS_FEMALE_1_NAME"] = "Yan",
["SOLDIER_TOWER_PANDAS_FEMALE_2_NAME"] = "Qingzhao",
["SOLDIER_TOWER_PANDAS_FEMALE_3_NAME"] = "Hui",
["SOLDIER_TOWER_PANDAS_FEMALE_4_NAME"] = "Ailing",
["SOLDIER_TOWER_PANDAS_MALE_1_NAME"] = "Tzu",
["SOLDIER_TOWER_PANDAS_MALE_2_NAME"] = "Qian",
["SOLDIER_TOWER_PANDAS_MALE_3_NAME"] = "Xueqin",
["SOLDIER_TOWER_PANDAS_MALE_4_NAME"] = "Nai'an",
["SOLDIER_TOWER_PANDAS_MALE_5_NAME"] = "Xun",
["SOLDIER_TOWER_PANDAS_MALE_6_NAME"] = "Xingjian",
["SOLDIER_TOWER_PANDAS_MALE_7_NAME"] = "Wei",
["SOLDIER_TOWER_PANDAS_MALE_8_NAME"] = "Chen",
["SOLDIER_TOWER_ROCKET_GUNNERS_10_NAME"] = "Fortus",
["SOLDIER_TOWER_ROCKET_GUNNERS_1_NAME"] = "Axl",
["SOLDIER_TOWER_ROCKET_GUNNERS_2_NAME"] = "Rose",
["SOLDIER_TOWER_ROCKET_GUNNERS_3_NAME"] = "Slash",
["SOLDIER_TOWER_ROCKET_GUNNERS_4_NAME"] = "Hudson",
["SOLDIER_TOWER_ROCKET_GUNNERS_5_NAME"] = "Izzy",
["SOLDIER_TOWER_ROCKET_GUNNERS_6_NAME"] = "Duff",
["SOLDIER_TOWER_ROCKET_GUNNERS_7_NAME"] = "Adler",
["SOLDIER_TOWER_ROCKET_GUNNERS_8_NAME"] = "Dizzy",
["SOLDIER_TOWER_ROCKET_GUNNERS_9_NAME"] = "Ferrer",
["SOLDIER_ZHU_APPRENTICE_NAME"] = "Zhu Bajie",
["SPECIAL_ARBOREAN_BARRACK_DESCRIPTION"] = "Beschwört 3 arborische Soldaten, die Feinde auf ihrem Weg bekämpfen.",
["SPECIAL_ARBOREAN_BARRACK_NAME"] = "Arborean Einwohner",
["SPECIAL_ARBOREAN_HONEY_DESCRIPTION"] = "Der Imker nimmt seinen Posten ein und befehligt seine Bienen, Gegner mit klebrigem Honig zu verlangsamen und zu schädigen !",
["SPECIAL_ARBOREAN_HONEY_NAME"] = "Arborean Bienenzüchter",
["SPECIAL_ARBOREAN_OLDTREE_DESCRIPTION"] = "Der mürrische Kerl entfesselt einen riesigen rollenden Baumstamm, der Feinde auf seinem Weg zermalmt.",
["SPECIAL_ARBOREAN_OLDTREE_NAME"] = "Alter Baum",
["SPECIAL_ARBOREAN_SENTINELS_SPEARMEN_DESCRIPTION"] = "Flinke Beschützer des Waldes.",
["SPECIAL_ARBOREAN_SENTINELS_SPEARMEN_NAME"] = "Arborean Dornenspeer",
["SPECIAL_PRIESTS_SOLDIERS_DESCRIPTION"] = "Erlöste Kultisten die sich in Abscheulichkeiten verwandeln wenn sie sterben.",
["SPECIAL_PRIESTS_SOLDIERS_NAME"] = "Blinde Kultisten",
["SPECIAL_REPAIR_HOLDER_DRAGON_DESCRIPTION"] = "Löscht die Flammen, um den Turm sofort zu befreien.",
["SPECIAL_REPAIR_HOLDER_DRAGON_NAME"] = "Von Flammen verschlungen",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_EARTH_DESCRIPTION"] = "Erhöht die Gesundheit der Einheiten des Turms.\nBeschwört bis zu 3 Stein-Krieger.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_EARTH_NAME"] = "Elemental Holder: Earth",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_FIRE_DESCRIPTION"] = "Erhöht den Schaden des errichteten Turms.\nTötet gelegentlich einen Feind sofort.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_FIRE_NAME"] = "Elemental Holder: Fire",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_METAL_DESCRIPTION"] = "Verringert die Baukosten.\nErzeugt Gold aus Feinden.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_METAL_NAME"] = "Elemental Holder: Metal",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WATER_DESCRIPTION"] = "Heilt ständig nahe befreundete Einheiten.\nTeleportiert Feinde zurück entlang des Pfades.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WATER_NAME"] = "Elemental Holder: Water",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WOOD_DESCRIPTION"] = "Erhöht die Reichweite des errichteten Turms.\nErzeugt gelegentlich kurz verweilende Wurzeln, die Feinde verlangsamen.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WOOD_NAME"] = "Elemental Holder: Wood",
["SPECIAL_REPAIR_HOLDER_SEA_OF_TREES_DESCRIPTION"] = "Räumen Sie den Schutt weg, um diese strategische Position freizugeben. ",
["SPECIAL_REPAIR_HOLDER_SEA_OF_TREES_NAME"] = "Schutt ",
["SPECIAL_REPAIR_HOLDER_SPIDERS_DESCRIPTION"] = "Befreie den Halter aus den Netzen, um diesen strategischen Punkt zu aktivieren.",
["SPECIAL_REPAIR_HOLDER_SPIDERS_NAME"] = "Eingesponnener Halter",
["SPECIAL_REPAIR_OVERSEER_DESCRIPTION"] = "Wehre die Tentakel ab, um diese strategische Position freizuschalten.",
["SPECIAL_REPAIR_OVERSEER_NAME"] = "Tentakel",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_1_DESCRIPTION"] = "Heuere einen Elfen-Söldner an, um im Kampf zu helfen. Er erscheint alle 10 Sekunden neu.",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_1_NAME"] = "Elfen-Söldner I",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_2_DESCRIPTION"] = "Heuere bis zu 2 Elfen-Söldner an, um im Kampf zu helfen. Sie erscheinen alle 10 Sekunden neu.",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_2_NAME"] = "Elfen-Söldner II",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_3_DESCRIPTION"] = "Heuere bis zu 3 Elfen-Söldner an, um im Kampf zu helfen. Sie erscheinen alle 10 Sekunden neu.",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_3_NAME"] = "Elfen-Söldner III",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_1"] = "Wirft Blitze von Magie, die Mydrias' Illusionen zerstören und verhindern, dass sie einige Sekunden lang weitere erschafft.",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_2"] = "Beschwört 2 Dämonenwächter, die den Weg entlanglaufen und gegen Feinde kämpfen.",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_3"] = "Fängt Denas, verhindert seine Bewegung und Angriff.",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_1"] = "Seeleneinschlag",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_2"] = "Höllische Brut",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_3"] = "Magische Fesseln",
["START BATTLE!"] = "KAMPF BEGINNEN!",
["START HERE!"] = "HIER BEGINNEN!",
["STRATEGY BASICS!"] = "STRATEGIEGRUNDLAGEN!",
["Select by tapping on the portrait or hero unit."] = "Auswählen durch Antippen des Porträts oder des Helden",
["Sell Tower"] = "Turm verkaufen",
["Sell this tower and get a %s GP refund."] = "Verkaufe diesen Turm und du bekommst %s Gold erstattet.",
["Shows level, health and experience."] = "Zeigt Level, Gesundheit und Erfahrung.",
["Special abilities"] = "Spezialfähigkeiten",
["Support your soldiers with ranged towers!"] = "Unterstütze deine Soldaten mit Fernangriff- Türmen!",
["Survival mode!"] = "Überleben-Modus!",
["TAP_TO_START"] = "Tippen, um zu starten",
["TAUNT_BOSS_PIG_FROM_POOL_0001"] = "Ich werde dich quieken lassen!",
["TAUNT_BOSS_PIG_FROM_POOL_0002"] = "Sag nochmal 'Bacon'. Ich fordere dich doppelt heraus!",
["TAUNT_BOSS_PIG_FROM_POOL_0003"] = "Menschen stehen wieder auf der Speisekarte, Jungs!",
["TAUNT_BOSS_PIG_FROM_POOL_0004"] = "Beeil dich! Ich habe Hunger.",
["TAUNT_BOSS_PIG_FROM_POOL_0005"] = "Ich werde es genießen, dich sterben zu sehen.",
["TAUNT_BOSS_PIG_FROM_POOL_0006"] = "Ich weiß, ich bin die Wurst.",
["TAUNT_LVL30_BOSS_ABILITY_01"] = "Labt euch, meine Kinder!",
["TAUNT_LVL30_BOSS_ABILITY_02"] = "Halte durch! MWAHAHAHA!",
["TAUNT_LVL30_BOSS_ABILITY_03"] = "Für den Kult!",
["TAUNT_LVL30_BOSS_ABILITY_04"] = "Leckere Mahlzeiten für alle!",
["TAUNT_LVL30_BOSS_ABILITY_05"] = "Mein Spinnensinn kribbelt!",
["TAUNT_LVL30_BOSS_ABILITY_06"] = "Knie nieder vor mir, Allianz!",
["TAUNT_LVL30_BOSS_ABILITY_07"] = "Mein Haus, meine Regeln!",
["TAUNT_LVL30_BOSS_ABILITY_08"] = "Niemand entkommt meinem Netz!",
["TAUNT_LVL30_BOSS_ABILITY_09"] = "Stirb, humanoide Plage!",
["TAUNT_LVL30_BOSS_ABILITY_10"] = "Ich ziehe eure Fäden!",
["TAUNT_LVL30_BOSS_ABILITY_11"] = "Tötet sie alle!",
["TAUNT_LVL30_BOSS_INTRO_01"] = "Endlich! Die Mörderer meiner Schwestern zeigen ihre Gesichter...",
["TAUNT_LVL30_BOSS_INTRO_02"] = "Zur Erinnerung an meine Geschwister Sarelgaz und Mactans...",
["TAUNT_LVL30_BOSS_INTRO_03"] = "Werded ihr auf euren Knien enden, um mich anzubeten!",
["TAUNT_LVL30_BOSS_PREFIGHT_01"] = "Genug davon...",
["TAUNT_LVL30_BOSS_PREFIGHT_02"] = "Ihr seid nichts als unbedeutende Insekten...",
["TAUNT_LVL30_BOSS_PREFIGHT_03"] = "Gefangen im Netz der Königin!",
["TAUNT_LVL32_BOSS_ABILITY_01"] = "Narren! Ich führe die göttliche Flamme, das Samadhi-Feuer!",
["TAUNT_LVL32_BOSS_ABILITY_02"] = "Flammende Feuer brechen vom Himmel hervor!",
["TAUNT_LVL32_BOSS_ABILITY_03"] = "Fürchtet das wahre Feuer in seiner reinsten Form!",
["TAUNT_LVL32_BOSS_ABILITY_04"] = "Fleisch und Seelen verbrennen gleichermaßen!",
["TAUNT_LVL32_BOSS_FIGHT_01"] = "Das Feuer in mir wird niemals sterben!",
["TAUNT_LVL32_BOSS_FINAL_01"] = "Meine Flamme erlischt...\naber ich habe noch meinen Drachen...",
["TAUNT_LVL32_BOSS_INTRO_01"] = "Du hast also eine Armee?",
["TAUNT_LVL32_BOSS_INTRO_02"] = "Ich habe einen Drachen! Ha ha ha ha!",
["TAUNT_LVL32_BOSS_PREFIGHT_01"] = "Genug! Jetzt kommt mein Sieg!",
["TAUNT_LVL32_BOSS_PREFIGHT_02"] = "Bewundert meine wahre Gestalt!",
["TAUNT_LVL34_BOSS_BOSSFIGHT_01"] = "Na gut, ich weiß genau, was wir brauchen. Mehr von mir. Ich, ich, ich...",
["TAUNT_LVL34_BOSS_DEATH_01"] = "Das kann nicht sein … Egal, mein Ehemann wird euch dafür bezahlen lassen …",
["TAUNT_LVL34_BOSS_INTRO_01"] = "Ihr Affen! Ihr wagt es, hierher zu kommen nach dem, was ihr meinem Sohn angetan habt?",
["TAUNT_LVL34_BOSS_WAVES_01"] = "Kostet meine Macht, ihr unverschämten Narren!",
["TAUNT_LVL34_BOSS_WAVES_02"] = "Das Ende ist nah!",
["TAUNT_LVL35_BOSS_DEATH_01"] = "Und so endet meine Herrschaft... in Blut.",
["TAUNT_LVL35_BOSS_DESTROY_HOUSE_01"] = "Mmm, das war teuer. Zeit für etwas Feuerkraft!",
["TAUNT_LVL35_BOSS_DESTROY_HOUSE_02"] = "Ah, der Klang der Ausdauer. Zeit fürs Wasser, gnädige Frau!",
["TAUNT_LVL35_BOSS_DESTROY_HOUSE_03"] = "Grrr! Ergib dich meiner vulgären Machtdemonstration!",
["TAUNT_LVL35_BOSS_INTRO_01"] = "Erbärmliche Menschen, freut euch, solange ihr noch unter den Lebenden weilt.",
["TAUNT_LVL35_BOSS_INTRO_02"] = "Es ist Zeit für die neue Ordnung.",
["TAUNT_LVL35_BOSS_INTRO_03"] = "Arrgh, ich schreie nach Rache!",
["TAUNT_LVL35_BOSS_PREFIGHT_01"] = "Na gut, dann zeige ich dir, warum TÖTEN mein Geschäft ist!",
["TAUNT_STAGE02_RAELYN_0001"] = "Lass uns das machen.",
["TAUNT_STAGE02_VEZNAN_0001"] = "Hier kommen sie. Ich werde euren winzigen Streitkräften helfen...",
["TAUNT_STAGE02_VEZNAN_0002"] = "...ich meine, eine meiner besten Soldaten wird es tun. HA!",
["TAUNT_STAGE02_VEZNAN_0003"] = "HA HA HA!",
["TAUNT_STAGE06_BOSS_PIG_PREBATTLE_0001"] = "Gut... Ich werde es selbst machen.",
["TAUNT_STAGE06_BOSS_PIG_RESPONSE_0001"] = "Entspann dich, alles ist unter Kontrolle.",
["TAUNT_STAGE06_CULTIST_GREETING_0001"] = "Ich sehe, du fühlst dich dort sehr wohl...",
["TAUNT_STAGE06_CULTIST_GREETING_0002"] = "...du solltest besser deinen Teil der Abmachung erfüllen.",
["TAUNT_STAGE11_CULTIST_LEADER_0001"] = "Es ist gut, dass du es so weit geschafft hast...",
["TAUNT_STAGE11_CULTIST_LEADER_0002"] = "...aber du kannst das Unvermeidliche nicht aufhalten!",
["TAUNT_STAGE11_CULTIST_LEADER_0003"] = "GENUG!!!",
["TAUNT_STAGE11_CULTIST_LEADER_0004"] = "Es ist Zeit, dass du dich vor uns VERNEIGST!",
["TAUNT_STAGE11_CULTIST_LEADER_0005"] = "Grrr... das ist nicht das Ende!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0001"] = "Eine neue Welt erwartet uns.",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0002"] = "Du unterschätzt meine Macht.",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0003"] = "Oculus Poculus!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0004"] = "Hör den Klang der Unvermeidbarkeit!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0005"] = "Bin ich böse? Ja, das bin ich!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0006"] = "Der Allsehende segnet uns!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0001"] = "Dein Ende naht!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0002"] = "Meine Augen wurden geöffnet!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0003"] = "Sag „Hallo“ zu meinen Freunden aus dem Nichts!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0004"] = "Oculus Poculus!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0005"] = "Erbärmlicher schwacher Abschaum!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0006"] = "Der Allsehende segnet uns!",
["TAUNT_STAGE11_VEZNAN_0001"] = "Denas, mein Freund. Lange nicht gesehen!",
["TAUNT_STAGE15_CULTIST_0001"] = "Es ist nah... Ich kann fühlen, wie es erwacht!",
["TAUNT_STAGE15_CULTIST_0002"] = "Eine neue Ära naht. Eure Anstrengungen werden vergeblich sein!",
["TAUNT_STAGE15_CULTIST_0003"] = "Grrr... eure Allianz ist mächtig.",
["TAUNT_STAGE15_CULTIST_0004"] = "Aber ich werde euch zeigen, was wahre Macht ist!",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0001"] = "Narren! Ihr seid gekommen, um zu sterben. ",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0002"] = "Ergebe dich seinem Blick!",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0003"] = "Du wirst ein wahrer Gläubiger werden.",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0004"] = "Bündnis oder nicht, du bist verdammt!",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0005"] = "Im Nichts gibt es kein Leben. Nur Tod",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0006"] = "Hör auf, meine Zeit zu verschwenden!",
["TAUNT_STAGE15_DENAS_0001"] = "Ich habe eine offene Rechnung zu begleichen. Diesen Kampf werde ich nicht verpassen!",
["TAUNT_STAGE16_DENAS_AFTER_BOSSFIGHT_0001"] = "Hast du nicht kommen sehen, oder?",
["TAUNT_STAGE18_ERIDAN_FIGHT_0001"] = "Heute Nacht wurde Blut vergossen.",
["TAUNT_STAGE18_ERIDAN_FIGHT_0002"] = "Elynie vertrauen wir.",
["TAUNT_STAGE18_ERIDAN_FIGHT_0003"] = "Gnillur speek Edihnori!",
["TAUNT_STAGE18_ERIDAN_FIGHT_0004"] = "Ich kann einfach nicht verfehlen.",
["TAUNT_STAGE18_ERIDAN_FIGHT_0005"] = "Aredhel wird siegen!",
["TAUNT_STAGE18_ERIDAN_FIGHT_0006"] = "Das sind keine gewöhnlichen Waldläufer!",
["TAUNT_STAGE18_ERIDAN_FIGHT_0007"] = "Zählst du die Treffer?",
["TAUNT_STAGE18_ERIDAN_FIGHT_0008"] = "Lass sie kommen!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0001"] = "Du hast meinen Bogen!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0002"] = "Handle schnell!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0003"] = "Auf eure Positionen!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0004"] = "Haltet eure Augen offen!",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0001"] = "Das reicht für das Aufwärmen!",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0002"] = "Du hast dich als lästige Störung erwiesen...",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0003"] = "Lasst den echten Kampf beginnen!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0001"] = "Ich bändige alle Seelen!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0002"] = "Die Elfen werden wieder auferstehen.",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0003"] = "Ich hebe sogar... die Toten!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0004"] = "Durch die uralten, unheiligen Kräfte!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0005"] = "Fürchtet meine Kinder des Grabes!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0006"] = "Ich werde meinem Volk den Ruhm zurückbringen.",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0001"] = "Ah, die mächtige Allianz ist gekommen, um uns zu besuchen.",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0002"] = "Es ist Zeit, den Schleier zu lüften!",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0003"] = "Lass mich dir die Macht des Todes zeigen!",
["TAUNT_STAGE22_BOSS_CROCS_BEFORE_BOSSFIGHT_0001"] = "Endlich frei um alles zu ...",
["TAUNT_STAGE22_BOSS_CROCS_BEFORE_BOSSFIGHT_0002"] = "VERSCHLINGEN!!!!!",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0001"] = "Genug Einmischung!",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0002"] = "Grymbeard wird euch Manieren beibringen.",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0003"] = "Alle an Bord, AHAHAHA!",
["TAUNT_STAGE25_BOSS_MACHINIST_END_0001"] = "Ihr unverschämten Dummköpfe!",
["TAUNT_STAGE25_BOSS_MACHINIST_END_0002"] = "Ihr werdet mich niemals fangen, HAHAHA!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0001"] = "Nein! Da ist noch mehr-...",
["TAUNT_STAGE26_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0002"] = "HEILIGES KANONENROHR!!!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0001"] = "Ihr habt keine Chance gegen diese Armee!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0002"] = "Grymbeard ist nicht in Gefahr.",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0003"] = "Grymbeard IST die Gefahr!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0004"] = "Könnte ein Wahnsinniger das hier schaffen?",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0005"] = "Die Welt wird sich vor Grymbeard verneigen!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0001"] = "Grymbeards Geduld ist am Ende.",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0002"] = "Ihr werdet etwas wirklich Ernstes sehen!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0003"] = "Grymbeard braucht niemanden außer sich selbst!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0004"] = "Beeilt euch doch mal?!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0001"] = "Ihr und eure verdammte neugierige Allianz!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0002"] = "Ich werde euch lehren, euch nicht mit...",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0003"] = "...dem HAUPT-Zwerg anzulegen!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0001"] = "Zerstört so viele Replikate, wie ihr wollt, ich mache einfach mehr.",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0002"] = "Wenn man will, dass etwas richtig gemacht wird, muss man es selbst tun.",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0003"] = "Oh Grymbeard, du Genie!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0004"] = "Damit kommst du nicht durch!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0005"] = "Versucht ihr es überhaupt?",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0006"] = "Glaubt ihr, ihr könnt meine Kreationen übertreffen?",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0001"] = "Ich schätze, ihr konntet einfach nicht genug von mir bekommen...",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0002"] = "...und jetzt wollt ihr es mit dem PRIMÄREN Zwerg aufnehmen?",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0003"] = "Ihr seid herzlich eingeladen, es zu versuchen.",
["TAUNT_TUTORIAL_ARBOREAN_ALL_0001"] = "Mach weiter! Wir glauben an dich.",
["TAUNT_TUTORIAL_ARBOREAN_BARRACK_0001"] = "Hier, baue eine Kaserne!",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_1_NAME"] = "Tentakel Willz",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_2_NAME"] = "Tentakel Henry",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_3_NAME"] = "Tentakel Geoffrey",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_4_NAME"] = "Tentakel Nicholas",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_5_NAME"] = "Tentakel Ed",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_6_NAME"] = "Tentakel Hob",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_7_NAME"] = "Tentakel Odo",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_8_NAME"] = "Tentakel Cedric",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_9_NAME"] = "Tentakel Hal",
["TERMS_OF_SERVICE_LINK"] = "Nutzungsbedingungen",
["TIP_TITLE"] = "Tipp:",
["TOWER_ARBOREAN_EMISSARY_1_DESCRIPTION"] = "Die Arboreans machen ihre Feinde durch mächtige Naturmagie verwundbarer",
["TOWER_ARBOREAN_EMISSARY_1_NAME"] = "Arborean Botschafter I",
["TOWER_ARBOREAN_EMISSARY_2_DESCRIPTION"] = "Die Arboreans machen ihre Feinde durch mächtige Naturmagie verwundbarer. ",
["TOWER_ARBOREAN_EMISSARY_2_NAME"] = "Arborean Botschafter II",
["TOWER_ARBOREAN_EMISSARY_3_DESCRIPTION"] = "Die Arboreans machen ihre Feinde durch mächtige Naturmagie verwundbarer. ",
["TOWER_ARBOREAN_EMISSARY_3_NAME"] = "Arborean Botschafter III",
["TOWER_ARBOREAN_EMISSARY_4_DESCRIPTION"] = "Die Arboreans machen ihre Feinde mit mächtiger Naturmagie verwundbarer",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_1_DESCRIPTION"] = "Beschwört Wisp, die %$towers.arborean_emissary.gift_of_nature.s_heal[1]%$ Gesundheit pro Sekunde für %$towers.arborean_emissary.gift_of_nature.duration[1]%$ Sekunden an Verbündete in einem Bereich heilen",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_1_NAME"] = "GABE DER NATUR",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_2_DESCRIPTION"] = "Beschwört Wisp, die %$towers.arborean_emissary.gift_of_nature.s_heal[2]%$ Gesundheit für %$towers.arborean_emissary.gift_of_nature.duration[2]%$ Sekunden an Verbündete in einem Bereich heilen.",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_2_NAME"] = "GABE DER NATUR",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_3_DESCRIPTION"] = "Beschwört Wisp, die %$towers.arborean_emissary.gift_of_nature.s_heal[3]%$ Gesundheit für %$towers.arborean_emissary.gift_of_nature.duration[3]%$ Sekunden an Verbündete in einem Bereich heilen.",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_3_NAME"] = "GABE DER NATUR",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_NAME"] = "GABE DER NATUR",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_NOTE"] = "Mische dich niemals mit dem Grünen ein.",
["TOWER_ARBOREAN_EMISSARY_4_NAME"] = "Arborean Botschafter IV",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_1_DESCRIPTION"] = "Lässt %$towers.arborean_emissary.wave_of_roots.max_targets[1]%$ Wurzeln entlang des Weges wachsen, verursacht %$towers.arborean_emissary.wave_of_roots.s_damage[1]%$ echten Schaden und betäubt Feinde für %$towers.arborean_emissary.wave_of_roots.mod_duration[1]%$ Sekunden.",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_1_NAME"] = "DORNENGRIFF",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_2_DESCRIPTION"] = "Lässt %$towers.arborean_emissary.wave_of_roots.max_targets[2]%$ Wurzeln entlang des Weges wachsen, die %$towers.arborean_emissary.wave_of_roots.s_damage[2]%$ echten Schaden zufügen und Feinde für %$towers.arborean_emissary.wave_of_roots.mod_duration[2]%$ Sekunden betäuben.",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_2_NAME"] = "DORNENGRIFF",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_3_DESCRIPTION"] = "Lässt %$towers.arborean_emissary.wave_of_roots.max_targets[3]%$ Wurzeln entlang des Weges wachsen, die %$towers.arborean_emissary.wave_of_roots.s_damage[3]%$ echten Schaden zufügen und Feinde für %$towers.arborean_emissary.wave_of_roots.mod_duration[3]%$ Sekunden betäuben.",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_3_NAME"] = "DORNENGRIFF",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_NAME"] = "DORNENGRIFF",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_NOTE"] = "Achte auf deinen Schritt.",
["TOWER_ARBOREAN_EMISSARY_DESC"] = "Wenn sie provoziert werden, ist bekannt, dass die Arboreans ihre Magie verwenden, um ihre Feinde zu markieren und zu schwächen.",
["TOWER_ARBOREAN_EMISSARY_NAME"] = "Arborean Botschafter",
["TOWER_ARBOREAN_SENTINELS_DESCRIPTION"] = "Flinke Beschützer des Waldes.",
["TOWER_ARBOREAN_SENTINELS_NAME"] = "Arborean Dornenspeere",
["TOWER_ARCANE_WIZARD_1_DESCRIPTION"] = "In den Künsten der Magie wohl bewandert, sind diese Zauberer immer bereit für einen Kampf. ",
["TOWER_ARCANE_WIZARD_1_NAME"] = "Arkaner Zauberer I ",
["TOWER_ARCANE_WIZARD_2_DESCRIPTION"] = "In den Künsten der Magie wohl bewandert, sind diese Zauberer immer bereit für einen Kampf. ",
["TOWER_ARCANE_WIZARD_2_NAME"] = "Arkaner Zauberer II ",
["TOWER_ARCANE_WIZARD_3_DESCRIPTION"] = "In den Künsten der Magie wohl bewandert, sind diese Zauberer immer bereit für einen Kampf. ",
["TOWER_ARCANE_WIZARD_3_NAME"] = "Arkaner Zauberer III ",
["TOWER_ARCANE_WIZARD_4_DESCRIPTION"] = "In den Künsten der Magie wohl bewandert, sind diese Zauberer immer bereit für einen Kampf. ",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_1_DESCRIPTION"] = "Wirft einen Strahl aus, der das Ziel sofort tötet. Bosse und Mini-Bosse erhalten stattdessen %$towers.arcane_wizard.disintegrate.boss_damage[1]%$ magischen Schaden.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_1_NAME"] = "DESINTEGRIEREN ",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_2_DESCRIPTION"] = "Reduziert seine Abkühlzeit auf %$towers.arcane_wizard.disintegrate.cooldown[2]%$ Sekunden. Schaden an Bossen und Mini-Bossen beträgt jetzt %$towers.arcane_wizard.disintegrate.boss_damage[2]%$.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_2_NAME"] = "DESINTEGRIEREN ",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_3_DESCRIPTION"] = "Reduziert seine Abkühlzeit auf %$towers.arcane_wizard.disintegrate.cooldown[3]%$ Sekunden. Der Schaden an Bossen und Mini-Bossen beträgt nun %$towers.arcane_wizard.disintegrate.boss_damage[3]%$.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_3_NAME"] = "DESINTEGRIEREN ",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_NAME"] = "DESINTEGRIEREN ",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_NOTE"] = "Staub zu Staub. ",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_1_DESCRIPTION"] = "Erhöht den Schaden von benachbarter Türme um %$towers.arcane_wizard.empowerment.s_damage_factor[1]%$%.",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_1_NAME"] = "Stärkung",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_2_DESCRIPTION"] = "Erhöht den Schaden von benachbarten Türmen um %$towers.arcane_wizard.empowerment.s_damage_factor[2]%$%.",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_2_NAME"] = "Stärkung",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_3_DESCRIPTION"] = "Erhöht den Schaden von benachbarten Türmen um %$towers.arcane_wizard.empowerment.s_damage_factor[3]%$%.",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_3_NAME"] = "Stärkung",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_NAME"] = "Stärkung",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_NOTE"] = "Unbegrenzte Macht.",
["TOWER_ARCANE_WIZARD_4_NAME"] = "Arkaner Zauberer IV ",
["TOWER_ARCANE_WIZARD_DESC"] = "Durch das Anzapfen reiner Magie, verfügen Linireanische Zauberer über genug Macht, um ihre Feinde völlig zu zerstören. ",
["TOWER_ARCANE_WIZARD_NAME"] = "Arkaner Zauberer ",
["TOWER_BALLISTA_1_DESCRIPTION"] = "Eine großartige Ergänzung zur Kriegsführung der Grünhäute, es ist ein Wunder, dass sie noch nicht auseinandergefallen ist.",
["TOWER_BALLISTA_1_NAME"] = "Ballistenaußenposten I",
["TOWER_BALLISTA_2_DESCRIPTION"] = "Eine großartige Ergänzung zur Kriegsführung der Grünhäute, es ist ein Wunder, dass sie noch nicht auseinandergefallen ist.",
["TOWER_BALLISTA_2_NAME"] = "Ballistenaußenposten II",
["TOWER_BALLISTA_3_DESCRIPTION"] = "Eine großartige Ergänzung zur Kriegsführung der Grünhäute, es ist ein Wunder, dass sie noch nicht auseinandergefallen ist.",
["TOWER_BALLISTA_3_NAME"] = "Ballistenaußenposten III",
["TOWER_BALLISTA_4_DESCRIPTION"] = "Eine großartige Ergänzung zur Kriegsführung der Grünhäute, es ist ein Wunder, dass sie noch nicht auseinandergefallen ist.",
["TOWER_BALLISTA_4_NAME"] = "Ballistenaußenposten IV",
["TOWER_BALLISTA_4_SKILL_BOMB_1_DESCRIPTION"] = "Feuert eine Schrottbombe über große Entfernung ab, die %$towers.ballista.skill_bomb.damage_min[1]%$-%$towers.ballista.skill_bomb.damage_max[1]%$ physischen Schaden verursacht und Trümmer hinterlässt, die Feinde für %$towers.ballista.skill_bomb.duration[1]%$ Sekunden verlangsamen.",
["TOWER_BALLISTA_4_SKILL_BOMB_1_NAME"] = "SCHROTTBOMBE",
["TOWER_BALLISTA_4_SKILL_BOMB_2_DESCRIPTION"] = "Die Schrottbombe verursacht %$towers.ballista.skill_bomb.damage_min[2]%$-%$towers.ballista.skill_bomb.damage_max[2]%$ physischen Schaden. Sie verlangsamt Gegner für %$towers.ballista.skill_bomb.duration[1]%$ Sekunden.",
["TOWER_BALLISTA_4_SKILL_BOMB_2_NAME"] = "SCHROTTBOMBE",
["TOWER_BALLISTA_4_SKILL_BOMB_3_DESCRIPTION"] = "Die Schrottbombe verursacht %$towers.ballista.skill_bomb.damage_min[3]%$-%$towers.ballista.skill_bomb.damage_max[3]%$ physischen Schaden. Sie verlangsamt Gegner für %$towers.ballista.skill_bomb.duration[1]%$ Sekunden.",
["TOWER_BALLISTA_4_SKILL_BOMB_3_NAME"] = "SCHROTTBOMBE",
["TOWER_BALLISTA_4_SKILL_BOMB_NOTE"] = "Vorsicht!",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_1_DESCRIPTION"] = "Der letzte Schuss des Turms verursacht %$towers.ballista.skill_final_shot.s_damage_factor[1]%$% mehr Schaden und betäubt das Ziel für %$towers.ballista.skill_final_shot.s_stun%$ Sekunde.",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_1_NAME"] = "LETZTER NAGEL",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_2_DESCRIPTION"] = "Der letzte Schuss verursacht %$towers.ballista.skill_final_shot.s_damage_factor[2]%$% mehr Schaden und betäubt das Ziel für %$towers.ballista.skill_final_shot.s_stun%$ Sekunde.",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_2_NAME"] = "LETZTER NAGEL",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_3_DESCRIPTION"] = "Der letzte Schuss verursacht %$towers.ballista.skill_final_shot.s_damage_factor[3]%$% mehr Schaden und betäubt das Ziel für %$towers.ballista.skill_final_shot.s_stun%$ Sekunde.",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_3_NAME"] = "LETZTER NAGEL",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_NOTE"] = "Das war eins zu einer Million, Junge!",
["TOWER_BALLISTA_DESC"] = "Überaus begeistert vom Krieg, gaben die Goblins extra Mühe, um sicherzustellen, dass sie nie wieder einen Bogen führen müssten.",
["TOWER_BALLISTA_NAME"] = "Ballistenaußenposten",
["TOWER_BARREL_1_DESCRIPTION"] = "Die Fässer mit Tränken der Nordmänner sind eine mächtige Waffe gegen Horden von Feinden.",
["TOWER_BARREL_1_NAME"] = "Schlacht Braumeister I",
["TOWER_BARREL_2_DESCRIPTION"] = "Die Fässer mit Tränken der Nordmänner sind eine mächtige Waffe gegen Horden von Feinden.",
["TOWER_BARREL_2_NAME"] = "Schlacht Braumeister II",
["TOWER_BARREL_3_DESCRIPTION"] = "Die Fässer mit Tränken der Nordmänner sind eine mächtige Waffe gegen Horden von Feinden.",
["TOWER_BARREL_3_NAME"] = "Schlacht Braumeister III",
["TOWER_BARREL_4_DESCRIPTION"] = "Die Fässer mit Tränken der Nordmänner sind eine mächtige Waffe gegen Horden von Feinden.",
["TOWER_BARREL_4_NAME"] = "Schlacht Braumeister IV",
["TOWER_BARREL_4_SKILL_BARREL_1_DESCRIPTION"] = "Wirft ein riesiges Fass voller Gift, das Feinden %$towers.barrel.skill_barrel.explosion.damage_min[1]%$-%$towers.barrel.skill_barrel.explosion.damage_max[1]%$ physischen Schaden zufügt. Das Gift des Fasses verursacht %$towers.barrel.skill_barrel.poison.s_damage%$ echten Schaden pro Sekunde für %$towers.barrel.skill_barrel.poison.duration%$ Sekunden.",
["TOWER_BARREL_4_SKILL_BARREL_1_NAME"] = "SCHLECHTE CHARGE",
["TOWER_BARREL_4_SKILL_BARREL_2_DESCRIPTION"] = "Die Explosion des Giftfasses verursacht %$towers.barrel.skill_barrel.explosion.damage_min[2]%$-%$towers.barrel.skill_barrel.explosion.damage_max[2]%$ physischen Schaden. Das Gift des Fasses verursacht %$towers.barrel.skill_barrel.poison.s_damage%$ echten Schaden pro Sekunde für %$towers.barrel.skill_barrel.poison.duration%$ Sekunden.",
["TOWER_BARREL_4_SKILL_BARREL_2_NAME"] = "SCHLECHTE CHARGE",
["TOWER_BARREL_4_SKILL_BARREL_3_DESCRIPTION"] = "Die Explosion des Giftfasses verursacht %$towers.barrel.skill_barrel.explosion.damage_min[3]%$-%$towers.barrel.skill_barrel.explosion.damage_max[3]%$ physischen Schaden. Das Gift des Fasses verursacht %$towers.barrel.skill_barrel.poison.s_damage%$ echten Schaden pro Sekunde für %$towers.barrel.skill_barrel.poison.duration%$ Sekunden.",
["TOWER_BARREL_4_SKILL_BARREL_3_NAME"] = "SCHLECHTE CHARGE",
["TOWER_BARREL_4_SKILL_BARREL_NOTE"] = "Nur für die Mutigen!",
["TOWER_BARREL_4_SKILL_WARRIOR_1_DESCRIPTION"] = "Beschwört einen verstärkten Krieger auf den Weg. Er hat %$towers.barrel.skill_warrior.entity.hp_max[1]%$ Gesundheit und verursacht %$towers.barrel.skill_warrior.entity.damage_min[1]%$-%$towers.barrel.skill_warrior.entity.damage_max[1]%$ physischen Schaden.",
["TOWER_BARREL_4_SKILL_WARRIOR_1_NAME"] = "ELIXIER DER MACHT",
["TOWER_BARREL_4_SKILL_WARRIOR_2_DESCRIPTION"] = "Der Krieger hat %$towers.barrel.skill_warrior.entity.hp_max[2]%$ Gesundheit und verursacht %$towers.barrel.skill_warrior.entity.damage_min[2]%$-%$towers.barrel.skill_warrior.entity.damage_max[2]%$ physischen Schaden.",
["TOWER_BARREL_4_SKILL_WARRIOR_2_NAME"] = "ELIXIER DER MACHT",
["TOWER_BARREL_4_SKILL_WARRIOR_3_DESCRIPTION"] = "Der Krieger hat %$towers.barrel.skill_warrior.entity.hp_max[3]%$ Gesundheitspunkte und verursacht %$towers.barrel.skill_warrior.entity.damage_min[3]%$-%$towers.barrel.skill_warrior.entity.damage_max[3]%$ physischen Schaden.",
["TOWER_BARREL_4_SKILL_WARRIOR_3_NAME"] = "ELIXIER DER MACHT",
["TOWER_BARREL_4_SKILL_WARRIOR_NOTE"] = "Schmeckt wie ein Sieg!",
["TOWER_BARREL_DESC"] = "Die Nordländer sind Experten in der Kunst der Trankherstellung und setzen ihre Getränke im Kampf gegen Feinde ein.",
["TOWER_BARREL_NAME"] = "Schlacht Braumeister",
["TOWER_BARREL_WARRIOR_NAME"] = "Halfdan der Schonungslose",
["TOWER_BROKEN_DESCRIPTION"] = "Dieser Turm ist beschädigt, zahlen Sie Gold, um ihn zu reparieren.",
["TOWER_BROKEN_NAME"] = "Beschädigter Turm",
["TOWER_CROCS_EATEN_DESCRIPTION"] = "Durch Magie, den Turm in seine ursprüngliche Form zurücksetzen.",
["TOWER_CROCS_EATEN_NAME"] = "Turmüberreste",
["TOWER_DARK_ELF_1_DESCRIPTION"] = "Egal wie weit entfernt oder stark ihr Ziel ist, sie treffen es immer.",
["TOWER_DARK_ELF_1_NAME"] = "Zwielicht-Langbögen I",
["TOWER_DARK_ELF_2_DESCRIPTION"] = "Egal wie weit entfernt oder stark ihr Ziel ist, sie treffen es immer.",
["TOWER_DARK_ELF_2_NAME"] = "Zwielicht-Langbögen II",
["TOWER_DARK_ELF_3_DESCRIPTION"] = "Egal wie weit entfernt oder stark ihr Ziel ist, sie treffen es immer.",
["TOWER_DARK_ELF_3_NAME"] = "Zwielicht-Langbögen III",
["TOWER_DARK_ELF_4_DESCRIPTION"] = "Egal wie weit entfernt oder stark ihr Ziel ist, sie treffen es immer.",
["TOWER_DARK_ELF_4_NAME"] = "Zwielicht-Langbögen IV",
["TOWER_DARK_ELF_4_SKILL_BUFF_1_DESCRIPTION"] = "Jedes Mal, wenn der Turm einen Feind tötet, erhöht er seinen Angriffsschaden um %$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$.",
["TOWER_DARK_ELF_4_SKILL_BUFF_1_NAME"] = "JAGDFIEBER",
["TOWER_DARK_ELF_4_SKILL_BUFF_2_DESCRIPTION"] = "Jedes Mal, wenn der Turm einen Feind tötet, erhöht er seinen Angriffsschaden um %$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$.",
["TOWER_DARK_ELF_4_SKILL_BUFF_2_NAME"] = "JAGDFIEBER",
["TOWER_DARK_ELF_4_SKILL_BUFF_3_DESCRIPTION"] = "Jedes Mal, wenn der Turm einen Feind tötet, erhöht er seinen Angriffsschaden um %$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$.",
["TOWER_DARK_ELF_4_SKILL_BUFF_3_NAME"] = "JAGDFIEBER",
["TOWER_DARK_ELF_4_SKILL_BUFF_NOTE"] = "Die Jagd beginnt!",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_1_DESCRIPTION"] = "Beschwört zwei Zwielicht Störenfriede. Sie haben %$towers.dark_elf.soldier.hp[1]%$ Lebenspunkte und verursachen %$towers.dark_elf.soldier.basic_attack.damage_min[1]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[1]%$ physischen Schaden.",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_1_NAME"] = "UNTERSTÜTZUNGS KLINGEN",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_2_DESCRIPTION"] = "Die Zwielicht Störenfriede haben nun %$towers.dark_elf.soldier.hp[2]%$ Lebenspunkte und verursachen %$towers.dark_elf.soldier.basic_attack.damage_min[2]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[2]%$ physischen Schaden.",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_2_NAME"] = "UNTERSTÜTZUNGS KLINGEN",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_3_DESCRIPTION"] = "Die Zwielicht Störenfriede haben nun %$towers.dark_elf.soldier.hp[3]%$ Lebenspunkte und verursachen %$towers.dark_elf.soldier.basic_attack.damage_min[3]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[3]%$ physischen Schaden.",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_3_NAME"] = "UNTERSTÜTZUNGS KLINGEN",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_NOTE"] = "Sie kommen, um zu spielen.",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_DESCRIPTION"] = "Ändert den Fokus des Turms auf den Feind der am nächsten am Ausgang ist.",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_NAME"] = "Feind Fokus: Vorderster",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_NOTE"] = "Lass niemanden durch!",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_DESCRIPTION"] = "Ändert den Fokus des Turms auf den Feind mit der meisten Gesundheit. ",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_NAME"] = "Feind Fokus: max. Gesundheit",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_NOTE"] = "Ziel auf den Großen! ",
["TOWER_DARK_ELF_DESC"] = "Bogenschützen die sich darauf spezialisiert haben starke Feinde aus der Ferne zu jagen und ihre Angriffe mit dunkler Energie zu verstärken. ",
["TOWER_DARK_ELF_NAME"] = "Zwielicht-Langbögen",
["TOWER_DEMON_PIT_1_DESCRIPTION"] = "Verschmitzt und gefährlich, suchen diese Dämonen immer nach Ärger.",
["TOWER_DEMON_PIT_1_NAME"] = "Dämonengrube I",
["TOWER_DEMON_PIT_2_DESCRIPTION"] = "Verschmitzt und gefährlich, suchen diese Dämonen immer nach Ärger.",
["TOWER_DEMON_PIT_2_NAME"] = "Dämonengrube II",
["TOWER_DEMON_PIT_3_DESCRIPTION"] = "Verschmitzt und gefährlich, suchen diese Dämonen immer nach Ärger.",
["TOWER_DEMON_PIT_3_NAME"] = "Dämonengrube III",
["TOWER_DEMON_PIT_4_BIG_DEMON_1_DESCRIPTION"] = "Beschwört einen riesigen Imp mit %$towers.demon_pit.big_guy.hp_max[1]%$ Gesundheit, der %$towers.demon_pit.big_guy.melee_attack.damage_min[1]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[1]%$ physischen Schaden verursacht. Bei einer Explosion richtet er %$towers.demon_pit.big_guy.explosion_damage[1]%$ Schaden an.",
["TOWER_DEMON_PIT_4_BIG_DEMON_1_NAME"] = "GROSSER BOSS",
["TOWER_DEMON_PIT_4_BIG_DEMON_2_DESCRIPTION"] = "Der Große Imp hat %$towers.demon_pit.big_guy.hp_max[2]%$ Gesundheit und fügt %$towers.demon_pit.big_guy.melee_attack.damage_min[2]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[2]%$ physischen Schaden zu. Die Explosion verursacht %$towers.demon_pit.big_guy.explosion_damage[2]%$ Schaden.",
["TOWER_DEMON_PIT_4_BIG_DEMON_2_NAME"] = "GROSSER BOSS",
["TOWER_DEMON_PIT_4_BIG_DEMON_3_DESCRIPTION"] = "Der Große Imp hat %$towers.demon_pit.big_guy.hp_max[3]%$ Gesundheit und fügt %$towers.demon_pit.big_guy.melee_attack.damage_min[3]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[3]%$ physischen Schaden zu. Die Explosion verursacht %$towers.demon_pit.big_guy.explosion_damage[3]%$ Schaden.",
["TOWER_DEMON_PIT_4_BIG_DEMON_3_NAME"] = "GROSSER BOSS",
["TOWER_DEMON_PIT_4_BIG_DEMON_NAME"] = "GROSSER BOSS",
["TOWER_DEMON_PIT_4_BIG_DEMON_NOTE"] = "Versuche nur, mich zu entspannen.",
["TOWER_DEMON_PIT_4_DESCRIPTION"] = "Verschmitzt und gefährlich, suchen diese Dämonen immer nach Ärger.",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_1_DESCRIPTION"] = "Die Explosion der Imps verursacht jetzt %$towers.demon_pit.master_exploders.s_damage_increase[1]%$% mehr Schaden und verbrennt Feinde, wobei sie %$towers.demon_pit.master_exploders.s_total_burning_damage_min[1]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[1]%$ echten Schaden pro Sekunde für %$towers.demon_pit.master_exploders.s_burning_duration[1]%$ Sekunden verursachen. ",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_1_NAME"] = "MEISTER DER EXPLOSION",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_2_DESCRIPTION"] = "Die Explosion der Imps verursacht %$towers.demon_pit.master_exploders.s_damage_increase[2]%$% mehr Schaden. Brennen verursacht %$towers.demon_pit.master_exploders.s_total_burning_damage_min[2]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[2]%$ echten Schaden pro Sekunde für %$towers.demon_pit.master_exploders.s_burning_duration[2]%$ Sekunden.",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_2_NAME"] = "MEISTER DER EXPLOSION",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_3_DESCRIPTION"] = "Die Explosion der Imps verursacht %$towers.demon_pit.master_exploders.s_damage_increase[3]%$% mehr Schaden. Brennen verursacht %$towers.demon_pit.master_exploders.s_total_burning_damage_min[3]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[3]%$ echten Schaden pro Sekunde für %$towers.demon_pit.master_exploders.s_burning_duration[3]%$ Sekunden.",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_3_NAME"] = "MEISTER DER EXPLOSION",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_NAME"] = "MEISTER DER EXPLOSION",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_NOTE"] = "Nur ein Idiot würde diesen Job machen.",
["TOWER_DEMON_PIT_4_NAME"] = "Dämonengrube IV",
["TOWER_DEMON_PIT_DESC"] = "Aus den Tiefen der Lava erscheinend, zögern diese Imps nicht, sich in den Weg der Feinde zu werfen.",
["TOWER_DEMON_PIT_NAME"] = "Dämonengrube",
["TOWER_DEMON_PIT_SOLDIER_BIG_GUY_NAME"] = "Großer Kerl",
["TOWER_DEMON_PIT_SOLDIER_NAME"] = "Dämonenwicht",
["TOWER_DWARF_1_DESCRIPTION"] = "Obwohl sie so kurz sind wie ihre Zündschnüre, kommt nichts lebend durch ihre Linien.",
["TOWER_DWARF_1_NAME"] = "Kanoniertrupp I",
["TOWER_DWARF_2_DESCRIPTION"] = "Obwohl sie so kurz sind wie ihre Zündschnüre, kommt nichts lebend durch ihre Linien.",
["TOWER_DWARF_2_NAME"] = "Kanoniertrupp II",
["TOWER_DWARF_3_DESCRIPTION"] = "Obwohl sie so kurz sind wie ihre Zündschnüre, kommt nichts lebend durch ihre Linien.",
["TOWER_DWARF_3_NAME"] = "Kanoniertrupp III",
["TOWER_DWARF_4_DESCRIPTION"] = "Obwohl sie so kurz sind wie ihre Zündschnüre, kommt nichts lebend durch ihre Linien.",
["TOWER_DWARF_4_FORMATION_1_DESCRIPTION"] = "Fügt dem Trupp eine dritte Kanonierin hinzu.",
["TOWER_DWARF_4_FORMATION_1_NAME"] = "WACHSENDE REIHEN",
["TOWER_DWARF_4_FORMATION_2_DESCRIPTION"] = "Fügt dem Trupp eine vierte Kanonierin hinzu.",
["TOWER_DWARF_4_FORMATION_2_NAME"] = "WACHSENDE REIHEN",
["TOWER_DWARF_4_FORMATION_3_DESCRIPTION"] = "Fügt dem Trupp eine fünfte Kanonierin hinzu.",
["TOWER_DWARF_4_FORMATION_3_NAME"] = "WACHSENDE REIHEN",
["TOWER_DWARF_4_FORMATION_NOTE"] = "Mädchen wollen nur Waffen haben.",
["TOWER_DWARF_4_INCENDIARY_AMMO_1_DESCRIPTION"] = "Feuert einen Sprengsatz, der %$towers.dwarf.incendiary_ammo.damages_min[1]%$ - %$towers.dwarf.incendiary_ammo.damages_max[1]%$ Schaden verursacht und Feinde im Bereich für %$towers.dwarf.incendiary_ammo.burn.s_damage[1]%$ Schaden über %$towers.dwarf.incendiary_ammo.burn.duration%$ Sekunden verbrennt.",
["TOWER_DWARF_4_INCENDIARY_AMMO_1_NAME"] = "BRANDBOMBENMUNITION",
["TOWER_DWARF_4_INCENDIARY_AMMO_2_DESCRIPTION"] = "Feuert einen Sprengsatz, der %$towers.dwarf.incendiary_ammo.damages_min[2]%$ - %$towers.dwarf.incendiary_ammo.damages_max[2]%$ Schaden verursacht und Feinde im Bereich für %$towers.dwarf.incendiary_ammo.burn.s_damage[2]%$ Schaden über %$towers.dwarf.incendiary_ammo.burn.duration%$ Sekunden verbrennt.",
["TOWER_DWARF_4_INCENDIARY_AMMO_2_NAME"] = "BRANDBOMBENMUNITION",
["TOWER_DWARF_4_INCENDIARY_AMMO_3_DESCRIPTION"] = "Feuert einen Sprengsatz, der %$towers.dwarf.incendiary_ammo.damages_min[3]%$ - %$towers.dwarf.incendiary_ammo.damages_max[3]%$ Schaden verursacht und Feinde im Bereich für %$towers.dwarf.incendiary_ammo.burn.s_damage[3]%$ Schaden über %$towers.dwarf.incendiary_ammo.burn.duration%$ Sekunden verbrennt.",
["TOWER_DWARF_4_INCENDIARY_AMMO_3_NAME"] = "BRANDBOMBENMUNITION",
["TOWER_DWARF_4_INCENDIARY_AMMO_NOTE"] = "Bereit zum Abfeuern!",
["TOWER_DWARF_4_NAME"] = "Kanoniertrupp IV",
["TOWER_DWARF_DESC"] = "Erfahrene Schützinen mit einem unvergleichlichen Teamgeist, aus dem Norden geschickt, um den unangemessenen Einsatz von Technologie zu kontrollieren.",
["TOWER_DWARF_NAME"] = "Kanoniertrupp",
["TOWER_ELVEN_STARGAZERS_DESC"] = "Indem sie die Energien des Kosmos herbeirufen, können die elfischen Sternengucker gegen viele Feinde gleichzeitig kämpfen.",
["TOWER_ELVEN_STARGAZERS_NAME"] = "Elfen Sternengucker",
["TOWER_FLAMESPITTER_1_DESCRIPTION"] = "Sein Feuer kann leicht mit dem eines Drachen verglichen werden, was Schrecken unter den Bösewichten verbreitet. ",
["TOWER_FLAMESPITTER_1_NAME"] = "Zwergen-Flammenspucker I ",
["TOWER_FLAMESPITTER_2_DESCRIPTION"] = "Sein Feuer kann leicht mit dem eines Drachen verglichen werden, was Schrecken unter den Bösewichten verbreitet. ",
["TOWER_FLAMESPITTER_2_NAME"] = "Zwergen-Flammenspucker II ",
["TOWER_FLAMESPITTER_3_DESCRIPTION"] = "Sein Feuer kann leicht mit dem eines Drachen verglichen werden, was Schrecken unter den Bösewichten verbreitet. ",
["TOWER_FLAMESPITTER_3_NAME"] = "Zwergen-Flammenspucker III ",
["TOWER_FLAMESPITTER_4_DESCRIPTION"] = "Sein Feuer kann leicht mit dem eines Drachen verglichen werden, was Schrecken unter den Bösewichten verbreitet. ",
["TOWER_FLAMESPITTER_4_NAME"] = "Zwergen-Flammenspucker IV ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_1_DESCRIPTION"] = "Schießt eine Flammenbombe ab, die %$towers.flamespitter.skill_bomb.s_damage[1]%$ physischen Schaden verursacht und Feinde verbrennt, welche %$towers.flamespitter.skill_bomb.burning.s_damage%$ echten Schaden pro Sekunde, für %$towers.flamespitter.skill_bomb.burning.duration%$ Sekunden erleiden.",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_1_NAME"] = "LODERNDER PFAD ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_2_DESCRIPTION"] = "Die Flammenbombe fügt %$towers.flamespitter.skill_bomb.s_damage[2]%$ physischen Schaden zu. Brennen verursacht %$towers.flamespitter.skill_bomb.burning.s_damage%$ echten Schaden pro Sekunde für %$towers.flamespitter.skill_bomb.burning.duration%$ Sekunden. ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_2_NAME"] = "LODERNDER PFAD ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_3_DESCRIPTION"] = "Die Flammenbombe fügt %$towers.flamespitter.skill_bomb.s_damage[3]%$ physischen Schaden zu. Brennen verursacht %$towers.flamespitter.skill_bomb.burning.s_damage%$ echten Schaden pro Sekunde für %$towers.flamespitter.skill_bomb.burning.duration%$ Sekunden. ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_3_NAME"] = "LODERNDER PFAD ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_NOTE"] = "Brenne, um wild zu sein. ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_1_DESCRIPTION"] = "Feuersäulen brechen aus dem Pfad hervor, die Feinden %$towers.flamespitter.skill_columns.s_damage_out[1]%$-%$towers.flamespitter.skill_columns.s_damage_in[1]%$ physischen Schaden zufügen und für %$towers.flamespitter.skill_columns.s_stun%$ Sekunden betäuben. ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_1_NAME"] = "BRENNENDE FACKELN ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_2_DESCRIPTION"] = "Die Feuersäulen verursachen %$towers.flamespitter.skill_columns.s_damage_out[2]%$-%$towers.flamespitter.skill_columns.s_damage_in[2]%$ physischen Schaden und betäuben Feinde für %$towers.flamespitter.skill_columns.s_stun%$ Sekunde. ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_2_NAME"] = "BRENNENDE FACKELN ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_3_DESCRIPTION"] = "Die Feuersäulen verursachen %$towers.flamespitter.skill_columns.s_damage_out[3]%$-%$towers.flamespitter.skill_columns.s_damage_in[3]%$ physischen Schaden und betäuben Feinde für %$towers.flamespitter.skill_columns.s_stun%$ Sekunde. ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_3_NAME"] = "BRENNENDE FACKELN ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_NOTE"] = "Achtung, wo Sie hintreten! ",
["TOWER_FLAMESPITTER_DESC"] = "Die Hitze der Schmiede in den Kampf bringend, leihen Zwerge ihre feurige Entschlossenheit der Allianz. ",
["TOWER_FLAMESPITTER_NAME"] = "Zwergen-Flammenspucker ",
["TOWER_GHOST_1_DESCRIPTION"] = "Jetzt siehst du sie, jetzt nicht mehr. Jetzt bist du tot.",
["TOWER_GHOST_1_NAME"] = "Grauenhafte Geister I",
["TOWER_GHOST_2_DESCRIPTION"] = "Jetzt siehst du sie, jetzt nicht mehr. Jetzt bist du tot.",
["TOWER_GHOST_2_NAME"] = "Grauenhafte Geister II",
["TOWER_GHOST_3_DESCRIPTION"] = "Jetzt siehst du sie, jetzt nicht mehr. Jetzt bist du tot.",
["TOWER_GHOST_3_NAME"] = "Grauenhafte Geister III",
["TOWER_GHOST_4_DESCRIPTION"] = "Jetzt siehst du sie, jetzt nicht mehr. Jetzt bist du tot.",
["TOWER_GHOST_4_EXTRA_DAMAGE_1_DESCRIPTION"] = "Die Gespenster verursachen %$towers.ghost.extra_damage.s_damage[1]%$% zusätzlichen Schaden, nachdem sie %$towers.ghost.extra_damage.cooldown_start%$ Sekunden im Kampf verbracht haben.",
["TOWER_GHOST_4_EXTRA_DAMAGE_1_NAME"] = "SEELEN-SIPHONIEREN",
["TOWER_GHOST_4_EXTRA_DAMAGE_2_DESCRIPTION"] = "Die Gespenster verursachen %$towers.ghost.extra_damage.s_damage[2]%$% zusätzlichen Schaden, nachdem sie %$towers.ghost.extra_damage.cooldown_start%$ Sekunden im Kampf verbracht haben.",
["TOWER_GHOST_4_EXTRA_DAMAGE_2_NAME"] = "SEELEN-SIPHONIEREN",
["TOWER_GHOST_4_EXTRA_DAMAGE_3_DESCRIPTION"] = "Die Gespenster verursachen %$towers.ghost.extra_damage.s_damage[3]%$% zusätzlichen Schaden, nachdem sie %$towers.ghost.extra_damage.cooldown_start%$ Sekunden im Kampf verbracht haben.",
["TOWER_GHOST_4_EXTRA_DAMAGE_3_NAME"] = "SEELEN-SIPHONIEREN",
["TOWER_GHOST_4_EXTRA_DAMAGE_NOTE"] = "Exposition nicht empfohlen.",
["TOWER_GHOST_4_NAME"] = "Grauenhafte Geister IV",
["TOWER_GHOST_4_SOUL_ATTACK_1_DESCRIPTION"] = "Besiegte Geister stürzen sich auf einen nahen Feind und fügen %$towers.ghost.soul_attack.s_damage[1]%$ wahren Schaden zu, reduzieren seine Geschwindigkeit und halbieren seinen Angriffsschaden.",
["TOWER_GHOST_4_SOUL_ATTACK_1_NAME"] = "UNSTERBLICHER SCHRECKEN",
["TOWER_GHOST_4_SOUL_ATTACK_2_DESCRIPTION"] = "Besiegte Geister stürzen sich auf einen nahen Feind, verursachen %$towers.ghost.soul_attack.s_damage[2]%$ echten Schaden, verringern seine Geschwindigkeit und halbieren seinen Angriffsschaden.",
["TOWER_GHOST_4_SOUL_ATTACK_2_NAME"] = "UNSTERBLICHER SCHRECKEN",
["TOWER_GHOST_4_SOUL_ATTACK_3_DESCRIPTION"] = "Besiegte Geister werfen sich auf einen nahen Feind und fügen %$towers.ghost.soul_attack.s_damage[3]%$ echten Schaden zu, verringern seine Geschwindigkeit und halbieren seinen Angriffsschaden.",
["TOWER_GHOST_4_SOUL_ATTACK_3_NAME"] = "UNSTERBLICHER SCHRECKEN",
["TOWER_GHOST_4_SOUL_ATTACK_NOTE"] = "Du kommst mit uns!",
["TOWER_GHOST_DESC"] = "Gespenster, die selbst nach dem Tod kämpfen. Ihre Macht ermöglicht es ihnen, sich durch die Schatten zu bewegen und Feinde zu überraschen.",
["TOWER_GHOST_NAME"] = "Grauenhafte Geister",
["TOWER_HERMIT_TOAD_1_DESCRIPTION"] = "Ein wenig Magie, ein wenig rohe Gewalt, was auch immer nötig ist, um lästige Eindringlinge loszuwerden.",
["TOWER_HERMIT_TOAD_1_NAME"] = "Mooreinsiedler I",
["TOWER_HERMIT_TOAD_2_DESCRIPTION"] = "Ein wenig Magie, ein wenig rohe Gewalt, alles, was nötig ist, um lästige Eindringlinge loszuwerden.",
["TOWER_HERMIT_TOAD_2_NAME"] = "Mooreinsiedler II",
["TOWER_HERMIT_TOAD_3_DESCRIPTION"] = "Ein wenig Magie, ein wenig rohe Gewalt, was auch immer nötig ist, um lästige Eindringlinge loszuwerden.",
["TOWER_HERMIT_TOAD_3_NAME"] = "Mooreinsiedler III",
["TOWER_HERMIT_TOAD_4_DESCRIPTION"] = "Ein wenig Magie, ein wenig rohe Gewalt, alles, was nötig ist, um lästige Eindringlinge loszuwerden.",
["TOWER_HERMIT_TOAD_4_INSTAKILL_1_DESCRIPTION"] = "Alle %$towers.hermit_toad.power_instakill.cooldown[1]%$ Sekunden benutzt er seine Zunge, um einen Feind zu verschlingen.",
["TOWER_HERMIT_TOAD_4_INSTAKILL_1_NAME"] = "Klebrige Zunge",
["TOWER_HERMIT_TOAD_4_JUMP_1_DESCRIPTION"] = "Alle %$towers.hermit_toad.power_jump.cooldown[1]%$ Sekunden springt der Einsiedler hoch in den Himmel, stürzt auf die Feinde nieder, verursacht %$towers.hermit_toad.power_jump.damage_min[1]%$ Schaden und betäubt sie für %$towers.hermit_toad.power_jump.stun_duration[1]%$ Sekunden bei der Landung.",
["TOWER_HERMIT_TOAD_4_JUMP_1_NAME"] = "Bodenstampfer",
["TOWER_HERMIT_TOAD_4_NAME"] = "Mooreinsiedler IV",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_1_DESCRIPTION"] = "Alle %$towers.hermit_toad.power_instakill.cooldown[1]%$ Sekunden verwendet er seine Zunge, um einen Feind zu verschlingen.",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_1_NAME"] = "Klebrige Zunge I",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_NOTE"] = "Eine klebrige Angelegenheit.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_1_DESCRIPTION"] = "Alle %$towers.hermit_toad.power_jump.cooldown[1]%$ Sekunden springt der Einsiedler hoch in den Himmel, stürzt auf die Feinde herab, verursacht %$towers.hermit_toad.power_jump.damage_min[1]%$ Schaden und betäubt sie für %$towers.hermit_toad.power_jump.stun_duration[1]%$ Sekunden bei der Landung.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_1_NAME"] = "Bodenstampfer I",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_2_DESCRIPTION"] = "Alle %$towers.hermit_toad.power_jump.cooldown[2]%$ Sekunden springt der Einsiedler hoch in den Himmel, stürzt auf die Feinde herab, verursacht %$towers.hermit_toad.power_jump.damage_min[2]%$ Schaden und betäubt sie für %$towers.hermit_toad.power_jump.stun_duration[2]%$ Sekunden bei der Landung.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_2_NAME"] = "Bodenstampfer II",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_3_DESCRIPTION"] = "Alle %$towers.hermit_toad.power_jump.cooldown[3]%$ Sekunden springt der Einsiedler hoch in den Himmel, stürzt auf die Feinde herab, verursacht %$towers.hermit_toad.power_jump.damage_min[3]%$ Schaden und betäubt sie für %$towers.hermit_toad.power_jump.stun_duration[3]%$ Sekunden bei der Landung.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_3_NAME"] = "Bodenstampfer III",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_NOTE"] = "Bereit für das Sumpf Volleyballteam.",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_DESCRIPTION"] = "Der Einsiedler nimmt eine physische Haltung ein.",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_NAME"] = "Schlammiger Sumpf",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_NOTE"] = "Dreckig werden!",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_DESCRIPTION"] = "Der Einsiedler wechselt in eine magische Haltung.",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_NAME"] = "Magischer Teich",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_NOTE"] = "Unbegrenzte Macht!!",
["TOWER_HERMIT_TOAD_DESC"] = "Ein riesiger Krötenmagier, der ein Händchen dafür hat, Schleimkugeln zu spucken. Alles, was er will, ist ein wenig Frieden und Ruhe für seine Teichbäder. STÖREN SIE IHN NICHT",
["TOWER_HERMIT_TOAD_NAME"] = "Mooreinsiedler",
["TOWER_NECROMANCER_1_DESCRIPTION"] = "Mit ihrer Beherrschung des Todes ernten Nekromanten das Chaos, das sie auf dem Schlachtfeld säen.",
["TOWER_NECROMANCER_1_NAME"] = "Nekromant I",
["TOWER_NECROMANCER_2_DESCRIPTION"] = "Mit ihrer Beherrschung des Todes ernten Nekromanten das Chaos, das sie auf dem Schlachtfeld säen.",
["TOWER_NECROMANCER_2_NAME"] = "Nekromant II",
["TOWER_NECROMANCER_3_DESCRIPTION"] = "Mit ihrer Beherrschung des Todes ernten Nekromanten das Chaos, das sie auf dem Schlachtfeld säen.",
["TOWER_NECROMANCER_3_NAME"] = "Nekromant III",
["TOWER_NECROMANCER_4_DESCRIPTION"] = "Mit ihrer Beherrschung des Todes ernten Nekromanten das Chaos, das sie auf dem Schlachtfeld säen.",
["TOWER_NECROMANCER_4_NAME"] = "Nekromant IV",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_1_DESCRIPTION"] = "Plaziert ein Totem, für %$towers.necromancer.skill_debuff.aura_duration[1]%$ Sekunden, das Feinde verflucht und Skeletten %$towers.necromancer.skill_debuff.s_damage_factor[1]%$% extra Schaden gibt.",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_1_NAME"] = "KLAPPERNDES TOTEM",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_2_DESCRIPTION"] = "Das Totem gibt Skeletten %$towers.necromancer.skill_debuff.s_damage_factor[2]%$% extra Schaden. Die Abklingzeit wird auf %$towers.necromancer.skill_debuff.cooldown[2]%$ Sekunden reduziert.",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_2_NAME"] = "KLAPPERNDES TOTEM",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_3_DESCRIPTION"] = "Das Totem gibt Skeletten %$towers.necromancer.skill_debuff.s_damage_factor[3]%$% extra Schaden. Die Abklingzeit wird auf %$towers.necromancer.skill_debuff.cooldown[3]%$ Sekunden reduziert.",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_3_NAME"] = "KLAPPERNDES TOTEM",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_NOTE"] = "starke-Knochen Armee",
["TOWER_NECROMANCER_4_SKILL_RIDER_1_DESCRIPTION"] = "Beschwört einen Todesreiter auf den Weg, der allen Feinden die er durchläuft %$towers.necromancer.skill_rider.s_damage[1]%$ echten Schaden zufügt.",
["TOWER_NECROMANCER_4_SKILL_RIDER_1_NAME"] = "TODESREITER",
["TOWER_NECROMANCER_4_SKILL_RIDER_2_DESCRIPTION"] = "Der Todesreiter fügt %$towers.necromancer.skill_rider.s_damage[2]%$ echten Schaden zu.",
["TOWER_NECROMANCER_4_SKILL_RIDER_2_NAME"] = "TODESREITER",
["TOWER_NECROMANCER_4_SKILL_RIDER_3_DESCRIPTION"] = "Der Todesreiter fügt %$towers.necromancer.skill_rider.s_damage[3]%$ echten Schaden zu.",
["TOWER_NECROMANCER_4_SKILL_RIDER_3_NAME"] = "TODESREITER",
["TOWER_NECROMANCER_4_SKILL_RIDER_NOTE"] = "Ein einfaches Ticket...",
["TOWER_NECROMANCER_DESC"] = "Mit der dunkelsten Form der Magie bewaffnet, benutzen die Nekromanten ihre Feinde als Teil der Reihen einer endlosen Armee.",
["TOWER_NECROMANCER_NAME"] = "Nekromant",
["TOWER_PALADIN_COVENANT_1_DESCRIPTION"] = "Wild und engagiert arbeiten die Paladine hart daran, das Königreich vor Gefahren zu schützen.",
["TOWER_PALADIN_COVENANT_1_NAME"] = "Paladinbund I",
["TOWER_PALADIN_COVENANT_2_DESCRIPTION"] = "Wild und engagiert arbeiten die Paladine hart daran, das Königreich vor Gefahren zu schützen.",
["TOWER_PALADIN_COVENANT_2_NAME"] = "Paladinbund II",
["TOWER_PALADIN_COVENANT_3_DESCRIPTION"] = "Wild und engagiert arbeiten die Paladine hart daran, das Königreich vor Gefahren zu schützen.",
["TOWER_PALADIN_COVENANT_3_NAME"] = "Paladinbund III",
["TOWER_PALADIN_COVENANT_4_DESCRIPTION"] = "Wild und engagiert arbeiten die Paladine hart daran, das Königreich vor Gefahren zu schützen.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_1_DESCRIPTION"] = "Wenn Soldaten %$towers.paladin_covenant.healing_prayer.health_trigger_factor[1]%$% ihrer Gesundheit erreichen, werden sie unbesiegbar und stellen %$towers.paladin_covenant.healing_prayer.s_healing[1]%$ Gesundheit pro Sekunde für %$towers.paladin_covenant.healing_prayer.duration%$ Sekunden wieder her.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_1_NAME"] = "HEILENDES GEBET",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_2_DESCRIPTION"] = "Die Heilung steigt auf %$towers.paladin_covenant.healing_prayer.s_healing[2]%$ Gesundheit pro Sekunde für %$towers.paladin_covenant.healing_prayer.duration%$ Sekunden.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_2_NAME"] = "HEILENDES GEBET",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_3_DESCRIPTION"] = "Die Heilung steigt auf %$towers.paladin_covenant.healing_prayer.s_healing[3]%$ Gesundheit pro Sekunde für %$towers.paladin_covenant.healing_prayer.duration%$ Sekunden.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_3_NAME"] = "HEILENDES GEBET",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_NAME"] = "HEILENDES GEBET",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_NOTE"] = "Pflicht bis in den Tod.",
["TOWER_PALADIN_COVENANT_4_LEAD_1_DESCRIPTION"] = "Ersetzt einen der Paladine durch einen Veteranen der Garde, was verbündeten Einheiten in der Nähe einen Angriffsschadensbonus von %$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$% gewährt.",
["TOWER_PALADIN_COVENANT_4_LEAD_1_NAME"] = "MIT BEISPIEL VORANGEHEN",
["TOWER_PALADIN_COVENANT_4_LEAD_2_DESCRIPTION"] = "Ersetzt einen der Paladine durch einen Veteranen der Garde, was verbündeten Einheiten in der Nähe einen Angriffsschadensbonus von %$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$% gewährt.",
["TOWER_PALADIN_COVENANT_4_LEAD_2_NAME"] = "MIT BEISPIEL VORANGEHEN",
["TOWER_PALADIN_COVENANT_4_LEAD_3_DESCRIPTION"] = "Ersetzt einen der Paladine durch einen Veteranen der Garde, was verbündeten Einheiten in der Nähe einen Angriffsschadensbonus von %$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$% gewährt.",
["TOWER_PALADIN_COVENANT_4_LEAD_3_NAME"] = "MIT BEISPIEL VORANGEHEN",
["TOWER_PALADIN_COVENANT_4_LEAD_NAME"] = "MIT BEISPIEL VORANGEHEN",
["TOWER_PALADIN_COVENANT_4_LEAD_NOTE"] = "Für den König, für das Land, für die Berge.",
["TOWER_PALADIN_COVENANT_4_NAME"] = "Paladinbund IV",
["TOWER_PALADIN_COVENANT_DESC"] = "Paladine sind das Rückgrat der Elitekräfte von Linirea, die ihre göttliche Macht nutzen, um sich im Kampf zu schützen und zu heilen.",
["TOWER_PALADIN_COVENANT_NAME"] = "Paladinbund",
["TOWER_PANDAS_1_DESCRIPTION"] = "Gerüstet mit Elementarmeisterschaft und unbeirrbarem Willen kämpfen die Meister unermüdlich für das natürliche Gleichgewicht der Welt.",
["TOWER_PANDAS_1_NAME"] = "Bambusmeister I",
["TOWER_PANDAS_2_DESCRIPTION"] = "Gerüstet mit Elementarmeisterschaft und unbeirrbarem Willen kämpfen die Meister unermüdlich für das natürliche Gleichgewicht der Welt.",
["TOWER_PANDAS_2_NAME"] = "Bambusmeister II",
["TOWER_PANDAS_3_DESCRIPTION"] = "Gerüstet mit Elementarmeisterschaft und unbeirrbarem Willen kämpfen die Meister unermüdlich für das natürliche Gleichgewicht der Welt.",
["TOWER_PANDAS_3_NAME"] = "Bambusmeister III",
["TOWER_PANDAS_4_DESCRIPTION"] = "Gerüstet mit Elementarmeisterschaft und unbeirrbarem Willen kämpfen die Meister unermüdlich für das natürliche Gleichgewicht der Welt.",
["TOWER_PANDAS_4_FIERY"] = "Kawoosh",
["TOWER_PANDAS_4_FIERY_1_DESCRIPTION"] = "Schießt einen Feuerblitz ab, der %$towers.pandas.soldier.teleport.damage_min[1]%$-%$towers.pandas.soldier.teleport.damage_max[1]%$ wahren Schaden verursacht und getroffene Feinde zurück auf dem Pfad teleportiert.",
["TOWER_PANDAS_4_FIERY_1_NAME"] = "Netherschwur",
["TOWER_PANDAS_4_FIERY_2_DESCRIPTION"] = "Feuert einen Feuerblitz ab, der %$towers.pandas.soldier.teleport.damage_min[2]%$-%$towers.pandas.soldier.teleport.damage_max[2]%$ wahren Schaden verursacht und getroffene Feinde auf dem Pfad zurückteleportiert.",
["TOWER_PANDAS_4_FIERY_2_NAME"] = "Netherschwur",
["TOWER_PANDAS_4_HAT"] = "Ein Hut, sie alle zu treffen",
["TOWER_PANDAS_4_HAT_1_DESCRIPTION"] = "Wirft ihren geschärften Hut auf einen Feind, der zwischen Gegnern abprallt und bei jedem Treffer %$towers.pandas.soldier.hat.damage_levels[1].min%$-%$towers.pandas.soldier.hat.damage_levels[1].max%$ Schaden verursacht.",
["TOWER_PANDAS_4_HAT_1_NAME"] = "Hattrick",
["TOWER_PANDAS_4_HAT_2_DESCRIPTION"] = "Wirft ihren geschärften Hut auf einen Feind, der zwischen Gegnern abprallt und bei jedem Treffer %$towers.pandas.soldier.hat.damage_levels[2].min%$-%$towers.pandas.soldier.hat.damage_levels[2].max%$ Schaden verursacht.",
["TOWER_PANDAS_4_HAT_2_NAME"] = "Hattrick",
["TOWER_PANDAS_4_NAME"] = "Bambusmeister IV",
["TOWER_PANDAS_4_THUNDER"] = "Panda-Kombat",
["TOWER_PANDAS_4_THUNDER_1_DESCRIPTION"] = "Ruft Blitze auf ein kleines Gebiet herab, die jeweils %$towers.pandas.soldier.thunder.damage_min[1]%$-%$towers.pandas.soldier.thunder.damage_max[1]%$ Flächenschaden verursachen und getroffene Feinde kurz betäuben.",
["TOWER_PANDAS_4_THUNDER_1_NAME"] = "Blitzüberladung",
["TOWER_PANDAS_4_THUNDER_2_DESCRIPTION"] = "Ruft Blitze auf ein kleines Gebiet herab, die jeweils %$towers.pandas.soldier.thunder.damage_min[2]%$-%$towers.pandas.soldier.thunder.damage_max[2]%$ Flächenschaden verursachen und getroffene Feinde kurz betäuben.",
["TOWER_PANDAS_4_THUNDER_2_NAME"] = "Blitzüberladung",
["TOWER_PANDAS_DESC"] = "Mit einer Mischung aus Kampfkunst und Elementaraffinität zerreißt dieses Pandatrio Feinde und bleibt selbst im scheinbaren Rückzug eine Gefahr.",
["TOWER_PANDAS_NAME"] = "Bambusmeister",
["TOWER_PANDAS_RETREAT_DESCRIPTION"] = "Zieht stehende Pandas für 8 Sekunden in die Zuflucht zurück.",
["TOWER_PANDAS_RETREAT_NAME"] = "Taktischer Rückzug",
["TOWER_PANDAS_RETREAT_NOTE"] = "Vorsicht ist die Mutter der Tapferkeit.",
["TOWER_RAY_1_DESCRIPTION"] = "Gefährliche, verdorbene Formen von Magie haben böse Magier nie davon abgehalten, finstere Ziele zu verfolgen.",
["TOWER_RAY_1_NAME"] = "Unheimlicher Seher I",
["TOWER_RAY_2_DESCRIPTION"] = "Gefährliche, verdorbene Formen von Magie haben böse Magier nie davon abgehalten, finstere Ziele zu verfolgen.",
["TOWER_RAY_2_NAME"] = "Unheimlicher Seher II",
["TOWER_RAY_3_DESCRIPTION"] = "Gefährliche, verdorbene Formen von Magie haben böse Magier nie davon abgehalten, finstere Ziele zu verfolgen.",
["TOWER_RAY_3_NAME"] = "Unheimlicher Seher III",
["TOWER_RAY_4_CHAIN_1_DESCRIPTION"] = "Der magische Strahl erstreckt sich jetzt auf %$towers.ray.skill_chain.s_max_enemies%$ zusätzliche Feinde, verlangsamt sie und verursacht %$towers.ray.skill_chain.damage_mult[1]%$% des gesamten magischen Schadens an jedem Ziel.",
["TOWER_RAY_4_CHAIN_1_NAME"] = "KRAFTÜBERFLUSS",
["TOWER_RAY_4_CHAIN_2_DESCRIPTION"] = "Der magische Strahl erstreckt sich jetzt auf %$towers.ray.skill_chain.s_max_enemies%$ zusätzliche Feinde, verlangsamt sie und verursacht %$towers.ray.skill_chain.damage_mult[2]%$% des gesamten magischen Schadens an jedem Ziel.",
["TOWER_RAY_4_CHAIN_2_NAME"] = "KRAFTÜBERFLUSS",
["TOWER_RAY_4_CHAIN_3_DESCRIPTION"] = "Der magische Strahl erstreckt sich nun über %$towers.ray.skill_chain.s_max_enemies%$ zusätzliche Feinde, verlangsamt sie und verursacht %$towers.ray.skill_chain.damage_mult[3]%$% des gesamten magischen Schadens an jedem Ziel.",
["TOWER_RAY_4_CHAIN_3_NAME"] = "KRAFTÜBERFLUSS",
["TOWER_RAY_4_CHAIN_NOTE"] = "Es ist genug Schmerz für alle da.",
["TOWER_RAY_4_DESCRIPTION"] = "Gefährliche, verdorbene Formen von Magie haben böse Magier nie davon abgehalten, finstere Ziele zu verfolgen.",
["TOWER_RAY_4_NAME"] = "Unheimlicher Seher IV",
["TOWER_RAY_4_SHEEP_1_DESCRIPTION"] = "Verwandelt einen nahen Feind in ein wehrloses Schaf. Das Schaf hat %$towers.ray.skill_sheep.sheep.hp_mult%$% der Gesundheit des Ziels.",
["TOWER_RAY_4_SHEEP_1_NAME"] = "Mutationsfluch",
["TOWER_RAY_4_SHEEP_2_DESCRIPTION"] = "Verwandelt einen nahen Feind in ein wehrloses Schaf. Das Schaf hat %$towers.ray.skill_sheep.sheep.hp_mult%$% der Gesundheit des Ziels.",
["TOWER_RAY_4_SHEEP_2_NAME"] = "Mutationsfluch",
["TOWER_RAY_4_SHEEP_3_DESCRIPTION"] = "Verwandelt einen nahen Feind in ein wehrloses Schaf. Das Schaf hat %$towers.ray.skill_sheep.sheep.hp_mult%$% der Gesundheit des Ziels.",
["TOWER_RAY_4_SHEEP_3_NAME"] = "Mutationsfluch",
["TOWER_RAY_4_SHEEP_NOTE"] = "Ehrlich gesagt, siehst du jetzt besser aus.",
["TOWER_RAY_DESC"] = "Die Lehrlinge von Vez'nan nutzen ihre korrupte Macht, um einen dunklen Strahl des Leidens über ihre Feinde zu werfen.",
["TOWER_RAY_NAME"] = "Unheimlicher Seher",
["TOWER_ROCKET_GUNNERS_1_DESCRIPTION"] = "Ausgestattet mit der neuesten Technologie der Dunklen Armee, patrouillieren die Kanoniere den Himmel.",
["TOWER_ROCKET_GUNNERS_1_NAME"] = "Raketenkanoniere I",
["TOWER_ROCKET_GUNNERS_2_DESCRIPTION"] = "Ausgestattet mit der neuesten Technologie der Dunklen Armee, patrouillieren die Kanoniere den Himmel.",
["TOWER_ROCKET_GUNNERS_2_NAME"] = "Raketenkanoniere II",
["TOWER_ROCKET_GUNNERS_3_DESCRIPTION"] = "Ausgestattet mit der neuesten Technologie der Dunklen Armee, patrouillieren die Kanoniere den Himmel.",
["TOWER_ROCKET_GUNNERS_3_NAME"] = "Raketenkanoniere III",
["TOWER_ROCKET_GUNNERS_4_DESCRIPTION"] = "Ausgestattet mit der neuesten Technologie der Dunklen Armee, patrouillieren die Kanoniere den Himmel.",
["TOWER_ROCKET_GUNNERS_4_NAME"] = "Raketenkanoniere IV",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_1_DESCRIPTION"] = "Jeder Angriff zerstört %$towers.rocket_gunners.soldier.phosphoric.armor_reduction[1]%$% der feindlichen Rüstung und verursacht Flächenschaden.",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_1_NAME"] = "PHOSPHOR BESCHICHTUNG",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_2_DESCRIPTION"] = "Jeder Angriff zerstört %$towers.rocket_gunners.soldier.phosphoric.armor_reduction[2]%$% der feindlichen Rüstung und verursacht %$towers.rocket_gunners.soldier.phosphoric.damage_area_min[2]%$-%$towers.rocket_gunners.soldier.phosphoric.damage_area_max[2]%$ Flächenschaden.",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_2_NAME"] = "PHOSPHOR BESCHICHTUNG",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_3_DESCRIPTION"] = "Jeder Angriff zerstört %$towers.rocket_gunners.soldier.phosphoric.armor_reduction[3]%$% der feindlichen Rüstung und verursacht %$towers.rocket_gunners.soldier.phosphoric.damage_area_min[3]%$-%$towers.rocket_gunners.soldier.phosphoric.damage_area_max[3]%$ Flächenschaden.",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_3_NAME"] = "PHOSPHOR BESCHICHTUNG",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_NOTE"] = "Böse gewürzte Kugeln.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_1_DESCRIPTION"] = "Feuert eine Rakete ab, die einen Feind mit bis zu %$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[1]%$ Gesundheit soffort tötet.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_1_NAME"] = "STING-RAKETEN",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_2_DESCRIPTION"] = "Verringert die Abkühlzeit auf %$towers.rocket_gunners.sting_missiles.cooldown[2]%$ Sekunden. Jetzt kann sie Feinde mit bis zu %$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[2]%$ Gesundheit anvisieren.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_2_NAME"] = "STING-RAKETEN",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_3_DESCRIPTION"] = "Verringert die Abkühlzeit auf %$towers.rocket_gunners.sting_missiles.cooldown[3]%$ Sekunden. Jetzt kann sie Feinde mit bis zu %$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[3]%$ Gesundheit anvisieren.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_3_NAME"] = "STING-RAKETEN",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_NOTE"] = "Weich dem aus!",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_DESCRIPTION"] = "Raketenkanoniere starten und können Feinde nicht blockieren.",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_NAME"] = "Start",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_NOTE"] = "Bis zur Unendlichkeit und noch viel weiter!",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_DESCRIPTION"] = "Raketenkanoniere landen auf dem Boden und können Feinde blockieren.",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_NAME"] = "Landen",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_NOTE"] = "Der Adler ist gelandet!",
["TOWER_ROCKET_GUNNERS_DESC"] = "Diese Spezialeinheiten können sich sowohl am Boden als auch in der Luft behaupten und setzen ihre fortgeschrittene Bewaffnung gegen ahnungslose Feinde ein.",
["TOWER_ROCKET_GUNNERS_NAME"] = "Raketenkanoniere",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_1"] = "Dieser Turm ist in der Kolossale Bedrohung Kampagne enthalten",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_2"] = "Dieser Turm ist in der Kampagne „Wukongs Reise“ enthalten.",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_1"] = "Kolossale Bedrohung Kampagne",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_2"] = "Wukongs Reise-Kampagne",
["TOWER_ROOM_EQUIPPED_TOWERS_TITLE"] = "Ausgewählte Türme",
["TOWER_ROOM_GET_DLC"] = "HOL ES DIR",
["TOWER_ROOM_LABEL_ROSTER_THUMB_NEW"] = "Neu!",
["TOWER_ROOM_SKILLS_TITLE"] = "Fähigkeiten",
["TOWER_ROYAL_ARCHERS_1_DESCRIPTION"] = "Bis zum bitteren Ende treu, schützen die Königlichen Bogenschützen die Linireaner Kräfte aus der Ferne. ",
["TOWER_ROYAL_ARCHERS_1_NAME"] = "Königliche Bogenschützen I ",
["TOWER_ROYAL_ARCHERS_2_DESCRIPTION"] = "Bis zum bitteren Ende treu, schützen die Königlichen Bogenschützen die Linireaner Kräfte aus der Ferne. ",
["TOWER_ROYAL_ARCHERS_2_NAME"] = "Königliche Bogenschützen II ",
["TOWER_ROYAL_ARCHERS_3_DESCRIPTION"] = "Bis zum bitteren Ende treu, schützen die Königlichen Bogenschützen die Linireaner Kräfte aus der Ferne. ",
["TOWER_ROYAL_ARCHERS_3_NAME"] = "Königliche Bogenschützen III ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_1_DESCRIPTION"] = "Schießt drei verstärkte Pfeile, die zwischen %$towers.royal_archers.armor_piercer.damage_min[1]%$ und %$towers.royal_archers.armor_piercer.damage_max[1]%$ physischen Schaden anrichten, und ignoriert %$towers.royal_archers.armor_piercer.armor_penetration[1]%$% der feindlichen Rüstung. ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_1_NAME"] = "RÜSTUNGSBRECHER ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_2_DESCRIPTION"] = "Schießt drei verstärkte Pfeile, die zwischen %$towers.royal_archers.armor_piercer.damage_min[2]%$ und %$towers.royal_archers.armor_piercer.damage_max[2]%$ physischen Schaden anrichten, und ignoriert %$towers.royal_archers.armor_piercer.armor_penetration[2]%$% der feindlichen Rüstung. ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_2_NAME"] = "RÜSTUNGSBRECHER ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_3_DESCRIPTION"] = "Schießt drei verstärkte Pfeile, die zwischen %$towers.royal_archers.armor_piercer.damage_min[3]%$ und %$towers.royal_archers.armor_piercer.damage_max[3]%$ physischen Schaden anrichten, und ignoriert %$towers.royal_archers.armor_piercer.armor_penetration[3]%$% der feindlichen Rüstung. ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_3_NAME"] = "RÜSTUNGSBRECHER ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_NAME"] = "RÜSTUNGSBRECHER ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_NOTE"] = "Wir haben dich im Visier. ",
["TOWER_ROYAL_ARCHERS_4_DESCRIPTION"] = "Bis zum bitteren Ende treu, schützen die Königlichen Bogenschützen die Linireaner Kräfte aus der Ferne. ",
["TOWER_ROYAL_ARCHERS_4_NAME"] = "Königliche Bogenschützen IV ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_1_DESCRIPTION"] = "Beschwört einen Adler, der Feinde auf dem Pfad angreift und zwischen %$towers.royal_archers.rapacious_hunter.damage_min[1]%$ und %$towers.royal_archers.rapacious_hunter.damage_max[1]%$ physischen Schaden verursacht.",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_1_NAME"] = "RAUBGREIFENDER JÄGER ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_2_DESCRIPTION"] = "Der Adler verursacht %$towers.royal_archers.rapacious_hunter.damage_min[2]%$-%$towers.royal_archers.rapacious_hunter.damage_max[2]%$ physischen Schaden. ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_2_NAME"] = "RAUBGREIFENDER JÄGER ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_3_DESCRIPTION"] = "Der Adler verursacht %$towers.royal_archers.rapacious_hunter.damage_min[3]%$-%$towers.royal_archers.rapacious_hunter.damage_max[3]%$ physischen Schaden. ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_3_NAME"] = "RAUBGREIFENDER JÄGER ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_NAME"] = "RAUBGREIFENDER JÄGER ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_NOTE"] = "Das Auge des Adlers verbirgt etwas Tragisches. ",
["TOWER_ROYAL_ARCHERS_DESC"] = "Die mächtigsten Schützen des Königreichs, bekannt dafür, von Kriegsadlern unterstützt zu werden. ",
["TOWER_ROYAL_ARCHERS_NAME"] = "Königliche Bogenschützen ",
["TOWER_SAND_1_DESCRIPTION"] = "Ihre Fähigkeit mit dem Wurfmesser reicht aus, um jeden Söldner, der zu voll von sich selbst ist, zu erschrecken.",
["TOWER_SAND_1_NAME"] = "Dünenwächter I",
["TOWER_SAND_2_DESCRIPTION"] = "Ihre Fähigkeit mit dem Wurfmesser reicht aus, um jeden Söldner, der zu voll von sich selbst ist, zu erschrecken.",
["TOWER_SAND_2_NAME"] = "Dünenwächter II",
["TOWER_SAND_3_DESCRIPTION"] = "Ihre Fähigkeit mit der geworfenen Klinge ist genug, um jeden Söldner zu erschrecken, der zu voll von sich selbst ist.",
["TOWER_SAND_3_NAME"] = "Dünenwächter III",
["TOWER_SAND_4_DESCRIPTION"] = "Ihre Fähigkeit mit der geworfenen Klinge ist genug, um jeden Söldner zu erschrecken, der zu voll von sich selbst ist.",
["TOWER_SAND_4_NAME"] = "Dünenwächter IV",
["TOWER_SAND_4_SKILL_BIG_BLADE_1_DESCRIPTION"] = "Schießt 2 wirbelnde Klingen auf den Weg, die %$towers.sand.skill_big_blade.s_damage_min[1]%$-%$towers.sand.skill_big_blade.s_damage_max[1]%$ physischen Schaden pro Sekunde, für %$towers.sand.skill_big_blade.duration[1]%$ Sekunden verursachen.",
["TOWER_SAND_4_SKILL_BIG_BLADE_1_NAME"] = "WIRBELNDES VERDERBEN",
["TOWER_SAND_4_SKILL_BIG_BLADE_2_DESCRIPTION"] = "Die wirbelnden Klingen verursachen %$towers.sand.skill_big_blade.s_damage_min[2]%$-%$towers.sand.skill_big_blade.s_damage_max[2]%$ physischen Schaden pro Sekunde, für  %$towers.sand.skill_big_blade.duration[2]%$ Sekunden.",
["TOWER_SAND_4_SKILL_BIG_BLADE_2_NAME"] = "WIRBELNDES VERDERBEN",
["TOWER_SAND_4_SKILL_BIG_BLADE_3_DESCRIPTION"] = "Die wirbelnden Klingen verursachen %$towers.sand.skill_big_blade.s_damage_min[3]%$-%$towers.sand.skill_big_blade.s_damage_max[3]%$ physischen Schaden pro Sekunde, für  %$towers.sand.skill_big_blade.duration[3]%$ Sekunden.",
["TOWER_SAND_4_SKILL_BIG_BLADE_3_NAME"] = "WIRBELNDES VERDERBEN",
["TOWER_SAND_4_SKILL_BIG_BLADE_NOTE"] = "Du drehst mich rundherum, Baby.",
["TOWER_SAND_4_SKILL_GOLD_1_DESCRIPTION"] = "Wirft ein springendes Messer, das %$towers.sand.skill_gold.s_damage[1]%$ physichen Schaden an anvisierten Feinden verursacht. För jedes getötete Ziel gibt es %$towers.sand.skill_gold.gold_extra[1]%$ Bonusgold.",
["TOWER_SAND_4_SKILL_GOLD_1_NAME"] = "KOPFGELDJAGD",
["TOWER_SAND_4_SKILL_GOLD_2_DESCRIPTION"] = "Das springende Messer fügt %$towers.sand.skill_gold.s_damage[2]%$ physischen Schaden zu. Ein Tötung gewährt %$towers.sand.skill_gold.gold_extra[2]%$ Bonusgold.",
["TOWER_SAND_4_SKILL_GOLD_2_NAME"] = "KOPFGELDJAGD",
["TOWER_SAND_4_SKILL_GOLD_3_DESCRIPTION"] = "Das springende Messer fügt %$towers.sand.skill_gold.s_damage[3]%$ physischen Schaden zu. Ein Tötung gewährt %$towers.sand.skill_gold.gold_extra[3]%$ Bonusgold.",
["TOWER_SAND_4_SKILL_GOLD_3_NAME"] = "KOPFGELDJAGD",
["TOWER_SAND_4_SKILL_GOLD_NOTE"] = "Der Flyer sagt tot ODER lebendig.",
["TOWER_SAND_DESC"] = "Aus Hammerhold stammend, könnten die Dünenwächter die tödlichsten Bewohner der Wüste sein.",
["TOWER_SAND_NAME"] = "Dünenwächter",
["TOWER_SELL"] = "Turm verkaufen",
["TOWER_SPARKING_GEODE_1_DESCRIPTION"] = "Sturmrufer und zertifizierter Chaosbringer. Achten Sie auf seinen Energieverbrauch.",
["TOWER_SPARKING_GEODE_1_NAME"] = "Sturmriese I",
["TOWER_SPARKING_GEODE_2_DESCRIPTION"] = "Sturmrufer und zertifizierter Chaosbringer. Achten Sie auf seinen Energieverbrauch.",
["TOWER_SPARKING_GEODE_2_NAME"] = "Sturmriese II",
["TOWER_SPARKING_GEODE_3_DESCRIPTION"] = "Sturmrufer und zertifizierter Chaosbringer. Achten Sie auf seinen Energieverbrauch.",
["TOWER_SPARKING_GEODE_3_NAME"] = "Sturmriese III",
["TOWER_SPARKING_GEODE_4_CRISTALIZE"] = "Donnerschlag!",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_1_DESCRIPTION"] = "Alle %$towers.sparking_geode.crystalize.cooldown[1]%$ Sekunden kristallisiert es %$towers.sparking_geode.crystalize.max_targets[1]%$ Feinde in Reichweite, betäubt sie und sorgt dafür, dass sie %$towers.sparking_geode.crystalize.s_received_damage_factor[1]%$% mehr Schaden erleiden.",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_1_NAME"] = "Kristallisation",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_2_DESCRIPTION"] = "Alle %$towers.sparking_geode.crystalize.cooldown[2]%$ Sekunden kristallisiert es %$towers.sparking_geode.crystalize.max_targets[2]%$ Feinde in Reichweite, betäubt sie und verursacht, dass sie %$towers.sparking_geode.crystalize.s_received_damage_factor[2]%$% mehr Schaden erleiden.",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_2_NAME"] = "Kristallisation",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_3_DESCRIPTION"] = "Alle %$towers.sparking_geode.crystalize.cooldown[3]%$ Sekunden kristallisiert es %$towers.sparking_geode.crystalize.max_targets[3]%$ Feinde in Reichweite, betäubt sie und verursacht, dass sie %$towers.sparking_geode.crystalize.s_received_damage_factor[3]%$% mehr Schaden erleiden.",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_3_NAME"] = "Kristallisation",
["TOWER_SPARKING_GEODE_4_CRYSTALIZE_1_DESCRIPTION"] = "Alle %$towers.sparking_geode.crystalize.cooldown[1]%$ Sekunden kristallisiert es %$towers.sparking_geode.crystalize.max_targets[1]%$ Feinde in Reichweite, betäubt sie und lässt sie %$towers.sparking_geode.crystalize.s_received_damage_factor[1]%$% mehr Schaden erleiden.",
["TOWER_SPARKING_GEODE_4_CRYSTALIZE_1_NAME"] = "Kristallisation",
["TOWER_SPARKING_GEODE_4_DESCRIPTION"] = "Sturmrufer und zertifizierter Chaosbringer. Achten Sie auf seinen Energieverbrauch.",
["TOWER_SPARKING_GEODE_4_NAME"] = "Schockkoloss IV",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST"] = "Harder, Better, Faster, Stronger.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_1_DESCRIPTION"] = "Alle %$towers.sparking_geode.spike_burst.cooldown[1]%$ Sekunden beschwört der Wächter ein elektrisches Feld, das nahe Feinde schädigt und verlangsamt für %$towers.sparking_geode.spike_burst.duration[1]%$ Sekunden.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_1_NAME"] = "Elektrischer Impuls",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_2_DESCRIPTION"] = "Alle %$towers.sparking_geode.spike_burst.cooldown[2]%$ Sekunden beschwört der Wächter ein elektrisches Feld, das nahe Feinde schädigt und verlangsamt für %$towers.sparking_geode.spike_burst.duration[2]%$ Sekunden.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_2_NAME"] = "Elektrischer Impuls",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_3_DESCRIPTION"] = "Alle %$towers.sparking_geode.spike_burst.cooldown[3]%$ Sekunden beschwört der Wächter ein elektrisches Feld, das nahestehende Feinde beschädigt und verlangsamt für %$towers.sparking_geode.spike_burst.duration[3]%$ Sekunden.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_3_NAME"] = "Elektrischer Impuls",
["TOWER_SPARKING_GEODE_DESC"] = "Stammend von einer friedlichen alten Rasse, folgt dieses mächtige Wesen seinem Schutzinstinkt und nutzt seine Blitzkräfte, um für die Allianz zu kämpfen, mit der Kraft eines Sturms zuschlagend.",
["TOWER_SPARKING_GEODE_NAME"] = "Sturmriese",
["TOWER_STAGE_13_SUNRAY_NAME"] = "Dunkelstrahl-Turm",
["TOWER_STAGE_13_SUNRAY_REPAIR_DESCRIPTION"] = "Repariere den Turm, um seine zerstörerischen Kräfte zu nutzen.",
["TOWER_STAGE_13_SUNRAY_REPAIR_NAME"] = "Reparieren",
["TOWER_STAGE_17_WEIRDWOOD_NAME"] = "Wirrwurzel",
["TOWER_STAGE_18_ELVEN_BARRACK_DESCRIPTION"] = "Angehäuerte Elfen die bis zum Ende kämpfen.",
["TOWER_STAGE_18_ELVEN_BARRACK_NAME"] = "Elfen Söldner",
["TOWER_STAGE_20_ARBOREAN_BARRACK_DESCRIPTION"] = "Rufe das Arboreanische Volk auf zu kämpfen.",
["TOWER_STAGE_20_ARBOREAN_BARRACK_NAME"] = "Arborean Einwohner",
["TOWER_STAGE_20_ARBOREAN_HONEY_DESCRIPTION"] = "Beschwöre den großen Anführer der Bienen.",
["TOWER_STAGE_20_ARBOREAN_HONEY_NAME"] = "Arborean Bienenzüchter",
["TOWER_STAGE_20_ARBOREAN_OLDTREE_DESCRIPTION"] = "Frag den alten Baum um hilfe.",
["TOWER_STAGE_20_ARBOREAN_OLDTREE_NAME"] = "Alter Baum",
["TOWER_STAGE_22_ARBOREAN_MAGES_NAME"] = "Arborean Magier",
["TOWER_STAGE_28_PRIESTS_BARRACK_DESCRIPTION"] = "Erlöste Kultisten die ihre Magie mit auf das Schlachtfeld bringen und sich in Abscheulichkeiten verwandeln wenn sie sterben.",
["TOWER_STAGE_28_PRIESTS_BARRACK_NAME"] = "Gläubige des Augenlosen.",
["TOWER_STARGAZER_1_DESCRIPTION"] = "Die Sternengucker versuchen, mächtige Magie aus dem Jenseits des irdischen Reichs zu nutzen.",
["TOWER_STARGAZER_1_NAME"] = "Elfen Sternengucker I",
["TOWER_STARGAZER_2_DESCRIPTION"] = "Die Sternengucker versuchen, mächtige Magie aus dem Jenseits des irdischen Reichs zu nutzen.",
["TOWER_STARGAZER_2_NAME"] = "Elfen Sternengucker II",
["TOWER_STARGAZER_3_DESCRIPTION"] = "Die Sternengucker versuchen, mächtige Magie aus dem Jenseits des irdischen Reichs zu nutzen.",
["TOWER_STARGAZER_3_NAME"] = "Elfen Sternengucker III",
["TOWER_STARGAZER_4_DESCRIPTION"] = "Die Sternengucker versuchen, mächtige Magie aus dem Jenseits des irdischen Reichs zu nutzen.",
["TOWER_STARGAZER_4_EVENT_HORIZON_1_DESCRIPTION"] = "Teleportiert bis zu %$towers.elven_stargazers.teleport.max_targets[1]%$ Feinde zurück auf den Weg.",
["TOWER_STARGAZER_4_EVENT_HORIZON_1_NAME"] = "EREIGNISHORIZONT",
["TOWER_STARGAZER_4_EVENT_HORIZON_2_DESCRIPTION"] = "Teleportiert bis zu %$towers.elven_stargazers.teleport.max_targets[2]%$ Feinde weiter zurück auf dem Weg. ",
["TOWER_STARGAZER_4_EVENT_HORIZON_2_NAME"] = "EREIGNISHORIZONT",
["TOWER_STARGAZER_4_EVENT_HORIZON_3_DESCRIPTION"] = "Teleportiert bis zu %$towers.elven_stargazers.teleport.max_targets[3]%$ Feinde noch weiter zurück auf den Pfad.",
["TOWER_STARGAZER_4_EVENT_HORIZON_3_NAME"] = "EREIGNISHORIZONT",
["TOWER_STARGAZER_4_EVENT_HORIZON_NAME"] = "EREIGNISHORIZONT",
["TOWER_STARGAZER_4_EVENT_HORIZON_NOTE"] = "Ausblenden, einblenden.",
["TOWER_STARGAZER_4_NAME"] = "Elfen Sternengucker IV",
["TOWER_STARGAZER_4_RISING_STAR_1_DESCRIPTION"] = "Von dem Turm getötete Feinde explodieren in einer Explosion von %$towers.elven_stargazers.stars_death.stars[1]%$ Sternen, die %$towers.elven_stargazers.stars_death.damage_min[1]%$-%$towers.elven_stargazers.stars_death.damage_max[1]%$ magischen Schaden an Feinden verursachen.",
["TOWER_STARGAZER_4_RISING_STAR_1_NAME"] = "AUFSTEIGENDER STERN",
["TOWER_STARGAZER_4_RISING_STAR_2_DESCRIPTION"] = "Die Sternenanzahl steigt auf %$towers.elven_stargazers.stars_death.stars[2]%$. Sterne verursachen %$towers.elven_stargazers.stars_death.damage_min[2]%$-%$towers.elven_stargazers.stars_death.damage_max[2]%$ magischen Schaden .",
["TOWER_STARGAZER_4_RISING_STAR_2_NAME"] = "AUFSTEIGENDER STERN",
["TOWER_STARGAZER_4_RISING_STAR_3_DESCRIPTION"] = "Die Sternenanzahl steigt auf %$towers.elven_stargazers.stars_death.stars[3]%$. Sterne verursachen %$towers.elven_stargazers.stars_death.damage_min[3]%$-%$towers.elven_stargazers.stars_death.damage_max[3]%$ magischen Schaden .",
["TOWER_STARGAZER_4_RISING_STAR_3_NAME"] = "AUFSTEIGENDER STERN",
["TOWER_STARGAZER_4_RISING_STAR_NAME"] = "AUFSTEIGENDER STERN",
["TOWER_STARGAZER_4_RISING_STAR_NOTE"] = "Es ist eine Sternenstaubrevolution!",
["TOWER_TRICANNON_1_DESCRIPTION"] = "Ein verheerendes Liebeslied an den Krieg und ein furchteinflößender Anblick für Feinde und Verbündete gleichermaßen.",
["TOWER_TRICANNON_1_NAME"] = "Tricannon I",
["TOWER_TRICANNON_2_DESCRIPTION"] = "Ein verheerendes Liebeslied an den Krieg und ein furchteinflößender Anblick für Feinde und Verbündete gleichermaßen.",
["TOWER_TRICANNON_2_NAME"] = "Tricannon II",
["TOWER_TRICANNON_3_DESCRIPTION"] = "Ein verheerendes Liebeslied an den Krieg und ein furchteinflößender Anblick für Feinde und Verbündete gleichermaßen.",
["TOWER_TRICANNON_3_NAME"] = "Tricannon III",
["TOWER_TRICANNON_4_BOMBARDMENT_1_DESCRIPTION"] = "Schießt schnell Bomben in einem weiten Bereich, die jeweils %$towers.tricannon.bombardment.damage_min[1]%$-%$towers.tricannon.bombardment.damage_max[1]%$ physischen Schaden anrichten.",
["TOWER_TRICANNON_4_BOMBARDMENT_1_NAME"] = "BOMBARDIERUNG",
["TOWER_TRICANNON_4_BOMBARDMENT_2_DESCRIPTION"] = "Schießt mehr Bomben in einer größeren Fläche, wobei jede %$towers.tricannon.bombardment.damage_min[2]%$-%$towers.tricannon.bombardment.damage_max[2]%$ physischen Schaden verursacht",
["TOWER_TRICANNON_4_BOMBARDMENT_2_NAME"] = "BOMBARDIERUNG",
["TOWER_TRICANNON_4_BOMBARDMENT_3_DESCRIPTION"] = "Schießt noch mehr Bomben über ein größeres Gebiet, jede verursacht %$towers.tricannon.bombardment.damage_min[3]%$-%$towers.tricannon.bombardment.damage_max[3]%$ physischen Schaden",
["TOWER_TRICANNON_4_BOMBARDMENT_3_NAME"] = "BOMBARDIERUNG",
["TOWER_TRICANNON_4_BOMBARDMENT_NAME"] = "BOMBARDIERUNG",
["TOWER_TRICANNON_4_BOMBARDMENT_NOTE"] = "Sprechen wir über Skalierbarkeit.",
["TOWER_TRICANNON_4_DESCRIPTION"] = "Ein verheerendes Liebeslied an den Krieg und ein furchteinflößender Anblick für Feinde und Verbündete gleichermaßen.",
["TOWER_TRICANNON_4_NAME"] = "Tricannon IV",
["TOWER_TRICANNON_4_OVERHEAT_1_DESCRIPTION"] = "Die Läufe des Tricannons werden für %$towers.tricannon.overheat.duration[1]%$ Sekunden rotglühend, sodass Bomben den Boden versengen und pro Sekunde %$towers.tricannon.overheat.decal.effect.s_damage[1]%$ echten Schaden an Feinden verursachen.",
["TOWER_TRICANNON_4_OVERHEAT_1_NAME"] = "ÜBERHITZUNG",
["TOWER_TRICANNON_4_OVERHEAT_2_DESCRIPTION"] = "Jeder brennende Bereich verursacht %$towers.tricannon.overheat.decal.effect.s_damage[2]%$ echten Schaden pro Sekunde. Die Dauer wird auf %$towers.tricannon.overheat.duration[2]%$ Sekunden erhöht.",
["TOWER_TRICANNON_4_OVERHEAT_2_NAME"] = "ÜBERHITZUNG",
["TOWER_TRICANNON_4_OVERHEAT_3_DESCRIPTION"] = "Jeder brennende Bereich verursacht %$towers.tricannon.overheat.decal.effect.s_damage[3]%$ echten Schaden pro Sekunde. Die Dauer wird auf %$towers.tricannon.overheat.duration[3]%$ Sekunden erhöht.",
["TOWER_TRICANNON_4_OVERHEAT_3_NAME"] = "ÜBERHITZUNG",
["TOWER_TRICANNON_4_OVERHEAT_NAME"] = "ÜBERHITZUNG",
["TOWER_TRICANNON_4_OVERHEAT_NOTE"] = "Wir sind glühend heiß.",
["TOWER_TRICANNON_DESC"] = "Die Dunkle Armee bringt eine neue Definition der modernen Kriegsführung mit, die durch ihre zahlreichen Kanonen Feuer und Zerstörung regnen lässt.",
["TOWER_TRICANNON_NAME"] = "Tricannon",
["TUTORIAL_hero_room_hero_points_desc"] = "Verdiene Heldenpunkte, indem du jeden Helden im Kampf auflevelst.",
["TUTORIAL_hero_room_hero_points_title"] = "Heldenpunkte",
["TUTORIAL_hero_room_power_desc"] = "Verwende Heldenpunkte, um Kräfte für deinen Helden zu kaufen und zu verbessern.",
["TUTORIAL_hero_room_power_title"] = "Heldenkräfte",
["TUTORIAL_hero_room_tutorial_navigate_desc"] = "Navigiere durch verschiedene Helden.",
["TUTORIAL_hero_room_tutorial_select_desc"] = "Rüste die Helden aus, die du auf dem Schlachtfeld einsetzen möchtest.",
["TUTORIAL_item_room_buy_desc"] = "Verwende deine Edelsteine, um Gegenstände zu kaufen, die dir auf dem Schlachtfeld helfen.",
["TUTORIAL_item_room_buy_title"] = "Kauf von Artikeln",
["TUTORIAL_item_room_tutorial_equip_desc"] = "Verwende jeden Slot, um deine Gegenstände auszurüsten. Ziehe sie, um ihre Reihenfolge zu ändern!",
["TUTORIAL_item_room_tutorial_navigate_desc"] = "Navigiere durch die verschiedenen verfügbaren Artikel.",
["TUTORIAL_tower_room_power_desc"] = "Diese Fähigkeiten sind verfügbar, sobald der Turm Level IV erreicht.",
["TUTORIAL_tower_room_power_title"] = "Fähigkeiten Level IV",
["TUTORIAL_tower_room_tutorial_equip_desc"] = "Rüste neue Türme aus, um verschiedene Kombinationen auszuprobieren.",
["TUTORIAL_tower_room_tutorial_navigate_desc"] = "Navigiere durch die verschiedenen Türme.",
["TUTORIAL_tower_room_tutorial_slots_desc"] = "Verwende jeden Slot, um deine Türme auszurüsten. Ziehe sie, um ihre Reihenfolge zu ändern!",
["TUTORIAL_upgrade_room_tooltip_buy_desc"] = "Verwende Punkte, um Upgrades für deine Kräfte, Türme und Helden zu kaufen.",
["TUTORIAL_upgrade_room_tooltip_souls_desc"] = "Verdiene Upgrade-Punkte, indem du Kampagnenstufen abschließt.",
["TUTORIAL_upgrade_room_tooltip_souls_title"] = "Upgrade-Punkte",
["Tap to continue"] = "Zum Fortfahren tippen",
["Touch on the path to move the hero."] = "Berühre den Weg, um den Helden zu bewegen.",
["Tower construction"] = "Turmbau",
["Typography"] = "Typografie",
["UPDATE_POPUP"] = "AKTUALISIEREN",
["UPDATING_CLOUDSAVE_MESSAGE"] = "Cloud-Speicher wird aktualisiert ...",
["UPGRADES"] = "VERBESSERUNGEN",
["UPGRADES AND HEROES RESTRICTIONS!"] = "BESCHRÄNKUNGEN BEI VERBESSERUNGEN UND HELDEN!",
["Use the earned hero points to train your hero!"] = "Benutze deine Heldenpunkte zur Ausbildung deines Helden!",
["Use the earned stars to improve your towers and powers!"] = "Benutze deine Sterne zur Verbesserung deiner Türme und Kräfte!",
["VICTORY"] = "SIEG",
["Veteran"] = "Veteran",
["Victory!"] = "Sieg!",
["Voice Talent"] = "Voice Talent",
["WAVE_TOOLTIP_TAP_AGAIN"] = "ERNEUT TIPPEN, UM VORZEITIG AUSZULÖSEN",
["WAVE_TOOLTIP_TITLE"] = "WELLE KOMMT",
["We would like to thank"] = "Besonderer Dank an",
["Yes"] = "Ja",
["You must log in to Google Play game services to track achievements."] = "Sie müssen sich bei Google Play-Spieldiensten anmelden, um Erfolge zu verfolgen.",
["You must watch the whole video."] = "Du musst dir das ganze Video ansehen.",
["You will no longer be tracking achievements."] = "Erfolge sind dann nicht mehr möglich.",
["_manually_included_characters"] = "$ ¥ ￥ ƒ ₩ € ™ × $ zł ¢ £ ¤ ¥ ƒ ден дин лв. ؋ ৳ ฿ ლ ₡ ₣ ₤ ₥ ₦ ₨ ₩ ₪ ₫ € ₭ ₮ ₱ ₲ ₴ ₵ ₹ ₺ ₽ ﷼",
["alliance_close_to_home_DESCRIPTION"] = "Gewährt zu Beginn der Stufe zusätzliches Gold.",
["alliance_close_to_home_NAME"] = "GETEILTE RESERVEN",
["alliance_corageous_stand_DESCRIPTION"] = "Jeder gebauter LINIREAN Turm erhöht die Gesundheitspunkte der Helden.",
["alliance_corageous_stand_NAME"] = "MUTIGER STAND",
["alliance_display_of_true_might_dark_DESCRIPTION"] = "Die Zauber des Dunklen Armee-Helden verlangsamen jetzt auch alle Feinde auf dem Bildschirm.",
["alliance_display_of_true_might_dark_NAME"] = "UNHEIMLICHER FLUCH",
["alliance_display_of_true_might_linirea_DESCRIPTION"] = "Die Zauber des Linirean-Helden heilen jetzt auch und beleben alle verbündeten Einheiten wieder.",
["alliance_display_of_true_might_linirea_NAME"] = "SEGEN DER VITALITÄT",
["alliance_flux_altering_coils_DESCRIPTION"] = "Ersetzt alle Ausgangsflaggen durch arkane Säulen, die nahe Feinde zurückteleportieren.",
["alliance_flux_altering_coils_NAME"] = "ARKANE SÄULEN",
["alliance_friends_of_the_crown_DESCRIPTION"] = "Jeder ausgerüstete LINIREAN Held reduziert die Kosten für den Bau und das Aufrüsten von Türmen.",
["alliance_friends_of_the_crown_NAME"] = "FREUNDE DER KRONE",
["alliance_merciless_DESCRIPTION"] = "Jeder gebauter DUNKLE ARMEE Turm erhöht den Angriffsschaden der Helden.",
["alliance_merciless_NAME"] = "GNADENLOSE VERTEIDIGUNG",
["alliance_seal_of_punishment_DESCRIPTION"] = "Ersetzt den Verteidigungspunkt durch ein magisches Siegel, das Feinde schädigt, die darüber laufen.",
["alliance_seal_of_punishment_NAME"] = "SIEGEL DER BESTRAFUNG",
["alliance_shady_company_DESCRIPTION"] = "Jeder ausgerüstete DUNKLE ARMEE Held erhöht den Angriffsschaden der Türme.",
["alliance_shady_company_NAME"] = "ZWIELICHTIGE GESELLSCHAFT",
["alliance_shared_reserves_DESCRIPTION"] = "Gewährt zu Beginn der Stufe zusätzliches Gold.",
["alliance_shared_reserves_NAME"] = "GETEILTE RESERVEN",
["baloon start battle iphone"] = "Doppelt tippen, um Welle auszulösen",
["build defensive towers along the road to stop them."] = "Errichte Abwehrtürme entlang des Wegs, um sie aufzuhalten.",
["build towers to defend the road."] = "Errichte Türme zur Verteidigung der Straße.",
["check the stage description to see:"] = "Sieh dir die Abschnittbeschreibung an:",
["deals area damage"] = "Verursacht Flächenschaden",
["don't let enemies past this point."] = "Lass Feinde nicht weiter als diesen Punkt kommen.",
["earn gold by killing enemies."] = "Verdiene Gold durch das Besiegen von Feinden.",
["good rate of fire"] = "Gute Feuerrate",
["heroes_desperate_effort_DESCRIPTION"] = "Die Angriffe der Helden ignorieren 10% des Widerstands der Feinde.",
["heroes_desperate_effort_NAME"] = "KENNE DEINEN FEIND",
["heroes_lethal_focus_DESCRIPTION"] = "Helden fügen in 20% ihrer Angriffe kritischen Schaden zu.",
["heroes_lethal_focus_NAME"] = "TÖDLICHER FOKUS",
["heroes_limit_pushing_DESCRIPTION"] = "Nachdem jeder Heldenzauber fünfmal verwendet wurde, wird seine Abkühlzeit sofort zurückgesetzt.",
["heroes_limit_pushing_NAME"] = "GRENZEN AUSREIZEN",
["heroes_lone_wolves_DESCRIPTION"] = "Helden verdienen mehr Erfahrung, wenn sie voneinander entfernt sind.",
["heroes_lone_wolves_NAME"] = "EINSAME WÖLFE",
["heroes_nimble_physique_DESCRIPTION"] = "Helden weichen 20% der feindlichen Angriffe aus.",
["heroes_nimble_physique_NAME"] = "BEWEGLICHER KÖRPERBAU",
["heroes_unlimited_vigor_DESCRIPTION"] = "Reduziert alle Abkühlzeiten der Heldenzauber.",
["heroes_unlimited_vigor_NAME"] = "UNBEGRENZTE KRAFT",
["heroes_visual_learning_DESCRIPTION"] = "Helden haben 10% extra Rüstung, wenn sie nah beieinander stehen.",
["heroes_visual_learning_NAME"] = "HILFREICHE HAND",
["high damage, armor piercing"] = "Hoher Schaden, Rüstungsdurchdringung",
["iron and heroic challenges may have restrictions on upgrades!"] = "In eisernen und heldenhafte Herausforderungen sind deine Verbesserungen oft beschränkt!",
["max lvl allowed"] = "Maximaler Level",
["multi-shot, armor piercing"] = "Mehrfachschuss, Rüstungsdurchdringung",
["no heroes"] = "Keine Helden",
["pause popup"] = "SPIEL PAUSIERT",
["protect your lands from the enemy attacks."] = "Schütze dein Land vor feindlichen Angriffen.",
["rally range"] = "Versammlungsreichweite",
["ready for action!"] = "Bereit für den Kampf!",
["reinforcements_intense_workout_DESCRIPTION"] = "Verbessert die Gesundheit und die Dauer der Verstärkungen.",
["reinforcements_intense_workout_NAME"] = "INTENSIVES TRAINING",
["reinforcements_master_blacksmiths_DESCRIPTION"] = "Verbessert den Angriffsschaden und die Rüstung der Verstärkungen.",
["reinforcements_master_blacksmiths_NAME"] = "MEISTER-SCHMIEDEN",
["reinforcements_night_veil_DESCRIPTION"] = "Schattenbogenschützen haben eine erhöhte Reichweite und Angriffsgeschwindigkeit.",
["reinforcements_night_veil_NAME"] = "ASCHEBÖGEN",
["reinforcements_power_trio_DESCRIPTION"] = "Das Rufen der Verstärkungen beschwört nun auch einen Paragon-Ritter.",
["reinforcements_power_trio_NAME"] = "LINIREANISCHER PARAGON",
["reinforcements_power_trio_dark_DESCRIPTION"] = "Das Rufen der Verstärkungen beschwört auch einen Schattenkrähenrufer.",
["reinforcements_power_trio_dark_NAME"] = "SCHATTENKRÄHENRUFER",
["reinforcements_rebel_militia_DESCRIPTION"] = "Verstärkungen werden durch Linireanische Rebellen ersetzt, strapazierfähige Kämpfer in großen Rüstungen.",
["reinforcements_rebel_militia_NAME"] = "LINIREANISCHE MILIZ",
["reinforcements_shadow_archer_DESCRIPTION"] = "Verstärkungen werden durch Schattenbogenschützen ersetzt, die aus der Ferne angreifen und fliegende Einheiten anvisieren.",
["reinforcements_shadow_archer_NAME"] = "ORDEN DER SCHATTEN",
["reinforcements_thorny_armor_DESCRIPTION"] = "Linireanische Rebellen reflektieren einen Teil des Schadens von feindlichen Nahkampfangriffen.",
["reinforcements_thorny_armor_NAME"] = "SPITZEN-RÜSTUNG",
["resists damage from"] = "schadensresistent gegen",
["select the rally point control"] = "Wähle die Versammlungspunktsteuerung.",
["select the tower you want to build!"] = "Wähle den Turm, den du bauen möchtest!",
["select where you want to move your soldiers"] = "Wähle, wohin du deine Soldaten schicken willst.",
["soldiers block enemies"] = "Soldaten blockieren Feinde.",
["some enemies enjoy different levels of magic resistance that protects them against magical attacks."] = "Manche Feinde besitzen unterschiedlich starke Magieresistenzen, die sie vor Magieangriffen schützen.",
["some enemies wear armor of different strengths that protects them against non-magical attacks."] = "Manche Feinde tragen unterschiedlich starke Rüstungen, die sie vor nicht-magischen Angriffen schützen.",
["tap these!"] = "Hier tippen!",
["tap to continue..."] = "Zum Fortfahren tippen ...",
["tap twice to call wave"] = "Doppelt tippen, um Welle auszulösen",
["this is a strategic point."] = "Dies ist eine strategische Stelle.",
["towers_favorite_customer_DESCRIPTION"] = "Beim Kauf der letzten Stufe einer Fähigkeit, reduziere ihre Kosten um 50 %.",
["towers_favorite_customer_NAME"] = "LIEBLINGSKUNDE",
["towers_golden_time_DESCRIPTION"] = "Erhöht das Bonusgold für das frühe Herbeirufen einer Welle.",
["towers_golden_time_NAME"] = "GOLDENE ZEIT",
["towers_improved_formulas_DESCRIPTION"] = "Maximiert den Schaden ALLER Explosionen von Türmen und erhöht deren Wirkungsbereich.",
["towers_improved_formulas_NAME"] = "VERBESSERTEN FORMELN",
["towers_keen_accuracy_DESCRIPTION"] = "Reduziert die Abkühlzeit aller Turmfähigkeiten um 20%.",
["towers_keen_accuracy_NAME"] = "KAMPFESFIEBER",
["towers_royal_training_DESCRIPTION"] = "Verringert die Spawnzeit der Turmeinheiten und die Abkühlzeit der Verstärkungen.",
["towers_royal_training_NAME"] = "AUFRUF ZUM HANDELN",
["towers_scoping_mechanism_DESCRIPTION"] = "Erhöht die Angriffsreichweite ALLER Türme um 10%.",
["towers_scoping_mechanism_NAME"] = "ZIELMECHANISMUS",
["towers_war_rations_DESCRIPTION"] = "Erhöht die Gesundheit aller Turmeinheiten um 10%.",
["towers_war_rations_NAME"] = "KRIEGSRATIONEN",
["towers_wise_investment_DESCRIPTION"] = "Türme erstatten beim Verkauf 90% ihrer Kosten zurück.",
["towers_wise_investment_NAME"] = "KLUGE INVESTITION",
["wOOt!"] = "wOOt!",
["you can adjust your soldiers rally point to make them defend a different area."] = "Du kannst den Versammlungspunkt deiner Soldaten verschieben, damit sie ein anderes Gebiet verteidigen.",
}
