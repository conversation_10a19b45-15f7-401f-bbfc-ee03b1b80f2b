-- ------------------------------------------------
-- -- WARNING: DO NOT EDIT BY HAND                 
-- -- Generated by kr-i18n/tools/strings-export.lua
-- ------------------------------------------------
return {
["!!!COMMENT_LOCALIZATION_SOURCE"] = "Keywords + testers (fiew for kr2)",
["%d Life"] = "%d Vida",
["%d Lives"] = "%d Vidas",
["%i sec."] = "%i seg.",
["- if heroes are allowed"] = "- com ou sem heróis",
["- max upgrade level allowed"] = "- nv. máx. de melhoria permitido",
["A good challenge!"] = "Grande desafio!",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_1_NAME"] = "Willy Abominado",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_2_NAME"] = "Henry Abominado",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_3_NAME"] = "Geoffrey Abominado",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_4_NAME"] = "Nicholas Constipado",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_5_NAME"] = "Edominação",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_6_NAME"] = "Hobominação",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_7_NAME"] = "Odominação",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_8_NAME"] = "Cedric Abominado",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_9_NAME"] = "Halbominação",
["ACHIEVEMENT"] = "CONQUISTA",
["ACHIEVEMENTS"] = "CONQUISTAS",
["ACHIEVEMENTS_TITLE"] = "CONQUISTAS",
["ACHIEVEMENT_AGE_OF_HEROES_DESCRIPTION"] = "Vence todos os desafios da campanha no Modo Heroico.",
["ACHIEVEMENT_AGE_OF_HEROES_NAME"] = "Era dos Heróis",
["ACHIEVEMENT_ALL_THE_SMALL_THINGS_DESCRIPTION"] = "Elimine 182 Piscadores.",
["ACHIEVEMENT_ALL_THE_SMALL_THINGS_NAME"] = "Todas as Pequenas Coisas",
["ACHIEVEMENT_ARACHNED_DESCRIPTION"] = "Derrote Mygale, a Rainha Aranha.",
["ACHIEVEMENT_ARACHNED_NAME"] = "Um Adeus às Armas",
["ACHIEVEMENT_A_COON_OF_SURPRISES_DESCRIPTION"] = "Ajude Fredo a escapar.",
["ACHIEVEMENT_A_COON_OF_SURPRISES_NAME"] = "Um casulo de surpresas",
["ACHIEVEMENT_A_TEST_OF_PROWESS_DESCRIPTION"] = "Vença uma fase com 3 estrelas.",
["ACHIEVEMENT_A_TEST_OF_PROWESS_NAME"] = "Teste de Proeza",
["ACHIEVEMENT_BREAKER_OF_CHAINS_DESCRIPTION"] = "Salve os quatro elfos nas Minas Carmim.",
["ACHIEVEMENT_BREAKER_OF_CHAINS_NAME"] = "Quebrador de Correntes",
["ACHIEVEMENT_BUTTERTENTACLES_DESCRIPTION"] = "Complete a Torre da Aberração impedindo que Mydrias capture suas unidades.",
["ACHIEVEMENT_BUTTERTENTACLES_NAME"] = "Soldados Escorregadios",
["ACHIEVEMENT_BYE_BYE_BEAUTIFUL_DESCRIPTION"] = "Derrote a Vidente Mydrias.",
["ACHIEVEMENT_BYE_BYE_BEAUTIFUL_NAME"] = "Adeus, Adeus, Bela",
["ACHIEVEMENT_CIRCLE_OF_LIFE_DESCRIPTION"] = "Assista à apresentação do recém-nascido Arbórean.",
["ACHIEVEMENT_CIRCLE_OF_LIFE_NAME"] = "Círculo da Vida",
["ACHIEVEMENT_CLEANSE_THE_KING_DESCRIPTION"] = "Resgate o Rei Linireano.",
["ACHIEVEMENT_CLEANSE_THE_KING_NAME"] = "Glória ao Rei",
["ACHIEVEMENT_CLEANUP_IS_OPTIONAL_DESCRIPTION"] = "Complete as Periferias Devastadas sem limpar os escombros dos pontos estratégicos.",
["ACHIEVEMENT_CLEANUP_IS_OPTIONAL_NAME"] = "A limpeza é opcional",
["ACHIEVEMENT_CONJUNTIVICTORY_DESCRIPTION"] = "Derrote o Onividente.",
["ACHIEVEMENT_CONJUNTIVICTORY_NAME"] = "Conjuntivitória ",
["ACHIEVEMENT_CONQUEROR_OF_THE_VOID_DESCRIPTION"] = "Consiga 3 estrelas em cada fase do Vazio Além.",
["ACHIEVEMENT_CONQUEROR_OF_THE_VOID_NAME"] = "Conquistador do Vácuo",
["ACHIEVEMENT_CRAFTING_IN_THE_MINES_DESCRIPTION"] = "Recolha os três bifes de porco na Toca das Feras Selvagens.",
["ACHIEVEMENT_CRAFTING_IN_THE_MINES_NAME"] = "Fabricação nas Minas",
["ACHIEVEMENT_CROWD_CONTROL_DESCRIPTION"] = "Complete o Vale da Corrupção sem que nenhum Behemoth surja do poço.",
["ACHIEVEMENT_CROWD_CONTROL_NAME"] = "Controle de Multidão",
["ACHIEVEMENT_CROW_SCARER_DESCRIPTION"] = "Assuste todos os corvos no Vale Sombrio.",
["ACHIEVEMENT_CROW_SCARER_NAME"] = "Espantalho",
["ACHIEVEMENT_CRYSTAL_CLEAR_DESCRIPTION"] = "Consiga 3 estrelas em todas as fases do Cânion Abandonado.",
["ACHIEVEMENT_CRYSTAL_CLEAR_NAME"] = "Cristalino",
["ACHIEVEMENT_DARK_LIEUTENANT_DESCRIPTION"] = "Alcance o nível 10 com Raelyn.",
["ACHIEVEMENT_DARK_LIEUTENANT_NAME"] = "Tenente Sombria",
["ACHIEVEMENT_DARK_RUTHLESSNESS_DESCRIPTION"] = "Vence uma fase usando apenas torres e heróis do Exército das Trevas.",
["ACHIEVEMENT_DARK_RUTHLESSNESS_NAME"] = "Escuridão Implacável",
["ACHIEVEMENT_DISTURBING_THE_PEACE_DESCRIPTION"] = "Interrompa o descanso dos trabalhadores no Domo de Dominação.",
["ACHIEVEMENT_DISTURBING_THE_PEACE_NAME"] = "Perturbando a Paz",
["ACHIEVEMENT_DLC1_WIN_BOSS_DESCRIPTION"] = "Derrote Grymbeard e pare a construção da máquina de guerra.",
["ACHIEVEMENT_DLC1_WIN_BOSS_NAME"] = "Demissão em Massa",
["ACHIEVEMENT_DLC2_GATHER_ENVELOPS_DESCRIPTION"] = "Colete 8 hongbaos na Ilha Tempestade.",
["ACHIEVEMENT_DLC2_GATHER_ENVELOPS_NAME"] = "Desejamos a você riqueza e prosperidade.",
["ACHIEVEMENT_DLC2_WIN_BOSS_KING_DESCRIPTION"] = "Derrote o Rei Demônio Touro em sua fortaleza.",
["ACHIEVEMENT_DLC2_WIN_BOSS_KING_NAME"] = "O Retorno do Rei Macaco",
["ACHIEVEMENT_DLC2_WIN_BOSS_PRINCESS_DESCRIPTION"] = "Derrote a Princesa Leque de Ferro e seu exército aquático.",
["ACHIEVEMENT_DLC2_WIN_BOSS_PRINCESS_NAME"] = "Um Vento Maligno Está Surgindo",
["ACHIEVEMENT_DLC2_WIN_BOSS_REDBOY_DESCRIPTION"] = "Derrote o Menino Vermelho e seu exército de fogo.",
["ACHIEVEMENT_DLC2_WIN_BOSS_REDBOY_NAME"] = "Tudo mudou...",
["ACHIEVEMENT_DOMO_ARIGATO_DESCRIPTION"] = "Faça o punho gigante esmagar 20 inimigos no Núcleo Colossal.",
["ACHIEVEMENT_DOMO_ARIGATO_NAME"] = "Domo Arigato",
["ACHIEVEMENT_FACTORY_STRIKE_DESCRIPTION"] = "Complete a Montagem Frenética sem deixar o Grymbeard operar a maquinaria.",
["ACHIEVEMENT_FACTORY_STRIKE_NAME"] = "Greve na Fábrica",
["ACHIEVEMENT_FIELD_TRIP_RUINER_DESCRIPTION"] = "Apague o fogo do campista.",
["ACHIEVEMENT_FIELD_TRIP_RUINER_NAME"] = "Estraga-Passeios",
["ACHIEVEMENT_FOREST_PROTECTOR_DESCRIPTION"] = "Alcance o nível 10 com Nyru.",
["ACHIEVEMENT_FOREST_PROTECTOR_NAME"] = "Protetor da Floresta",
["ACHIEVEMENT_GARBAGE_DISPOSAL_DESCRIPTION"] = "Elimine 10 Mecânicos Loucos antes que criem Drones de Sucata.",
["ACHIEVEMENT_GARBAGE_DISPOSAL_NAME"] = "Eliminação de Lixo",
["ACHIEVEMENT_GEM_SPILLER_DESCRIPTION"] = "Quebre todas as cestas de gemas.",
["ACHIEVEMENT_GEM_SPILLER_NAME"] = "Derramador de Gemas",
["ACHIEVEMENT_GET_THE_PARTY_STARTED_DESCRIPTION"] = "Resolva o enigma e convoque a banda.",
["ACHIEVEMENT_GET_THE_PARTY_STARTED_NAME"] = "Que a Festa Comece ",
["ACHIEVEMENT_GIFT_OF_LIFE_DESCRIPTION"] = "Libere o clone experimental na Câmara Replicadora.",
["ACHIEVEMENT_GIFT_OF_LIFE_NAME"] = "O Dom da Vida",
["ACHIEVEMENT_GREENLIT_ALLIES_DESCRIPTION"] = "Invoque 10 Lanças-espinho Arborean.",
["ACHIEVEMENT_GREENLIT_ALLIES_NAME"] = "Aliados Aprovados",
["ACHIEVEMENT_HAIL_TO_THE_K_BABY_DESCRIPTION"] = "Encontre o rei dos crocodilos.",
["ACHIEVEMENT_HAIL_TO_THE_K_BABY_NAME"] = "Salve ao K, querido!",
["ACHIEVEMENT_HEARTLESS_VICTORY_DESCRIPTION"] = "Complete o Coração da Floresta sem usar o poder do Coração dos Arborean.",
["ACHIEVEMENT_HEARTLESS_VICTORY_NAME"] = "Vitória Sem Coração",
["ACHIEVEMENT_INTO_THE_OGREVERSE_DESCRIPTION"] = "Descubra os segredos da misteriosa pessoa-aranha.",
["ACHIEVEMENT_INTO_THE_OGREVERSE_NAME"] = "Vizinho Hostil",
["ACHIEVEMENT_IRONCLAD_DESCRIPTION"] = "Vence todos os desafios da campanha no Modo Ferro.",
["ACHIEVEMENT_IRONCLAD_NAME"] = "Blindado",
["ACHIEVEMENT_ITS_A_SECRET_TO_EVERYONE_DESCRIPTION"] = "Ajude Lank a pescar 5 rúpias.",
["ACHIEVEMENT_ITS_A_SECRET_TO_EVERYONE_NAME"] = "É segredo para todos",
["ACHIEVEMENT_KEPT_YOU_WAITING_DESCRIPTION"] = "Encontre o soldado sigiloso no Núcleo Colossal.",
["ACHIEVEMENT_KEPT_YOU_WAITING_NAME"] = "Te Fiz Esperar, Hein?",
["ACHIEVEMENT_LEARNING_THE_ROPES_DESCRIPTION"] = "Concluir o tutorial com 3 estrelas.",
["ACHIEVEMENT_LEARNING_THE_ROPES_NAME"] = "Aprendendo como se faz",
["ACHIEVEMENT_LINIREAN_RESISTANCE_DESCRIPTION"] = "Vença uma fase usando apenas torres e heróis Linireanos.",
["ACHIEVEMENT_LINIREAN_RESISTANCE_NAME"] = "Resistência Linireana",
["ACHIEVEMENT_LUCAS_SPIDER_DESCRIPTION"] = "Brinque com Lucus até que ele esteja feliz.",
["ACHIEVEMENT_LUCAS_SPIDER_NAME"] = "Lucus, a Aranha",
["ACHIEVEMENT_MASTER_TACTICIAN_DESCRIPTION"] = "Complete a campanha na dificuldade Impossível.",
["ACHIEVEMENT_MASTER_TACTICIAN_NAME"] = "Mestre Tático",
["ACHIEVEMENT_MECHANICAL_BURNOUT_DESCRIPTION"] = "Alimente demais a máquina nas Portas da Forja.",
["ACHIEVEMENT_MECHANICAL_BURNOUT_NAME"] = "Exaustão Mecânica",
["ACHIEVEMENT_MIGHTY_III_DESCRIPTION"] = "Mate 10000 inimigos.",
["ACHIEVEMENT_MIGHTY_III_NAME"] = "Poderoso III",
["ACHIEVEMENT_MIGHTY_II_DESCRIPTION"] = "Mate 3000 inimigos.",
["ACHIEVEMENT_MIGHTY_II_NAME"] = "Poderoso II",
["ACHIEVEMENT_MIGHTY_I_DESCRIPTION"] = "Mate 500 inimigos.",
["ACHIEVEMENT_MIGHTY_I_NAME"] = "Poderoso I",
["ACHIEVEMENT_MOST_DELICIOUS_DESCRIPTION"] = "Dê um pouco de mel para o Biggie, o Arborean.",
["ACHIEVEMENT_MOST_DELICIOUS_NAME"] = "Mais Delicioso",
["ACHIEVEMENT_NATURES_WRATH_DESCRIPTION"] = "Mate 30 inimigos usando o Coração dos Arboreans.",
["ACHIEVEMENT_NATURES_WRATH_NAME"] = "Ira da Natureza",
["ACHIEVEMENT_NONE_SHALL_PASS_DESCRIPTION"] = "Complete a Toca das Feras Selvagens sem deixar inimigos extras passarem pela porta.",
["ACHIEVEMENT_NONE_SHALL_PASS_NAME"] = "Ninguém Passará!",
["ACHIEVEMENT_NOT_A_MOMENT_TO_WASTE_DESCRIPTION"] = "Chame 15 ondas mais cedo.",
["ACHIEVEMENT_NOT_A_MOMENT_TO_WASTE_NAME"] = "Nenhum Momento a Perder",
["ACHIEVEMENT_NO_FLY_ZONE_DESCRIPTION"] = "Elimine 50 Aranhas Flutuantes.",
["ACHIEVEMENT_NO_FLY_ZONE_NAME"] = "Zona de exclusão aérea",
["ACHIEVEMENT_OBLITERATE_DESCRIPTION"] = "Encontre as partes do robô proibido em cada cenário de Ameaça Colossal.",
["ACHIEVEMENT_OBLITERATE_NAME"] = "Manifeste-se!",
["ACHIEVEMENT_ONE_SHOT_TOWER_DESCRIPTION"] = "Elimine 10 inimigos com um único raio da Torre Raio Sombrio.",
["ACHIEVEMENT_ONE_SHOT_TOWER_NAME"] = "Um Tiro de Glória",
["ACHIEVEMENT_OUTBACK_BARBEQUICK_DESCRIPTION"] = "Derrote o Goregrind antes de pular em dificuldade Impossível.",
["ACHIEVEMENT_OUTBACK_BARBEQUICK_NAME"] = "Emaranhado",
["ACHIEVEMENT_OVER_THE_EDGE_DESCRIPTION"] = "Empurre os Arboreans dos topos das árvores.",
["ACHIEVEMENT_OVER_THE_EDGE_NAME"] = "Fim de jogo",
["ACHIEVEMENT_OVINE_JOURNALISM_DESCRIPTION"] = "Encontre o Sheepy em cada terreno da campanha.",
["ACHIEVEMENT_OVINE_JOURNALISM_NAME"] = "Jornalismo Ovino",
["ACHIEVEMENT_PEST_CONTROL_DESCRIPTION"] = "Mate 300 Glarelings.",
["ACHIEVEMENT_PEST_CONTROL_NAME"] = "Controle de Pragas",
["ACHIEVEMENT_PLAYFUL_FRIENDS_DESCRIPTION"] = "Jogue \"tocas\" com todos os Arboreans no Coração da Floresta.",
["ACHIEVEMENT_PLAYFUL_FRIENDS_NAME"] = "Amigos Brincalhões",
["ACHIEVEMENT_PORKS_OFF_THE_MENU_DESCRIPTION"] = "Derrote o Goregrind.",
["ACHIEVEMENT_PORKS_OFF_THE_MENU_NAME"] = "Porco Fora do Cardápio",
["ACHIEVEMENT_PROMOTION_DENIED_DESCRIPTION"] = "Mate 30 Sacerdotes do Culto antes que eles se transformem em Abominações.",
["ACHIEVEMENT_PROMOTION_DENIED_NAME"] = "Promoção Negada",
["ACHIEVEMENT_ROCK_BEATS_ROCK_DESCRIPTION"] = "Faça com que a estátua se derrote sozinha.",
["ACHIEVEMENT_ROCK_BEATS_ROCK_NAME"] = "Pedra Vence... Pedra?",
["ACHIEVEMENT_ROOM_achievement_claim"] = "Reivindique sua Recompensa!",
["ACHIEVEMENT_ROYAL_CAPTAIN_DESCRIPTION"] = "Alcance o nível 10 com Vesper.",
["ACHIEVEMENT_ROYAL_CAPTAIN_NAME"] = "Capitão Real",
["ACHIEVEMENT_RUNEQUEST_DESCRIPTION"] = "Ative todas as seis runas ao longo da Floresta Radiante.",
["ACHIEVEMENT_RUNEQUEST_NAME"] = "Runequest",
["ACHIEVEMENT_RUST_IN_PEACE_DESCRIPTION"] = "Complete um nível sem permitir que nenhuma Armadura Animada reviva.",
["ACHIEVEMENT_RUST_IN_PEACE_NAME"] = "Enferruje em Paz",
["ACHIEVEMENT_SAVIOUR_OF_THE_FOREST_DESCRIPTION"] = "Vença a etapa sem perder nenhuma flor arbórea.",
["ACHIEVEMENT_SAVIOUR_OF_THE_FOREST_NAME"] = "Salvador da floresta",
["ACHIEVEMENT_SAVIOUR_OF_THE_GREEN_DESCRIPTION"] = "Consiga 3 estrelas em todas as fases da Floresta Radiante.",
["ACHIEVEMENT_SAVIOUR_OF_THE_GREEN_NAME"] = "Salvador do Verde",
["ACHIEVEMENT_SCRAMBLED_EGGS_DESCRIPTION"] = "Mate 50 crokinder antes de eles eclodirem.",
["ACHIEVEMENT_SCRAMBLED_EGGS_NAME"] = "Ovos mexidos",
["ACHIEVEMENT_SEASONED_GENERAL_DESCRIPTION"] = "Complete a campanha na dificuldade Veterano.",
["ACHIEVEMENT_SEASONED_GENERAL_NAME"] = "General Experiente",
["ACHIEVEMENT_SEE_YA_LATER_ALLIGATOR_DESCRIPTION"] = "Derrote Abominor, o devorador.",
["ACHIEVEMENT_SEE_YA_LATER_ALLIGATOR_NAME"] = "Até mais tarde, jacaré",
["ACHIEVEMENT_SHUT_YOUR_MOUTH_DESCRIPTION"] = "Complete o Domo de Dominação evitando que Grymbeard incinere suas torres.",
["ACHIEVEMENT_SHUT_YOUR_MOUTH_NAME"] = "Feche Essa Boca!",
["ACHIEVEMENT_SIGNATURE_TECHNIQUES_DESCRIPTION"] = "Use os Poderes do Herói 500 vezes.",
["ACHIEVEMENT_SIGNATURE_TECHNIQUES_NAME"] = "Técnicas Distintivas",
["ACHIEVEMENT_SILVER_FOR_MONSTERS_DESCRIPTION"] = "Ajude Gerhart a matar o monstro árvore.",
["ACHIEVEMENT_SILVER_FOR_MONSTERS_NAME"] = "Prata para Monstros",
["ACHIEVEMENT_SMOOTH_OPER_GATOR_DESCRIPTION"] = "Ajude o jacaré amistoso a iniciar seu barco.",
["ACHIEVEMENT_SMOOTH_OPER_GATOR_NAME"] = "Oper-gator Suave",
["ACHIEVEMENT_SPECTRAL_FURY_DESCRIPTION"] = "Derrote Navira e impeça a invasão de Aparições.",
["ACHIEVEMENT_SPECTRAL_FURY_NAME"] = "Fúria Espectral",
["ACHIEVEMENT_STARLIGHT_DESCRIPTION"] = "Ajude Fredo e Sammy a escapar da Aranha Gigante.",
["ACHIEVEMENT_STARLIGHT_NAME"] = "Luz das estrelas",
["ACHIEVEMENT_TAKE_ME_HOME_DESCRIPTION"] = "Retorne Riff, o Goblin, para sua dimensão de origem.",
["ACHIEVEMENT_TAKE_ME_HOME_NAME"] = "Take On Me",
["ACHIEVEMENT_THE_CAVALRY_IS_HERE_DESCRIPTION"] = "Convoque 1000 reforços.",
["ACHIEVEMENT_THE_CAVALRY_IS_HERE_NAME"] = "A cavalaria chegou!",
["ACHIEVEMENT_TIPPING_THE_SCALES_DESCRIPTION"] = "Lance Robin Wood ao rio.",
["ACHIEVEMENT_TIPPING_THE_SCALES_NAME"] = "Inclinando a Balança",
["ACHIEVEMENT_TREE_HUGGER_DESCRIPTION"] = "Complete as Ruínas Nebulosas com pelo menos um Bosquestranho em pé.",
["ACHIEVEMENT_TREE_HUGGER_NAME"] = "Amante das Árvores",
["ACHIEVEMENT_TURN_A_BLIND_EYE_DESCRIPTION"] = "Mate 100 Proles da Corrupção enquanto estiverem sob os efeitos do Brilho.",
["ACHIEVEMENT_TURN_A_BLIND_EYE_NAME"] = "Fazer Vista Grossa",
["ACHIEVEMENT_UNBOUND_VICTORY_DESCRIPTION"] = "Complete a Travessia Perversa sem que nenhum Pesadelo se transforme em Pesadelo Amarrado.",
["ACHIEVEMENT_UNBOUND_VICTORY_NAME"] = "Vitória Desamarrada",
["ACHIEVEMENT_UNENDING_RICHES_DESCRIPTION"] = "Colete um total de 150000 ouro.",
["ACHIEVEMENT_UNENDING_RICHES_NAME"] = "Riquezas Infindáveis",
["ACHIEVEMENT_UNTAMED_BEAST_DESCRIPTION"] = "Alcance o nível 10 com Grimson.",
["ACHIEVEMENT_UNTAMED_BEAST_NAME"] = "Fera Indomável",
["ACHIEVEMENT_WAR_MASONRY_DESCRIPTION"] = "Construa 100 torres.",
["ACHIEVEMENT_WAR_MASONRY_NAME"] = "Alvenaria de Guerra",
["ACHIEVEMENT_WEIRDER_THINGS_DESCRIPTION"] = "Ajude Ernie e Daston a repelir os Piscantes nas Campinas Assoladas.",
["ACHIEVEMENT_WEIRDER_THINGS_NAME"] = "Coisas Mais Estranhas",
["ACHIEVEMENT_WE_ARE_ALL_MAD_HERE_DESCRIPTION"] = "Encontre o gato furtivo em cada nível da campanha de Fúria Eterna.",
["ACHIEVEMENT_WE_ARE_ALL_MAD_HERE_NAME"] = "Aqui Somos Todos Loucos",
["ACHIEVEMENT_WE_RE_NOT_GONNA_TAKE_IT_DESCRIPTION"] = "Mate 15 Irmãs Retorcidas antes que elas possam gerar um Pesadelo.",
["ACHIEVEMENT_WE_RE_NOT_GONNA_TAKE_IT_NAME"] = "Nós Não Vamos Aceitar",
["ACHIEVEMENT_WOBBA_LUBBA_DUB_DUB_DESCRIPTION"] = "Conserte a arma de portal de Nick e Marty.",
["ACHIEVEMENT_WOBBA_LUBBA_DUB_DUB_NAME"] = "Wobba-Lubba-Dub-Dub!",
["ACHIEVEMENT_YOU_SHALL_NOT_CAST_DESCRIPTION"] = "Salve o Rey Denas sem deixar a Vidente Mydrias lançar projeções na Dificuldade Impossível.",
["ACHIEVEMENT_YOU_SHALL_NOT_CAST_NAME"] = "Não Deverás Lançar Feitiços!",
["ADS_MESSAGE_OK"] = "Ok",
["ADS_MESSAGE_TITLE"] = "MAIS GEMAS",
["ADS_NO_REWARD_VIDEO_AVAILABLE"] = "Não há vídeos de recompensas no momento. Tente de novo mais tarde.",
["ADS_REWARD_EARNED"] = "Você ganhou %i joias por assistir o vídeo",
["ADVANCED TOWERS"] = "TORRES AVANÇADAS",
["ALERT_VERSION"] = "Uma versão mais recente do jogo está disponível. Baixe na loja.",
["ALL FOR"] = "TUDO POR APENAS",
["ARCHER TOWER"] = "TORRE ARQUEIRA",
["ARE YOU SURE YOU WANT TO QUIT?"] = "TEM CERTEZA DE QUE DESEJA SAIR?",
["ARMORED ENEMIES!"] = "Inimigos com armadura",
["ARTILLERY"] = "ARTILHARIA",
["Achievements"] = "Conquistas",
["BARRACKS"] = "QUARTEL",
["BASIC TOWERS"] = "TORRES BÁSICAS",
["BEST VALUE"] = "MELHOR OFERTA",
["BOSS_BULL_KING_DESCRIPTION"] = "Um líder implacável e autoritário, veterano de guerra e estrategista pragmático. Famoso por sua imensa força, personalidade rancorosa e destreza marcial.",
["BOSS_BULL_KING_EXTRA"] = "-Alta armadura e resistência mágica\n- Grande atordoamento em área em unidades e torres",
["BOSS_BULL_KING_NAME"] = "Rei Demônio Touro",
["BOSS_CORRUPTED_DENAS_DESCRIPTION"] = "O rei derrotado de Linirea, agora transformado em uma abominação imponente pelos poderes sombrios do Culto do Onividente.",
["BOSS_CORRUPTED_DENAS_EXTRA"] = "- Gera Glarelings ",
["BOSS_CORRUPTED_DENAS_NAME"] = "Denas Corrompido",
["BOSS_CROCS_DESCRIPTION"] = "A fome personificada, Um ser antigo capaz de devorar o próprio mundo se deixado sem controle.",
["BOSS_CROCS_EXTRA"] = "- Come torres\n- Evolui após satisfazer sua fome\n- Invoca crokinders",
["BOSS_CROCS_LVL1_DESCRIPTION"] = "A fome personificada, Um ser antigo capaz de devorar o próprio mundo se deixado sem controle.",
["BOSS_CROCS_LVL1_EXTRA"] = "- Come torres\n- Evolui após satisfazer sua fome\n- Invoca crokinders",
["BOSS_CROCS_LVL1_NAME"] = "Abominor",
["BOSS_CROCS_LVL2_DESCRIPTION"] = "A fome personificada, Um ser antigo capaz de devorar o próprio mundo se deixado sem controle.",
["BOSS_CROCS_LVL2_EXTRA"] = "- Come torres\n- Evolui após satisfazer sua fome\n- Invoca crokinders",
["BOSS_CROCS_LVL2_NAME"] = "Abominor",
["BOSS_CROCS_LVL3_DESCRIPTION"] = "A fome personificada, Um ser antigo capaz de devorar o próprio mundo se deixado sem controle.",
["BOSS_CROCS_LVL3_EXTRA"] = "- Come torres\n- Evolui após satisfazer sua fome\n- Invoca crokinders",
["BOSS_CROCS_LVL3_NAME"] = "Abominor",
["BOSS_CROCS_LVL4_DESCRIPTION"] = "A fome personificada, Um ser antigo capaz de devorar o próprio mundo se deixado sem controle.",
["BOSS_CROCS_LVL4_EXTRA"] = "- Come torres\n- Evolui após satisfazer sua fome\n- Invoca crokinders",
["BOSS_CROCS_LVL4_NAME"] = "Abominor",
["BOSS_CROCS_LVL5_DESCRIPTION"] = "A fome personificada, Um ser antigo capaz de devorar o próprio mundo se deixado sem controle.",
["BOSS_CROCS_LVL5_EXTRA"] = "- Come torres\n- Evolui após satisfazer sua fome\n- Invoca crokinders",
["BOSS_CROCS_LVL5_NAME"] = "Abominor",
["BOSS_CROCS_NAME"] = "Abominor",
["BOSS_CULT_LEADER_DESCRIPTION"] = "O atual líder do Culto, Mydrias atua como a mão do Onividente, orquestrando a invasão de mundos.",
["BOSS_CULT_LEADER_EXTRA"] = "- Alta armadura e resistência mágica enquanto não está bloqueada\n - Alto dano em área",
["BOSS_CULT_LEADER_NAME"] = "Vidente Mydrias",
["BOSS_GRYMBEARD_DESCRIPTION"] = "Um anão egomaníaco com delírios de grandeza, tão perigoso quanto insano.",
["BOSS_GRYMBEARD_EXTRA"] = "Lança um punho foguete em direção às unidades do jogador.",
["BOSS_GRYMBEARD_NAME"] = "Grymbeard",
["BOSS_MACHINIST_DESCRIPTION"] = "No topo de sua última invenção, Grymbeard persegue seus inimigos fazendo chover fogo e metal.",
["BOSS_MACHINIST_EXTRA"] = "- Voador\n- Dispara sucata nas unidades",
["BOSS_MACHINIST_NAME"] = "Grymbeard",
["BOSS_NAVIRA_DESCRIPTION"] = "Caído em desgraça e intrometendo-se com magia sombria, Navira busca devolver aos elfos seu status de glória.",
["BOSS_NAVIRA_EXTRA"] = "- Bloqueia torres com bolas de fogo\n- Transforma-se em um tornado imbloqueável",
["BOSS_NAVIRA_NAME"] = "Navira",
["BOSS_PIG_DESCRIPTION"] = "O único e auto-proclamado Rei das Feras Selvagens usa um mangual gigantesco para esmagar seus inimigos.",
["BOSS_PIG_EXTRA"] = "- Pula grandes distâncias entre caminhos",
["BOSS_PIG_NAME"] = "Goregrind",
["BOSS_PRINCESS_IRON_FAN_DESCRIPTION"] = "Elegante mas mortal, a Princesa Leque de Ferro empunha o lendário Leque de Ferro, capaz de extinguir chamas e provocar tempestades.",
["BOSS_PRINCESS_IRON_FAN_EXTRA"] = "- Clona a si mesma\n- Prende heróis em um frasco\n- Transforma torres em geradores de inimigos",
["BOSS_PRINCESS_IRON_FAN_NAME"] = "Princess Iron Fan",
["BOSS_REDBOY_TEEN_DESCRIPTION"] = "O jovem príncipe demônio ardente e orgulhoso. Temperamental, arrogante e implacavelmente ambicioso. Mestre do Fogo Samadhi e habilidoso lanceiro.",
["BOSS_REDBOY_TEEN_EXTRA"] = "- Grande ataque em área (dano)\n- Ordena ao seu dragão que atordoe torres",
["BOSS_REDBOY_TEEN_NAME"] = "Red Boy",
["BOSS_SPIDER_QUEEN_DESCRIPTION"] = "Uma antiga Rainha Aranha, uma força primordial despertada de seu sono para recuperar o que é seu por direito.",
["BOSS_SPIDER_QUEEN_EXTRA"] = "- Atordoa torres\n- Drena vida dos inimigos próximos\n- Invoca Aranhas Vampiras\n- Joga teias nos seus olhos",
["BOSS_SPIDER_QUEEN_NAME"] = "Mygale",
["BRIEFING_LEVEL_WARNING"] = "Esta campanha tem um nível de dificuldade alto.",
["BUTTON_BUG_CRASH"] = "FALHA DO JOGO",
["BUTTON_BUG_OTHER"] = "OUTRO",
["BUTTON_BUG_REPORT"] = "ERRO",
["BUTTON_BUY"] = "COMPRAR",
["BUTTON_BUY_UPGRADE"] = "COMPRAR MELHORIA",
["BUTTON_CLOSE"] = "FECHAR",
["BUTTON_CONFIRM"] = "CONFIRME",
["BUTTON_CONTINUE"] = "CONTINUAR",
["BUTTON_DISABLE"] = "Desativar",
["BUTTON_DONE"] = "PRONTO",
["BUTTON_ENDLESS_QUIT"] = "SAIR",
["BUTTON_ENDLESS_TRYAGAIN"] = "TENTAR DE NOVO",
["BUTTON_GET_GEMS"] = "COMPRAR ITENS",
["BUTTON_LEVEL_SELECT_FIGHT"] = "LUTE!",
["BUTTON_LOST_CONTENT"] = "CONTEÚDO PERDIDO",
["BUTTON_MAIN_MENU"] = "MENU PRINCIPAL",
["BUTTON_NO"] = "NÃO",
["BUTTON_OK"] = "OK!",
["BUTTON_QUIT"] = "SAIR",
["BUTTON_RESET"] = "RESTAURAR",
["BUTTON_RESTART"] = "REINICIAR",
["BUTTON_UNDO"] = "DESFAZER",
["BUTTON_YES"] = "SIM",
["BUY UPGRADES!"] = "COMPRE MELHORIAS!",
["Basic Tower Types"] = "Torres: Tipos Básicos",
["CARD_REWARDS_CAMPAIGN"] = "Nova campanha",
["CARD_REWARDS_DLC_1"] = "Ameaça Colossal",
["CARD_REWARDS_DLC_2"] = "A Jornada de Wukong",
["CARD_REWARDS_HERO"] = "NOVO HERÓI!",
["CARD_REWARDS_TOWER"] = "NOVA TORRE!",
["CARD_REWARDS_TOWER_LEVEL"] = "NOVO NÍVEL DE TORRE!",
["CARD_REWARDS_TOWER_LEVEL_PREFIX"] = "NÍV.",
["CARD_REWARDS_UPDATE_01"] = "Fúria Eterna",
["CARD_REWARDS_UPDATE_02"] = "Fome Antiga",
["CARD_REWARDS_UPDATE_03"] = "Aracnofobia",
["CARD_REWARDS_UPGRADES"] = "PONTOS DE MELHORIA!",
["CArmor0"] = "Nenhuma",
["CArmor1"] = "Pequena",
["CArmor2"] = "Média",
["CArmor3"] = "Grande",
["CArmor4"] = "Gigante",
["CArmor9"] = "Imune",
["CArmorSmall0"] = "N/D",
["CArmorSmall1"] = "Peq.",
["CArmorSmall2"] = "Méd.",
["CArmorSmall3"] = "Grd.",
["CArmorSmall4"] = "Gig.",
["CArmorSmall9"] = "Imu.",
["CHALLENGE_RULE_DIFFICULTY_CASUAL"] = "Casual",
["CHALLENGE_RULE_DIFFICULTY_IMPOSSIBLE"] = "Impossível",
["CHALLENGE_RULE_DIFFICULTY_NORMAL"] = "Normal",
["CHALLENGE_RULE_DIFFICULTY_VETERAN"] = "Veterano",
["CHANGE_LANGUAGE_QUESTION"] = "Tem certeza que deseja alterar as configurações de idioma?",
["CINEMATICS_TAP_TO_CONTINUE"] = "Toque e continue...",
["CINEMATICS_TAP_TO_CONTINUE_KR1"] = "toque e continue...",
["CINEMATICS_TAP_TO_CONTINUE_KR2"] = "toque e continue...",
["CINEMATICS_TAP_TO_CONTINUE_KR3"] = "toque e continue...",
["CINEMATICS_TAP_TO_CONTINUE_KR5"] = "toque e continue...",
["CLAIM_GIFT"] = "Resgatar presente",
["CLOUDSYNC_PLEASE_WAIT"] = "Atualizando jogos salvos na nuvem ...",
["CLOUD_DIALOG_NO"] = "Não",
["CLOUD_DIALOG_OK"] = "Ok",
["CLOUD_DIALOG_YES"] = "Sim",
["CLOUD_DOWNLOAD_QUESTION"] = "Baixar jogo salvo da iCloud?",
["CLOUD_DOWNLOAD_TITLE"] = "Baixar da iCloud",
["CLOUD_SAVE"] = "Salvamento na nuvem",
["CLOUD_SAVE_DISABLE_EXTRA"] = "Nota: Você pode perder o progresso do jogo se o jogo for desinstalado.",
["CLOUD_SAVE_DISABLE_GENERIC_DESCRIPTION"] = "Tem certeza de que quer desativar o salvamento do progresso do jogo na nuvem?",
["CLOUD_SAVE_OFF"] = "Nuvem desligada",
["CLOUD_SAVE_ON"] = "Nuvem ligada",
["CLOUD_UPLOAD_QUESTION"] = "Enviar jogo salvo para iCloud?",
["CLOUD_UPLOAD_TITLE"] = "Enviar para iCloud",
["COMIC_10_1_KR5_KR5"] = "Soltem-me! Estou fazendo o melhor para o reino!",
["COMIC_10_2_KR5_KR5"] = "Acabe com esta blasfêmia, irmão. Esse não é o caminho dos elfos.",
["COMIC_10_3_KR5_KR5"] = "Obrigado, meu velho aprendiz. Nós cuidaremos disso.",
["COMIC_10_4_KR5_KR5"] = "Mais tarde, no acampamento...",
["COMIC_10_5_KR5_KR5"] = "Então... tem certeza de que podemos confiar em Vez'nan?",
["COMIC_10_6_KR5_KR5"] = "Estamos de olho nele...",
["COMIC_10_7_KR5_KR5"] = "...por enquanto, está se comportando bem.",
["COMIC_10_8_KR5_KR5"] = "Ha. Por enquanto...",
["COMIC_11_1_KR5_KR5"] = "O pântano parece ter despertado...",
["COMIC_11_2_KR5_KR5"] = "...como se estivesse nos observando...",
["COMIC_11_3_KR5_KR5"] = "...avançando e espreitando...",
["COMIC_11_4_KR5_KR5"] = "...pronto para nos devorar.",
["COMIC_11_5_KR5_KR5"] = "Tenha cuidado!",
["COMIC_11_6_KR5_KR5"] = "Estamos sob ataque!",
["COMIC_11_7_KR5_KR5"] = "Vai, pequeno fogo-fátuo! Nossa segurança depende da tua pressa!",
["COMIC_12_1_KR5_KR5"] = "Simplesmente te trancar foi um erro. Um que não repetirei.",
["COMIC_12_2_KR5_KR5"] = "NÃÃÃÃOOO!!!",
["COMIC_12_3_KR5_KR5"] = "Eu te bano para sempre!!",
["COMIC_12_4_KR5_KR5"] = "Cof!",
["COMIC_12_5_KR5_KR5"] = "Cof, Cof!",
["COMIC_12_6_KR5_KR5"] = "Uh-Acho que devo estar fora de prática.",
["COMIC_13_1_KR5_KR5"] = "Disseram que era loucura.",
["COMIC_13_2_KR5_KR5"] = "Que uma arma dessas era impossível.",
["COMIC_13_3_KR5_KR5"] = "Mas em breve eles saberão o quanto estavam errados...",
["COMIC_13_4_KR5_KR5"] = "...e se renderão diante do gênio de Grymbeard!",
["COMIC_14_1_KR5_KR5"] = "O que faremos com eles?",
["COMIC_14_2_KR5_KR5"] = "Deixe comigo!",
["COMIC_14_3_KR5_KR5"] = "Conheço um bom lugar.",
["COMIC_14_4_KR5_KR5"] = "Isso é tudo?",
["COMIC_14_5_KR5_KR5"] = "Que Grymbeard apodreça na prisão?",
["COMIC_14_6_KR5_KR5"] = "Ao contrário, amiguinho...",
["COMIC_14_7_KR5_KR5"] = "...tenho grandes planos para esse cérebro seu!",
["COMIC_15_10_KR5_KR5"] = "...mas não em boas condições.",
["COMIC_15_1_KR5_KR5"] = "Em algum lugar na montanha.",
["COMIC_15_2_KR5_KR5"] = "Ei, Goblin!",
["COMIC_15_3_KR5_KR5"] = "Mãos à obra!",
["COMIC_15_4_KR5_KR5"] = "Você precisa entregar uma mensagem.",
["COMIC_15_5_KR5_KR5"] = "Devemos enviar mais batedores. Não podemos ficar tranquilos com todos esses cultistas à solta.",
["COMIC_15_6_KR5_KR5"] = "Poderíamos enviar alguns espíritos para ajudar; eles...",
["COMIC_15_7_KR5_KR5"] = "Notícias urgentes!",
["COMIC_15_8_KR5_KR5"] = "Bem...",
["COMIC_15_9_KR5_KR5"] = "Encontramos nossos batedores...",
["COMIC_16_1_KR5_KR5"] = "Serei vingada!",
["COMIC_16_2_KR5_KR5"] = "Minha irmã...o quêêê?",
["COMIC_17_10_KR5_KR5"] = "Se não os detivermos, eles destruirão todos os reinos!",
["COMIC_17_11_KR5_KR5"] = "Temos que ajudá-lo!",
["COMIC_17_12_KR5_KR5"] = "Ah, com certeza.",
["COMIC_17_13_KR5_KR5"] = "Claro que sim…",
["COMIC_17_1_KR5_KR5"] = "Bela tarde, não é?",
["COMIC_17_2_KR5_KR5"] = "Eu poderia me acostumar com essa paz.",
["COMIC_17_3_KR5_KR5"] = "Melhor não.",
["COMIC_17_4_KR5_KR5"] = "Sol, é você?! Podia só ter acenado, sabia…",
["COMIC_17_5_KR5_KR5"] = "Amigos, algo terrível aconteceu...",
["COMIC_17_6_KR5_KR5"] = "Eu estava meditando em paz na minha tartaruga quando...",
["COMIC_17_7_KR5_KR5"] = "Os Três Reis Demônios apareceram do nada!",
["COMIC_17_8_KR5_KR5"] = "Claro que lutei com valentia, mas...",
["COMIC_17_9_KR5_KR5"] = "Eles tomaram desonrosamente minhas esferas celestiais!",
["COMIC_18_1_KR5_KR5"] = "Perto das margens do covil do Rei Demônio Touro...",
["COMIC_18_2_KR5_KR5"] = "Alvo avistado!",
["COMIC_18_3_KR5_KR5"] = "Vamos explodir aquela fortaleza!",
["COMIC_18_4_KR5_KR5"] = "Minhas muralhas riem das suas pedrinhas!",
["COMIC_18_5_KR5_KR5"] = "Pela Linirea!",
["COMIC_18_6_KR5_KR5"] = "Recuem, rapazes! Precisamos de uma abertura aqui!",
["COMIC_19_1_KR5_KR5"] = "As esferas celestiais não podem permanecer sob sua custódia, é absurdo!",
["COMIC_19_2_KR5_KR5"] = "É, tenha cuidado com isso, amigo.",
["COMIC_19_3_KR5_KR5"] = "Você foi muito sábio, nobre macaco!",
["COMIC_19_4_KR5_KR5"] = "O que vou fazer com vocês três?",
["COMIC_1_1_KR5"] = "Já faz um mês desde que chegamos a esta terra, à procura do nosso Rei perdido...",
["COMIC_1_2B_KR5"] = "...Depois de ter sido banido por Vez'nan, o mago das trevas.",
["COMIC_1_4_KR5"] = "Encontramos um lugar e montamos o acampamento para recuperar nossas forças...",
["COMIC_1_5_KR5"] = "...em paz...",
["COMIC_1_8_KR5"] = "...Mas parece que isso acabou agora.",
["COMIC_2_1_KR5"] = "Viva!",
["COMIC_2_3_KR5"] = "Vez'nan?!",
["COMIC_2_4a_KR5"] = "Calma... Eu venho propor...",
["COMIC_2_4b_KR5"] = "...um acordo.",
["COMIC_2_5_KR5"] = "Depois do que você fez ao nosso reino?",
["COMIC_2_6_KR5"] = "Era necessário abrir os olhos do rei Denas.",
["COMIC_2_7_KR5"] = "Ele se recusou a ver o perigo que infestava o reino.",
["COMIC_2_8_1_KR5"] = "Mas deixe-nos encontrar o seu rei...",
["COMIC_2_8_2_KR5"] = "...e ponhamos um fim a esta ameaça.",
["COMIC_2_8b_KR5"] = "...juntos.",
["COMIC_3_1_KR5"] = "Nossa! O que temos aqui...?",
["COMIC_3_2_KR5"] = "A poderosa espada de Elynie!",
["COMIC_3_3_KR5"] = "Ai!",
["COMIC_3_4a_KR5"] = "Claro...",
["COMIC_3_4b_KR5"] = "Pare de perder tempo!",
["COMIC_3_5a_KR5"] = "Ah... mas ele está mais perto do que você pensa.",
["COMIC_3_5b_KR5"] = "Nosso rei ainda está desaparecido.",
["COMIC_3_6_KR5"] = "Pode ser uma batalha difícil, no entanto.",
["COMIC_4_10a_KR5"] = "Ha! Sempre estive.",
["COMIC_4_10b_KR5"] = "Então... o que acontece agora?",
["COMIC_4_11_KR5"] = "Podemos ter nossas diferenças...",
["COMIC_4_12_KR5"] = "...mas temos uma ameaça maior em comum.",
["COMIC_4_1_KR5"] = "Elynie...",
["COMIC_4_2_KR5"] = "...dê-lhe força!",
["COMIC_4_4_KR5"] = "Aaurrrgh!",
["COMIC_4_7a_KR5"] = "Vejo que suas 'férias' fizeram maravilhas por você!",
["COMIC_4_7b_KR5"] = "VOCÊ!!!",
["COMIC_4_8_KR5"] = "Você deveria pagar por suas travessuras!",
["COMIC_4_9_KR5"] = "Mas você estava certo.",
["COMIC_5_1_KR2"] = "Vitória!",
["COMIC_5_1_KR5_KR5"] = "Vocês vermes não podem parar...",
["COMIC_5_2_KR2"] = "Vitória!",
["COMIC_5_2_KR5_KR5"] = "O NOVO MUNDO!",
["COMIC_5_6_KR5_KR5"] = "Acordou!",
["COMIC_5_7a_KR5_KR5"] = "Então é isso...",
["COMIC_5_7b_KR5_KR5"] = "o confronto final.",
["COMIC_6_1a_KR5_KR5"] = "Você é corajoso me desafiando...",
["COMIC_6_1b_KR5_KR5"] = "mas isso não tem lugar aqui!",
["COMIC_6_4_KR5_KR5"] = "Ei!",
["COMIC_6_5_KR5_KR5"] = "Você, lesma cósmica...",
["COMIC_6_6_KR5_KR5"] = "...subestima MEU poder!!!",
["COMIC_6_8_KR5_KR5"] = "Prepare-se. Não consigo segurá-lo por muito tempo!",
["COMIC_7_1_KR5_KR5"] = "NÃO! Isso... não pode ser!!!",
["COMIC_7_3_KR5_KR5"] = "Então... e agora?",
["COMIC_7_4a_KR5_KR5"] = "Bem, minha missão está cumprida...",
["COMIC_7_4b_KR5_KR5"] = "...e eu acho que eles precisam do seu rei.",
["COMIC_7_5_2_KR2"] = "Nada",
["COMIC_7_6_KR5_KR5"] = "Até a próxima vez, caro inimigo.",
["COMIC_7_7_KR5_KR5"] = "Mais tarde, na Floresta Radiante",
["COMIC_8_1_KR5_KR5"] = "Ah, finalmente!",
["COMIC_8_2_KR5_KR5"] = "Este poder é, mais uma vez...",
["COMIC_8_4_KR5_KR5"] = "... MEU!",
["COMIC_8_5_KR5_KR5"] = "MUA HA HA HA HA!",
["COMIC_9_1_KR5_KR5"] = "Há não muito tempo, os elfos éramos admirados por nossa magia e graça...",
["COMIC_9_2_KR5_KR5"] = "...até que nossa relíquia sagrada foi corrompida, e nos tornamos sombras do que éramos.",
["COMIC_9_3_KR5_KR5"] = "Mas com este exército, restabelecerei nossa glória...",
["COMIC_9_4_KR5_KR5"] = "...e os liderarei em um novo mundo governado pelos elfos!!!",
["COMIC_BALLOON_0002_KR1"] = "Vitória!",
["COMIC_BALLOON_02_KR1"] = "Vitória!",
["COMIC_balloon_0002_KR1"] = "Vitória!",
["COMMAND YOUR TROOPS!"] = "COMANDE  SUAS  TROPAS!",
["CONFIRM_EXIT"] = "Sair?",
["CONFIRM_RESTART"] = "Reiniciar?",
["CONTROLLER_STAGE_16_OVERSEER_DESCRIPTION"] = "Uma monstruosidade extradimensional que invade e conquista outros mundos para absorver sua energia. Deve ser parado a todo custo.",
["CONTROLLER_STAGE_16_OVERSEER_EXTRA"] = "- Troca as torres do jogador\n- Gera Glarelings\n- Destroi pontos estratégicos",
["CONTROLLER_STAGE_16_OVERSEER_NAME"] = "O Onividente",
["CREDITS_COPYRIGHT"] = "© 2014 Ironhide Game Studio. Todos os direitos reservados.",
["CREDITS_POWERED_BY"] = "Apoiado por",
["CREDITS_SUBTITLE_01"] = "(em ordem alfabética)",
["CREDITS_SUBTITLE_07"] = "(em ordem alfabética)",
["CREDITS_SUBTITLE_09"] = "(em ordem alfabética)",
["CREDITS_SUBTITLE_16"] = "(em ordem alfabética)",
["CREDITS_TEXT_18"] = "Às nossas famílias, amigos e à comunidade",
["CREDITS_TEXT_18_2"] = "por nos apoiar durante todos estes anos.",
["CREDITS_TITLE_01"] = "Diretores de Criação e Produtores Executivos",
["CREDITS_TITLE_01_CREATIVE_DIRECTORS"] = "Diretores criativos",
["CREDITS_TITLE_01_EXECUTIVE_PRODUCERS"] = "Produtores executivos",
["CREDITS_TITLE_02"] = "Chefe de Design de Jogo",
["CREDITS_TITLE_02_LEAD_GAME_DESIGNERS"] = "Líderes de Design do Jogo",
["CREDITS_TITLE_03"] = "Designers de Jogo",
["CREDITS_TITLE_03_GAME_DESIGNER"] = "Designer do jogo",
["CREDITS_TITLE_04"] = "Roteirista",
["CREDITS_TITLE_04_STORY_WRITERS"] = "Escritores da história",
["CREDITS_TITLE_05"] = "Escritores",
["CREDITS_TITLE_06"] = "Programador Chefe",
["CREDITS_TITLE_06_LEAD_PROGRAMMERS"] = "Líderes de Programação",
["CREDITS_TITLE_07"] = "Programadores",
["CREDITS_TITLE_08"] = "Artista Chefe",
["CREDITS_TITLE_09"] = "Artistas",
["CREDITS_TITLE_10"] = "Quadrinista",
["CREDITS_TITLE_11"] = "Escritor de Quadrinhos",
["CREDITS_TITLE_12"] = "Artista Técnico",
["CREDITS_TITLE_13"] = "Efeitos de Som",
["CREDITS_TITLE_14"] = "Música original de",
["CREDITS_TITLE_15"] = "Ator de Voz",
["CREDITS_TITLE_16"] = "Testes e CQ",
["CREDITS_TITLE_17"] = "Testes Beta",
["CREDITS_TITLE_18"] = "Agradecimentos Especiais",
["CREDITS_TITLE_19_PMO"] = "PMO",
["CREDITS_TITLE_20_PRODUCER"] = "Produtor",
["CREDITS_TITLE_21_MARKETING"] = "Marketing",
["CREDITS_TITLE_22_SPECIAL_COLLAB"] = "Colaboradores especiais",
["CREDITS_TITLE_ANCIENT_HUNGER_UPDATE"] = "Fome Antiga / Aracnofobia / A Jornada de Wukong",
["CREDITS_TITLE_GAME_ENGINE_PROGRAMMER"] = "Programador de motor de jogo",
["CREDITS_TITLE_LOCALIZATION"] = "Localização",
["CREDITS_TITLE_LOGO"] = "UM JOGO DE",
["CRange0"] = "Curto",
["CRange1"] = "Médio",
["CRange2"] = "Longo",
["CRange3"] = "Gigante",
["CRange4"] = "Extremo",
["CReload0"] = "Muito lenta",
["CReload1"] = "Lenta",
["CReload2"] = "Média",
["CReload3"] = "Veloz",
["CReload4"] = "Muito veloz",
["CSpeed0"] = "Lenta",
["CSpeed1"] = "Média",
["CSpeed2"] = "Veloz",
["C_DIFFICULTY_EASY"] = "Completou Casual",
["C_DIFFICULTY_HARD"] = "Completou Veterano",
["C_DIFFICULTY_IMPOSSIBLE"] = "Completou Impossível",
["C_DIFFICULTY_NORMAL"] = "Completou Normal",
["C_REWARD"] = "Recompensa:",
["Campaign"] = "Campanha",
["Cancel"] = "Cancelar",
["Casual"] = "Casual",
["Challenge Rules"] = "Regras do Desafio",
["Clear_progress"] = "Limpar progresso",
["Community Manager"] = "Gerente da comunidade",
["Credits"] = "Créditos",
["DAYS_ABBREVIATION"] = "d",
["DELETE SLOT?"] = "Apagar espaço?",
["DIFFICULTY_SELECTION_EASY_DESCRIPTION"] = "Para iniciantes em jogos de estratégia!",
["DIFFICULTY_SELECTION_HARD_DESCRIPTION"] = "Radical! Jogue por sua conta!",
["DIFFICULTY_SELECTION_IMPOSSIBLE_DESCRIPTION"] = "Só os mais fortes têm chance!",
["DIFFICULTY_SELECTION_IMPOSSIBLE_LOCKED_DESCRIPTION"] = "Conclua a campanha para desbloquear este modo",
["DIFFICULTY_SELECTION_NORMAL_DESCRIPTION"] = "Grande desafio!",
["DIFFICULTY_SELECTION_NOTE"] = "Você sempre pode alterar o nível de dificuldade da fase.",
["DIFFICULTY_SELECTION_TITLE"] = "Escolha o nível de dificuldade!",
["DISCOUNT"] = "DESCONTO",
["DLC_OWNED"] = "COMPRADO",
["Difficulty Level"] = "Nível de dificuldade",
["ELITE STAGE!"] = "FASE ELITE!.",
["ENEMY_ACOLYTE_DESCRIPTION"] = "Pequenos e dóceis, os acólitos fazem com que seus números contem na batalha.",
["ENEMY_ACOLYTE_EXTRA"] = "- Gera um tentáculo ao morrer",
["ENEMY_ACOLYTE_NAME"] = "Acólito do Culto",
["ENEMY_ACOLYTE_SPECIAL"] = "Gera um tentáculo ao morrer",
["ENEMY_ACOLYTE_TENTACLE_DESCRIPTION"] = "Como último recurso, os Acólitos sacrificam sua vida para o Supervisor, gerando tentáculos mortais.",
["ENEMY_ACOLYTE_TENTACLE_EXTRA"] = "- Nasce de Acólitos mortos",
["ENEMY_ACOLYTE_TENTACLE_NAME"] = "Tentáculo de Acólito",
["ENEMY_AMALGAM_DESCRIPTION"] = "Monstruosidades feitas tanto de carne quanto do solo do Vazio Além. Os Behemoths espalham medo apesar de sua lentidão para atravessar o campo de batalha.",
["ENEMY_AMALGAM_EXTRA"] = "- Mini-chefe\n- Explode quando morre",
["ENEMY_AMALGAM_NAME"] = "Behemoth",
["ENEMY_ANIMATED_ARMOR_DESCRIPTION"] = "Relíquias desgastadas de batalhas passadas, agora possuídas por espectros que as conduzem à batalha.",
["ENEMY_ANIMATED_ARMOR_EXTRA"] = "- Quando derrotada, pode ser reanimada por um espectro",
["ENEMY_ANIMATED_ARMOR_NAME"] = "Armadura Animada",
["ENEMY_ARMORED_NIGHTMARE_DESCRIPTION"] = "Graças à magia do culto e vestidos em armaduras, esses Pesadelos lançam-se de cabeça na batalha",
["ENEMY_ARMORED_NIGHTMARE_EXTRA"] = "- Alta armadura\n- Transforma-se em um Pesadelo quando derrotado",
["ENEMY_ARMORED_NIGHTMARE_NAME"] = "Pesadelo Amarrado",
["ENEMY_ARMORED_NIGHTMARE_SPECIAL"] = "Transforma-se em um Pesadelo quando derrotado.",
["ENEMY_ASH_SPIRIT_DESCRIPTION"] = "Espíritos poderosos transformados em monstros assustadores, nascidos da lava, cinzas e tristeza.",
["ENEMY_ASH_SPIRIT_EXTRA"] = "- Saúde alta\n- Armadura alta\n- Regenera saúde enquanto está em solo flamejante",
["ENEMY_ASH_SPIRIT_NAME"] = "Ash Spirit",
["ENEMY_BALLOONING_SPIDER_DESCRIPTION"] = "Aranhas rápidas e furtivas com talento para evitar problemas.",
["ENEMY_BALLOONING_SPIDER_EXTRA"] = "- Começa a voar quando encurralada\n- Armadura média",
["ENEMY_BALLOONING_SPIDER_FLYER_DESCRIPTION"] = "Aranhas rápidas e furtivas com talento para evitar problemas.",
["ENEMY_BALLOONING_SPIDER_FLYER_EXTRA"] = "- Começa a voar quando encurralada\n- Armadura média",
["ENEMY_BALLOONING_SPIDER_FLYER_NAME"] = "Aranha Flutuante",
["ENEMY_BALLOONING_SPIDER_NAME"] = "Aranha Flutuante",
["ENEMY_BANE_WOLF_DESCRIPTION"] = "Lobos distorcidos que caçam aqueles que são lentos demais para vê-los chegar.",
["ENEMY_BANE_WOLF_EXTRA"] = "- Fica mais rápido cada vez que recebe dano",
["ENEMY_BANE_WOLF_NAME"] = "Lobo Maldito",
["ENEMY_BEAR_VANGUARD_DESCRIPTION"] = "Grandes, largos e maus, eles destroçam seus inimigos às dúzias.",
["ENEMY_BEAR_VANGUARD_EXTRA"] = "- Alta armadura\n- Fica enfurecido quando um Urso morre por perto.",
["ENEMY_BEAR_VANGUARD_NAME"] = "Urso de Vanguarda ",
["ENEMY_BEAR_VANGUARD_SPECIAL"] = "Entra em um estado frenético quando outro urso próximo morre.",
["ENEMY_BEAR_WOODCUTTER_DESCRIPTION"] = "Tende a dormir enquanto está de serviço, mas quando acorda, as coisas ficam sérias.",
["ENEMY_BEAR_WOODCUTTER_EXTRA"] = "- Alta armadura\n- Fica enfurecido quando um Urso morre por perto",
["ENEMY_BEAR_WOODCUTTER_NAME"] = "Urso Lenhador",
["ENEMY_BIG_TERRACOTA_DESCRIPTION"] = "Um aglomerado de lama antropomórfico nascido da fusão de várias almas movidas por intenção assassina.",
["ENEMY_BIG_TERRACOTA_EXTRA"] = "- Corpo a corpo",
["ENEMY_BIG_TERRACOTA_NAME"] = "Isca Ilusória de Monstro",
["ENEMY_BLAZE_RAIDER_DESCRIPTION"] = "Capitães orgulhosos e corpulentos, iniciados no Caminho do Fogo, que empunham lanças-cobra para superar os inimigos.",
["ENEMY_BLAZE_RAIDER_EXTRA"] = "- Baixa armadura\n- Ataque especial em solo flamejante",
["ENEMY_BLAZE_RAIDER_NAME"] = "Saqueador de Chamas",
["ENEMY_BLINKER_DESCRIPTION"] = "Com seu olhar ameaçador e asas de morcego, os Piscadores espreitam os inimigos indefesos.",
["ENEMY_BLINKER_EXTRA"] = "- Atordoa unidades do jogador",
["ENEMY_BLINKER_NAME"] = "Piscador do Vácuo",
["ENEMY_BLINKER_SPECIAL"] = "Atordoa unidades de jogador",
["ENEMY_BOSS_BULL_KING_NAME"] = "Bull Demon King",
["ENEMY_BOSS_CORRUPTED_DENAS_NAME"] = "Denas Corrompido",
["ENEMY_BOSS_CROCS_2_NAME"] = "Veneno Abominor",
["ENEMY_BOSS_CROCS_3_NAME"] = "Fogo Abominor",
["ENEMY_BOSS_CROCS_NAME"] = "Abominor",
["ENEMY_BOSS_CULT_LEADER_NAME"] = "Vidente Mydrias",
["ENEMY_BOSS_DEFORMED_GRYMBEARD_NAME"] = "Grymbeard Deformado",
["ENEMY_BOSS_GRYMBEARD_NAME"] = "Grymbeard",
["ENEMY_BOSS_MACHINIST_NAME"] = "Grymbeard",
["ENEMY_BOSS_NAVIRA_NAME"] = "Navira",
["ENEMY_BOSS_OVERSEER_NAME"] = "O Onividente",
["ENEMY_BOSS_PIG_NAME"] = "Goregrind",
["ENEMY_BOSS_PRINCESS_IRON_FAN_CLONE_NAME"] = "Clone da Princesa Leque de Ferro",
["ENEMY_BOSS_PRINCESS_IRON_FAN_NAME"] = "Princess Iron Fan",
["ENEMY_BOSS_REDBOY_TEEN_NAME"] = "Menino Vermelho",
["ENEMY_BOSS_SPIDER_QUEEN_NAME"] = "Mygale",
["ENEMY_BRUTE_WELDER_DESCRIPTION"] = "Esses operários usam seus maçaricos contra inimigos, mesmo sem provocação.",
["ENEMY_BRUTE_WELDER_EXTRA"] = "- Bloqueia uma torre quando morto",
["ENEMY_BRUTE_WELDER_NAME"] = "Soldador Brutamontes",
["ENEMY_BURNING_TREANT_DESCRIPTION"] = "Criaturas de madeira com intenções malignas, nascidas em meio a uma floresta em chamas.",
["ENEMY_BURNING_TREANT_EXTRA"] = "- Dano em área\n- Deixa o solo em chamas ao atacar",
["ENEMY_BURNING_TREANT_NAME"] = "Burning Treant",
["ENEMY_CITIZEN_1_DESCRIPTION"] = "Pescadores sinistros a serviço da Princesa, infiltrando-se no mercado negro.",
["ENEMY_CITIZEN_1_EXTRA"] = "- Fraco",
["ENEMY_CITIZEN_1_NAME"] = "Velho peixeiro",
["ENEMY_CITIZEN_2_DESCRIPTION"] = "Pescadores sinistros que servem à Princesa, contrabandeando pelo mercado negro.",
["ENEMY_CITIZEN_2_EXTRA"] = "- Fraco",
["ENEMY_CITIZEN_2_NAME"] = "Pescador de Blackwater",
["ENEMY_CITIZEN_3_DESCRIPTION"] = "Pescadores sinistros a serviço da Princesa, infiltrando-se no mercado negro.",
["ENEMY_CITIZEN_3_EXTRA"] = "- Fraco",
["ENEMY_CITIZEN_3_NAME"] = "Contrabandista de Tinta",
["ENEMY_CITIZEN_4_DESCRIPTION"] = "Pescadores sinistros a serviço da Princesa, infiltrando-se no mercado negro.",
["ENEMY_CITIZEN_4_EXTRA"] = "- Fraco",
["ENEMY_CITIZEN_4_NAME"] = "Saqueador das Marés",
["ENEMY_COMMON_CLONE_DESCRIPTION"] = "Nada notável, nada especial, muito parecido com o original.",
["ENEMY_COMMON_CLONE_EXTRA"] = "- Avança sem pensar",
["ENEMY_COMMON_CLONE_NAME"] = "Clone",
["ENEMY_CORRUPTED_ELF_DESCRIPTION"] = "Elfos reanimados que caçam inimigos à distância. Mesmo na morte, continuam extremamente eficazes.",
["ENEMY_CORRUPTED_ELF_EXTRA"] = "- Invoca um espectro quando morre",
["ENEMY_CORRUPTED_ELF_NAME"] = "Andarilho Renascido",
["ENEMY_CORRUPTED_STALKER_DESCRIPTION"] = "Espreitanuvens domesticados pelos Acólitos, agora servem como montarias para o Culto.",
["ENEMY_CORRUPTED_STALKER_EXTRA"] = "- Voador",
["ENEMY_CORRUPTED_STALKER_NAME"] = "Espreitanuvens Adestrado",
["ENEMY_CORRUPTED_STALKER_SPECIAL"] = "Voador",
["ENEMY_CROCS_BASIC_DESCRIPTION"] = "Orgulhoso guerreiro Crok, ainda no início da vida e apenas algumas calorias de se transformar na máquina de matar que sabe que pode ser. ",
["ENEMY_CROCS_BASIC_EGG_DESCRIPTION"] = "Recém-nascidos e imparáveis nos seus pés, \"eles crescem tão rápido\" era uma frase inventada graças a estes pequeninos cheios de surpresas. ",
["ENEMY_CROCS_BASIC_EGG_EXTRA"] = "- Inbloqueável\n- Baixa Armadura\n- Transforma-se em um Gator após alguns segundos ",
["ENEMY_CROCS_BASIC_EGG_NAME"] = "Crokinder ",
["ENEMY_CROCS_BASIC_EXTRA"] = "- Corpo a corpo ",
["ENEMY_CROCS_BASIC_NAME"] = "Gator",
["ENEMY_CROCS_EGG_SPAWNER_DESCRIPTION"] = "Este crok está carregando um ninho cheio de problemas! A cada poucos passos, ela solta ovos que eclodem em uma frenesi de crokinders. É como uma creche móvel, mas com muito mais mordida!",
["ENEMY_CROCS_EGG_SPAWNER_EXTRA"] = "- Gera Crokinders no Caminho",
["ENEMY_CROCS_EGG_SPAWNER_NAME"] = "Gator aninhado",
["ENEMY_CROCS_FLIER_DESCRIPTION"] = "Croks astutos que, no seu desprezo pela evolução natural, forjaram as suas próprias asas para obter uma vantagem aérea.",
["ENEMY_CROCS_FLIER_EXTRA"] = "- Voando",
["ENEMY_CROCS_FLIER_NAME"] = "Crok Alado",
["ENEMY_CROCS_HYDRA_DESCRIPTION"] = "Duas cabeças são melhores do que uma e as hidras provam isso. Existe um velho mito sobre uma besta de três cabeças como esta, mas é provavelmente uma mentira.",
["ENEMY_CROCS_HYDRA_EXTRA"] = "- Alta saúde\n- Alto dano\n- Alta resistência mágica\n- Gera uma terceira cabeça ao morrer\n- Cospe veneno no chão",
["ENEMY_CROCS_HYDRA_NAME"] = "Hidra",
["ENEMY_CROCS_QUICKFEET_GATOR_NAME"] = "Pés Rápidos",
["ENEMY_CROCS_RANGED_DESCRIPTION"] = "Rápidos e furtivos, lagartos caçadores que lidam com seus inimigos usando estilingues de longo alcance. ",
["ENEMY_CROCS_RANGED_EXTRA"] = "- Rápido\n- De longo alcance ",
["ENEMY_CROCS_RANGED_NAME"] = "Lizardshot ",
["ENEMY_CROCS_SHAMAN_DESCRIPTION"] = "Seres mágicos de grande importância para os Croks. Afinal, para uma raça de sangue frio, a habilidade de prever os caprichos dos céus é uma questão de vida ou morte.",
["ENEMY_CROCS_SHAMAN_EXTRA"] = "- Dano Mágico à Distância\n- Alta Resistência Mágica\n- Cura outros crocodilos\n- Atordoa torres",
["ENEMY_CROCS_SHAMAN_NAME"] = "Crok Sábio",
["ENEMY_CROCS_TANK_DESCRIPTION"] = "Pilares das forças dos Croks, com a mentalidade de que \"uma boa defesa é o melhor ataque\", eles roubaram algumas conchas e começaram a usá-las como achavam que era o melhor caminho.",
["ENEMY_CROCS_TANK_EXTRA"] = "- Alta saúde\n- Alta armadura\n- Gira quando bloqueado",
["ENEMY_CROCS_TANK_NAME"] = "Tankzard",
["ENEMY_CRYSTAL_GOLEM_DESCRIPTION"] = "Imbuídos com magia de outro mundo de seus cristais, essas efígies de pedra são quase imparáveis.",
["ENEMY_CRYSTAL_GOLEM_EXTRA"] = "- Mini-chefe\n- Armadura muito alta",
["ENEMY_CRYSTAL_GOLEM_NAME"] = "Golem de Cristal",
["ENEMY_CULTBROOD_DESCRIPTION"] = "Meio aranha, meio abominação cultista, avançam para a batalha sem medo ou misericórdia.",
["ENEMY_CULTBROOD_EXTRA"] = "- Rápido\n- Ataque venenoso\n- Se um inimigo morrer envenenado, gera outro Cultista Engendro",
["ENEMY_CULTBROOD_NAME"] = "Cultista Engendro",
["ENEMY_CUTTHROAT_RAT_DESCRIPTION"] = "Astutos e ardilosos por natureza, os ratos são assassinos e infiltradores natos.",
["ENEMY_CUTTHROAT_RAT_EXTRA"] = "- Velocidade rápida\n- Torna-se invisível após atingir um inimigo.",
["ENEMY_CUTTHROAT_RAT_NAME"] = "Rato Degolador",
["ENEMY_CUTTHROAT_RAT_SPECIAL"] = "Torna-se invisível após atingir um inimigo.",
["ENEMY_DARKSTEEL_ANVIL_DESCRIPTION"] = "A resposta anã aos tambores de guerra. Quanto mais pesados parecem, mais alto eles cantam.",
["ENEMY_DARKSTEEL_ANVIL_EXTRA"] = "- Concede bônus de armadura e velocidade aos inimigos",
["ENEMY_DARKSTEEL_ANVIL_NAME"] = "Bigorna de Aço-Sombrio",
["ENEMY_DARKSTEEL_FIST_DESCRIPTION"] = "Mecanicamente aprimorado para dobrar metal, mas prefere socar outras pessoas.",
["ENEMY_DARKSTEEL_FIST_EXTRA"] = "- Ataque especial atordoa as unidades do jogador",
["ENEMY_DARKSTEEL_FIST_NAME"] = "Punho de Aço-Sombrio",
["ENEMY_DARKSTEEL_GUARDIAN_DESCRIPTION"] = "Armaduras de batalha resistentes operadas por guerreiros anões e alimentadas por motores flamejantes. Prontos para matar.",
["ENEMY_DARKSTEEL_GUARDIAN_EXTRA"] = "- Mini-chefe\n- Fica frenético com pouca saúde",
["ENEMY_DARKSTEEL_GUARDIAN_NAME"] = "Guardião de Aço-Sombrio",
["ENEMY_DARKSTEEL_HAMMERER_DESCRIPTION"] = "Guerreiros tão brutais quanto sua arma de preferência.",
["ENEMY_DARKSTEEL_HAMMERER_EXTRA"] = " ",
["ENEMY_DARKSTEEL_HAMMERER_NAME"] = "Martelador de Aço-Sombrio",
["ENEMY_DARKSTEEL_HULK_DESCRIPTION"] = "Rabugento e com aço derretido correndo pelas veias, é o mais pesado que os anões podem ser.",
["ENEMY_DARKSTEEL_HULK_EXTRA"] = "- Mini-chefe\n- Com pouca saúde, investe pelo caminho causando dano",
["ENEMY_DARKSTEEL_HULK_NAME"] = "Colosso de Aço-Sombrio",
["ENEMY_DARKSTEEL_SHIELDER_DESCRIPTION"] = "Protegidos por enormes escudos, empurram os inimigos para o lado enquanto avançam.",
["ENEMY_DARKSTEEL_SHIELDER_EXTRA"] = "- Transforma-se em Martelador quando derrotado",
["ENEMY_DARKSTEEL_SHIELDER_NAME"] = "Escudeiro de Aço-Sombrio",
["ENEMY_DEATHWOOD_DESCRIPTION"] = "Bosquestranhos corrompidos pelos espectros sombrios que agora vagam pela floresta causando estragos.",
["ENEMY_DEATHWOOD_EXTRA"] = "- Mini-chefe\n- Lança uma bolota amaldiçoada que causa dano em uma área",
["ENEMY_DEATHWOOD_NAME"] = "Bosquemorte",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_DESCRIPTION"] = "O resultado da arrogância desenfreada de Grymbeard. Seu poder mental só é igualado por sua feiura.",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_EXTRA"] = "- Voador\n- Escudo com alta resistência mágica",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_NAME"] = "Clone Deformado",
["ENEMY_DEMON_MINOTAUR_DESCRIPTION"] = "Demônios híbridos meio-humanos, meio-touros, com um ataque de investida devastador. Eles não conhecem misericórdia.",
["ENEMY_DEMON_MINOTAUR_EXTRA"] = "- Ataque de investida\n- Não pode ser morto instantaneamente",
["ENEMY_DEMON_MINOTAUR_NAME"] = "Demon Minotaur",
["ENEMY_DOOM_BRINGER_DESCRIPTION"] = "Guerreiros temíveis trazendo a destruição a todo custo.",
["ENEMY_DOOM_BRINGER_EXTRA"] = "- Atordoa torres",
["ENEMY_DOOM_BRINGER_NAME"] = "Doombringer",
["ENEMY_DRAINBROOD_DESCRIPTION"] = "Uma aranha ancestral com uma mordida mortal. Alguns especulam que ela é a principal culpada pela cristalização das outras aranhas.",
["ENEMY_DRAINBROOD_EXTRA"] = "- Cristaliza inimigos enquanto drena sua vida",
["ENEMY_DRAINBROOD_NAME"] = "Aranha Vampírica",
["ENEMY_DREADEYE_VIPER_DESCRIPTION"] = "Revestindo suas flechas com seu próprio veneno, elas são inimigas mortais de longe.",
["ENEMY_DREADEYE_VIPER_EXTRA"] = "- Baixa resistência mágica\n- Ataques venenosos",
["ENEMY_DREADEYE_VIPER_NAME"] = "Víbora Olho do Pavor",
["ENEMY_DREADEYE_VIPER_SPECIAL"] = "As flechas aplicam veneno ao alvo.",
["ENEMY_DUST_CRYPTID_DESCRIPTION"] = "Antes, uma visão maravilhosa, agora uma aparição assustadora para aqueles que se aventuram longe demais.",
["ENEMY_DUST_CRYPTID_EXTRA"] = "- Voador\n- Deixa uma nuvem de pólen que torna os inimigos invulneráveis a danos",
["ENEMY_DUST_CRYPTID_NAME"] = "Críptido de Pó",
["ENEMY_EVOLVING_SCOURGE_DESCRIPTION"] = "Podem parecer quase fofinhos à primeira vista, mas se o Flagelo se alimentar de presas caídas, as coisas vão desandar rapidamente.",
["ENEMY_EVOLVING_SCOURGE_EXTRA"] = "- Come unidades caídas para evoluir para uma forma mais forte\n - Evolui instantaneamente para sua forma final quando afetado pelo Fulgor.",
["ENEMY_EVOLVING_SCOURGE_NAME"] = "Flagelo Evolutivo",
["ENEMY_FAN_GUARD_DESCRIPTION"] = "Guerreiras fortes e altamente versáteis, hábeis tanto em causar dor quanto em se proteger com seus leques mágicos.",
["ENEMY_FAN_GUARD_EXTRA"] = "- Possui armadura média e resistência mágica enquanto não estiver bloqueado.",
["ENEMY_FAN_GUARD_NAME"] = "Fan Guard",
["ENEMY_FIRE_FOX_DESCRIPTION"] = "Raposas esquivas e fofas nascidas do fogo. Rápidas e voláteis demais para serem domadas.",
["ENEMY_FIRE_FOX_EXTRA"] = "- Baixa resistência mágica\n- Mais rápido em chão em chamas\n- Deixa o chão em chamas ao morrer",
["ENEMY_FIRE_FOX_NAME"] = "Raposa de Fogo",
["ENEMY_FIRE_PHOENIX_DESCRIPTION"] = "Criaturas voadoras míticas que se alimentam do próprio fogo. Vivem e morrem em uma chama ardente.",
["ENEMY_FIRE_PHOENIX_EXTRA"] = "- Voador\n- Deixa o chão em chamas ao morrer",
["ENEMY_FIRE_PHOENIX_NAME"] = "Zhuque",
["ENEMY_FLAME_GUARD_DESCRIPTION"] = "Em busca da aprovação de seus mestres, esses discípulos de baixo escalão se destacam com lâminas pequenas.",
["ENEMY_FLAME_GUARD_EXTRA"] = "- Ataque especial em chão em chamas",
["ENEMY_FLAME_GUARD_NAME"] = "Guarda das Chamas",
["ENEMY_GALE_WARRIOR_DESCRIPTION"] = "Graciosas e elegantes, essas guerreiras foram escolhidas por sua princesa e estão dispostas a morrer por ela.",
["ENEMY_GALE_WARRIOR_EXTRA"] = "- Armadura média\n- Causa sangramento a cada 3 ataques",
["ENEMY_GALE_WARRIOR_NAME"] = "Gale Warrior",
["ENEMY_GLAREBROOD_CRYSTAL_NAME"] = "Cristal de Fulgor",
["ENEMY_GLARELING_DESCRIPTION"] = "Se deixados sem controle, estas criaturas dóceis podem superar até mesmo o exército mais robusto.",
["ENEMY_GLARELING_EXTRA"] = "- Alta velocidade",
["ENEMY_GLARELING_NAME"] = "Glareling",
["ENEMY_GLARENWARDEN_DESCRIPTION"] = "Essas aranhas abomináveis são o produto da fusão dos Glarebroods, tornando-as mais fortes e resistentes do que nunca.",
["ENEMY_GLARENWARDEN_EXTRA"] = "- Alta armadura\n- Rouba vida ao atacar",
["ENEMY_GLARENWARDEN_NAME"] = "Guardião do Fulgor",
["ENEMY_GOLDEN_EYED_DESCRIPTION"] = "Uma besta colossal cujo rugido infunde medo nos corações de seus inimigos.",
["ENEMY_GOLDEN_EYED_EXTRA"] = "- Mini-chefe\n- Aumenta a velocidade de movimento dos aliados",
["ENEMY_GOLDEN_EYED_NAME"] = "Golden-Eyed Beast",
["ENEMY_HARDENED_HORROR_DESCRIPTION"] = "Esta estirpe de Horrores tem lâminas afiadas nas mãos e abrirá um caminho através dos seus inimigos.",
["ENEMY_HARDENED_HORROR_EXTRA"] = "- Rola em alta velocidade e não pode ser bloqueado quando afetado pelo Fulgor.",
["ENEMY_HARDENED_HORROR_NAME"] = "Horror Garra-afiada",
["ENEMY_HELLFIRE_WARLOCK_DESCRIPTION"] = "Feiticeiros extremamente perigosos, especialistas em invocar criaturas e chamas das profundezas do inferno.",
["ENEMY_HELLFIRE_WARLOCK_EXTRA"] = "- Lança bolas de fogo\n- Invoca uma Raposa de Nove Caudas",
["ENEMY_HELLFIRE_WARLOCK_NAME"] = "Hellfire Warlock",
["ENEMY_HOG_INVADER_DESCRIPTION"] = "Encrenqueiros sujos e desorganizados. A maior parte do exército de Feras Selvagens.",
["ENEMY_HOG_INVADER_EXTRA"] = "- Saúde baixa",
["ENEMY_HOG_INVADER_NAME"] = "Porco Invasor ",
["ENEMY_HYENA5_DESCRIPTION"] = "Lutadores terríveis com uma predileção por se banquetear com seus inimigos caídos.",
["ENEMY_HYENA5_EXTRA"] = "- Armadura média\n- Cura-se ao comer unidades de jogador caídas",
["ENEMY_HYENA5_NAME"] = "Hiena Presapodre",
["ENEMY_HYENA5_SPECIAL"] = "Cura-se ao comer inimigos mortos.",
["ENEMY_KILLERTILE_DESCRIPTION"] = "Poderosos destruidores, anos de experiência em combate (ou um frango) deixaram-nos com uma mordida forte e letal. ",
["ENEMY_KILLERTILE_EXTRA"] = "- Alta Saúde\nAlto Dano",
["ENEMY_KILLERTILE_NAME"] = "Killertile ",
["ENEMY_LESSER_EYE_DESCRIPTION"] = "Olhos malignos que flutuam sobre o campo de batalha, atuando como batedores dos Geradores Vils.",
["ENEMY_LESSER_EYE_EXTRA"] = "- Voador",
["ENEMY_LESSER_EYE_NAME"] = "Olho Menor",
["ENEMY_LESSER_SISTER_DESCRIPTION"] = "Com sua mágica maliciosa, as Irmãs Retorcidas facilitam a entrada de Pesadelos no mundo físico. ",
["ENEMY_LESSER_SISTER_EXTRA"] = "- Alta resistência mágica\n- Invoca Pesadelos ",
["ENEMY_LESSER_SISTER_NAME"] = "Irmã Retorcida",
["ENEMY_LESSER_SISTER_NIGHTMARE_DESCRIPTION"] = "Sombras etéreas tecidas do livro de cantos das Irmãs do Culto",
["ENEMY_LESSER_SISTER_NIGHTMARE_EXTRA"] = "- Não pode ser alvo a menos que esteja bloqueado por unidades de combate corpo a corpo",
["ENEMY_LESSER_SISTER_NIGHTMARE_NAME"] = "Pesadelo",
["ENEMY_LESSER_SISTER_SPECIAL"] = "Invoca Pesadelos",
["ENEMY_MACHINIST_DESCRIPTION"] = "Obcecado por engrenagens e motores, este anão vive para a automação industrial e a guerra.",
["ENEMY_MACHINIST_EXTRA"] = "- Opera uma linha de montagem que gera Sentinelas",
["ENEMY_MACHINIST_NAME"] = "Grymbeard",
["ENEMY_MAD_TINKERER_DESCRIPTION"] = "Engenhoqueiros não se preocupam com nada além de construir coisas com sucata.",
["ENEMY_MAD_TINKERER_EXTRA"] = "- Cria Drones usando sucata deixada por outras unidades",
["ENEMY_MAD_TINKERER_NAME"] = "Engenhoqueiro Louco",
["ENEMY_MINDLESS_HUSK_DESCRIPTION"] = "Devido à sua aparência, os Despojos parecem ser inimigos fracos, ainda assim, cada um deles carrega uma surpresa para o campo de batalha.",
["ENEMY_MINDLESS_HUSK_EXTRA"] = "- Ao morrer, gera um Glareling",
["ENEMY_MINDLESS_HUSK_NAME"] = "Despojo Obediente",
["ENEMY_NINE_TAILED_FOX_DESCRIPTION"] = "Criaturas misteriosas, belas e poderosas. Vão atravessar os inimigos como uma fogueira em fúria.",
["ENEMY_NINE_TAILED_FOX_EXTRA"] = "- Resistência mágica média\n- Teleporta-se para a frente, atordoando inimigos ao chegar\n- Dano em área",
["ENEMY_NINE_TAILED_FOX_NAME"] = "Raposa de Nove Caudas",
["ENEMY_NOXIOUS_HORROR_DESCRIPTION"] = "Criaturas com aparência anfíbia que cospem bile venenosa sobre sua presa. Também são perigosas de perto.",
["ENEMY_NOXIOUS_HORROR_EXTRA"] = "- Ganha resistência mágica e emite uma aura venenosa quando afetado pelo Fulgor.",
["ENEMY_NOXIOUS_HORROR_NAME"] = "Cuspidor Nocivo",
["ENEMY_PALACE_GUARD_DESCRIPTION"] = "Recrutas pouco talentosos cuja única motivação é realizar os desejos de sua Princesa.",
["ENEMY_PALACE_GUARD_EXTRA"] = "- Corpo a corpo\n- Armadura baixa",
["ENEMY_PALACE_GUARD_NAME"] = "Guarda do Palácio",
["ENEMY_PUMPKIN_WITCH_DESCRIPTION"] = "Inimigo transformado em abóbora. Fácil de esmagar.",
["ENEMY_PUMPKIN_WITCH_EXTRA"] = "- Imbloqueável",
["ENEMY_PUMPKIN_WITCH_FLYING_DESCRIPTION"] = "Inimigo transformado em abóbora. Fácil de esmagar.",
["ENEMY_PUMPKIN_WITCH_FLYING_EXTRA"] = "- Imbloqueável",
["ENEMY_PUMPKIN_WITCH_FLYING_NAME"] = "Abóbora",
["ENEMY_PUMPKIN_WITCH_NAME"] = "Abóbora",
["ENEMY_QIONGQI_DESCRIPTION"] = "Leões voadores ferozes que atacam com o poder do relâmpago. Os reis da tempestade.",
["ENEMY_QIONGQI_EXTRA"] = "- Voador\n- Dano muito alto\n- Resistência mágica média",
["ENEMY_QIONGQI_NAME"] = "Qiongqi",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_DESCRIPTION"] = "Após anos entregando frangos aos seus irmãos, eles se tornaram tão rápidos que às vezes esquecem até mesmo de trazer o frango.",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_EXTRA"] = "- Rápido\n- De longo alcance\n- Cuidado! Pode entregar uma coxa de frango a um Gator, fazendo-o evoluir",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_NAME"] = "Pés Rápidos",
["ENEMY_QUICKFEET_GATOR_DESCRIPTION"] = "Após anos entregando frangos aos seus irmãos, eles se tornaram tão rápidos que às vezes esquecem até mesmo de trazer o frango.",
["ENEMY_QUICKFEET_GATOR_EXTRA"] = "- Rápido\n- De longo alcance\n- Cuidado! Pode entregar uma coxa de frango a um Gator, fazendo-o evoluir",
["ENEMY_QUICKFEET_GATOR_NAME"] = "Pés Rápidos",
["ENEMY_REVENANT_HARVESTER_DESCRIPTION"] = "Sacerdotisas antigas agora vagam pela floresta, espalhando sua influência através dos espectros.",
["ENEMY_REVENANT_HARVESTER_EXTRA"] = "- Transforma espectros próximos em Ceifadoras Renascidas",
["ENEMY_REVENANT_HARVESTER_NAME"] = "Ceifadora Renascida",
["ENEMY_REVENANT_SOULCALLER_DESCRIPTION"] = "Magos élficos sofreram a atração da magia da morte e se erguem da terra para invocar os espectros dos caídos.",
["ENEMY_REVENANT_SOULCALLER_EXTRA"] = "- Desativa torres\n- Invoca espectros",
["ENEMY_REVENANT_SOULCALLER_NAME"] = "Invocador Renascido",
["ENEMY_RHINO_DESCRIPTION"] = "Um aríete vivo que pisa sem consideração pelo campo de batalha.",
["ENEMY_RHINO_EXTRA"] = "- Mini-chefe\n- Investe contra os inimigos",
["ENEMY_RHINO_NAME"] = "Rinoceronte Arrasador",
["ENEMY_RHINO_SPECIAL"] = "Avança em direção aos inimigos.",
["ENEMY_ROLLING_SENTRY_DESCRIPTION"] = "Depois de abatidas, continuam a caçar no chão.",
["ENEMY_ROLLING_SENTRY_EXTRA"] = "- Transforma-se em sucata quando morto\n- De longo alcance",
["ENEMY_ROLLING_SENTRY_NAME"] = "Sentinela Rolante",
["ENEMY_SCRAP_DRONE_DESCRIPTION"] = "Montado de forma rudimentar com o único objetivo de importunar as tropas.",
["ENEMY_SCRAP_DRONE_EXTRA"] = "- Voador",
["ENEMY_SCRAP_DRONE_NAME"] = "Drone de Sucata",
["ENEMY_SCRAP_SPEEDSTER_DESCRIPTION"] = "Barulhento e irritante, com uma necessidade constante de velocidade.",
["ENEMY_SCRAP_SPEEDSTER_EXTRA"] = "- Transforma-se em sucata quando morto",
["ENEMY_SCRAP_SPEEDSTER_NAME"] = "Corredor de Sucata",
["ENEMY_SKUNK_BOMBARDIER_DESCRIPTION"] = "Levando suas toxinas naturais para outro nível, os gambás espalham desordem nas linhas inimigas.",
["ENEMY_SKUNK_BOMBARDIER_EXTRA"] = "- Baixa velocidade\n- Resistência mágica média\n- Ataques enfraquecem unidades do jogador\n- Explode quando morre",
["ENEMY_SKUNK_BOMBARDIER_NAME"] = "Gambá Bombardeiro",
["ENEMY_SKUNK_BOMBARDIER_SPECIAL"] = "Ataques enfraquecem unidades do jogador. Explode quando morre, causando dano.",
["ENEMY_SMALL_STALKER_DESCRIPTION"] = "Corrompidos pela magia do Culto, esses Espreitanuvens teleportam-se pelo campo de batalha semeando o caos.",
["ENEMY_SMALL_STALKER_EXTRA"] = "- Teletransporta-se para frente quando atacado",
["ENEMY_SMALL_STALKER_NAME"] = "Espreitanuvens Corrompido",
["ENEMY_SMALL_STALKER_SPECIAL"] = "Teletransporta-se uma curta distância, evadindo ataques.",
["ENEMY_SPECTER_DESCRIPTION"] = "Escravizados além da morte, condenados a assombrar os vivos.",
["ENEMY_SPECTER_EXTRA"] = "- Pode interagir com outros inimigos e elementos",
["ENEMY_SPECTER_NAME"] = "Espectro",
["ENEMY_SPIDEAD_DESCRIPTION"] = "Descendentes diretas da Rainha Aranha Mygale, essas aranhas sempre encontram uma maneira de ser irritantes, mesmo depois da morte.",
["ENEMY_SPIDEAD_EXTRA"] = "- Resistência mágica\n- Gera uma teia de aranha ao morrer",
["ENEMY_SPIDEAD_NAME"] = "Filha da Seda",
["ENEMY_SPIDERLING_DESCRIPTION"] = "Aranhas melhoradas pela magia do Culto. Rápidas e furiosas. Vão morder.",
["ENEMY_SPIDERLING_EXTRA"] = "- Velocidade rápida\n- Baixa resistência mágica",
["ENEMY_SPIDERLING_NAME"] = "Aranha Fulgurante",
["ENEMY_SPIDER_PRIEST_DESCRIPTION"] = "Enlaçados pelo seu novo deus, os sacerdotes entram no campo de batalha empunhando magia negra.",
["ENEMY_SPIDER_PRIEST_EXTRA"] = "- Alta resistência mágica\n- Transforma-se em Guardião do Fulgor ao se aproximar da morte",
["ENEMY_SPIDER_PRIEST_NAME"] = "Sacerdote da Teia",
["ENEMY_SPIDER_SISTER_DESCRIPTION"] = "Como devotas fervorosas da Rainha Aranha, elas usam sua magia para invocar seus parentes.",
["ENEMY_SPIDER_SISTER_EXTRA"] = "- Resistência mágica\n- Invoca Glarebroods",
["ENEMY_SPIDER_SISTER_NAME"] = "Irmã Aranha",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_DESCRIPTION"] = "Doppelgangers de sombra que Mydrias usa para intervir na batalha.",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_EXTRA"] = "- Protege os inimigos de danos\n- Aprisiona torres com tentáculos escuros",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_NAME"] = "Ilusão de Mydrias",
["ENEMY_STORM_ELEMENTAL_DESCRIPTION"] = "Elementais poderosos gerados por tufões, relâmpagos e raiva. Um primo distante do Espírito das Cinzas.",
["ENEMY_STORM_ELEMENTAL_EXTRA"] = "- Alta armadura\n- À distância\n- Atordoa uma torre próxima ao morrer",
["ENEMY_STORM_ELEMENTAL_NAME"] = "Espírito da Tempestade",
["ENEMY_STORM_SPIRIT_DESCRIPTION"] = "Pequenos dragões saltando por entre nuvens de tempestade, evitando perigos e inimigos com destreza.",
["ENEMY_STORM_SPIRIT_EXTRA"] = "- Voador\n- Baixa resistência mágica\n- Avança rapidamente ao ser danificado",
["ENEMY_STORM_SPIRIT_NAME"] = "Draconete da Tempestade",
["ENEMY_SURVEILLANCE_SENTRY_DESCRIPTION"] = "Projetada pelos anões para vigiar os inimigos do alto.",
["ENEMY_SURVEILLANCE_SENTRY_EXTRA"] = "- Voador",
["ENEMY_SURVEILLANCE_SENTRY_NAME"] = "Sentinela Voadora",
["ENEMY_SURVEYOR_HARPY_DESCRIPTION"] = "À procura de carniça, os abutres seguem as feras selvagens por todo o lado.",
["ENEMY_SURVEYOR_HARPY_EXTRA"] = "- Voador",
["ENEMY_SURVEYOR_HARPY_NAME"] = "Abutre Patrulheiro",
["ENEMY_SURVEYOR_HARPY_SPECIAL"] = "Voador.",
["ENEMY_TERRACOTA_DESCRIPTION"] = "Sombras manifestadas que servem como distração.",
["ENEMY_TERRACOTA_EXTRA"] = "- Corpo a corpo",
["ENEMY_TERRACOTA_NAME"] = "Isca Ilusória",
["ENEMY_TOWER_RAY_SHEEP_DESCRIPTION"] = "Beeeeee.",
["ENEMY_TOWER_RAY_SHEEP_EXTRA"] = "- Inbloqueável",
["ENEMY_TOWER_RAY_SHEEP_FLYING_DESCRIPTION"] = "Beeeeee.",
["ENEMY_TOWER_RAY_SHEEP_FLYING_EXTRA"] = "- Voador",
["ENEMY_TOWER_RAY_SHEEP_FLYING_NAME"] = "Ovelha voadora",
["ENEMY_TOWER_RAY_SHEEP_NAME"] = "Ovelha",
["ENEMY_TURTLE_SHAMAN_DESCRIPTION"] = "De aparência pacífica mas de espírito mesquinho, os xamãs mantêm as Feras Selvagens curadas e prontas para lutar.",
["ENEMY_TURTLE_SHAMAN_EXTRA"] = "- Velocidade lenta\n- Alto HP\n- Alta resistência mágica\n- Cura unidades inimigas",
["ENEMY_TURTLE_SHAMAN_NAME"] = "Tartaruga Xamã",
["ENEMY_TURTLE_SHAMAN_SPECIAL"] = "Cura unidades inimigas.",
["ENEMY_TUSKED_BRAWLER_DESCRIPTION"] = "Mais tenazes que os invasores e equipados com armaduras precárias. Sempre prontos para a briga.",
["ENEMY_TUSKED_BRAWLER_EXTRA"] = "- Baixa armadura",
["ENEMY_TUSKED_BRAWLER_NAME"] = "Lutador com Presas",
["ENEMY_UNBLINDED_ABOMINATION_DESCRIPTION"] = "Sacerdotes totalmente corrompidos do Culto, conhecidos pela sua selvageria no combate.",
["ENEMY_UNBLINDED_ABOMINATION_EXTRA"] = "- Devora unidades com pouca saúde",
["ENEMY_UNBLINDED_ABOMINATION_NAME"] = "Abominação do Culto",
["ENEMY_UNBLINDED_ABOMINATION_SPECIAL"] = "Ocasionalmente devora uma unidade com pouca saúde",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_DESCRIPTION"] = "Após escravizar os elfos, algumas Abominações foram nomeadas para garantir que o trabalho nas minas corra suavemente.",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_EXTRA"] = "- Deve ser morto para libertar os elfos",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_NAME"] = "Capataz Abominação",
["ENEMY_UNBLINDED_PRIEST_DESCRIPTION"] = "Entre orações e o oculto, os sacerdotes avançam para a batalha empunhando magia negra.",
["ENEMY_UNBLINDED_PRIEST_EXTRA"] = "- Alta resistência à magia\n- Transforma-se em uma Abominação quando próximo da morte",
["ENEMY_UNBLINDED_PRIEST_NAME"] = "Sacerdote do Culto",
["ENEMY_UNBLINDED_PRIEST_SPECIAL"] = "Com pouca saúde, transforma-se em uma Abominação.",
["ENEMY_UNBLINDED_SHACKLER_DESCRIPTION"] = "Canalizando magia corrompida através dos cristais embutidos em seus braços, os Algemadores são inimigos temíveis em combates corpo a corpo",
["ENEMY_UNBLINDED_SHACKLER_EXTRA"] = "- Resistência mágica média\n- Desabilita torres com pouca saúde",
["ENEMY_UNBLINDED_SHACKLER_NAME"] = "Algemador",
["ENEMY_UNBLINDED_SHACKLER_SPECIAL"] = "Acorrenta torres, impedindo-as de atacar",
["ENEMY_VILE_SPAWNER_DESCRIPTION"] = "Lançando seus muitos olhos voadores sobre os inimigos, os Geradores Vils estão sempre observando em todas as direções.",
["ENEMY_VILE_SPAWNER_EXTRA"] = "- Gera Olhos Menores.",
["ENEMY_VILE_SPAWNER_NAME"] = "Gerador Vil",
["ENEMY_WATER_SORCERESS_DESCRIPTION"] = "Velhos conjuradores elementais que comandam o poder da água para curar aliados e derrotar inimigos à distância.",
["ENEMY_WATER_SORCERESS_EXTRA"] = "- À distância\n- Resistência mágica média\n- Cura aliados",
["ENEMY_WATER_SORCERESS_NAME"] = "Water Master",
["ENEMY_WATER_SPIRIT_DESCRIPTION"] = "Entidades aquáticas sem alma avançam em ondas implacáveis, devastando as margens com fúria.",
["ENEMY_WATER_SPIRIT_EXTRA"] = "- Baixa resistência mágica\n- Pode surgir da água",
["ENEMY_WATER_SPIRIT_NAME"] = "Water Spirit",
["ENEMY_WATER_SPIRIT_SPAWNLESS_DESCRIPTION"] = "Entidades aquáticas sem alma avançam em ondas implacáveis, devastando as margens com fúria.",
["ENEMY_WATER_SPIRIT_SPAWNLESS_EXTRA"] = "- Baixa resistência mágica\n- Pode surgir da água",
["ENEMY_WATER_SPIRIT_SPAWNLESS_NAME"] = "Water Spirit",
["ENEMY_WUXIAN_DESCRIPTION"] = "Magos poderosos e duráveis que obliteram seus inimigos com magia.",
["ENEMY_WUXIAN_EXTRA"] = "- À distância\n- Armadura média\n- Ataque especial em chão em chamas",
["ENEMY_WUXIAN_NAME"] = "Wuxian",
["ERROR_MESSAGE_GENERIC"] = "Ops! Alguma coisa saiu errada.",
["Earn huge bonus points and gold by calling waves earlier!"] = "Antecipe as ondas para ganhar um bônus enorme em pontos e ouro!",
["FIRST_WEEK_PACK"] = "Presente",
["FULLADS_BONUS_REWARDS_TITLE"] = "RECOMPENSAS DE BÔNUS!",
["FULLADS_BUTTON_BUY"] = "COMPRAR",
["FULLADS_BUTTON_CLAIM"] = "VEJA O ANÚNCIO PARA GANHAR RECOMPENSAS!",
["FULLADS_BUTTON_CLAIM_SHORT"] = "RESGATE!",
["FULLADS_BUTTON_HIRE"] = "CONTRATAR",
["FULLADS_BUTTON_INFO"] = "INFO",
["FULLADS_BUTTON_PLAY"] = "VEJA O ANÚNCIO PARA GANHAR RECOMPENSAS!",
["FULLADS_BUTTON_PLAY_SHORT"] = "JOGUE!",
["FULLADS_BUTTON_SPIN"] = "VEJA O ANÚNCIO PARA GIRAR A RODA!",
["FULLADS_BUTTON_SPIN_SHORT"] = "GIRE!",
["FULLADS_BUTTON_UNLOCK"] = "DESBLOQUEAR",
["FULLADS_DEFEAT_ENDLESS_REWARDS_TITLE"] = "RECOMPENSAS DE DERROTA!",
["FULLADS_DEFEAT_REWARDS_TITLE"] = "RECOMPENSAS DE DERROTA!",
["FULLADS_GNOME_REWARDS_TITLE"] = "RECOMPENSAS DO GNOMO!",
["FULLADS_MAP_CROWNS_DESCRIPTION"] = "Use coroas para contratar um herói por um dia.",
["FULLADS_MAP_GEMS_DESCRIPTION"] = "Use gemas para comprar itens e desbloquear heróis para sempre.",
["FULLADS_MAP_HEROROOM_HELP_CROWNS"] = "Contratar por um dia",
["FULLADS_MAP_HEROROOM_HELP_GEMS"] = "Compre para sempre",
["FULLADS_MAP_STARS_DESCRIPTION"] = "Use estrelas para comprar melhorias.",
["FULLADS_VICTORY_CLAIM_BONUS"] = "VEJA O ANÚNCIO PARA RESGATAR O BÔNUS DE %sX",
["FULLADS_VICTORY_REWARDS_TITLE"] = "RECOMPENSAS DE VITÓRIA!",
["FULLADS_WHEEL_PROBABILITIES_TITLE"] = "Probabilidades de recompensa",
["FULLADS_WHEEL_REWARDS_TITLE"] = "RODA DA FORTUNA!",
["FULLADS_YOUR_REWARDS_TITLE"] = "SUAS RECOMPENSAS!",
["FULLADS_YOUR_REWARD_TITLE"] = "SUA RECOMPENSA!",
["Face an endless unrelenting enemy force and try to defeat as many as possible to comete for the best score!"] = "Enfrente uma força inimiga infindável e tente derrotar o máximo possível para competir pela melhor pontuação!",
["Face an endless unrelenting enemy force and try to defeat as many as possible to compete for the best score!"] = "Enfrente uma força inimiga infindável e tente derrotar o máximo possível para competir pela melhor pontuação!",
["Failed to load Rewarded Video, first session !"] = "Falha ao carregar o vídeo premiado",
["Failed to load Rewarded Video, internal error !"] = "Falha ao carregar o vídeo premiado",
["Failed to load Rewarded Video, missing location parameter !"] = "Falha ao carregar o vídeo premiado",
["Failed to load Rewarded Video, network error !"] = "Falha ao carregar o vídeo premiado",
["Failed to load Rewarded Video, no Internet connection !"] = "Falha ao carregar o vídeo premiado",
["Failed to load Rewarded Video, no ad found !"] = "Falha ao carregar o vídeo premiado",
["Failed to load Rewarded Video, session not started !"] = "Falha ao carregar o vídeo premiado",
["Failed to load Rewarded Video, too many connections !"] = "Falha ao carregar o vídeo premiado",
["Failed to load Rewarded Video, unknown error !"] = "Falha ao carregar o vídeo premiado",
["Failed to load Rewarded Video, wrong orientation !"] = "Falha ao carregar o vídeo premiado",
["GAME PAUSED"] = "JOGO PAUSADO",
["GAME_TITLE_KR5"] = "Kingdom Rush 5: Alliance",
["GEMS_BARREL_NAME"] = "BARRIL DE GEMAS",
["GEMS_CHEST_NAME"] = "BAÚ DE GEMAS",
["GEMS_HANDFUL_NAME"] = "PUNHADO DE GEMAS",
["GEMS_MOUNTAIN_NAME"] = "MONTANHA DE GEMAS",
["GEMS_POUCH_NAME"] = "BOLSA DE GEMAS",
["GEMS_WAGON_NAME"] = "VAGÃO DE GEMAS",
["GET_ALL_AWESOME_HEROES"] = "PEGUE ESTES HERÓIS INCRÍVEIS",
["GET_THIS_AWESOME"] = "PEGUE ESTE\nHERÓI INCRÍVEL",
["GET_THIS_AWESOME_2"] = "PEGUE ESTES\n HERÓIS INCRÍVEIS",
["GET_THIS_AWESOME_3"] = "PEGUE ESTES\n HERÓIS INCRÍVEIS",
["GIFT_CLAIMED"] = "Presente resgatado!",
["GOOGLE_PLAY"] = "GOOGLE PLAY",
["Got it!"] = "Saquei!",
["HERO LEVEL UP!"] = "HERÓI GANHOU NÍVEL!",
["HERO ROOM"] = "HERÓIS",
["HERO UNLOCKED!"] = "HERÓI LIBERADO!",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_1"] = "Invoca grifos que voam sobre a área durante %$heroes.hero_bird.ultimate.bird.duration[2]%$ segundos atacando os inimigos, causando %$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[2]%$ de dano a cada vez.",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_2"] = "Invoca grifos que voam sobre a área durante %$heroes.hero_bird.ultimate.bird.duration[3]%$ segundos atacando os inimigos, causando %$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[3]%$ de dano a cada vez.",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_3"] = "Invoca grifos que voam sobre a área durante %$heroes.hero_bird.ultimate.bird.duration[4]%$ segundos atacando os inimigos, causando %$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[4]%$ de dano a cada vez.",
["HERO_BIRD_BIRDS_OF_PREY_MENUBOTTOM_DESCRIPTION"] = "Invoca grifos que voam acima da área atacando inimigos.",
["HERO_BIRD_BIRDS_OF_PREY_MENUBOTTOM_NAME"] = "Aves de Combate",
["HERO_BIRD_BIRDS_OF_PREY_TITLE"] = "AVES DE COMBATE",
["HERO_BIRD_CLASS"] = "O Ginete Nato",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_1"] = "Lança um explosivo que se divide sobre os inimigos, causando %$heroes.hero_bird.cluster_bomb.explosion_damage_min[1]%$ de dano cada um e incendiando o chão por %$heroes.hero_bird.cluster_bomb.fire_duration[1]%$ segundos, queimando os inimigos por %$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$ de dano ao longo de 3 segundos.",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_2"] = "Lança um explosivo que se divide sobre os inimigos, causando %$heroes.hero_bird.cluster_bomb.explosion_damage_min[2]%$ de dano cada um e incendiando o chão por %$heroes.hero_bird.cluster_bomb.fire_duration[2]%$ segundos, queimando os inimigos por %$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$ de dano ao longo de 3 segundos.",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_3"] = "Lança um explosivo que se divide sobre os inimigos, causando %$heroes.hero_bird.cluster_bomb.explosion_damage_min[3]%$ de dano cada um e incendiando o chão por %$heroes.hero_bird.cluster_bomb.fire_duration[3]%$ segundos, queimando os inimigos por %$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$ de dano ao longo de 3 segundos.",
["HERO_BIRD_CLUSTER_BOMB_TITLE"] = "BOMBARDEIO DE CARPETE",
["HERO_BIRD_DESC"] = "O corajoso ginete de grifos voa para a batalha brandindo um arsenal de aço e fogo. Embora tenha se juntado à Aliança de forma relutante desde que o Exército das Trevas invadiu sua casa, Broden concordou em despejar destruição sobre o Culto como um meio para restaurar o status quo em Linirea.",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_1"] = "O grifo mergulha no chão para devorar um inimigo com até %$heroes.hero_bird.eat_instakill.hp_max[1]%$ de saúde.",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_2"] = "O grifo mergulha no chão para devorar um inimigo com até %$heroes.hero_bird.eat_instakill.hp_max[2]%$ de saúde.",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_3"] = "O grifo mergulha no chão para devorar um inimigo com até %$heroes.hero_bird.eat_instakill.hp_max[3]%$ de saúde.",
["HERO_BIRD_EAT_INSTAKILL_TITLE"] = "MERGULHO DE CAÇA",
["HERO_BIRD_GATTLING_DESCRIPTION_1"] = "Chove balas contra um inimigo, causando %$heroes.hero_bird.gattling.s_damage_min[1]%$-%$heroes.hero_bird.gattling.s_damage_max[1]%$ de dano físico.",
["HERO_BIRD_GATTLING_DESCRIPTION_2"] = "Chove balas contra um inimigo, causando %$heroes.hero_bird.gattling.s_damage_min[2]%$-%$heroes.hero_bird.gattling.s_damage_max[2]%$ de dano físico.",
["HERO_BIRD_GATTLING_DESCRIPTION_3"] = "Chove balas contra um inimigo, causando %$heroes.hero_bird.gattling.s_damage_min[3]%$-%$heroes.hero_bird.gattling.s_damage_max[3]%$ de dano físico.",
["HERO_BIRD_GATTLING_TITLE"] = "EXEMPLO A SEGUIR",
["HERO_BIRD_NAME"] = "Broden",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_1"] = "O grifo produz um grito ensurdecedor, atordoando os inimigos por %$heroes.hero_bird.shout_stun.stun_duration[1]%$ segundo e os desacelerando por %$heroes.hero_bird.shout_stun.slow_duration[1]%$ segundos depois.",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_2"] = "O grifo produz um grito ensurdecedor, atordoando os inimigos por %$heroes.hero_bird.shout_stun.stun_duration[2]%$ segundo e os desacelerando por %$heroes.hero_bird.shout_stun.slow_duration[2]%$ segundos depois.",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_3"] = "O grifo produz um grito ensurdecedor, atordoando os inimigos por %$heroes.hero_bird.shout_stun.stun_duration[3]%$ segundo e os desacelerando por %$heroes.hero_bird.shout_stun.slow_duration[3]%$ segundos depois.",
["HERO_BIRD_SHOUT_STUN_TITLE"] = "GRITO DE TERROR",
["HERO_BUILDER_CLASS"] = "Mestre de Obras",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_1"] = "Constrói uma torre improvisada que ataca inimigos que passam por %$heroes.hero_builder.defensive_turret.duration[1]%$ segundos, causando %$heroes.hero_builder.defensive_turret.attack.damage_min[1]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[1]%$ de dano físico por ataque.",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_2"] = "Constrói uma torre improvisada que ataca inimigos que passam por %$heroes.hero_builder.defensive_turret.duration[2]%$ segundos, causando %$heroes.hero_builder.defensive_turret.attack.damage_min[2]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[2]%$ de dano físico por ataque.",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_3"] = "Constrói uma torre improvisada que ataca inimigos que passam por %$heroes.hero_builder.defensive_turret.duration[3]%$ segundos, causando %$heroes.hero_builder.defensive_turret.attack.damage_min[3]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[3]%$ de dano físico por ataque.",
["HERO_BUILDER_DEFENSIVE_TURRET_TITLE"] = "TORRE DEFENSIVA",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_1"] = "Gira rapidamente sua viga de madeira, causando %$heroes.hero_builder.demolition_man.s_damage_min[1]%$-%$heroes.hero_builder.demolition_man.s_damage_max[1]%$ de dano físico aos inimigos ao seu redor.",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_2"] = "Gira rapidamente sua viga de madeira, causando %$heroes.hero_builder.demolition_man.s_damage_min[2]%$-%$heroes.hero_builder.demolition_man.s_damage_max[2]%$ de dano físico aos inimigos ao seu redor.",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_3"] = "Gira rapidamente sua viga de madeira, causando %$heroes.hero_builder.demolition_man.s_damage_min[3]%$-%$heroes.hero_builder.demolition_man.s_damage_max[3]%$ de dano físico aos inimigos ao seu redor.",
["HERO_BUILDER_DEMOLITION_MAN_TITLE"] = "DEMOLIDOR",
["HERO_BUILDER_DESC"] = "Anos encarregado de construir as defesas de Linirea ensinaram a Torres uma ou duas coisas sobre batalha. Agora que todo o reino está em perigo (e cansado de assistir das laterais) ele usa todas as suas ferramentas e conhecimento na luta.",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_1"] = "Torres para de lutar para comer um lanche, curando-se %$heroes.hero_builder.lunch_break.heal_hp[1]%$ de saúde.",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_2"] = "Torres para de lutar para comer um lanche, curando-se %$heroes.hero_builder.lunch_break.heal_hp[2]%$ de saúde.",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_3"] = "Torres para de lutar para comer um lanche, curando-se %$heroes.hero_builder.lunch_break.heal_hp[3]%$ de saúde.",
["HERO_BUILDER_LUNCH_BREAK_TITLE"] = "HORA DO ALMOÇO",
["HERO_BUILDER_NAME"] = "Torres",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_1"] = "Convoca dois construtores que lutam ao seu lado por %$heroes.hero_builder.overtime_work.soldier.duration%$ segundos.",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_2"] = "Os construtores têm %$heroes.hero_builder.overtime_work.soldier.hp_max[2]%$ de saúde e causam %$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_max[2]%$ de dano físico. Eles lutam por %$heroes.hero_builder.overtime_work.soldier.duration%$ segundos.",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_3"] = "Os construtores têm %$heroes.hero_builder.overtime_work.soldier.hp_max[3]%$ de saúde e causam %$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_max[3]%$ de dano físico. Eles lutam por %$heroes.hero_builder.overtime_work.soldier.duration%$ segundos. ",
["HERO_BUILDER_OVERTIME_WORK_TITLE"] = "HOMENS TRABALHANDO",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_1"] = "Solta uma bola de aço gigante no caminho, causando %$heroes.hero_builder.ultimate.damage[2]%$ de dano físico e atordoando os inimigos por %$heroes.hero_builder.ultimate.stun_duration[2]%$ segundos.",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_2"] = "Solta uma bola de aço gigante no caminho, causando %$heroes.hero_builder.ultimate.damage[3]%$ de dano físico e atordoando os inimigos por %$heroes.hero_builder.ultimate.stun_duration[3]%$ segundos.",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_3"] = "Solta uma bola de aço gigante no caminho, causando %$heroes.hero_builder.ultimate.damage[4]%$ de dano físico e atordoando os inimigos por %$heroes.hero_builder.ultimate.stun_duration[4]%$ segundos.",
["HERO_BUILDER_WRECKING_BALL_MENUBOTTOM_DESCRIPTION"] = "Solta uma bola de demolição no caminho, danificando os inimigos.",
["HERO_BUILDER_WRECKING_BALL_MENUBOTTOM_NAME"] = "Bola de demolição",
["HERO_BUILDER_WRECKING_BALL_TITLE"] = "BOLA DE DEMOLIÇÃO",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_1"] = "Sylvara desbloqueia sua verdadeira forma por %$heroes.hero_dragon_arb.ultimate.duration[2]%$ segundos, durante os quais ela ganha %$heroes.hero_dragon_arb.ultimate.s_bonuses[2]%$% de dano, velocidade, resistências e evolui alguns de seus poderes.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_2"] = "Sylvara desbloqueia sua verdadeira forma por %$heroes.hero_dragon_arb.ultimate.duration[3]%$ segundos, durante os quais ela ganha %$heroes.hero_dragon_arb.ultimate.s_bonuses[3]%$% de dano, velocidade, resistências e evolui alguns de seus poderes.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_3"] = "Sylvara desbloqueia sua verdadeira forma por %$heroes.hero_dragon_arb.ultimate.duration[4]%$ segundos, durante os quais ela ganha %$heroes.hero_dragon_arb.ultimate.s_bonuses[4]%$% de dano, velocidade, resistências e evolui alguns de seus poderes.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_MENUBOTTOM_DESCRIPTION"] = "Liberte a verdadeira forma de Sylvara.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_MENUBOTTOM_NAME"] = "Natureza Interior",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_TITLE"] = "Natureza Interna",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_1"] = "Transforma manchas verdes em arbóreos que lutam por %$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[1]%$ segundos, durante a Natureza Interna, ele invoca arbóreos mais fortes.",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_2"] = "Transforma manchas verdes em arbóreos que lutam por %$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[2]%$ segundos, durante a Natureza Interna, ele invoca arbóreos mais fortes.",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_3"] = "Transforma manchas verdes em arbóreos que lutam por %$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[3]%$ segundos, durante a Natureza Interna, ele invoca arbóreos mais fortes.",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_TITLE"] = "Chamado da Floresta",
["HERO_DRAGON_ARB_CLASS"] = "Força da Natureza",
["HERO_DRAGON_ARB_DESC"] = "O dragão da natureza e protetor dos arbóreos, ela tece florestas com seu sopro e faz o vento dançar com suas asas. Como a própria natureza, ela pode ser tanto cuidadosa quanto punitiva. Certifique-se de não jogar lixo!",
["HERO_DRAGON_ARB_NAME"] = "Sylvara",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_1"] = "A cada %$heroes.hero_dragon_arb.thorn_bleed.cooldown[1]%$ segundos, Sylvara potencializa sua próxima respiração para danificar inimigos dependendo de sua velocidade, durante a Natureza Interna, ela tem uma chance de %$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[1]%$% de matar instantaneamente.",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_2"] = "A cada %$heroes.hero_dragon_arb.thorn_bleed.cooldown[2]%$ segundos, Sylvara potencializa sua próxima respiração para danificar inimigos dependendo de sua velocidade, durante a Natureza Interna, ela tem uma chance de %$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[2]%$% de matar instantaneamente.",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_3"] = "A cada %$heroes.hero_dragon_arb.thorn_bleed.cooldown[3]%$ segundos, Sylvara potencializa sua próxima respiração para danificar inimigos dependendo de sua velocidade, durante a Natureza Interna, ela tem uma chance de %$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[3]%$% de matar instantaneamente.",
["HERO_DRAGON_ARB_THORN BLEED_TITLE"] = "Sopro Espinhoso",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_1"] = "Aumenta o dano das torres próximas em %$heroes.hero_dragon_arb.tower_runes.s_damage_factor[1]%$% por %$heroes.hero_dragon_arb.tower_runes.duration[1]%$ segundos.",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_2"] = "Aumenta o dano das torres próximas em %$heroes.hero_dragon_arb.tower_runes.s_damage_factor[2]%$% por %$heroes.hero_dragon_arb.tower_runes.duration[2]%$ segundos.",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_3"] = "Aumenta o dano das torres próximas em %$heroes.hero_dragon_arb.tower_runes.s_damage_factor[3]%$% por %$heroes.hero_dragon_arb.tower_runes.duration[3]%$ segundos.",
["HERO_DRAGON_ARB_TOWER RUNES_TITLE"] = "Raízes Profundas",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_1"] = "Invoca plantas perto de torres que duram %$heroes.hero_dragon_arb.tower_plants.duration[1]%$ segundos. Dependendo de sua aliança, elas se tornam plantas venenosas que causam dano e lentidão, ou plantas curativas que curam aliados.",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_2"] = "Invoca plantas perto de torres que duram %$heroes.hero_dragon_arb.tower_plants.duration[2]%$ segundos. Dependendo de sua aliança, elas se tornam plantas venenosas que causam dano e lentidão, ou plantas curativas que curam aliados.",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_3"] = "Invoca plantas perto de torres que duram %$heroes.hero_dragon_arb.tower_plants.duration[3]%$ segundos. Dependendo de sua aliança, elas se tornam plantas venenosas que causam dano e lentidão, ou plantas curativas que curam aliados.",
["HERO_DRAGON_ARB_TOWER_PLANTS_TITLE"] = "Portador da Vida",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_1"] = "Lança %$heroes.hero_dragon_bone.burst.proj_count[1]%$ projéteis mágicos. Cada um causa %$heroes.hero_dragon_bone.burst.damage_min[1]%$-%$heroes.hero_dragon_bone.burst.damage_max[1]%$ de dano verdadeiro e aplica praga.",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_2"] = "Lança %$heroes.hero_dragon_bone.burst.proj_count[2]%$ projéteis mágicos. Cada um causa %$heroes.hero_dragon_bone.burst.damage_min[2]%$-%$heroes.hero_dragon_bone.burst.damage_max[2]%$ de dano verdadeiro e aplica praga.",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_3"] = "Lança %$heroes.hero_dragon_bone.burst.proj_count[3]%$ projéteis mágicos. Cada um causa %$heroes.hero_dragon_bone.burst.damage_min[3]%$-%$heroes.hero_dragon_bone.burst.damage_max[3]%$ de dano verdadeiro e aplica praga.",
["HERO_DRAGON_BONE_BURST_TITLE"] = "EXPLOSÃO DE CONTÁGIO",
["HERO_DRAGON_BONE_CLASS"] = "Dracolich",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_1"] = "Cobre uma área com uma nuvem pestilenta que aplica praga aos inimigos e os desacelera por %$heroes.hero_dragon_bone.cloud.duration[1]%$ segundos.",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_2"] = "Cobre uma área com uma nuvem pestilenta que aplica praga aos inimigos e os desacelera por %$heroes.hero_dragon_bone.cloud.duration[2]%$ segundos.",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_3"] = "Cobre uma área com uma nuvem pestilenta que aplica praga aos inimigos e os desacelera por %$heroes.hero_dragon_bone.cloud.duration[3]%$ segundos.",
["HERO_DRAGON_BONE_CLOUD_TITLE"] = "NUVEM DE PRAGA",
["HERO_DRAGON_BONE_DESC"] = "Após ser revivido por Vez'nan durante sua campanha de conquista, Bonehart ofereceu seus poderes para saldar sua dívida perseguindo aqueles usuários de magia que poderiam ameaçar os planos do Feiticeiro Sombrio.",
["HERO_DRAGON_BONE_NAME"] = "Bonehart",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_1"] = "Investida no caminho, causando %$heroes.hero_dragon_bone.nova.damage_min[1]%$-%$heroes.hero_dragon_bone.nova.damage_max[1]%$ de dano explosivo aos inimigos e aplicando praga.",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_2"] = "Investida no caminho, causando %$heroes.hero_dragon_bone.nova.damage_min[2]%$-%$heroes.hero_dragon_bone.nova.damage_max[2]%$ de dano explosivo aos inimigos e aplicando praga.",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_3"] = "Investida no caminho, causando %$heroes.hero_dragon_bone.nova.damage_min[3]%$-%$heroes.hero_dragon_bone.nova.damage_max[3]%$ de dano explosivo aos inimigos e aplicando praga.",
["HERO_DRAGON_BONE_NOVA_TITLE"] = "LÂMINA DOENTE",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_1"] = "Lança %$heroes.hero_dragon_bone.rain.bones_count[1]%$ espinhos de ossos nos inimigos, causando %$heroes.hero_dragon_bone.rain.damage_min[1]%$-%$heroes.hero_dragon_bone.rain.damage_max[1]%$ de dano verdadeiro e atordoando-os brevemente.",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_2"] = "Lança %$heroes.hero_dragon_bone.rain.bones_count[2]%$ espinhos de ossos nos inimigos, causando %$heroes.hero_dragon_bone.rain.damage_min[2]%$-%$heroes.hero_dragon_bone.rain.damage_max[2]%$ de dano verdadeiro e atordoando-os brevemente.",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_3"] = "Lança %$heroes.hero_dragon_bone.rain.bones_count[3]%$ espinhos de ossos nos inimigos, causando %$heroes.hero_dragon_bone.rain.damage_min[3]%$-%$heroes.hero_dragon_bone.rain.damage_max[3]%$ de dano verdadeiro e atordoando-os brevemente.",
["HERO_DRAGON_BONE_RAIN_TITLE"] = "CHUVA DE ESPINHOS",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_1"] = "Invoca dois dracos de ossos. Cada draco tem %$heroes.hero_dragon_bone.ultimate.dog.hp[2]%$ de vida e causa %$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[2]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[2]%$ de dano físico.",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_2"] = "Invoca dois dracos de ossos. Cada draco tem %$heroes.hero_dragon_bone.ultimate.dog.hp[3]%$ de vida e causa %$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[3]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[3]%$ de dano físico.",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_3"] = "Invoca dois dracos de ossos. Cada draco tem %$heroes.hero_dragon_bone.ultimate.dog.hp[4]%$ de vida e causa %$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[4]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[4]%$ de dano físico.",
["HERO_DRAGON_BONE_RAISE_DRAKES_MENUBOTTOM_DESCRIPTION"] = "Invoca dois dracos de ossos.",
["HERO_DRAGON_BONE_RAISE_DRAKES_MENUBOTTOM_NAME"] = "Levantar Dracos",
["HERO_DRAGON_BONE_RAISE_DRAKES_TITLE"] = "LEVANTAR DRACOS",
["HERO_DRAGON_GEM_CLASS"] = "Inquebrável",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_1"] = "Envolve um inimigo em cristal por alguns segundos. Em seguida, o cristal explode, matando o alvo instantaneamente e causando %$heroes.hero_dragon_gem.crystal_instakill.s_damage[1]%$ de dano verdadeiro ao redor.",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_2"] = "Envolve um inimigo em cristal por alguns segundos. Em seguida, o cristal explode, matando o alvo instantaneamente e causando %$heroes.hero_dragon_gem.crystal_instakill.s_damage[2]%$ de dano verdadeiro ao redor.",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_3"] = "Envolve um inimigo em cristal por alguns segundos. Em seguida, o cristal explode, matando o alvo instantaneamente e causando %$heroes.hero_dragon_gem.crystal_instakill.s_damage[3]%$ de dano verdadeiro ao redor.",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_TITLE"] = "TUMBA DE GRANADA",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_1"] = "Lança um cristal no caminho que reduz a velocidade do inimigo em %$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$% e causa %$heroes.hero_dragon_gem.crystal_totem.s_damage[1]%$ de dano mágico a cada 1 segundos ao redor. Dura %$heroes.hero_dragon_gem.crystal_totem.duration[1]%$ segundos.",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_2"] = "Lança um cristal no caminho que reduz a velocidade do inimigo em %$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$% e causa %$heroes.hero_dragon_gem.crystal_totem.s_damage[2]%$ de dano mágico a cada 1 segundos ao redor. Dura %$heroes.hero_dragon_gem.crystal_totem.duration[2]%$ segundos.",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_3"] = "Lança um cristal no caminho que reduz a velocidade do inimigo em %$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$% e causa %$heroes.hero_dragon_gem.crystal_totem.s_damage[3]%$ de dano mágico a cada 1 segundos ao redor. Dura %$heroes.hero_dragon_gem.crystal_totem.duration[3]%$ segundos.",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_TITLE"] = "CONDUTOR DE PODER",
["HERO_DRAGON_GEM_DESC"] = "A vida isolada de Kosmyr foi interrompida quando o Culto começou suas operações no Cânion Abandonado. Querendo se livrar dos invasores, o dragão fez um acordo com Vez'nan para se juntar à Aliança contra um inimigo em comum.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_1"] = "Invoca %$heroes.hero_dragon_gem.ultimate.max_shards[2]%$ barragens de cristal que causam %$heroes.hero_dragon_gem.ultimate.damage_min[2]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[2]%$ de dano verdadeiro aos inimigos pegos na área.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_2"] = "Invoca %$heroes.hero_dragon_gem.ultimate.max_shards[3]%$ barragens de cristal que causam %$heroes.hero_dragon_gem.ultimate.damage_min[3]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[3]%$ de dano verdadeiro aos inimigos pegos na área.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_3"] = "Invoca %$heroes.hero_dragon_gem.ultimate.max_shards[4]%$ barragens de cristal que causam %$heroes.hero_dragon_gem.ultimate.damage_min[4]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[4]%$ de dano verdadeiro aos inimigos pegos na área.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_MENUBOTTOM_DESCRIPTION"] = "Lança várias barragens de cristais contra os inimigos.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_MENUBOTTOM_NAME"] = "Avalanche de Cristal",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_TITLE"] = "AVALANCHE DE CRISTAL",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_1"] = "Faz crescer espinhos de cristal pelos caminhos ao seu redor, causando %$heroes.hero_dragon_gem.floor_impact.damage_min[1]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[1]%$ de dano físico a cada inimigo atingido.",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_2"] = "Faz crescer espinhos de cristal pelos caminhos ao seu redor, causando %$heroes.hero_dragon_gem.floor_impact.damage_min[2]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[2]%$ de dano físico a cada inimigo atingido.",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_3"] = "Faz crescer espinhos de cristal pelos caminhos ao seu redor, causando %$heroes.hero_dragon_gem.floor_impact.damage_min[3]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[3]%$ de dano físico a cada inimigo atingido.",
["HERO_DRAGON_GEM_FLOOR_IMPACT_TITLE"] = "FRAGMENTSO PRISMÁTICOS",
["HERO_DRAGON_GEM_NAME"] = "Kosmyr",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_1"] = "Cristaliza um grupo de inimigos, atordoando-os por %$heroes.hero_dragon_gem.stun.duration[1]%$ segundos.",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_2"] = "Cristaliza um grupo de inimigos, atordoando-os por %$heroes.hero_dragon_gem.stun.duration[2]%$ segundos.",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_3"] = "Cristaliza um grupo de inimigos, atordoando-os por %$heroes.hero_dragon_gem.stun.duration[3]%$ segundos.",
["HERO_DRAGON_GEM_STUN_TITLE"] = "HÁLITO PARALISANTE",
["HERO_HUNTER_BEASTS_DESCRIPTION_1"] = "Invoca 2 morcegos que atacam inimigos próximos por %$heroes.hero_hunter.beasts.duration[1]%$ segundos, causando %$heroes.hero_hunter.beasts.damage_min[1]%$-%$heroes.hero_hunter.beasts.damage_max[1]%$ de dano físico. Cada morcego tem uma chance de roubar %$heroes.hero_hunter.beasts.gold_to_steal[1]%$ ouro de seu alvo.",
["HERO_HUNTER_BEASTS_DESCRIPTION_2"] = "Invoca 2 morcegos que atacam inimigos próximos por %$heroes.hero_hunter.beasts.duration[2]%$ segundos, causando %$heroes.hero_hunter.beasts.damage_min[2]%$-%$heroes.hero_hunter.beasts.damage_max[2]%$ de dano físico. Cada morcego tem uma chance de roubar %$heroes.hero_hunter.beasts.gold_to_steal[2]%$ ouro de seu alvo.",
["HERO_HUNTER_BEASTS_DESCRIPTION_3"] = "Invoca 2 morcegos que atacam inimigos próximos por %$heroes.hero_hunter.beasts.duration[3]%$ segundos, causando %$heroes.hero_hunter.beasts.damage_min[3]%$-%$heroes.hero_hunter.beasts.damage_max[3]%$ de dano físico. Cada morcego tem uma chance de roubar %$heroes.hero_hunter.beasts.gold_to_steal[3]%$ ouro de seu alvo.",
["HERO_HUNTER_BEASTS_TITLE"] = "FERAS DO CREPÚSCULO",
["HERO_HUNTER_CLASS"] = "Caçadora Prateada",
["HERO_HUNTER_DESC"] = "Nascida da união de um vampiro e um caçador renomado, Anya segue os passos de seu pai lutando contra os habitantes das trevas. A implacável caça aos cultistas rapidamente a levou às terras do sul e a juntar-se à Aliança.",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_1"] = "A cada 7º ataque corpo a corpo causa de %$heroes.hero_hunter.heal_strike.damage_min[1]%$ a %$heroes.hero_hunter.heal_strike.damage_max[1]%$ de dano verdadeiro e cura Anya por %$heroes.hero_hunter.heal_strike.heal_factor[1]%$% da saúde máxima do seu alvo.",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_2"] = "A cada 7º ataque corpo a corpo causa de %$heroes.hero_hunter.heal_strike.damage_min[2]%$ a %$heroes.hero_hunter.heal_strike.damage_max[2]%$ de dano verdadeiro e cura Anya por %$heroes.hero_hunter.heal_strike.heal_factor[2]%$% da saúde máxima do seu alvo.",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_3"] = "A cada 7º ataque corpo a corpo causa de %$heroes.hero_hunter.heal_strike.damage_min[3]%$ a %$heroes.hero_hunter.heal_strike.damage_max[3]%$ de dano verdadeiro e cura Anya por %$heroes.hero_hunter.heal_strike.heal_factor[3]%$% da saúde máxima do seu alvo.",
["HERO_HUNTER_HEAL_STRIKE_TITLE"] = "GARRA VAMPÍRICA",
["HERO_HUNTER_NAME"] = "Anya",
["HERO_HUNTER_RICOCHET_DESCRIPTION_1"] = "Anya se transforma em névoa e salta entre %$heroes.hero_hunter.ricochet.s_bounces[1]%$ inimigos, causando %$heroes.hero_hunter.ricochet.damage_min[1]%$-%$heroes.hero_hunter.ricochet.damage_max[1]%$ de dano físico a cada um.",
["HERO_HUNTER_RICOCHET_DESCRIPTION_2"] = "Anya se transforma em névoa e salta entre %$heroes.hero_hunter.ricochet.s_bounces[2]%$ inimigos, causando %$heroes.hero_hunter.ricochet.damage_min[2]%$-%$heroes.hero_hunter.ricochet.damage_max[2]%$ de dano físico a cada um.",
["HERO_HUNTER_RICOCHET_DESCRIPTION_3"] = "Anya se transforma em névoa e salta entre %$heroes.hero_hunter.ricochet.s_bounces[3]%$ inimigos, causando %$heroes.hero_hunter.ricochet.damage_min[3]%$-%$heroes.hero_hunter.ricochet.damage_max[3]%$ de dano físico a cada um.",
["HERO_HUNTER_RICOCHET_TITLE"] = "PASSO NEBULOSO",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_1"] = "Atira em todos os inimigos ao seu redor, causando um dano verdadeiro de %$heroes.hero_hunter.shoot_around.s_damage_min[1]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[1]%$ a cada um.",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_2"] = "Atira em todos os inimigos ao seu redor, causando %$heroes.hero_hunter.shoot_around.s_damage_min[2]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[2]%$ de dano verdadeiro por segundo a cada um.",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_3"] = "Atira em todos os inimigos ao seu redor, causando %$heroes.hero_hunter.shoot_around.s_damage_min[3]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[3]%$ de dano verdadeiro por segundo a cada um.",
["HERO_HUNTER_SHOOT_AROUND_TITLE"] = "TEMPESTADE DE PRATA",
["HERO_HUNTER_SPIRIT_DESCRIPTION_1"] = "Invoca uma projeção de Dante que causa %$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[2]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[2]%$ de dano verdadeiro durante %$heroes.hero_hunter.ultimate.duration%$ segundos. Ressuscita Anya se o corpo dela estiver por perto.",
["HERO_HUNTER_SPIRIT_DESCRIPTION_2"] = "Invoca uma projeção de Dante que causa %$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[3]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[3]%$ de dano verdadeiro durante %$heroes.hero_hunter.ultimate.duration%$ segundos. Ressuscita Anya se o corpo dela estiver por perto.",
["HERO_HUNTER_SPIRIT_DESCRIPTION_3"] = "Invoca uma projeção de Dante que causa %$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[4]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[4]%$ de dano verdadeiro durante %$heroes.hero_hunter.ultimate.duration%$ segundos. Ressuscita Anya se o corpo dela estiver por perto.",
["HERO_HUNTER_SPIRIT_MENUBOTTOM_DESCRIPTION"] = "Invoca uma projeção de Dante que desacelera e ataca inimigos.",
["HERO_HUNTER_SPIRIT_MENUBOTTOM_NAME"] = "Auxílio do Caçador",
["HERO_HUNTER_SPIRIT_TITLE"] = "AUXÍLIO DO CAÇADOR",
["HERO_HUNTER_ULTIMATE_ENTITY_NAME"] = "Projeção de Dante",
["HERO_LAVA_CLASS"] = "Fúria Derretida",
["HERO_LAVA_DESC"] = "Um ser de fogo e destruição com um temperamento explosivo, despertado de um sono profundo pelas atividades de Grymbeard. Como o diálogo não é seu forte, Kratoa irá abrir caminho através das linhas inimigas até se acalmar e poder dormir novamente.",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_1"] = "Lança uma bola de lava que causa %$heroes.hero_lava.double_trouble.s_damage[1]%$ de dano explosivo aos inimigos e gera um magmite de %$heroes.hero_lava.double_trouble.soldier.hp_max[1]%$ de vida que luta por %$heroes.hero_lava.double_trouble.soldier.duration%$ segundos.",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_2"] = "Lança uma bola de lava que causa %$heroes.hero_lava.double_trouble.s_damage[2]%$ de dano explosivo aos inimigos e gera um magmite de %$heroes.hero_lava.double_trouble.soldier.hp_max[2]%$ de vida que luta por %$heroes.hero_lava.double_trouble.soldier.duration%$ segundos.",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_3"] = "Lança uma bola de lava que causa %$heroes.hero_lava.double_trouble.s_damage[3]%$ de dano explosivo aos inimigos e gera um magmite de %$heroes.hero_lava.double_trouble.soldier.hp_max[3]%$ de vida que luta por %$heroes.hero_lava.double_trouble.soldier.duration%$ segundos.",
["HERO_LAVA_DOUBLE_TROUBLE_SOLDIER_NAME"] = "Magmito",
["HERO_LAVA_DOUBLE_TROUBLE_TITLE"] = "PROBLEMA EM DOBRO",
["HERO_LAVA_HOTHEADED_DESCRIPTION_1"] = "Quando Kratoa revive, concede um aumento de %$heroes.hero_lava.hotheaded.s_damage_factors[1]%$% de dano para as torres próximas por %$heroes.hero_lava.hotheaded.durations[1]%$ segundos.",
["HERO_LAVA_HOTHEADED_DESCRIPTION_2"] = "Quando Kratoa revive, concede um aumento de %$heroes.hero_lava.hotheaded.s_damage_factors[2]%$% de dano para as torres próximas por %$heroes.hero_lava.hotheaded.durations[2]%$ segundos.",
["HERO_LAVA_HOTHEADED_DESCRIPTION_3"] = "Quando Kratoa revive, concede um aumento de %$heroes.hero_lava.hotheaded.s_damage_factors[3]%$% de dano para as torres próximas por %$heroes.hero_lava.hotheaded.durations[3]%$ segundos.",
["HERO_LAVA_HOTHEADED_TITLE"] = "CABEÇA-QUENTE",
["HERO_LAVA_NAME"] = "Kratoa",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_1"] = "Ataca repetidamente um inimigo, causando de %$heroes.hero_lava.temper_tantrum.s_damage_min[1]%$ a %$heroes.hero_lava.temper_tantrum.s_damage_max[1]%$ de dano físico e atordoando o alvo por %$heroes.hero_lava.temper_tantrum.duration[1]%$ segundos.",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_2"] = "Ataca repetidamente um inimigo, causando de %$heroes.hero_lava.temper_tantrum.s_damage_min[2]%$ a %$heroes.hero_lava.temper_tantrum.s_damage_max[2]%$ de dano físico e atordoando o alvo por %$heroes.hero_lava.temper_tantrum.duration[2]%$ segundos.",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_3"] = "Ataca repetidamente um inimigo, causando de %$heroes.hero_lava.temper_tantrum.s_damage_min[3]%$ a %$heroes.hero_lava.temper_tantrum.s_damage_max[3]%$ de dano físico e atordoando o alvo por %$heroes.hero_lava.temper_tantrum.duration[3]%$ segundos.",
["HERO_LAVA_TEMPER_TANTRUM_TITLE"] = "BIRRA",
["HERO_LAVA_ULTIMATE_DESCRIPTION_1"] = "Lança %$heroes.hero_lava.ultimate.fireball_count[2]%$ explosões de lava no caminho, cada uma causando %$heroes.hero_lava.ultimate.bullet.s_damage[2]%$ de dano verdadeiro a cada inimigo atingido e queimando-os por %$heroes.hero_lava.ultimate.bullet.scorch.duration%$ segundos.",
["HERO_LAVA_ULTIMATE_DESCRIPTION_2"] = "Lança %$heroes.hero_lava.ultimate.fireball_count[3]%$ explosões de lava no caminho, cada uma causando %$heroes.hero_lava.ultimate.bullet.s_damage[3]%$ de dano verdadeiro a cada inimigo atingido e queimando-os por %$heroes.hero_lava.ultimate.bullet.scorch.duration%$ segundos.",
["HERO_LAVA_ULTIMATE_DESCRIPTION_3"] = "Lança %$heroes.hero_lava.ultimate.fireball_count[4]%$ explosões de lava no caminho, cada uma causando %$heroes.hero_lava.ultimate.bullet.s_damage[4]%$ de dano verdadeiro a cada inimigo atingido e queimando-os por %$heroes.hero_lava.ultimate.bullet.scorch.duration%$ segundos.",
["HERO_LAVA_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "Lança explosões de lava no caminho, queimando o chão.",
["HERO_LAVA_ULTIMATE_MENUBOTTOM_NAME"] = "Explosão Raivosa",
["HERO_LAVA_ULTIMATE_TITLE"] = "EXPLOSÃO RAIVOSA",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_1"] = "Jorra lava sobre os inimigos, causando %$heroes.hero_lava.wild_eruption.s_damage[1]%$ de dano verdadeiro por segundo e queimando os inimigos por %$heroes.hero_lava.wild_eruption.duration[1]%$ segundos.",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_2"] = "Jorra lava sobre os inimigos, causando %$heroes.hero_lava.wild_eruption.s_damage[2]%$ de dano verdadeiro por segundo e queimando os inimigos por %$heroes.hero_lava.wild_eruption.duration[2]%$ segundos.",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_3"] = "Jorra lava sobre os inimigos, causando %$heroes.hero_lava.wild_eruption.s_damage[3]%$ de dano verdadeiro por segundo e queimando os inimigos por %$heroes.hero_lava.wild_eruption.duration[3]%$ segundos.",
["HERO_LAVA_WILD_ERUPTION_TITLE"] = "ERUPÇÃO SELVAGEM",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_1"] = "Invoca %$heroes.hero_lumenir.ultimate.soldier_count[1]%$ guerreiros de luz que atordoam brevemente inimigos próximos e causam %$heroes.hero_lumenir.ultimate.damage_min[1]%$-%$heroes.hero_lumenir.ultimate.damage_max[1]%$ de dano verdadeiro.",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_2"] = "Invoca %$heroes.hero_lumenir.ultimate.soldier_count[2]%$ guerreiros de luz que atordoam brevemente inimigos próximos e causam %$heroes.hero_lumenir.ultimate.damage_min[2]%$-%$heroes.hero_lumenir.ultimate.damage_max[2]%$ de dano verdadeiro.",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_3"] = "Invoca %$heroes.hero_lumenir.ultimate.soldier_count[3]%$ guerreiros de luz que atordoam brevemente inimigos próximos e causam %$heroes.hero_lumenir.ultimate.damage_min[3]%$-%$heroes.hero_lumenir.ultimate.damage_max[3]%$ de dano verdadeiro.",
["HERO_LUMENIR_ARROW_STORM_MENUBOTTOM_DESCRIPTION"] = "Invoca guerreiros divinos que combatem os inimigos.",
["HERO_LUMENIR_ARROW_STORM_MENUBOTTOM_NAME"] = "Chamado do Triunfo",
["HERO_LUMENIR_ARROW_STORM_TITLE"] = "CHAMADO DO TRIUNFO",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_1"] = "Lança uma espada divina de luz sobre o inimigo mais forte por perto, causando %$heroes.hero_lumenir.celestial_judgement.damage[1]%$ de dano verdadeiro e atordoando-o por %$heroes.hero_lumenir.celestial_judgement.stun_duration[1]%$ segundos.",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_2"] = "Lança uma espada divina de luz sobre o inimigo mais forte por perto, causando %$heroes.hero_lumenir.celestial_judgement.damage[2]%$ de dano verdadeiro e atordoando-o por %$heroes.hero_lumenir.celestial_judgement.stun_duration[2]%$ segundos.",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_3"] = "Lança uma espada divina de luz sobre o inimigo mais forte por perto, causando %$heroes.hero_lumenir.celestial_judgement.damage[3]%$ de dano verdadeiro e atordoando-o por %$heroes.hero_lumenir.celestial_judgement.stun_duration[3]%$ segundos.",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_TITLE"] = "JULGAMENTO CELESTIAL",
["HERO_LUMENIR_CLASS"] = "Portadora da Luz",
["HERO_LUMENIR_DESC"] = "Sobrevoando os reinos, Lumenir se destaca como o avatar da justiça e determinação. Ela é a lendária Portadora da Luz, reverenciada pelos paladinos de Linirea, aos quais ela concede suas bênçãos, dando-lhes grandes poderes para auxiliar na luta contra o mal.",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_1"] = "Solta %$heroes.hero_lumenir.fire_balls.flames_count[1]%$ orbes de luz divina que percorrem o caminho danificando inimigos. Cada orbe causa de %$heroes.hero_lumenir.fire_balls.flame_damage_min[1]%$ a %$heroes.hero_lumenir.fire_balls.flame_damage_max[1]%$ de dano verdadeiro a cada inimigo que atravessa.",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_2"] = "Solta %$heroes.hero_lumenir.fire_balls.flames_count[2]%$ orbes de luz divina que percorrem o caminho danificando inimigos. Cada orbe causa de %$heroes.hero_lumenir.fire_balls.flame_damage_min[2]%$ a %$heroes.hero_lumenir.fire_balls.flame_damage_max[2]%$ de dano verdadeiro a cada inimigo que atravessa.",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_3"] = "Solta %$heroes.hero_lumenir.fire_balls.flames_count[3]%$ orbes de luz divina que percorrem o caminho danificando inimigos. Cada orbe causa de %$heroes.hero_lumenir.fire_balls.flame_damage_min[3]%$ a %$heroes.hero_lumenir.fire_balls.flame_damage_max[3]%$ de dano verdadeiro a cada inimigo que atravessa.",
["HERO_LUMENIR_FIRE_BALLS_TITLE"] = "ONDA RADIANTE",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_1"] = "Invoca um pequeno dragão de luz que segue o outro herói equipado por %$heroes.hero_lumenir.mini_dragon.dragon.duration[1]%$ segundos. O dragão causa %$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[1]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[1]%$ de dano físico por ataque.",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_2"] = "Invoca um pequeno dragão de luz que segue o outro herói equipado por %$heroes.hero_lumenir.mini_dragon.dragon.duration[2]%$ segundos. O dragão causa %$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[2]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[2]%$ de dano físico por ataque.",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_3"] = "Invoca um pequeno dragão de luz que segue o outro herói equipado por %$heroes.hero_lumenir.mini_dragon.dragon.duration[3]%$ segundos. O dragão causa %$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[3]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[3]%$ de dano físico por ataque.",
["HERO_LUMENIR_MINI_DRAGON_TITLE"] = "COMPANHEIRO LUMINOSO",
["HERO_LUMENIR_NAME"] = "Lumenir",
["HERO_LUMENIR_SHIELD_DESCRIPTION_1"] = "Concede às unidades aliadas um escudo de armadura de %$heroes.hero_lumenir.shield.armor[1]%$% que reflete %$heroes.hero_lumenir.shield.spiked_armor[1]%$% do dano de volta aos inimigos.",
["HERO_LUMENIR_SHIELD_DESCRIPTION_2"] = "Concede às unidades aliadas um escudo de armadura de %$heroes.hero_lumenir.shield.armor[2]%$% que reflete %$heroes.hero_lumenir.shield.spiked_armor[2]%$% do dano de volta aos inimigos.",
["HERO_LUMENIR_SHIELD_DESCRIPTION_3"] = "Concede às unidades aliadas um escudo de armadura de %$heroes.hero_lumenir.shield.armor[3]%$% que reflete %$heroes.hero_lumenir.shield.spiked_armor[3]%$% do dano de volta aos inimigos.",
["HERO_LUMENIR_SHIELD_TITLE"] = "BÊNÇÃO DA RETRIBUIÇÃO",
["HERO_MECHA_CLASS"] = "Ameaça Móvel",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_1"] = "Convoca um zepelim goblin que bombardeia inimigos perto da área alvo, causando %$heroes.hero_mecha.ultimate.ranged_attack.damage_min[2]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[2]%$ de dano verdadeiro em área por ataque.",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_2"] = "Convoca um zepelim goblin que bombardeia inimigos perto da área alvo, causando %$heroes.hero_mecha.ultimate.ranged_attack.damage_min[3]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[3]%$ de dano verdadeiro em área por ataque.",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_3"] = "Convoca um zepelim goblin que bombardeia inimigos perto da área alvo, causando %$heroes.hero_mecha.ultimate.ranged_attack.damage_min[4]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[4]%$ de dano verdadeiro em área por ataque.",
["HERO_MECHA_DEATH_FROM_ABOVE_MENUBOTTOM_DESCRIPTION"] = "Invoca um zepelim que bombardeia inimigos na área.",
["HERO_MECHA_DEATH_FROM_ABOVE_MENUBOTTOM_NAME"] = "Morte de Cima",
["HERO_MECHA_DEATH_FROM_ABOVE_TITLE"] = "MORTE VINDA DE CIMA",
["HERO_MECHA_DESC"] = "Nascido da mente de dois goblins inventores malucos e construído sobre os fundamentos da tecnologia anã roubada, Onagro é a máquina de guerra definitiva para os peles-verdes e uma visão aterrorizante para os inimigos do Exército das Trevas.",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_1"] = "Invoca %$heroes.hero_mecha.goblidrones.units%$ drones que atacam inimigos por %$heroes.hero_mecha.goblidrones.drone.duration[1]%$ segundos, causando %$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[1]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[1]%$ de dano físico por ataque.",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_2"] = "Invoca %$heroes.hero_mecha.goblidrones.units%$ drones que atacam inimigos por %$heroes.hero_mecha.goblidrones.drone.duration[2]%$ segundos, causando %$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[2]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[2]%$ de dano físico por ataque.",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_3"] = "Invoca %$heroes.hero_mecha.goblidrones.units%$ drones que atacam inimigos por %$heroes.hero_mecha.goblidrones.drone.duration[3]%$ segundos, causando %$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[3]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[3]%$ de dano físico por ataque.",
["HERO_MECHA_GOBLIDRONES_TITLE"] = "GOBLIDRONES",
["HERO_MECHA_MINE_DROP_DESCRIPTION_1"] = "Enquanto permanece parado, o mecha deixa periodicamente até %$heroes.hero_mecha.mine_drop.max_mines[1]%$ minas explosivas no caminho. As minas explodem causando %$heroes.hero_mecha.mine_drop.damage_min[1]%$-%$heroes.hero_mecha.mine_drop.damage_max[1]%$ de dano explosivo cada uma.",
["HERO_MECHA_MINE_DROP_DESCRIPTION_2"] = "Enquanto permanece parado, o mecha deixa periodicamente até %$heroes.hero_mecha.mine_drop.max_mines[2]%$ minas explosivas no caminho. As minas explodem causando %$heroes.hero_mecha.mine_drop.damage_min[2]%$-%$heroes.hero_mecha.mine_drop.damage_max[2]%$ de dano explosivo cada uma.",
["HERO_MECHA_MINE_DROP_DESCRIPTION_3"] = "Enquanto permanece parado, o mecha deixa periodicamente até %$heroes.hero_mecha.mine_drop.max_mines[3]%$ minas explosivas no caminho. As minas explodem causando %$heroes.hero_mecha.mine_drop.damage_min[3]%$-%$heroes.hero_mecha.mine_drop.damage_max[3]%$ de dano explosivo cada uma.",
["HERO_MECHA_MINE_DROP_TITLE"] = "LANÇAMENTO DE MINAS",
["HERO_MECHA_NAME"] = "Onagro",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_1"] = "O mecha bate no chão, atordoando brevemente e causando %$heroes.hero_mecha.power_slam.s_damage[1]%$ de dano físico a todos os inimigos próximos.",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_2"] = "O mecha bate no chão, atordoando brevemente e causando %$heroes.hero_mecha.power_slam.s_damage[2]%$ de dano físico a todos os inimigos próximos.",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_3"] = "O mecha bate no chão, atordoando brevemente e causando %$heroes.hero_mecha.power_slam.s_damage[3]%$ de dano físico a todos os inimigos próximos.",
["HERO_MECHA_POWER_SLAM_TITLE"] = "GOLPE DE FORÇA",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_1"] = "Lança uma bomba que espalha alcatrão no caminho, reduzindo a velocidade dos inimigos em %$heroes.hero_mecha.tar_bomb.slow_factor%$% por %$heroes.hero_mecha.tar_bomb.duration[1]%$ segundos.",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_2"] = "Lança uma bomba que espalha alcatrão no caminho, reduzindo a velocidade dos inimigos em %$heroes.hero_mecha.tar_bomb.slow_factor%$% por %$heroes.hero_mecha.tar_bomb.duration[2]%$ segundos.",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_3"] = "Lança uma bomba que espalha alcatrão no caminho, reduzindo a velocidade dos inimigos em %$heroes.hero_mecha.tar_bomb.slow_factor%$% por %$heroes.hero_mecha.tar_bomb.duration[3]%$ segundos.",
["HERO_MECHA_TAR_BOMB_TITLE"] = "BOMBA DE ALCATRÃO",
["HERO_MUYRN_CLASS"] = "Guardião da Floresta",
["HERO_MUYRN_DESC"] = "Apesar de sua aparência infantil, o trapaceiro Nyru vem protegendo a floresta por centenas de anos usando sua conexão com as forças da natureza. Ele se juntou à Aliança para pôr um fim às crescentes ondas de invasores ameaçando seu lar.",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_1"] = "Encanta todos os inimigos numa área, reduzindo o dano de ataque deles em %$heroes.hero_muyrn.faery_dust.s_damage_factor[1]%$% por %$heroes.hero_muyrn.faery_dust.duration[1]%$ segundos.",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_2"] = "Encanta todos os inimigos numa área, reduzindo o dano de ataque deles em %$heroes.hero_muyrn.faery_dust.s_damage_factor[2]%$% por %$heroes.hero_muyrn.faery_dust.duration[2]%$ segundos.",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_3"] = "Encanta todos os inimigos numa área, reduzindo o dano de ataque deles em %$heroes.hero_muyrn.faery_dust.s_damage_factor[3]%$% por %$heroes.hero_muyrn.faery_dust.duration[3]%$ segundos.",
["HERO_MUYRN_FAERY_DUST_TITLE"] = "Encanto de Enfraquecimento",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_1"] = "Em combate, Nyru cria um escudo de folhas ao seu redor. O escudo causa %$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[1]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[1]%$ de dano mágico por segundo e cura Nyru por %$heroes.hero_muyrn.leaf_whirlwind.duration[1]%$ segundos.",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_2"] = "Em combate, Nyru cria um escudo de folhas ao seu redor. O escudo causa %$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[2]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[2]%$ de dano mágico por segundo e cura Nyru por %$heroes.hero_muyrn.leaf_whirlwind.duration[2]%$ segundos.",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_3"] = "Em combate, Nyru cria um escudo de folhas ao seu redor. O escudo causa %$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[3]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[3]%$ de dano mágico por segundo e cura Nyru por %$heroes.hero_muyrn.leaf_whirlwind.duration[3]%$ segundos.",
["HERO_MUYRN_LEAF_WHIRLWIND_TITLE"] = "Turbilhão de Folhas",
["HERO_MUYRN_NAME"] = "Nyru",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_1"] = "Gera raízes sobre uma área por %$heroes.hero_muyrn.ultimate.duration[2]%$ segundos, retardando inimigos e causando %$heroes.hero_muyrn.ultimate.s_damage_min[2]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[2]%$ de dano real por segundo.",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_2"] = "Gera raízes sobre uma área por %$heroes.hero_muyrn.ultimate.duration[3]%$ segundos, retardando inimigos e causando %$heroes.hero_muyrn.ultimate.s_damage_min[3]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[3]%$ de dano real por segundo.",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_3"] = "Gera raízes sobre uma área por %$heroes.hero_muyrn.ultimate.duration[4]%$ segundos, retardando inimigos e causando %$heroes.hero_muyrn.ultimate.s_damage_min[4]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[4]%$ de dano real por segundo.",
["HERO_MUYRN_ROOT_DEFENDER_MENUBOTTOM_DESCRIPTION"] = "Gera raízes que danificam e retardam inimigos.",
["HERO_MUYRN_ROOT_DEFENDER_MENUBOTTOM_NAME"] = "Raízes Defensoras",
["HERO_MUYRN_ROOT_DEFENDER_TITLE"] = "RAÍZES DEFENSORAS",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_1"] = "Invoca %$heroes.hero_muyrn.sentinel_wisps.max_summons[1]%$ espírito amistoso que segue Nyru por %$heroes.hero_muyrn.sentinel_wisps.wisp.duration[1]%$ segundos. O espírito causa %$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[1]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[1]%$ de dano mágico.",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_2"] = "Invoca %$heroes.hero_muyrn.sentinel_wisps.max_summons[2]%$ espíritos amistosos que seguem Nyru por %$heroes.hero_muyrn.sentinel_wisps.wisp.duration[2]%$ segundos. Os espíritos causam %$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[2]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[2]%$ de dano mágico.",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_3"] = "Invoca %$heroes.hero_muyrn.sentinel_wisps.max_summons[3]%$ espíritos amistosos que seguem Nyru por %$heroes.hero_muyrn.sentinel_wisps.wisp.duration[3]%$ segundos. Os espíritos causam %$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[3]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[3]%$ de dano mágico.",
["HERO_MUYRN_SENTINEL_WISPS_TITLE"] = "FOGO-FÁTUO",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_1"] = "Dispara uma explosão verde de energia em direção a um inimigo, causando %$heroes.hero_muyrn.verdant_blast.s_damage[1]%$ de dano mágico.",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_2"] = "Dispara uma explosão verde de energia em direção a um inimigo, causando %$heroes.hero_muyrn.verdant_blast.s_damage[2]%$ de dano mágico.",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_3"] = "Dispara uma explosão verde de energia em direção a um inimigo, causando %$heroes.hero_muyrn.verdant_blast.s_damage[3]%$ de dano mágico.",
["HERO_MUYRN_VERDANT_BLAST_TITLE"] = "Explosão Verde",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_1"] = "Ataca brutalmente um inimigo com sua espada, causando %$heroes.hero_raelyn.brutal_slash.s_damage[1]%$ de dano verdadeiro.",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_2"] = "Ataca brutalmente um inimigo com sua espada, causando %$heroes.hero_raelyn.brutal_slash.s_damage[2]%$ de dano verdadeiro.",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_3"] = "Ataca brutalmente um inimigo com sua espada, causando %$heroes.hero_raelyn.brutal_slash.s_damage[3]%$ de dano verdadeiro.",
["HERO_RAELYN_BRUTAL_SLASH_TITLE"] = "TALHO BRUTAL",
["HERO_RAELYN_CLASS"] = "Tenente Sombria",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_1"] = "Invoca um Cavaleiro Negro que possui %$heroes.hero_raelyn.ultimate.entity.hp_max[2]%$ de saúde e causa %$heroes.hero_raelyn.ultimate.entity.damage_min[2]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[2]%$ de dano verdadeiro.",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_2"] = "O Cavaleiro Negro possui %$heroes.hero_raelyn.ultimate.entity.hp_max[3]%$ de saúde e causa %$heroes.hero_raelyn.ultimate.entity.damage_min[3]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[3]%$ de dano verdadeiro.",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_3"] = "O Cavaleiro Negro possui %$heroes.hero_raelyn.ultimate.entity.hp_max[4]%$ de saúde e causa %$heroes.hero_raelyn.ultimate.entity.damage_min[4]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[4]%$ de dano verdadeiro.",
["HERO_RAELYN_COMMAND_ORDERS_MENUBOTTOM_DESCRIPTION"] = "Invoca um Cavaleiro Negro no campo de batalha.",
["HERO_RAELYN_COMMAND_ORDERS_MENUBOTTOM_NAME"] = "Ordens de Comando",
["HERO_RAELYN_COMMAND_ORDERS_TITLE"] = "ORDENS DE COMANDO",
["HERO_RAELYN_DESC"] = "A imponente Raelyn vive para liderar os Cavaleiros Negros na vanguarda. Sua brutalidade e implacabilidade lhe renderam o reconhecimento de Vez’nan e o medo dos Linireanos. Sempre pronta para uma boa luta, foi a primeira voluntária a se juntar às fileiras do Mago Negro.",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_1"] = "Atordoa inimigos próximos por %$heroes.hero_raelyn.inspire_fear.stun_duration[1]%$ segundos e reduz o dano de ataque deles em %$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[1]%$% por %$heroes.hero_raelyn.inspire_fear.damage_duration[1]%$ segundos.",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_2"] = "Atordoa inimigos próximos por %$heroes.hero_raelyn.inspire_fear.stun_duration[2]%$ segundos e reduz o dano de ataque deles em %$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[2]%$% por %$heroes.hero_raelyn.inspire_fear.damage_duration[2]%$ segundos.",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_3"] = "Atordoa inimigos próximos por %$heroes.hero_raelyn.inspire_fear.stun_duration[3]%$ segundos e reduz o dano de ataque deles em %$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[3]%$% por %$heroes.hero_raelyn.inspire_fear.damage_duration[3]%$ segundos.",
["HERO_RAELYN_INSPIRE_FEAR_TITLE"] = "INSPIRAR MEDO",
["HERO_RAELYN_NAME"] = "Raelyn",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_1"] = "Por %$heroes.hero_raelyn.onslaught.duration[1]%$ segundos, Raelyn ataca mais rápido e causa %$heroes.hero_raelyn.onslaught.damage_factor[1]%$% do seu dano de ataque em uma pequena área ao redor do alvo principal.",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_2"] = "Por %$heroes.hero_raelyn.onslaught.duration[2]%$ segundos, Raelyn ataca mais rápido e causa %$heroes.hero_raelyn.onslaught.damage_factor[2]%$% do seu dano de ataque em uma pequena área ao redor do alvo principal.",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_3"] = "Por %$heroes.hero_raelyn.onslaught.duration[3]%$ segundos, Raelyn ataca mais rápido e causa %$heroes.hero_raelyn.onslaught.damage_factor[3]%$% do seu dano de ataque em uma pequena área ao redor do alvo principal.",
["HERO_RAELYN_ONSLAUGHT_TITLE"] = "ARREMETIDA",
["HERO_RAELYN_ULTIMATE_ENTITY_NAME"] = "Cavaleiro Negro",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_1"] = "Quando em combate, Raelyn gera um escudo de saúde baseado na quantidade de inimigos próximos a ela (%$heroes.hero_raelyn.unbreakable.shield_per_enemy[1]%$% do total de sua vida por cada um de até %$heroes.hero_raelyn.unbreakable.max_targets%$ inimigos)",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_2"] = "Quando em combate, Raelyn gera um escudo de saúde baseado na quantidade de inimigos próximos a ela (%$heroes.hero_raelyn.unbreakable.shield_per_enemy[2]%$% do total de sua vida por cada um de até %$heroes.hero_raelyn.unbreakable.max_targets%$ inimigos)",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_3"] = "Quando em combate, Raelyn gera um escudo de saúde baseado na quantidade de inimigos próximos a ela (%$heroes.hero_raelyn.unbreakable.shield_per_enemy[3]%$% do total de sua vida por cada um de até %$heroes.hero_raelyn.unbreakable.max_targets%$ inimigos)",
["HERO_RAELYN_UNBREAKABLE_TITLE"] = "INQUEBRÁVEL",
["HERO_ROBOT_CLASS"] = "Golem de Cerco",
["HERO_ROBOT_DESC"] = "Os mestres ferreiros do Exército das Trevas se superaram ao criar um autômato de guerra que eles apropriadamente nomearam Warhead. Reforçado por motores ardentes e indiferente às emoções, Warhead lança-se à batalha desconsiderando tanto amigos quanto inimigos.",
["HERO_ROBOT_EXPLODE_DESCRIPTION_1"] = "Gera uma explosão ardente que causa %$heroes.hero_robot.explode.damage_min[1]%$-%$heroes.hero_robot.explode.damage_max[1]%$ de dano explosivo aos inimigos e os queima por %$heroes.hero_robot.explode.burning_duration%$ segundos. A queimadura causa %$heroes.hero_robot.explode.s_burning_damage[1]%$ de dano por segundo.",
["HERO_ROBOT_EXPLODE_DESCRIPTION_2"] = "Gera uma explosão ardente que causa %$heroes.hero_robot.explode.damage_min[2]%$-%$heroes.hero_robot.explode.damage_max[2]%$ de dano explosivo aos inimigos e os queima por %$heroes.hero_robot.explode.burning_duration%$ segundos. A queimadura causa %$heroes.hero_robot.explode.s_burning_damage[2]%$ de dano por segundo.",
["HERO_ROBOT_EXPLODE_DESCRIPTION_3"] = "Gera uma explosão ardente que causa %$heroes.hero_robot.explode.damage_min[3]%$-%$heroes.hero_robot.explode.damage_max[3]%$ de dano explosivo aos inimigos e os queima por %$heroes.hero_robot.explode.burning_duration%$ segundos. A queimadura causa %$heroes.hero_robot.explode.s_burning_damage[3]%$ de dano por segundo.",
["HERO_ROBOT_EXPLODE_TITLE"] = "IMOLAÇÃO",
["HERO_ROBOT_FIRE_DESCRIPTION_1"] = "Dispara um canhão cheio de brasas ardentes, causando %$heroes.hero_robot.fire.damage_min[1]%$-%$heroes.hero_robot.fire.damage_max[1]%$ de dano físico e retardando os inimigos por %$heroes.hero_robot.fire.s_slow_duration[1]%$ segundos.",
["HERO_ROBOT_FIRE_DESCRIPTION_2"] = "Dispara um canhão cheio de brasas ardentes, causando %$heroes.hero_robot.fire.damage_min[2]%$-%$heroes.hero_robot.fire.damage_max[2]%$ de dano físico e retardando os inimigos por %$heroes.hero_robot.fire.s_slow_duration[1]%$ segundos.",
["HERO_ROBOT_FIRE_DESCRIPTION_3"] = "Dispara um canhão cheio de brasas ardentes, causando %$heroes.hero_robot.fire.damage_min[3]%$-%$heroes.hero_robot.fire.damage_max[3]%$ de dano físico e retardando os inimigos por %$heroes.hero_robot.fire.s_slow_duration[1]%$ segundos.",
["HERO_ROBOT_FIRE_TITLE"] = "CORTINA DE FUMAÇA",
["HERO_ROBOT_JUMP_DESCRIPTION_1"] = "Pula sobre um inimigo, atordoando-o por %$heroes.hero_robot.jump.stun_duration[1]%$ segundos e causando %$heroes.hero_robot.jump.s_damage[1]%$ de dano físico em uma área.",
["HERO_ROBOT_JUMP_DESCRIPTION_2"] = "Pula sobre um inimigo, atordoando-o por %$heroes.hero_robot.jump.stun_duration[2]%$ segundos e causando %$heroes.hero_robot.jump.s_damage[2]%$ de dano físico em uma área.",
["HERO_ROBOT_JUMP_DESCRIPTION_3"] = "Pula sobre um inimigo, atordoando-o por %$heroes.hero_robot.jump.stun_duration[3]%$ segundos e causando %$heroes.hero_robot.jump.s_damage[3]%$ de dano físico em uma área.",
["HERO_ROBOT_JUMP_TITLE"] = "IMPACTO PROFUNDO",
["HERO_ROBOT_NAME"] = "Warhead",
["HERO_ROBOT_TRAIN_DESCRIPTION_1"] = "Invoca uma carroça de guerra que percorre o caminho causando %$heroes.hero_robot.ultimate.s_damage[2]%$ de dano aos inimigos e queimando-os por %$heroes.hero_robot.ultimate.burning_duration%$ segundos. A queimadura causa %$heroes.hero_robot.ultimate.s_burning_damage%$ de dano por segundo.",
["HERO_ROBOT_TRAIN_DESCRIPTION_2"] = "Invoca uma carroça de guerra que percorre o caminho causando %$heroes.hero_robot.ultimate.s_damage[3]%$ de dano aos inimigos e queimando-os por %$heroes.hero_robot.ultimate.burning_duration%$ segundos. A queimadura causa %$heroes.hero_robot.ultimate.s_burning_damage%$ de dano por segundo.",
["HERO_ROBOT_TRAIN_DESCRIPTION_3"] = "Invoca uma carroça de guerra que percorre o caminho causando %$heroes.hero_robot.ultimate.s_damage[4]%$ de dano aos inimigos e queimando-os por %$heroes.hero_robot.ultimate.burning_duration%$ segundos. A queimadura causa %$heroes.hero_robot.ultimate.s_burning_damage%$ de dano por segundo.",
["HERO_ROBOT_TRAIN_MENUBOTTOM_DESCRIPTION"] = "Invoca uma carroça de guerra que atropela inimigos.",
["HERO_ROBOT_TRAIN_MENUBOTTOM_NAME"] = "Cabeça de Motor",
["HERO_ROBOT_TRAIN_TITLE"] = "CABEÇA DE MOTOR",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_1"] = "Ataca um inimigo com menos de %$heroes.hero_robot.uppercut.s_life_threshold[1]%$% de saúde, finalizando-o instantaneamente.",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_2"] = "Ataca um inimigo com menos de %$heroes.hero_robot.uppercut.s_life_threshold[2]%$% de saúde, finalizando-o instantaneamente.",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_3"] = "Ataca um inimigo com menos de %$heroes.hero_robot.uppercut.s_life_threshold[3]%$% de saúde, finalizando-o instantaneamente.",
["HERO_ROBOT_UPPERCUT_TITLE"] = "GANCHO DE FERRO",
["HERO_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_1"] = "Este herói está incluído na Campanha de Ameaça Colossal.",
["HERO_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_2"] = "Este herói está incluído na campanha \"Jornada do Wukong\".",
["HERO_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_1"] = "Campanha de Ameaça Colossal",
["HERO_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_2"] = "Campanha A Jornada de Wukong",
["HERO_ROOM_EQUIPPED_HEROES"] = "Heróis Equipados",
["HERO_ROOM_GET_DLC"] = "PEGUE",
["HERO_ROOM_LABEL_ROSTER_THUMB_NEW"] = "Novo!",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_1"] = "Invoca uma reflexão mágica de Therien que ataca inimigos, causando %$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[1]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[1]%$ de dano mágico.",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_2"] = "Invoca uma reflexão mágica de Therien que ataca inimigos, causando %$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[2]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[2]%$ de dano mágico.",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_3"] = "Invoca uma reflexão mágica de Therien que ataca inimigos, causando %$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[3]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[3]%$ de dano mágico.",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_ENTITY_NAME"] = "Reflexão Astral",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_TITLE"] = "Reflexão Astral",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_1"] = "Protege uma unidade aliada prevenindo até %$heroes.hero_space_elf.black_aegis.shield_base[1]%$ de dano. O escudo explode após um momento, causando %$heroes.hero_space_elf.black_aegis.explosion_damage[1]%$ de dano mágico numa área.",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_2"] = "Protege uma unidade aliada prevenindo até %$heroes.hero_space_elf.black_aegis.shield_base[2]%$ de dano. O escudo explosivo agora causa %$heroes.hero_space_elf.black_aegis.explosion_damage[2]%$ de dano mágico numa área.",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_3"] = "Protege uma unidade aliada prevenindo até %$heroes.hero_space_elf.black_aegis.shield_base[3]%$ de dano. O escudo explosivo agora causa %$heroes.hero_space_elf.black_aegis.explosion_damage[3]%$ de dano mágico numa área.",
["HERO_SPACE_ELF_BLACK_AEGIS_TITLE"] = "PROTEÇÃO ESCURA",
["HERO_SPACE_ELF_CLASS"] = "Feiticeira do Vácuo",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_1"] = "Atrai um grupo de inimigos para o vácuo por %$heroes.hero_space_elf.ultimate.duration[2]%$ segundos, causando %$heroes.hero_space_elf.ultimate.damage[2]%$ de dano.",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_2"] = "Atrai um grupo de inimigos para o vácuo por %$heroes.hero_space_elf.ultimate.duration[3]%$ segundos, causando %$heroes.hero_space_elf.ultimate.damage[3]%$ de dano.",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_3"] = "Atrai um grupo de inimigos para o vácuo por %$heroes.hero_space_elf.ultimate.duration[4]%$ segundos, causando %$heroes.hero_space_elf.ultimate.damage[4]%$ de dano.",
["HERO_SPACE_ELF_COSMIC_PRISON_MENUBOTTOM_DESCRIPTION"] = "Aprisiona inimigos em uma área, causando-lhes dano.",
["HERO_SPACE_ELF_COSMIC_PRISON_MENUBOTTOM_NAME"] = "Prisão Cósmica",
["HERO_SPACE_ELF_COSMIC_PRISON_TITLE"] = "PRISÃO CÓSMICA",
["HERO_SPACE_ELF_DESC"] = "Rejeitada por anos por seus pares por se intrometer com forças desconhecidas e de outros mundos, a feiticeira do vácuo Therien agora se encontra como um dos maiores ativos da Aliança para entender o Onividente e quaisquer forças além deste plano.",
["HERO_SPACE_ELF_NAME"] = "Therien",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_1"] = "Distorce o espaço ao redor de todas as torres por %$heroes.hero_space_elf.spatial_distortion.duration[1]%$ segundos, aumentando o seu alcance em %$heroes.hero_space_elf.spatial_distortion.s_range_factor[1]%$%. ",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_2"] = "Distorce o espaço ao redor de todas as torres por %$heroes.hero_space_elf.spatial_distortion.duration[2]%$ segundos, aumentando o seu alcance em %$heroes.hero_space_elf.spatial_distortion.s_range_factor[2]%$%. ",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_3"] = "Distorce o espaço ao redor de todas as torres por %$heroes.hero_space_elf.spatial_distortion.duration[3]%$ segundos, aumentando o seu alcance em %$heroes.hero_space_elf.spatial_distortion.s_range_factor[3]%$%. ",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_TITLE"] = "DISTORÇÃO ESPACIAL",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_1"] = "Abre %$heroes.hero_space_elf.void_rift.cracks_amount[1]%$ fenda no caminho por %$heroes.hero_space_elf.void_rift.duration[1]%$ segundos, causando %$heroes.hero_space_elf.void_rift.s_damage_min[1]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[1]%$ de dano por segundo a cada inimigo que estiver sobre ela.",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_2"] = "Abre %$heroes.hero_space_elf.void_rift.cracks_amount[2]%$ fendas no caminho por %$heroes.hero_space_elf.void_rift.duration[2]%$ segundos, causando %$heroes.hero_space_elf.void_rift.s_damage_min[2]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[2]%$ de dano por segundo a cada inimigo que estiver sobre ela.",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_3"] = "Abre %$heroes.hero_space_elf.void_rift.cracks_amount[3]%$ fendas no caminho por %$heroes.hero_space_elf.void_rift.duration[3]%$ segundos, causando %$heroes.hero_space_elf.void_rift.s_damage_min[3]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[3]%$ de dano por segundo a cada inimigo que estiver sobre ela.",
["HERO_SPACE_ELF_VOID_RIFT_TITLE"] = "FENDA DO VAZIO",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_1"] = "Invoca %$heroes.hero_spider.ultimate.spawn_amount[2]%$ aranhas que lutam por %$heroes.hero_spider.ultimate.spider.duration[2]%$ segundos, atordoando inimigos ao acertar.",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_2"] = "Invoca %$heroes.hero_spider.ultimate.spawn_amount[3]%$ aranhas que lutam por %$heroes.hero_spider.ultimate.spider.duration[3]%$ segundos, atordoando inimigos ao acertar.",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_3"] = "Invoca %$heroes.hero_spider.ultimate.spawn_amount[4]%$ aranhas que lutam por %$heroes.hero_spider.ultimate.spider.duration[4]%$ segundos, atordoando inimigos ao acertar.",
["HERO_SPIDER_ARACNID_SPAWNER_MENUBOTTOM_DESCRIPTION"] = "Invoca um grupo de aranhas atordoantes.",
["HERO_SPIDER_ARACNID_SPAWNER_MENUBOTTOM_NAME"] = "Chamado do Caçador",
["HERO_SPIDER_ARACNID_SPAWNER_TITLE"] = "Chamado do Caçador",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_1"] = "A cada %$heroes.hero_spider.area_attack.cooldown[1]%$ segundos, Spydyr impõe sua presença, atordoando inimigos próximos por %$heroes.hero_spider.area_attack.s_stun_time[1]%$ segundos.",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_2"] = "A cada %$heroes.hero_spider.area_attack.cooldown[2]%$ segundos, Spydyr impõe sua presença, atordoando inimigos próximos por %$heroes.hero_spider.area_attack.s_stun_time[2]%$ segundos.",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_3"] = "A cada %$heroes.hero_spider.area_attack.cooldown[3]%$ segundos, Spydyr impõe sua presença, atordoando inimigos próximos por %$heroes.hero_spider.area_attack.s_stun_time[3]%$ segundos.",
["HERO_SPIDER_AREA_ATTACK_TITLE"] = "Opressão Avassaladora",
["HERO_SPIDER_DESC"] = "Spydyr é a última sobrevivente de um grupo de Elfos do Crepúsculo encarregado de aniquilar o Culto da Rainha Aranha. Combinando magia das sombras com sua habilidade de caça incomparável, ela é temida como uma das assassinas mais letais de todos os reinos.",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_1"] = "A cada %$heroes.hero_spider.instakill_melee.cooldown[1]%$ segundos, Spydyr pode executar um inimigo atordoado cuja vida esteja abaixo de %$heroes.hero_spider.instakill_melee.life_threshold[1]%$.",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_2"] = "A cada %$heroes.hero_spider.instakill_melee.cooldown[2]%$ segundos, Spydyr pode executar um inimigo atordoado cuja vida esteja abaixo de %$heroes.hero_spider.instakill_melee.life_threshold[2]%$.",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_3"] = "A cada %$heroes.hero_spider.instakill_melee.cooldown[3]%$ segundos, Spydyr pode executar um inimigo atordoado cuja vida esteja abaixo de %$heroes.hero_spider.instakill_melee.life_threshold[3]%$.",
["HERO_SPIDER_INSTAKILL_MELEE_TITLE"] = "Aperto da Morte",
["HERO_SPIDER_NAME"] = "Spydyr",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_1"] = "Num piscar de olhos, Spydyr se teleporta para o inimigo com a maior quantidade de vida, causando %$heroes.hero_spider.supreme_hunter.damage_min[1]%$-%$heroes.hero_spider.supreme_hunter.damage_max[1]%$ de dano.",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_2"] = "Num piscar de olhos, Spydyr se teletransporta para o inimigo com a maior vida, causando %$heroes.hero_spider.supreme_hunter.damage_min[2]%$-%$heroes.hero_spider.supreme_hunter.damage_max[2]%$.",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_3"] = "Num piscar de olhos, Spydyr se teletransporta para o inimigo com a maior vida, causando %$heroes.hero_spider.supreme_hunter.damage_min[3]%$-%$heroes.hero_spider.supreme_hunter.damage_max[3]%$.",
["HERO_SPIDER_SUPREME_HUNTER_TITLE"] = "Passo Sombrio",
["HERO_SPIDER_TUNNELING_DESCRIPTION_1"] = "O tunelamento de Spydyr agora causa %$heroes.hero_spider.tunneling.damage_min[1]%$-%$heroes.hero_spider.tunneling.damage_max[1]%$ de dano ao ressurgir.",
["HERO_SPIDER_TUNNELING_DESCRIPTION_2"] = "O tunelamento de Spydyr agora causa %$heroes.hero_spider.tunneling.damage_min[2]%$-%$heroes.hero_spider.tunneling.damage_max[2]%$ de dano ao ressurgir.",
["HERO_SPIDER_TUNNELING_DESCRIPTION_3"] = "O tunelamento de Spydyr agora causa %$heroes.hero_spider.tunneling.damage_min[3]%$-%$heroes.hero_spider.tunneling.damage_max[3]%$ de dano ao ressurgir.",
["HERO_SPIDER_TUNNELING_TITLE"] = "Escavação",
["HERO_VENOM_CLASS"] = "Assassino Corrompido",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_1"] = "Preenche uma área com uma substância pegajosa que desacelera os inimigos e, após um momento, se transforma em espinhos penetrantes que causam %$heroes.hero_venom.ultimate.s_damage[2]%$ de dano verdadeiro.",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_2"] = "Preenche uma área com uma substância pegajosa que desacelera os inimigos e, após um momento, se transforma em espinhos penetrantes que causam %$heroes.hero_venom.ultimate.s_damage[3]%$ de dano verdadeiro.",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_3"] = "Preenche uma área com uma substância pegajosa que desacelera os inimigos e, após um momento, se transforma em espinhos penetrantes que causam %$heroes.hero_venom.ultimate.s_damage[4]%$ de dano verdadeiro.",
["HERO_VENOM_CREEPING_DEATH_MENUBOTTOM_DESCRIPTION"] = "Invoca uma substância pegajosa no caminho que desacelera e danifica os inimigos.",
["HERO_VENOM_CREEPING_DEATH_MENUBOTTOM_NAME"] = "Morte Rastejante",
["HERO_VENOM_CREEPING_DEATH_TITLE"] = "MORTE RASTEJANTE",
["HERO_VENOM_DESC"] = "Após resistir ser transformado em uma abominação pelo Culto, o mercenário Grimson foi preso e deixado para apodrecer. O processo torturante concedeu a Grimson poderes de metamorfose, que ele usou para escapar do Culto, jurando voltar para se vingar.",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_1"] = "Grimson devora um inimigo com menos de %$heroes.hero_venom.eat_enemy.hp_trigger%$% de saúde, recuperando %$heroes.hero_venom.eat_enemy.regen[1]%$% de sua própria saúde total no processo.",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_2"] = "Grimson devora um inimigo com menos de %$heroes.hero_venom.eat_enemy.hp_trigger%$% de saúde, recuperando %$heroes.hero_venom.eat_enemy.regen[2]%$% de sua própria saúde total no processo.",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_3"] = "Grimson devora um inimigo com menos de %$heroes.hero_venom.eat_enemy.hp_trigger%$% de saúde, recuperando %$heroes.hero_venom.eat_enemy.regen[3]%$% de sua própria saúde total no processo.",
["HERO_VENOM_EAT_ENEMY_TITLE"] = "RENOVAÇÃO DA CARNE",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_1"] = "Esparrama espinhos pelo caminho, causando %$heroes.hero_venom.floor_spikes.s_damage[1]%$ de dano verdadeiro por espinho aos inimigos próximos.",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_2"] = "Esparrama espinhos pelo caminho, causando %$heroes.hero_venom.floor_spikes.s_damage[2]%$ de dano verdadeiro por espinho aos inimigos próximos.",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_3"] = "Esparrama espinhos pelo caminho, causando %$heroes.hero_venom.floor_spikes.s_damage[3]%$ de dano verdadeiro por espinho aos inimigos próximos.",
["HERO_VENOM_FLOOR_SPIKES_TITLE"] = "ESPINHOS MORTAIS",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_1"] = "Quando abaixo de %$heroes.hero_venom.inner_beast.trigger_hp%$% de saúde, Grimson se transforma completamente, ganhando %$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[1]%$% de dano extra e curando a si mesmo em %$heroes.hero_venom.inner_beast.basic_melee.regen_health%$% de sua vida total por golpe por %$heroes.hero_venom.inner_beast.duration%$ segundos.",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_2"] = "Quando abaixo de %$heroes.hero_venom.inner_beast.trigger_hp%$% de saúde, Grimson se transforma completamente, ganhando %$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[2]%$% de dano extra e curando a si mesmo em %$heroes.hero_venom.inner_beast.basic_melee.regen_health%$% de sua vida total por golpe por %$heroes.hero_venom.inner_beast.duration%$ segundos.",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_3"] = "Quando abaixo de %$heroes.hero_venom.inner_beast.trigger_hp%$% de saúde, Grimson se transforma completamente, ganhando %$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[3]%$% de dano extra e curando a si mesmo em %$heroes.hero_venom.inner_beast.basic_melee.regen_health%$% de sua vida total por golpe por %$heroes.hero_venom.inner_beast.duration%$ segundos.",
["HERO_VENOM_INNER_BEAST_TITLE"] = "FERA INTERIOR",
["HERO_VENOM_NAME"] = "Grimson",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_1"] = "Ataca um inimigo distante, causando %$heroes.hero_venom.ranged_tentacle.s_damage[1]%$ de dano físico com uma chance de %$heroes.hero_venom.ranged_tentacle.bleed_chance[1]%$% de causar sangramento. Sangramento causa %$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$ de dano por segundo por %$heroes.hero_venom.ranged_tentacle.bleed_duration[1]%$ segundos.",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_2"] = "Ataca um inimigo distante, causando %$heroes.hero_venom.ranged_tentacle.s_damage[2]%$ de dano físico com uma chance de %$heroes.hero_venom.ranged_tentacle.bleed_chance[2]%$% de causar sangramento. Sangramento causa %$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$ de dano por segundo por %$heroes.hero_venom.ranged_tentacle.bleed_duration[2]%$ segundos.",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_3"] = "Ataca um inimigo distante, causando %$heroes.hero_venom.ranged_tentacle.s_damage[3]%$ de dano físico com uma chance de %$heroes.hero_venom.ranged_tentacle.bleed_chance[3]%$% de causar sangramento. Sangramento causa %$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$ de dano por segundo por %$heroes.hero_venom.ranged_tentacle.bleed_duration[3]%$ segundos.",
["HERO_VENOM_RANGED_TENTACLE_TITLE"] = "CAÇA-CORAÇÕES",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_1"] = "Cobre uma área com %$heroes.hero_vesper.ultimate.s_spread[2]%$ flechas, cada uma causando %$heroes.hero_vesper.ultimate.damage[2]%$ de dano físico aos inimigos.",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_2"] = "Cobre uma área com %$heroes.hero_vesper.ultimate.s_spread[3]%$ flechas, cada uma causando %$heroes.hero_vesper.ultimate.damage[3]%$ de dano físico aos inimigos.",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_3"] = "Cobre uma área com %$heroes.hero_vesper.ultimate.s_spread[4]%$ flechas, cada uma causando %$heroes.hero_vesper.ultimate.damage[4]%$ de dano físico aos inimigos.",
["HERO_VESPER_ARROW_STORM_MENUBOTTOM_DESCRIPTION"] = "Cobre uma área com flechas, causando dano aos inimigos.",
["HERO_VESPER_ARROW_STORM_MENUBOTTOM_NAME"] = "Tempestade de Flechas",
["HERO_VESPER_ARROW_STORM_TITLE"] = "TEMPESTADE DE FLECHAS",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_1"] = "Dispara uma flecha que atordoa o inimigo por %$heroes.hero_vesper.arrow_to_the_knee.stun_duration[1]%$ segundos, causando %$heroes.hero_vesper.arrow_to_the_knee.s_damage[1]%$ de dano físico.",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_2"] = "Dispara uma flecha que atordoa o inimigo por %$heroes.hero_vesper.arrow_to_the_knee.stun_duration[2]%$ segundos, causando %$heroes.hero_vesper.arrow_to_the_knee.s_damage[2]%$ de dano físico.",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_3"] = "当然了……",
["HERO_VESPER_ARROW_TO_THE_KNEE_TITLE"] = "FLECHA NO JOELHO",
["HERO_VESPER_CLASS"] = "Capitão Real",
["HERO_VESPER_DESC"] = "Hábil tanto com a espada quanto com o arco, Vesper conquistou seu lugar como comandante das forças de Linirea. Depois que Linirea caiu e o rei Denas desapareceu, ele reuniu todas as tropas que pôde e iniciou uma cruzada para trazer de volta o antigo governante.",
["HERO_VESPER_DISENGAGE_DESCRIPTION_1"] = "Quando abaixo de %$heroes.hero_vesper.disengage.hp_to_trigger%$% de saúde, Vesper evita o próximo ataque corpo a corpo saltando para trás. Em seguida, dispara três flechas que causam %$heroes.hero_vesper.disengage.s_damage[1]%$ de dano físico cada a inimigos próximos.",
["HERO_VESPER_DISENGAGE_DESCRIPTION_2"] = "Quando abaixo de %$heroes.hero_vesper.disengage.hp_to_trigger%$% de saúde, Vesper evita o próximo ataque corpo a corpo saltando para trás. Em seguida, dispara três flechas que causam %$heroes.hero_vesper.disengage.s_damage[2]%$ de dano físico cada a inimigos próximos.",
["HERO_VESPER_DISENGAGE_DESCRIPTION_3"] = "Quando abaixo de %$heroes.hero_vesper.disengage.hp_to_trigger%$% de saúde, Vesper evita o próximo ataque corpo a corpo saltando para trás. Em seguida, dispara três flechas que causam %$heroes.hero_vesper.disengage.s_damage[3]%$ de dano físico cada a inimigos próximos.",
["HERO_VESPER_DISENGAGE_TITLE"] = "DESLOCAMENTO TÁTICO",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_1"] = "Ataca um inimigo três vezes, causando %$heroes.hero_vesper.martial_flourish.s_damage[1]%$ de dano físico.",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_2"] = "Ataca um inimigo três vezes, causando %$heroes.hero_vesper.martial_flourish.s_damage[2]%$ de dano físico.",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_3"] = "Ataca um inimigo três vezes, causando %$heroes.hero_vesper.martial_flourish.s_damage[3]%$ de dano físico.",
["HERO_VESPER_MARTIAL_FLOURISH_TITLE"] = "FLORILÉGIO MARCIAL",
["HERO_VESPER_NAME"] = "Vesper",
["HERO_VESPER_RICOCHET_DESCRIPTION_1"] = "Dispara uma flecha que ricocheteia entre %$heroes.hero_vesper.ricochet.s_bounces[1]%$ inimigos, causando %$heroes.hero_vesper.ricochet.s_damage[1]%$ de dano físico a cada vez.",
["HERO_VESPER_RICOCHET_DESCRIPTION_2"] = "Dispara uma flecha que ricocheteia entre %$heroes.hero_vesper.ricochet.s_bounces[2]%$ inimigos, causando %$heroes.hero_vesper.ricochet.s_damage[2]%$ de dano físico a cada vez.",
["HERO_VESPER_RICOCHET_DESCRIPTION_3"] = "Dispara uma flecha que ricocheteia entre %$heroes.hero_vesper.ricochet.s_bounces[3]%$ inimigos, causando %$heroes.hero_vesper.ricochet.s_damage[3]%$ de dano físico a cada vez.",
["HERO_VESPER_RICOCHET_TITLE"] = "RICOCHETE",
["HERO_WITCH_CLASS"] = "Bruxa Travessa",
["HERO_WITCH_DESC"] = "Embora goste de surpreender os estranhos que passam pela Floresta Feérica com truques divertidos e inofensivos, aqueles que representam uma ameaça para a floresta ou para outros gnomos logo descobrem que seu sorriso esconde uma bruxa implacável e temível.",
["HERO_WITCH_DISENGAGE_DESCRIPTION_1"] = "Quando tem menos de %$heroes.hero_witch.disengage.hp_to_trigger%$% de vida, ela se teletransporta para trás, deixando um espantalho que luta por ela. O espantalho tem %$heroes.hero_witch.disengage.decoy.hp_max[1]%$ de vida e explode quando destruído, atordoando os inimigos por %$heroes.hero_witch.disengage.decoy.explotion.stun_duration[1]%$ segundo.",
["HERO_WITCH_DISENGAGE_DESCRIPTION_2"] = "Quando tem menos de %$heroes.hero_witch.disengage.hp_to_trigger%$% de vida, ela se teletransporta para trás, deixando um espantalho que luta por ela. O espantalho tem %$heroes.hero_witch.disengage.decoy.hp_max[2]%$ de vida e explode quando destruído, atordoando os inimigos por %$heroes.hero_witch.disengage.decoy.explotion.stun_duration[2]%$ segundos.",
["HERO_WITCH_DISENGAGE_DESCRIPTION_3"] = "Quando tem menos de %$heroes.hero_witch.disengage.hp_to_trigger%$% de vida, ela se teletransporta para trás, deixando um espantalho que luta por ela. O espantalho tem %$heroes.hero_witch.disengage.decoy.hp_max[3]%$ de vida e explode quando destruído, atordoando os inimigos por %$heroes.hero_witch.disengage.decoy.explotion.stun_duration[3]%$ segundos.",
["HERO_WITCH_DISENGAGE_TITLE"] = "ESPANTALHO RESPLANDECENTE",
["HERO_WITCH_NAME"] = "Stregi",
["HERO_WITCH_PATH_AOE_DESCRIPTION_1"] = "Lança uma poção gigante sobre o caminho, causando %$heroes.hero_witch.skill_path_aoe.s_damage[1]%$ de dano mágico em área e retardando os inimigos por %$heroes.hero_witch.skill_path_aoe.duration[1]%$ segundos.",
["HERO_WITCH_PATH_AOE_DESCRIPTION_2"] = "Lança uma poção gigante sobre o caminho, causando %$heroes.hero_witch.skill_path_aoe.s_damage[2]%$ de dano mágico em área e retardando os inimigos por %$heroes.hero_witch.skill_path_aoe.duration[2]%$ segundos.",
["HERO_WITCH_PATH_AOE_DESCRIPTION_3"] = "Lança uma poção gigante sobre o caminho, causando %$heroes.hero_witch.skill_path_aoe.s_damage[3]%$ de dano mágico em área e retardando os inimigos por %$heroes.hero_witch.skill_path_aoe.duration[3]%$ segundos.",
["HERO_WITCH_PATH_AOE_TITLE"] = "AGITAR E ESPATIFAR",
["HERO_WITCH_POLYMORPH_DESCRIPTION_1"] = "Transforma um inimigo em uma abóbora por %$heroes.hero_witch.skill_polymorph.duration[1]%$ segundos. A abobrinha tem %$heroes.hero_witch.skill_polymorph.pumpkin.hp[1]%$% da saúde do alvo.",
["HERO_WITCH_POLYMORPH_DESCRIPTION_2"] = "Transforma um inimigo em uma abóbora por %$heroes.hero_witch.skill_polymorph.duration[2]%$ segundos. A abobrinha tem %$heroes.hero_witch.skill_polymorph.pumpkin.hp[2]%$% da saúde do alvo.",
["HERO_WITCH_POLYMORPH_DESCRIPTION_3"] = "Transforma um inimigo em uma abóbora por %$heroes.hero_witch.skill_polymorph.duration[3]%$ segundos. A abobrinha tem %$heroes.hero_witch.skill_polymorph.pumpkin.hp[3]%$% da saúde do alvo.",
["HERO_WITCH_POLYMORPH_TITLE"] = "VERDURIZAÇÃO!",
["HERO_WITCH_SOLDIERS_DESCRIPTION_1"] = "Invoca %$heroes.hero_witch.skill_soldiers.soldiers_amount[1]%$ gato que luta contra os inimigos. O gato tem %$heroes.hero_witch.skill_soldiers.soldier.hp_max[1]%$ de vida e causa %$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[1]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[1]%$ de dano físico.",
["HERO_WITCH_SOLDIERS_DESCRIPTION_2"] = "Invoca %$heroes.hero_witch.skill_soldiers.soldiers_amount[2]%$ gatos que lutam contra os inimigos. Os gatos têm %$heroes.hero_witch.skill_soldiers.soldier.hp_max[2]%$ de vida e causam %$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[2]%$ de dano físico.",
["HERO_WITCH_SOLDIERS_DESCRIPTION_3"] = "Invoca %$heroes.hero_witch.skill_soldiers.soldiers_amount[3]%$ gatos que lutam contra os inimigos. Os gatos têm %$heroes.hero_witch.skill_soldiers.soldier.hp_max[3]%$ de vida e causam %$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[3]%$ de dano físico.",
["HERO_WITCH_SOLDIERS_TITLE"] = "FÚRIAS NOTURNAS",
["HERO_WITCH_ULTIMATE_DESCRIPTION_1"] = "Teleporta %$heroes.hero_witch.ultimate.max_targets[2]%$ inimigos para trás, deixando-os adormecidos por %$heroes.hero_witch.ultimate.duration[2]%$ segundos.",
["HERO_WITCH_ULTIMATE_DESCRIPTION_2"] = "Teleporta %$heroes.hero_witch.ultimate.max_targets[3]%$ inimigos para trás, deixando-os adormecidos por %$heroes.hero_witch.ultimate.duration[3]%$ segundos.",
["HERO_WITCH_ULTIMATE_DESCRIPTION_3"] = "Teleporta %$heroes.hero_witch.ultimate.max_targets[4]%$ inimigos para trás, deixando-os adormecidos por %$heroes.hero_witch.ultimate.duration[4]%$ segundos.",
["HERO_WITCH_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "Teleporta inimigos para trás no caminho, deixando-os adormecidos por um momento.",
["HERO_WITCH_ULTIMATE_MENUBOTTOM_NAME"] = "Retorno Dorminhoco",
["HERO_WITCH_ULTIMATE_TITLE"] = "RETORNO DORMINHOCO",
["HERO_WUKONG_CLASS"] = "Sun Wukong, o Rei Macaco",
["HERO_WUKONG_DESC"] = "Nascido de uma pedra celestial de Yin e Yang, Sun Wukong foi agraciado com força, agilidade e imortalidade. Mas os reis demônios roubaram as esferas de poder dele. Agora, o lendário trapaceiro se ergue para recuperá-las antes que seja tarde demais.",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_1"] = "Amplia o bastão Jingu e se joga para esmagar um inimigo, matando-o instantaneamente e causando %$heroes.hero_wukong.giant_staff.area_damage.damage_min[1]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[1]%$ de dano em área ao redor do alvo.",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_2"] = "Tropeça e aumenta o Jingu Bang para esmagar um inimigo, matando-o instantaneamente e causando %$heroes.hero_wukong.giant_staff.area_damage.damage_min[2]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[2]%$ de dano em uma área ao redor do alvo.",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_3"] = "Tropeça e aumenta o Jingu Bang para esmagar um inimigo, matando-o instantaneamente e causando %$heroes.hero_wukong.giant_staff.area_damage.damage_min[3]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[3]%$ de dano em uma área ao redor do alvo.",
["HERO_WUKONG_GIANT_STAFF_TITLE"] = "Técnica do Jingu Bang",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_1"] = "Invoca 2 clones de cabelo de Sun Wukong para lutar ao seu lado. Eles causam %$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[1]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[1]%$ de dano e duram %$heroes.hero_wukong.hair_clones.soldier.duration[1]%$ segundos.",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_2"] = "Invoca 2 clones de cabelo de Sun Wukong para lutar ao seu lado. Eles causam %$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[2]%$ de dano e duram por %$heroes.hero_wukong.hair_clones.soldier.duration[2]%$ segundos.",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_3"] = "Invoca 2 clones de cabelo de Sun Wukong para lutar ao seu lado. Eles causam %$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[3]%$ de dano e duram %$heroes.hero_wukong.hair_clones.soldier.duration[3]%$ segundos.",
["HERO_WUKONG_HAIR_CLONES_TITLE"] = "Hair Clones",
["HERO_WUKONG_NAME"] = "Sun Wukong",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_1"] = "Lança o Jingu Bang para o ar, multiplicando-o em %$heroes.hero_wukong.pole_ranged.pole_amounts[1]%$ bastões que caem sobre os inimigos, cada um causando %$heroes.hero_wukong.pole_ranged.damage_min[1]%$ de dano e atordoando os inimigos em uma pequena área.",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_2"] = "Lança o Jingu Bang para o ar, multiplicando-o em %$heroes.hero_wukong.pole_ranged.pole_amounts[2]%$ bastões que caem sobre os inimigos, cada um causando %$heroes.hero_wukong.pole_ranged.damage_min[2]%$ de dano e atordoando os inimigos em uma pequena área.",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_3"] = "Lança o Jingu Bang para o ar, multiplicando-o em %$heroes.hero_wukong.pole_ranged.pole_amounts[3]%$ bastões que caem sobre os inimigos, cada um causando %$heroes.hero_wukong.pole_ranged.damage_min[3]%$ de dano e atordoando os inimigos em uma pequena área.",
["HERO_WUKONG_POLE_RANGED_TITLE"] = "Barragem de Postes",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_1"] = "O Dragão Branco colide com o chão com força tremenda, causando %$heroes.hero_wukong.ultimate.damage_total[2]%$ de dano verdadeiro e deixando uma área de lentidão.",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_2"] = "O Dragão Branco irrompe contra o solo com força tremenda, causando %$heroes.hero_wukong.ultimate.damage_total[3]%$ de dano verdadeiro e deixando uma área de lentidão.",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_3"] = "O Dragão Branco se lança ao chão com força tremenda, causando %$heroes.hero_wukong.ultimate.damage_total[4]%$ de dano verdadeiro e deixando uma área de lentidão.",
["HERO_WUKONG_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "Invoca o Dragão Branco.",
["HERO_WUKONG_ULTIMATE_MENUBOTTOM_NAME"] = "O Dragão Branco",
["HERO_WUKONG_ULTIMATE_TITLE"] = "O Dragão Branco",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_1"] = "Zhu Bajie, o leal companheiro de Sun Wukong, o segue para todos os lugares. Causa %$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[1]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[1]%$ de dano e tem uma pequena chance de causar um grande ataque de dano em área.",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_2"] = "Zhu Bajie, o leal companheiro de Sun Wukong, o segue para todos os lugares. Causa %$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[2]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[2]%$ de dano e tem uma pequena chance de causar um grande ataque de dano em área.",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_3"] = "Zhu Bajie, o fiel companheiro de Sun Wukong, o segue por toda parte. Causa %$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[3]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[3]%$ de dano e tem uma pequena chance de causar um ataque de dano em área poderoso.",
["HERO_WUKONG_ZHU_APPRENTICE_TITLE"] = "Aprendiz de Zhu",
["HOURS_ABBREVIATION"] = "h",
["Hero at your command!"] = "Herói sob seu comando!",
["Heroes are elite units that can face strong enemies and support your forces."] = "Heróis são unidades de elite que enfrentam inimigos fortes e apoiam suas tropas.",
["Heroes gain experience every time they damage an enemy or use an ability."] = "Heróis ganham experiência a cada vez que usam habilidades ou causam dano.",
["IAP_CONNECTION_ERROR"] = "Impossível conectar a %@",
["INCOMING NEXT WAVE!"] = "PRÓXIMA ONDA CHEGANDO!",
["INCOMING WAVE"] = "ONDA CHEGANDO",
["INGAME_BALLOON_BUILD_HERE"] = "Construa aqui!",
["INGAME_BALLOON_GOAL"] = "Não deixe os inimigos passarem deste ponto",
["INGAME_BALLOON_GOLD"] = "Ganhe ouro matando inimigos",
["INGAME_BALLOON_INCOMING"] = "PRÓXIMA ONDA CHEGANDO!",
["INGAME_BALLOON_NEW_HERO"] = "Novo herói!",
["INGAME_BALLOON_NEW_POWER"] = "Novo poder!",
["INGAME_BALLOON_NOTIFICATION_TAP_HERE"] = "Toque aqui!",
["INGAME_BALLOON_SELECT_HERO"] = "Toque para selecionar!",
["INGAME_BALLOON_START_BATTLE"] = "COMEÇAR BATALHA!",
["INGAME_BALLOON_TAP_HERE"] = "Toque na estrada",
["INGAME_BALLOON_TAP_TO_CALL"] = "TOQUE PARA CHAMÁ-LO CEDO",
["INGAME_BALLOON_TAP_TWICE_BUILD"] = "Toque duas vezes para construir uma torre",
["INGAME_BALLOON_TAP_TWICE_START"] = "TOQUE DUAS VEZES PARA COMEÇAR A BATALHA",
["INGAME_BALLOON_TAP_TWICE_WAVE"] = "Toque duas vezes para chamar a onda",
["INGAME_TUTORIAL1_HELP1"] = "Não deixe os inimigos passarem deste ponto.",
["INGAME_TUTORIAL1_HELP2"] = "Construa torres para defender o caminho.",
["INGAME_TUTORIAL1_HELP3"] = "Ganhe ouro matando inimigos.",
["INGAME_TUTORIAL1_SUBTITLE1"] = "Proteja suas terras dos ataques inimigos.",
["INGAME_TUTORIAL1_SUBTITLE2"] = "Construa torres defensivas ao longo da estrada para pará-los.",
["INGAME_TUTORIAL1_TITLE"] = "Objetivo",
["INGAME_TUTORIAL_GOTCHA_1"] = "Entendi!",
["INGAME_TUTORIAL_GOTCHA_2"] = "Estou pronto, vamos lá!",
["INGAME_TUTORIAL_HINT"] = "DICA",
["INGAME_TUTORIAL_INSTRUCTIONS"] = "INSTRUÇÕES",
["INGAME_TUTORIAL_NEW_TIP"] = "NOVA DICA",
["INGAME_TUTORIAL_NEXT"] = "Próximo!",
["INGAME_TUTORIAL_OK"] = "Ok!",
["INGAME_TUTORIAL_SKIP"] = "Pular!",
["INGAME_TUTORIAL_TIP_CHALLENGE"] = "AVISO",
["ITEM_CLUSTER_BOMB_BOTTOM_DESC"] = "Como pipoca, mas muito mais divertido e menos saboroso.",
["ITEM_CLUSTER_BOMB_BOTTOM_INFO"] = "Uma bomba que cria novas bombas menores.",
["ITEM_CLUSTER_BOMB_DESC"] = "Lança uma bomba que danifica inimigos na área e lança outras bombas menores ao redor.",
["ITEM_CLUSTER_BOMB_NAME"] = "Bomba de fragmentação",
["ITEM_DEATHS_TOUCH_BOTTOM_DESC"] = "Ótimo para quando você quer se sentir como um deus... DA MORTE!",
["ITEM_DEATHS_TOUCH_BOTTOM_INFO"] = "Selecione. Toque no seu alvo. Mate.",
["ITEM_DEATHS_TOUCH_DESC"] = "Carregue-se com o poder da Morte e toque em qualquer inimigo para eliminá-lo instantaneamente. Não funciona em chefes ou mini chefes.",
["ITEM_DEATHS_TOUCH_NAME"] = "Toque da Morte",
["ITEM_LOOT_BOX_BOTTOM_DESC"] = "Um par destes e você terá a vida feita.",
["ITEM_LOOT_BOX_BOTTOM_INFO"] = "Solte uma caixa no caminho, danificando inimigos e obtendo ouro instantaneamente.",
["ITEM_LOOT_BOX_DESC"] = "Solte uma caixa no caminho, danificando inimigos e obtendo instantaneamente 300 de ouro.",
["ITEM_LOOT_BOX_NAME"] = "Prêmio principal ",
["ITEM_MEDICAL_KIT_BOTTOM_DESC"] = "Tudo o que você precisa para se recuperar, general.",
["ITEM_MEDICAL_KIT_BOTTOM_INFO"] = "Restaura até 3 corações ao jogador.",
["ITEM_MEDICAL_KIT_DESC"] = "Um kit especial que restaura até 3 corações ao jogador.",
["ITEM_MEDICAL_KIT_NAME"] = "Kit Médico",
["ITEM_PORTABLE_COIL_BOTTOM_DESC"] = "Zip! Zap! Frito como um rato!",
["ITEM_PORTABLE_COIL_BOTTOM_INFO"] = "Configura uma armadilha que causa dano e atordoa inimigos em uma área.",
["ITEM_PORTABLE_COIL_DESC"] = "Configura uma armadilha de área que danifica e atordoa inimigos que a ativam. Seus efeitos podem se propagar para inimigos próximos.",
["ITEM_PORTABLE_COIL_NAME"] = "Bobina portátil",
["ITEM_ROOM_EQUIP"] = "Equipar",
["ITEM_ROOM_EQUIPPED"] = "Equipado",
["ITEM_ROOM_EQUIPPED_ITEMS"] = "Itens equipados",
["ITEM_SCROLL_OF_SPACESHIFT_BOTTOM_DESC"] = "Já ficou sem tempo para lutar contra seus inimigos? Não se preocupe mais!",
["ITEM_SCROLL_OF_SPACESHIFT_BOTTOM_INFO"] = "Teletransporta um grupo de inimigos de volta pelo caminho.",
["ITEM_SCROLL_OF_SPACESHIFT_DESC"] = "Teletransporta um grupo de inimigos de volta pelo caminho.",
["ITEM_SCROLL_OF_SPACESHIFT_NAME"] = "Pergaminho de Mudança",
["ITEM_SECOND_BREATH_BOTTOM_DESC"] = "Levante-se do túmulo, sem a desvantagem dos mortos-vivos.",
["ITEM_SECOND_BREATH_BOTTOM_INFO"] = "Revive heróis caídos, cura os feridos e reinicia o tempo de recarga dos poderes dos heróis.",
["ITEM_SECOND_BREATH_DESC"] = "Uma bênção divina que revive heróis caídos, cura os feridos e reinicia o tempo de recarga dos poderes dos heróis.",
["ITEM_SECOND_BREATH_NAME"] = "Segundo Fôlego",
["ITEM_SUMMON_BLACKBURN_BOTTOM_DESC"] = "O primeiro. O único. O inimitável.",
["ITEM_SUMMON_BLACKBURN_BOTTOM_INFO"] = "Convoque o poderoso Blackburn para lutar ao seu lado.",
["ITEM_SUMMON_BLACKBURN_DESC"] = "Convoque o poderoso guerreiro fantasmal para derrotar seus inimigos.",
["ITEM_SUMMON_BLACKBURN_NAME"] = "Elmo de Blackburn",
["ITEM_VEZNAN_WRATH_BOTTOM_DESC"] = "Deixe-os provar um pouco do poder ilimitado do Feiticeiro Negro!",
["ITEM_VEZNAN_WRATH_BOTTOM_INFO"] = "Dizima todos os inimigos no campo de batalha.",
["ITEM_VEZNAN_WRATH_DESC"] = "Vez'nan lança um poderoso feitiço que dizima todos os inimigos no campo de batalha.",
["ITEM_VEZNAN_WRATH_NAME"] = "Ira de Vez'nan",
["ITEM_WINTER_AGE_BOTTOM_DESC"] = "Também é útil se você simplesmente NÃO gosta do verão.",
["ITEM_WINTER_AGE_BOTTOM_INFO"] = "Congele todos os inimigos na tela.",
["ITEM_WINTER_AGE_DESC"] = "Um feitiço poderoso que cria ventos gelados para congelar todos os inimigos por vários segundos.",
["ITEM_WINTER_AGE_NAME"] = "Era do Gelo",
["If you enjoy using %@, would you mind taking a moment to rate it? It won't take more than a minute. Thanks for your support!"] = "Se você gosta de usar o %@, que tal avaliá-lo? Não levará mais de um minuto. Agradecemos o seu apoio!",
["Impossible"] = "IMPOSSÍVEL",
["Iron Challenge"] = "Desafio Ferrenho",
["KR5_NO_GEMS"] = "Você não tem gemas suficientes.\nQuer comprar mais?",
["KR5_PURCHASE_ERROR"] = "Ocorreu um erro ao processar sua compra.",
["KR5_RATE_US"] = "Está gostando do jogo? Avalie-nos na loja!",
["LEVEL_10_HEROIC"] = "Descrição Heroica 10",
["LEVEL_10_HISTORY"] = "Descobriu-se que o Culto está usando os cristais minerados para construir um artefato de aparência ominosa logo à saída do cânion. Ele vibra com uma energia estranha e o ar ao redor do local parece pesado. Temos que garantir que ele seja destruído antes de avançarmos.",
["LEVEL_10_IRON"] = "Descrição de Ferro 10",
["LEVEL_10_IRON_UNLOCK"] = "A definir",
["LEVEL_10_MODES_UPGRADES"] = "nível máximo 5",
["LEVEL_10_TITLE"] = "10. Pátio do Templo",
["LEVEL_11_HEROIC"] = "Descrição Heroica 11",
["LEVEL_11_HISTORY"] = "Finalmente saímos dos cânions, mas ainda há um longo caminho a percorrer. Agora estamos diante de um portal gigante incrustado com cristais enquanto a Vidente Mydrias finaliza seus rituais. O que virá do além, não sabemos, mas ainda estamos prontos. Preparem-se!",
["LEVEL_11_IRON"] = "Descrição de Ferro 11",
["LEVEL_11_IRON_UNLOCK"] = "A definir",
["LEVEL_11_MODES_UPGRADES"] = "nível máximo 5",
["LEVEL_11_TITLE"] = "11. Planalto do Cânion",
["LEVEL_12_HEROIC"] = "Descrição Heroica 12",
["LEVEL_12_HISTORY"] = "Com Denas de volta ao nosso lado, atravessamos o portal para o desconhecido. Este mundo estranho parece um reflexo distorcido de Linirea, mas um engolido pela praga. Cuidado por onde pisa, algo pior do que o Culto espreita na escuridão.",
["LEVEL_12_IRON"] = "Descrição de Ferro 12",
["LEVEL_12_IRON_UNLOCK"] = "A definir",
["LEVEL_12_MODES_UPGRADES"] = "nível máximo 5",
["LEVEL_12_TITLE"] = "12. Campinas Assoladas",
["LEVEL_13_HEROIC"] = "Descrição Heroica 13",
["LEVEL_13_HISTORY"] = "A familiar silhueta do Templo da Tempestade se destaca no horizonte. O caminho é claro o suficiente, siga o fedor e a corrupção à medida que crescem e encontraremos a fonte de tudo. Só temos que sobreviver aos horrores distorcidos que parecem emergir da própria terra.",
["LEVEL_13_IRON"] = "Descrição de Ferro 13",
["LEVEL_13_IRON_UNLOCK"] = "A definir",
["LEVEL_13_MODES_UPGRADES"] = "nível máximo 5",
["LEVEL_13_TITLE"] = "13. Templo Profanado",
["LEVEL_14_HEROIC"] = "Descrição Heroica 14",
["LEVEL_14_HISTORY"] = "Essas malditas criaturas parecem surgir do nada! As tropas estão inquietas, tudo o que tocamos parece vivo e pronto para nos atacar, como se a própria terra estivesse lutando contra nós com toda a sua força. A vidente Mydrias e seus minions devem estar por perto.",
["LEVEL_14_IRON"] = "Descrição de Ferro 14",
["LEVEL_14_IRON_UNLOCK"] = "A definir",
["LEVEL_14_MODES_UPGRADES"] = "nível 5 máximo",
["LEVEL_14_TITLE"] = "14. Vale da Corrupção",
["LEVEL_15_HEROIC"] = "Descrição heroica 15",
["LEVEL_15_HISTORY"] = "Saímos do vale vitoriosos e agora a única coisa que está entre nós e o Onividente é a própria Mydrias. Vimos do que ela era capaz nos cânions, mas aqui, sob a visão e o poder de seu mestre, ela tem a vantagem. Não que as probabilidades tenham nos parado antes. Fique atento!",
["LEVEL_15_IRON"] = "Descrição de Ferro 15",
["LEVEL_15_IRON_UNLOCK"] = "A ser definido",
["LEVEL_15_MODES_UPGRADES"] = "nível 5 máximo",
["LEVEL_15_TITLE"] = "15. A Torre Abominável",
["LEVEL_16_HEROIC"] = "Descrição heroica 16",
["LEVEL_16_HISTORY"] = "Mydrias não existe mais e o Onividente é o maior inimigo que resta. Esta é a nossa última chance de acabar com o Culto e a invasão. O que acontecer depois não importa se não nos unirmos mais uma vez. Avante!",
["LEVEL_16_IRON"] = "Descrição de Ferro 16",
["LEVEL_16_IRON_UNLOCK"] = "A definir",
["LEVEL_16_MODES_UPGRADES"] = "nível 5 máximo",
["LEVEL_16_TITLE"] = "16. Pico da Fome",
["LEVEL_17_HISTORY"] = "Os arredores da fantástica Floresta Feérica estão se tornando hostis e horripilantes. Dizem que hordas de guerreiros elfos caídos e seres espectrais agora vagam por essas terras, atacando viajantes e corrompendo a própria floresta com sua presença. General, devemos investigar mais a fundo.",
["LEVEL_17_TITLE"] = "17. Ruínas Nebulosas",
["LEVEL_18_HISTORY"] = "Recebemos uma mensagem do Posto Folhaprofunda, onde alguns elfos mal conseguem resistir ao avanço da horda renascida. Devemos nos apressar para ajudá-los, junto com seu capitão, Eridan, antes que seja tarde demais. Uma vez que o Posto esteja devidamente seguro, poderemos avançar para chegar à raiz desta invasão.",
["LEVEL_18_TITLE"] = "18. Posto Folhaprofunda",
["LEVEL_19_HISTORY"] = "Um exausto Eridan nos indicou o caminho para o Templo dos Caídos, de onde a horda é reanimada para invadir o continente, comandada por um feiticeiro que se autodenomina Navira, o Subjugador de Almas. Temos que detê-lo a todo custo!",
["LEVEL_19_TITLE"] = "19. Templo dos Caídos",
["LEVEL_1_HEROIC"] = "Desafio Heroico 1",
["LEVEL_1_HISTORY"] = "Há meses que estamos vasculhando as florestas do sul sem sucesso, pois o Rei Denas não é encontrado em lugar algum. Enquanto isso, fizemos amizade com os Arboreans, espíritos da natureza, e conhecemos seus vizinhos em guerra, os Selvagens, que nos atacam à vista.\nVamos encerrar esta luta para que possamos continuar procurando pelo Rei.",
["LEVEL_1_IRON"] = "Desafio de Ferro 1",
["LEVEL_1_IRON_UNLOCK"] = "Arqueiros Reais\nPacto de Paladinos",
["LEVEL_1_MODES_UPGRADES"] = "nível 1 máximo",
["LEVEL_1_TITLE"] = "1. Mar de Árvores",
["LEVEL_20_HISTORY"] = "Recebemos um sussurro urgente dos Arbóreos na orla da floresta, pedindo desesperadamente por ajuda. Eles estão sob ataque dos incansáveis Croks. Eles não serão capazes de resistir por muito mais tempo. Lembre-se de ser cauteloso, General. Os Croks têm muitos truques sob suas escamas.",
["LEVEL_20_TITLE"] = "20. Aldeia Arbórea",
["LEVEL_21_HISTORY"] = "Após garantir a segurança da vila, os Arbóreos revelaram que, pouco antes do ataque, sentiram seu antigo selo vacilar. Armados com uma pista sobre a invasão súbita dos Croks, mergulhamos no coração do pântano. Deparamo-nos com um antigo círculo de pedra arbóreo, parece um covil... o covil de algo enorme.",
["LEVEL_21_TITLE"] = "21. As Ruínas Submersas",
["LEVEL_22_HISTORY"] = "Ao chegarmos ao antigo templo, nossos piores medos foram confirmados. O selo que há muito protegia nosso mundo de Abominor — o Devorador de Reinos — estava quase desfeito, mantido unido apenas pela magia de ligação desesperada de alguns xamãs Arbóreos. General, impeça Abominor ou os reinos serão consumidos por sua boca insaciável.",
["LEVEL_22_TITLE"] = "22. Hollow Faminto",
["LEVEL_23_HISTORY"] = "Batedores relataram deslizamentos de terra anormais nas montanhas vizinhas. Investigações adicionais revelaram que estes são causados por anões que não reconhecemos. Eles estão montando um autômato gigante na face sul da montanha. Você deveria dar uma olhada, General.",
["LEVEL_23_TITLE"] = "23. Portões da Forja",
["LEVEL_24_HISTORY"] = "Os anões sempre foram conhecidos como inventores, mas este autoproclamado clã Aço-Sombrio leva sua devoção ao metal longe demais, envergonhando até o povo de Bolgur ao usar sua forja para se \"aperfeiçoarem\" rapidamente. Quem está por trás dessa loucura? Precisamos descobrir!",
["LEVEL_24_TITLE"] = "24. Montagem Frenética",
["LEVEL_25_HISTORY"] = "As coisas são como temíamos, apenas o interior de uma montanha tão grande poderia sustentar uma forja capaz de criar este autômato. Quantos anões estão aqui? Eles resistem ao nosso avanço e ainda assim continuam a forjar e soldar. E o que é ainda mais estranho, todos eles parecem iguais? Algo está errado.",
["LEVEL_25_TITLE"] = "25. Núcleo Colossal",
["LEVEL_26_HISTORY"] = "Nosso caminho dentro e fora da montanha nos levou a uma câmara cheia de tanques, e eles não estavam vazios. Não é à toa que eles têm força em números e também habilidade artesanal e aparência. Eles são todos o mesmo anão, Grymbeard! Ele tem criado réplicas de si mesmo por meio de uma ciência perversa. General, precisamos parar com isso!",
["LEVEL_26_TITLE"] = "26. Câmara de Replicação",
["LEVEL_27_HISTORY"] = "Conseguimos interromper a maior parte da operação Aço-Sombrio na montanha, mas tudo será em vão se Grymbeard ainda estiver à solta. Ele certamente está trabalhando nos retoques finais da cabeça do autômato. General, leve as tropas aos picos e esperemos que desta vez lidemos com o anão certo.",
["LEVEL_27_TITLE"] = "27. Cúpula da Dominação",
["LEVEL_28_HISTORY"] = "Seguindo as pistas deixadas por nossos batedores, descobrimos um rastro que leva aos remanescentes desses malditos cultistas. Parece que encontraram uma nova deusa para adorar, uma vil abominação tecelã de teias… Cultistas E aranhas? Nada de bom pode sair dessa combinação.",
["LEVEL_28_TITLE"] = "28. Templo Maculado",
["LEVEL_29_HISTORY"] = "Quanto mais fundo avançamos, mais claro fica que esse terror vem crescendo sob nossos pés há muito tempo, esperando o momento certo para atacar. Pelo espessamento das teias ao nosso redor e pela escuridão sinistra que parece respirar em nossa nuca, eu apostaria que estamos nos aproximando do coração do covil.",
["LEVEL_29_TITLE"] = "29. Câmara de Reprodução",
["LEVEL_2_HEROIC"] = "Descrição Heroica 2",
["LEVEL_2_HISTORY"] = "Estejam alertos, a notícia nos chegou por um sussurro! O Coração da Floresta está sob ataque! Devemos voltar e ajudar os Arboreans. Algumas forças do Exército das Trevas se juntarão a nós no campo de batalha, então fiquem de olho. Podemos estar no mesmo barco por agora, mas isso pode mudar a qualquer momento.",
["LEVEL_2_IRON"] = "Descrição de Ferro 2",
["LEVEL_2_IRON_UNLOCK"] = "Mago Arcano\nTricannon",
["LEVEL_2_MODES_UPGRADES"] = "nível 2 máximo",
["LEVEL_2_TITLE"] = "2. O Portão do Guardião",
["LEVEL_30_HISTORY"] = "Finalmente, chegamos ao covil de seu chamado deus—um templo decadente, abandonado há muito tempo, desmoronando sob o peso de seu próprio passado esquecido. Um trono adequado para uma divindade renegada. Desta vez, vamos garantir que nenhum sobreviva e exterminaremos essas pragas de uma vez por todas.",
["LEVEL_30_TITLE"] = "30. O Trono Esquecido",
["LEVEL_31_HISTORY"] = "Depois de toda a luta e esforço, a paz finalmente retornou aos reinos. Agora, ouvir o som das ondas e jogar jogos de tabuleiro enquanto espera por um velho amigo é a única tarefa. E mesmo com tudo parecendo tão calmo, eu me pergunto quanto tempo essa paz vai durar...",
["LEVEL_31_TITLE"] = "31. Floresta dos Macacos Celestial",
["LEVEL_32_HISTORY"] = "Nossa perseguição nos levou ao coração do vulcão, onde um templo há muito esquecido prestava homenagem às chamas.\n\nMas o Grande Dragão de Fogo, outrora um guardião neutro dessas profundezas flamejantes, agora se agita com uma fúria antinatural. Todos os sinais apontam para a influência de Red Boy corrompendo sua vontade.",
["LEVEL_32_TITLE"] = "32. Caverna do Dragão de Fogo",
["LEVEL_33_HISTORY"] = "Após um confronto exaustivo com o Garoto Vermelho, seguimos adiante até a Ilha Tempestade. Assim que pisamos aqui, nuvens carregadas de raios e rajadas ferozes começaram a uivar em padrões estranhos e retorcidos. Ainda assim, não temos escolha: a ilha guarda a única entrada para o palácio da Princesa. Preparem-se... uma tempestade está chegando.",
["LEVEL_33_TITLE"] = "33. Ilha da Tempestade",
["LEVEL_34_HISTORY"] = "Devemos agradecer à Princesa e seu Leque de Ferro pelas dificuldades que enfrentamos. Após cruzar a ponte e resistir às tempestades mais ferozes, agora estamos no centro de tudo. Este lugar permanece intacto—enganosamente calmo e belo. Não podemos baixar a guarda. Nem mesmo a realeza demoníaca nos impedirá.",
["LEVEL_34_TITLE"] = "34. O Olho da Tempestade",
["LEVEL_35_HISTORY"] = "Chegou a hora. O Rei Demônio Touro ergue-se imponente em sua fortaleza impenetrável. Com o restante de nossas tropas, avançamos de frente com força e astúcia. Devemos atacar antes que ele libere totalmente o poder dos orbes.\nPor tudo o que você preza nesta boa terra... Eu lhes peço que resistam, Aliança!",
["LEVEL_35_TITLE"] = "35. Fortaleza do Rei Demônio",
["LEVEL_3_HEROIC"] = "Descrição Heroica 3",
["LEVEL_3_HISTORY"] = "Conseguimos voltar para o Coração no último momento, mas as Feras Selvagens já estão passando. Fiquem espertos e fortifiquem suas posições! Protejam o Coração a todo custo ou a floresta e os Arbóreos certamente perecerão.",
["LEVEL_3_IRON"] = "Descrição do Ferro 3",
["LEVEL_3_IRON_UNLOCK"] = "Arqueiros Reais\nPacto de Paladinos",
["LEVEL_3_MODES_UPGRADES"] = "nível 3 máximo",
["LEVEL_3_TITLE"] = "3. O Coração da Floresta",
["LEVEL_4_HEROIC"] = "Descrição Heroica 4",
["LEVEL_4_HISTORY"] = "Agora que o Coração da Floresta está seguro, devemos nos reagrupar e aproveitar a vantagem. É hora de levar a batalha para o território das Feras Selvagens. Leve as tropas para o topo das árvores da floresta e procure o acampamento delas de cima.",
["LEVEL_4_IRON"] = "Descrição do Ferro 4",
["LEVEL_4_IRON_UNLOCK"] = "Tricannon\nEmissário Arbóreo",
["LEVEL_4_MODES_UPGRADES"] = "nível 4 máximo",
["LEVEL_4_TITLE"] = "4. Copas das Árvores Esmeralda",
["LEVEL_5_HEROIC"] = "Descrição Heroica 5",
["LEVEL_5_HISTORY"] = "Graças aos seus esforços para tomar o terreno elevado, localizamos o acampamento das Feras Selvagens em algumas ruínas antigas além dos limites da floresta. Lidere as forças em direção ao território deles e fique atento às suas táticas. Podemos ter vencido outra batalha, mas isso está longe de terminar.",
["LEVEL_5_IRON"] = "Descrição do Ferro 5",
["LEVEL_5_IRON_UNLOCK"] = "Mago Arcano\nAliança de Paladino",
["LEVEL_5_MODES_UPGRADES"] = "nível 5 máximo",
["LEVEL_5_TITLE"] = "5. Periferias Devastadas",
["LEVEL_6_HEROIC"] = "Descrição Heroica 6",
["LEVEL_6_HISTORY"] = "Podemos ter a vantagem contra as Feras Selvagens, mas ainda temos que enfrentar o líder deles, Goregrind. O autoproclamado Rei das Feras Selvagens é um inimigo poderoso, então não se deixe enganar por suas artimanhas ou você encontrará seu fim sob suas presas.",
["LEVEL_6_IRON"] = "Descrição do Ferro 6",
["LEVEL_6_IRON_UNLOCK"] = "Arqueiros Reais\nFosso Demoníaco",
["LEVEL_6_MODES_UPGRADES"] = "nível 5 máximo",
["LEVEL_6_TITLE"] = "6. A Toca das Feras Selvagens",
["LEVEL_7_HEROIC"] = "Descrição Heroica 7",
["LEVEL_7_HISTORY"] = "Seguindo o rastro dos cultistas que auxiliaram as Feras Selvagens a devastar parte da floresta, chegamos a um lugar desolado onde suspeitamos que o Culto está realizando seus estranhos planos. Precisamos ser cautelosos, pois não sabemos exatamente contra o que estamos lutando... mas eles parecem ter alguns truques debaixo das mangas.",
["LEVEL_7_IRON"] = "Descrição do Ferro 7",
["LEVEL_7_IRON_UNLOCK"] = "Sem arqueiros reais",
["LEVEL_7_MODES_UPGRADES"] = "nível 5 máximo",
["LEVEL_7_TITLE"] = "7. Vale Sombrio ",
["LEVEL_8_HEROIC"] = "Descrição Heroica 8",
["LEVEL_8_HISTORY"] = "Ao entrarmos no território dos cultistas, chegamos a um conjunto de enormes cavernas cheias de cristais que ressoam com uma magia estranha. O Culto está minerando esses cristais, certamente para usá-los como uma fonte de poder. Para quais fins, não sabemos, mas interromper suas atividades é uma boa maneira de causar caos em suas fileiras. ",
["LEVEL_8_IRON"] = "Descrição do Ferro 8",
["LEVEL_8_IRON_UNLOCK"] = "Tricannon\nPacto dos Paladinos",
["LEVEL_8_MODES_UPGRADES"] = "nível máximo 5",
["LEVEL_8_TITLE"] = "8. Minas Carmim",
["LEVEL_9_HEROIC"] = "Descrição Heróica 9",
["LEVEL_9_HISTORY"] = "As reviravoltas destes túneis são enlouquecedoras, mas sabemos que estamos no caminho certo, pois a atividade dos cultistas continua aumentando. Conforme avançamos, enfrentamos novos tipos de horrores, o que levanta a questão de quão profunda é a corrupção dentro das fileiras do Culto.",
["LEVEL_9_IRON"] = "Descrição de Ferro 9",
["LEVEL_9_IRON_UNLOCK"] = "Poço Demoníaco\nFeiticeiro Arcano",
["LEVEL_9_MODES_UPGRADES"] = "nível máximo 5",
["LEVEL_9_TITLE"] = "9. Travessia Maldita",
["LEVEL_DEFEAT_ADVICE"] = "USE JOIAS PARA OBTER ITENS ESPECIAIS E DETONAR SEUS INIMIGOS!",
["LEVEL_DEFEAT_GEMS_COLLECTED"] = "VOCÊ COLETOU",
["LEVEL_DEFEAT_GEMS_COUNT"] = "%i JOIAS",
["LEVEL_DEFEAT_TITLE"] = "DERROTA!",
["LEVEL_MODE_CAMPAIGN"] = "Campanha",
["LEVEL_MODE_HEROIC"] = "Desafio Heroico",
["LEVEL_MODE_HEROIC_DESCRIPTION"] = "Teste suas habilidades estratégicas contra uma força de inimigos de elite; desafio apenas para defensores mais heroicos!",
["LEVEL_MODE_IRON"] = "Desafio Ferrenho",
["LEVEL_MODE_IRON_DESCRIPTION"] = "Um teste para o supremo defensor, o desafio ferrenho levará suas habilidades estratégicas ao limite.",
["LEVEL_SELECT_AVAILABLE_TOWERS"] = "Torres disponíveis",
["LEVEL_SELECT_CHALLENGE_ONE_ELITE_WAVE"] = "1 onda de elite",
["LEVEL_SELECT_CHALLENGE_ONE_LIFE"] = "1 vida no total",
["LEVEL_SELECT_CHALLENGE_ONE_WAVE"] = "1 super onda",
["LEVEL_SELECT_CHALLENGE_RULES"] = "Regras do Desafio",
["LEVEL_SELECT_CHALLENGE_SIX_ELITE_WAVE"] = "6 ondas de elite",
["LEVEL_SELECT_DIFFICULTY_CASUAL"] = "CASUAL",
["LEVEL_SELECT_DIFFICULTY_IMPOSSIBLE"] = "IMPOSSÍVEL",
["LEVEL_SELECT_DIFFICULTY_NORMAL"] = "NORMAL",
["LEVEL_SELECT_DIFFICULTY_VETERAN"] = "VETERANO",
["LEVEL_SELECT_GAME_MODE"] = "Modo de jogo",
["LEVEL_SELECT_GET_DLC"] = "PEGUE",
["LEVEL_SELECT_HELP1"] = "Selecione o modo de jogo aqui!",
["LEVEL_SELECT_HELP2"] = "Selecione a dificuldade!",
["LEVEL_SELECT_HELP3"] = "Começar Guerra!",
["LEVEL_SELECT_MODE_LOCKED1"] = "Modo trancado",
["LEVEL_SELECT_MODE_LOCKED2"] = "Desbloqueie este modo completando esta fase.",
["LEVEL_SELECT_TO_BATTLE"] = "À\nGUERRA",
["LOADING"] = "CARREGANDO",
["LV22_BOSS_BEFORE_FIGHT_EAT_01"] = "Gostoso! Ha Ha Ha",
["LV22_BOSS_BEFORE_FIGHT_EAT_02"] = "Eu odeio plantas",
["LV22_BOSS_BEFORE_FIGHT_EAT_03"] = "Você é o que você come",
["LV22_BOSS_BEFORE_FIGHT_EAT_04"] = "Essa mordida foi refrescante",
["LV22_BOSS_BEFORE_FIGHT_EAT_05"] = "Já cansado?",
["LV22_BOSS_BEFORE_FIGHT_EAT_06"] = "Nunca mais sentirei fome",
["LV22_BOSS_BEFORE_FIGHT_EAT_07"] = "Era uma grande torre, hahaha",
["LV22_BOSS_BEFORE_FIGHT_EAT_08"] = "Tem gosto de liberdade",
["LV22_BOSS_INTRO_01"] = "Trazendo lanches para a minha primeira refeição.",
["LV22_BOSS_INTRO_02"] = "Eles parecem... crocantes",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_01"] = "Você só vai provar creepers.",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_02"] = "Os verdes são amigos, não comida",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_03"] = "E você não comerá mais nada!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_04"] = "Retorne à sua prisão, monstro!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_05"] = "Você não comerá!!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_06"] = "Eu vou proteger o Verde!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_07"] = "Você não vai rir no final",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_08"] = "Ele está ficando mais forte! Ajuda!!",
["LV22_MAGE_INTRO_01"] = "Fecha a boca!",
["LV22_MAGE_INTRO_02"] = "Depressa! Eu não posso segurá-lo por muito mais tempo!",
["Log in"] = "Entrar",
["Log out?"] = "Desconectou?",
["Login Required"] = "Entrar",
["MAGES’ GUILD"] = "GUILDA DE MAGOS",
["MAGIC RESISTANT ENEMIES!"] = "INIMIGOS COM RESISTÊNCIA MÁGICA!",
["MAP_BALLON_BUY_UPGRADES_DESCRIPTION"] = "Use as estrelas conquistadas para melhorar torres e poderes!",
["MAP_BALLON_BUY_UPGRADES_TITLE"] = "COMPRE MELHORIAS!",
["MAP_BALLON_HERO_LEVELUP_DESCRIPTION"] = "Use os pontos de herói para treinar seu herói!",
["MAP_BALLON_HERO_LEVELUP_TITLE"] = "HERÓI GANHOU NÍVEL!",
["MAP_BALLON_HERO_UNLOCKED"] = "HERÓI LIBERADO!",
["MAP_BALLON_START_HERE"] = "COMECE AQUI!",
["MAP_BUTTON_ACHIEVEMENTS"] = "CONQUISTAS",
["MAP_BUTTON_CHALLENGES"] = "DESAFIOS",
["MAP_BUTTON_ENCYCLOPEDIA"] = "ENCICLOPÉDIA",
["MAP_BUTTON_HERO_ROOM"] = "HERÓIS",
["MAP_BUTTON_ITEMS"] = "ITENS",
["MAP_BUTTON_SHOP"] = "LOJA",
["MAP_BUTTON_TOWER_ROOM"] = "TORRES",
["MAP_BUTTON_UPGRADES"] = "MELHORIAS",
["MAP_ENCYCLOPEDIA_STRATEGY_GUIDE"] = "GUIA ESTRATÉGICO",
["MAP_ENCYCLOPEDIA_TIPS"] = "DICAS",
["MAP_HEROROOM_HELP1"] = "Selecione e treine habilidades!",
["MAP_HEROROOM_HELP2"] = "Toque e selecione",
["MAP_HEROROOM_HELP3"] = "Melhore poderes do herói!",
["MAP_HERO_ROOM_GET_IT_NOW"] = "PEGUE AGORA!",
["MAP_HERO_ROOM_SELECT"] = "EQUIPAR",
["MAP_HERO_ROOM_SELECTED"] = "EQUIPADO",
["MAP_HERO_ROOM_TRAIN"] = "TREINAR",
["MAP_HERO_ROOM_UNLOCK"] = "LIBERA NA FASE %d",
["MAP_HERO_ROOM_UNLOCK_10"] = "LIBERA NA FASE 10",
["MAP_HERO_ROOM_UNLOCK_14"] = "LIBERA NA FASE 14",
["MAP_HERO_ROOM_UNLOCK_15"] = "LIBERA NA FASE 15",
["MAP_HERO_ROOM_UNLOCK_4"] = "LIBERA NA FASE 4",
["MAP_HERO_ROOM_UNLOCK_7"] = "LIBERA NA FASE 7",
["MAP_HERO_ROOM_UNLOCK_9"] = "LIBERA NA FASE 9",
["MAP_HERO_ROOM_UNLOCK_AFTER_CAMPAIGN"] = "Desbloqueia ao finalizar o jogo",
["MAP_INAPPS_BUBBLE_INFO_1"] = "Junte joias jogando.",
["MAP_INAPPS_BUBBLE_INFO_2"] = "Use as joias para comprar itens especiais!",
["MAP_INAPPS_BUBBLE_MORE_GEMS"] = "Você precisa de mais joias!",
["MAP_INAPPS_BUBBLE_SUCCESSFUL"] = "Compra\nbem sucedida!",
["MAP_INAPP_GEMS_GEM_SHOP_TITLE"] = "LOJA DE JOIAS",
["MAP_INAPP_GEM_PACK_1"] = "PUNHADO DE GEMAS",
["MAP_INAPP_GEM_PACK_2"] = "BOLSA DE GEMAS",
["MAP_INAPP_GEM_PACK_3"] = "BARRIL DE GEMAS",
["MAP_INAPP_GEM_PACK_4"] = "BAÚ DE GEMAS",
["MAP_INAPP_GEM_PACK_5"] = "VAGÃO DE GEMAS",
["MAP_INAPP_GEM_PACK_6"] = "MONTANHA DE GEMAS",
["MAP_INAPP_GEM_PACK_BAG"] = "Bolsa de Gemas",
["MAP_INAPP_GEM_PACK_BARREL"] = "Barril de Joias",
["MAP_INAPP_GEM_PACK_CHEST"] = "Baú de Gemas",
["MAP_INAPP_GEM_PACK_FREE"] = "Gemas Grátis",
["MAP_INAPP_GEM_PACK_HANDFUL"] = "Punhado de Gemas",
["MAP_INAPP_GEM_PACK_VAULT"] = "Cofre de Joias",
["MAP_INAPP_GEM_PACK_WAGON"] = "Carroça de Joias",
["MAP_INAPP_MORE_GEMS"] = "MAIS GEMAS",
["MAP_INAPP_TEXT_1"] = "Punhado de Gemas",
["MAP_INAPP_TEXT_2"] = "Bolsa de Gemas",
["MAP_INAPP_TEXT_3"] = "Baú de Gemas",
["MAP_INAPP_TEXT_4"] = "Gemas Grátis",
["MAP_INAPP_TEXT_GEMS"] = "Gemas",
["MAP_NEW_GAMEMODE_UNLOCKED_DESCRIPTION"] = "Enfrente inimigos sem fim, e dispute a melhor pontuação!",
["MAP_NEW_GAMEMODE_UNLOCKED_TITLE"] = "NOVO DESAFIO!",
["MAP_NEW_HERO_ALERT"] = "NOVO\nHERÓI!",
["MAP_NEW_TOWER_ALERT"] = "NOVA\nTORRE!",
["MAP_TOWER_ROOM_SELECT"] = "EQUIPAR",
["MAP_TOWER_ROOM_SELECTED"] = "EQUIPADO",
["MEDIUM"] = "MÉDIO",
["MENU_HUD_WAVES"] = "%i/%i",
["MINUTES_ABBREVIATION"] = "m",
["MORE_GAMES"] = "Mais jogos",
["Magic resistant enemies take less damage from mages."] = "Inimigos com resistência mágica sofrem menos dano de magos.",
["NEW"] = "NOVO",
["NEW SPECIAL POWER!"] = "NOVO ESPECIAL!",
["NEW TOWER UNLOCKED"] = "NOVA TORRE LIBERADA",
["NEW TOWER UPGRADES"] = "NOVAS MELHORIAS DE TORRES",
["NEWS"] = "NOVIDADES",
["NEWS_ERROR"] = "Falha ao conectar. Verifique sua conexão com a internet ou volte mais tarde.",
["NOTIFICATION_NEW_ENEMY_TITLE"] = "NOVO INIMIGO",
["NOTIFICATION_NEW_SPECIAL_TITLE"] = "NOVO PODER ESPECIAL!",
["NOTIFICATION_NEW_TOWERS_SUB_DESCRIPTION"] = "Agora você pode melhorar suas torres até o nível %d.",
["NOTIFICATION_NEW_TOWERS_SUB_TITLE"] = "TORRES DE NÍVEL %d DISPONÍVEIS",
["NOTIFICATION_NEW_TOWERS_TITLE"] = "NOVAS MELHORIAS PARA TORRE",
["NOTIFICATION_NEW_TOWER_TITLE"] = "NOVA TORRE DESBLOQUEADA",
["NOTIFICATION_armored_enemies_desc_body_1"] = "Alguns inimigos usam armaduras de diferentes forças, o que os protege contra ataques não mágicos.",
["NOTIFICATION_armored_enemies_desc_body_2"] = "Resiste danos de",
["NOTIFICATION_armored_enemies_desc_body_3"] = "Inimigos blindados recebem menos dano das torres de Atiradores, Quartel e Artilharia.",
["NOTIFICATION_armored_enemies_desc_title"] = "Inimigos Blindados!",
["NOTIFICATION_armored_enemies_enemy_name"] = "Lutador com Presas",
["NOTIFICATION_bottom_info_desc_body"] = "Você pode revisar as informações do inimigo a qualquer momento tocando em uma unidade e seu retrato.",
["NOTIFICATION_bottom_info_desc_title"] = "INFORMAÇÃO DO INIMIGO",
["NOTIFICATION_bottom_info_tap_portrait_desc"] = "Toque aqui para reabrir",
["NOTIFICATION_button_ok"] = "Ok",
["NOTIFICATION_glare_desc_body"] = "O Onividente pousa seu olhar sobre o campo de batalha, fortalecendo os inimigos próximos com seu Fulgor Corrompido.",
["NOTIFICATION_glare_desc_bullets"] = " - Cura os inimigos que estão na área\n- Ativa as habilidades únicas dos inimigos",
["NOTIFICATION_glare_desc_title"] = "Fulgor do Onividente",
["NOTIFICATION_hero_desc"] = "Mostra nível, saúde e experiência.",
["NOTIFICATION_hero_desc_baloon_1"] = "Selecione-o tocando no herói ou em seu retrato",
["NOTIFICATION_hero_desc_baloon_2"] = "Toque ou arraste sobre o caminho para movê-lo",
["NOTIFICATION_hero_desc_body_1"] = "Heróis são unidades de elite que podem enfrentar inimigos fortes e apoiar suas forças.",
["NOTIFICATION_hero_desc_body_2"] = "Heróis ganham experiência toda vez que atacam um inimigo ou usam uma habilidade.",
["NOTIFICATION_hero_desc_title"] = "Herói ao seu comando!",
["NOTIFICATION_magic_resistant_enemies_desc_body_1"] = "Alguns inimigos têm diferentes níveis de resistência mágica, o que os protege contra ataques mágicos.",
["NOTIFICATION_magic_resistant_enemies_desc_body_2"] = "Resiste danos de",
["NOTIFICATION_magic_resistant_enemies_desc_body_3"] = "Inimigos resistentes à magia recebem menos dano das torres de Magos.",
["NOTIFICATION_magic_resistant_enemies_desc_title"] = "Inimigos Resistentes à Magia!",
["NOTIFICATION_magic_resistant_enemies_enemy_name"] = "Tartaruga Xamã",
["NOTIFICATION_rally_point_desc_body_1"] = "Você pode ajustar o ponto de encontro de seus quartéis para fazer as unidades defenderem uma área diferente.",
["NOTIFICATION_rally_point_desc_body_2"] = "escolha o controle de ponto de guarda",
["NOTIFICATION_rally_point_desc_body_3"] = "escolha aonde deseja mover seus soldados",
["NOTIFICATION_rally_point_desc_subtitle"] = "Alcance do Encontro",
["NOTIFICATION_rally_point_desc_title"] = "Comande suas tropas!",
["NOTIFICATION_special_desc_body"] = "Você pode convocar tropas adicionais para ajudá-lo no campo de batalha.",
["NOTIFICATION_special_desc_bullets"] = "Os reforços são ótimos para atrasar os inimigos.",
["NOTIFICATION_special_desc_title"] = "Chamar Reforços",
["NOTIFICATION_title_enemy"] = "Informação do Inimigo",
["NOTIFICATION_title_glare"] = "NOVA DICA!",
["NOTIFICATION_title_hint"] = "HERÓI DESBLOQUEADO",
["NOTIFICATION_title_new_tip"] = "NOVA DICA",
["NOTIFICATION_title_special"] = "ESPECIAL DESBLOQUEADO",
["No"] = "Não",
["No, Thanks"] = "Não, obrigado",
["None"] = "Nenhum",
["Nope"] = "Nada",
["Normal"] = "Normal",
["OFF!"] = "OFF!",
["OFFERS_END"] = "A oferta termina em:",
["OFFER_GET_IT_NOW"] = "PEGUE AGORA",
["OFFER_GET_THEM_NOW"] = "PEGUE AGORA",
["OFFER_ICON_BANNER"] = "OFERTA",
["OFFER_OFF"] = "OFF",
["OFFER_PACK_DESCRIPTION_ALL_HEROES"] = "CONSIGA TODOS OS HERÓIS AGORA MESMO!",
["OFFER_PACK_DESCRIPTION_ALL_TOWERS"] = "CONSIGA TODAS AS TORRES AGORA MESMO!",
["OFFER_PACK_DESCRIPTION_TEXT_01"] = "Aumente as suas tropas com esta oferta exclusiva por tempo limitado!",
["OFFER_PACK_DESCRIPTION_TEXT_02"] = "Compre esta oferta!",
["OFFER_PACK_TIMELEFT_TEXT"] = "A oferta termina em:",
["OFFER_PACK_TITLE_01"] = "Oferta de Halloween",
["OFFER_PACK_TITLE_02"] = "Oferta de Black Friday",
["OFFER_PACK_TITLE_03"] = "Oferta de Natal",
["OFFER_PACK_TITLE_04"] = "Oferta de Ano Novo",
["OFFER_PACK_TITLE_05"] = "Oferta de Festival da Primavera",
["OFFER_PACK_TITLE_06"] = "Oferta de Verão",
["OFFER_PACK_TITLE_07"] = "Oferta do dia de Ironhide",
["OFFER_PACK_TITLE_08"] = "Oferta do Pacote de Iniciante",
["OFFER_PACK_TITLE_09"] = "Oferta por tempo limitado",
["OFFER_PACK_TITLE_ALL_HEROES"] = "MEGA PACOTE DE HERÓIS",
["OFFER_PACK_TITLE_ALL_TOWERS"] = "MEGA PACOTE DE TORRES",
["OFFER_PACK_TITLE_STARTER_PACK"] = "Oferta do Pacote de Iniciante",
["OFFER_REGULAR"] = "PREÇO NORMAL",
["ONE_TIME_OFFER"] = "OFERTA ÚNICA",
["ON_SALE"] = "PROMOÇÃO",
["OPTIONS"] = "OPÇÕES",
["OPTIONS_PAGE_CONTROLS"] = "CONTROLES",
["OPTIONS_PAGE_HELP"] = "AJUDA",
["OPTIONS_PAGE_SHORTCUTS"] = "AJUDA DO TECLADO",
["OPTIONS_PAGE_VIDEO"] = "VÍDEO",
["Objective"] = "Objetivo",
["Over 50 stars are recommended to face this stage."] = "É recomendado ter mais de 50 estrelas nesta fase.",
["POPUP_CLEAR_PROGRESS_CONFIRM"] = "VOCÊ TEM CERTEZA DE QUE QUER APAGAR SEU PROGRESSO?",
["POPUP_LABEL_MAIN_MENU"] = "Menu principal",
["POPUP_SETTINGS_LANGUAGE"] = "Idioma",
["POPUP_SETTINGS_MUSIC"] = "MÚSICA",
["POPUP_SETTINGS_SFX"] = "Som",
["POPUP_label_error_msg"] = "Ops! Alguma coisa saiu errada.",
["POPUP_label_error_msg2"] = "Ops! Alguma coisa saiu errada.",
["POPUP_label_purchasing"] = "PROCESSANDO SEU PEDIDO",
["POPUP_label_title_options"] = "Opções",
["POPUP_label_version"] = "Versão 0.0.8b",
["POWER_REINFORCEMENTS_NAME"] = "Reforços",
["POWER_SUMMON_DESCRIPTION"] = "Convoque tropas para a batalha.",
["POWER_SUMMON_LARGE_DESCRIPTION"] = "Você pode convocar tropas para ajudá-lo na batalha.\n\nReforços são grátis, e você pode convocá-los a cada 15 segundos.",
["POWER_SUMMON_NAME"] = "Reforços",
["PRICE_FREE"] = "Grátis",
["PRIVACY_POLICY_ASK_AGE"] = "Quando você nasceu?",
["PRIVACY_POLICY_BUTTON_LINK"] = "Política de privacidade",
["PRIVACY_POLICY_CONSENT_SHORT"] = "Antes de jogar, confirme que você (e seus pais, se você for uma criança ou adolescente) leu e aprovou nossa política de privacidade.",
["PRIVACY_POLICY_LINK"] = "Política de privacidade",
["PRIVACY_POLICY_WELCOME"] = "Bem-vindo!",
["PROCESSING ITEMS TO RESTORE"] = "ARTIGOS DE PROCESSAMENTO PARA RESTAURAR",
["PROCESSING YOUR REQUEST"] = "PROCESSANDO SEU PEDIDO",
["PURCHASE_PENDING_MESSAGE"] = "A compra está pendente e será entregue após a conclusão do pagamento ou processamento.",
["PUSH_NOTIFICATIONS_PERMISSION_RATIONALE"] = "Gostaria de receber notificações sobre vendas de produtos e novos jogos da Ironhide?",
["Produced by %s"] = "Produzido por %s",
["QUIT"] = "Sair",
["Quit"] = "Sair",
["RESTORE"] = "RESTAURAR",
["RESTORE_PURCHASES"] = "Rest. compras",
["RESTORE_SLOT_ADD_GEMS_TITLE"] = "Escolha o slot para adicionar joias",
["RESTORE_SLOT_PROGRESS_MSG"] = "Obtendo dados de restauração do servidor…",
["RESTORE_SLOT_STATS_TITLE"] = "ESTATÍSTICAS",
["RESTORE_SLOT_TITLE"] = "Escolha o slot para substituir",
["Rate %@"] = "Avaliar o %@",
["Remind me later"] = "Mais tarde",
["SALE_SCREEN_MAP_ROOMS"] = "PROMO",
["SECONDS_ABBREVIATION"] = "s",
["SETTINGS_LANGUAGE"] = "Idioma",
["SETTINGS_SUPPORT"] = "Suporte",
["SHOP_DESKTOP_GET_DLC_BUTTON"] = "PEGUE",
["SHOP_DESKTOP_TITLE"] = "LOJA",
["SHOP_ROOM_BEST_VALUE_TITLE"] = "MELHOR\nVALOR",
["SHOP_ROOM_DLC_1_DESCRIPTION"] = "EMBARQUE NESTA NOVA AVENTURA ÉPICA",
["SHOP_ROOM_DLC_1_TITLE"] = "CAMPANHA DE AMEAÇA COLOSSAL",
["SHOP_ROOM_DLC_1_TOOLTIP_DESCRIPTION"] = "5 Novas Fases\nNova Torre\nNovo Herói\nMais de 10 Novos Inimigos\n2 Lutas contra Mini Chefes\nUma Luta Épica contra o Chefe\nE muito mais...",
["SHOP_ROOM_DLC_1_TOOLTIP_TITLE"] = "CAMPANHA DE AMEAÇA COLOSSAL",
["SHOP_ROOM_DLC_2_DESCRIPTION"] = "EMBARQUE NESTA NOVA AVENTURA ÉPICA",
["SHOP_ROOM_DLC_2_TITLE"] = "CAMPANHA DA JORNADA DO WUKONG",
["SHOP_ROOM_MOST_POPULAR_TITLE"] = "MAIS\nPOPULAR",
["SLOT_CLOUD_DOWNLOADING"] = "Baixando...",
["SLOT_CLOUD_DOWNLOAD_FAILED"] = "Erro ao baixar jogo salvo na iCloud. Tente mais tarde.",
["SLOT_CLOUD_DOWNLOAD_SUCCESSFUL"] = "Baixado com sucesso.",
["SLOT_CLOUD_UPLOADING"] = "Enviando...",
["SLOT_CLOUD_UPLOAD_FAILED"] = "Erro ao enviar o jogo salvo à iCloud. Tente mais tarde.",
["SLOT_CLOUD_UPLOAD_ICLOUD_NOT_CONFIGURED"] = "iCloud não configurada em seu dispositivo.",
["SLOT_CLOUD_UPLOAD_SUCCESSFUL"] = "Enviado com sucesso.",
["SLOT_DELETE_SLOT"] = "Apagar espaço?",
["SLOT_NAME"] = "Espaço",
["SLOT_NEW_GAME"] = "NOVO JOGO",
["SOLDIER_ARBOREAN_BARRACK_NAME"] = "Soldado Arbóreo",
["SOLDIER_ARBOREAN_SENTINELS_1_NAME"] = "Baluu",
["SOLDIER_ARBOREAN_SENTINELS_2_NAME"] = "Vylla",
["SOLDIER_ARBOREAN_SENTINELS_3_NAME"] = "Ykkon",
["SOLDIER_ARBOREAN_SENTINELS_4_NAME"] = "Haavi",
["SOLDIER_ARBOREAN_SENTINELS_5_NAME"] = "Plook",
["SOLDIER_ARBOREAN_SENTINELS_6_NAME"] = "Guldd",
["SOLDIER_ARBOREAN_SENTINELS_7_NAME"] = "Teena",
["SOLDIER_ARBOREAN_SENTINELS_8_NAME"] = "Uuzky",
["SOLDIER_ARBOREAN_SENTINELS_9_NAME"] = "Deluu",
["SOLDIER_DRAGON_BONE_ULTIMATE_DOG_NAME"] = "Draco Esquelético",
["SOLDIER_EARTH_HOLDER_NAME"] = "Guerreiro de Pedra",
["SOLDIER_GHOST_TOWER_NAME"] = "Espectro",
["SOLDIER_HERO_BUILDER_WORKER_1_NAME"] = "Hemmar",
["SOLDIER_HERO_BUILDER_WORKER_2_NAME"] = "O'Tool",
["SOLDIER_HERO_BUILDER_WORKER_3_NAME"] = "Crews",
["SOLDIER_HERO_BUILDER_WORKER_4_NAME"] = "Birck",
["SOLDIER_HERO_BUILDER_WORKER_5_NAME"] = "Lauck",
["SOLDIER_HERO_BUILDER_WORKER_6_NAME"] = "O'Nail",
["SOLDIER_HERO_BUILDER_WORKER_7_NAME"] = "Hovels",
["SOLDIER_HERO_BUILDER_WORKER_8_NAME"] = "Woody",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL1_NAME"] = "Guardião Arbóreo",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL2_NAME"] = "Guardião Arbóreo",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL3_NAME"] = "Guardião Arbóreo",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL1_NAME"] = "Paragão Arbóreo",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL2_NAME"] = "Paragão Arbóreo",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL3_NAME"] = "Paragão Arbóreo",
["SOLDIER_HERO_SPIDER_ULTIMATE_NAME"] = "Aranhinha",
["SOLDIER_HERO_WITCH_CAT_1_NAME"] = "Conan",
["SOLDIER_HERO_WITCH_CAT_2_NAME"] = "Alfajor",
["SOLDIER_HERO_WITCH_CAT_3_NAME"] = "Babieca",
["SOLDIER_HERO_WITCH_CAT_4_NAME"] = "Peluche",
["SOLDIER_HERO_WITCH_CAT_5_NAME"] = "Pipa",
["SOLDIER_HERO_WITCH_CAT_6_NAME"] = "Watson",
["SOLDIER_HERO_WITCH_CAT_7_NAME"] = "Chimi",
["SOLDIER_HERO_WITCH_CAT_8_NAME"] = "Pantufla",
["SOLDIER_HERO_WITCH_DECOY_NAME"] = "Trapito",
["SOLDIER_HERO_WUKONG_HAIR_CLONES_1_NAME"] = "San Wikung",
["SOLDIER_HERO_WUKONG_HAIR_CLONES_2_NAME"] = "Son Wokeng",
["SOLDIER_ITEM_SUMMON_BLACKBURN_NAME"] = "Lorde Blackburn",
["SOLDIER_PALADINS_10_NAME"] = "Sir Joacim",
["SOLDIER_PALADINS_11_NAME"] = "Sir Andre",
["SOLDIER_PALADINS_12_NAME"] = "Sir Sammet",
["SOLDIER_PALADINS_13_NAME"] = "Sir Udo",
["SOLDIER_PALADINS_14_NAME"] = "Sir Eric",
["SOLDIER_PALADINS_15_NAME"] = "Sir Bruce",
["SOLDIER_PALADINS_16_NAME"] = "Sir Rob",
["SOLDIER_PALADINS_17_NAME"] = "Sir Biff",
["SOLDIER_PALADINS_18_NAME"] = "Sir Bowes",
["SOLDIER_PALADINS_1_NAME"] = "Sir Kai",
["SOLDIER_PALADINS_2_NAME"] = "Sir Hansi",
["SOLDIER_PALADINS_3_NAME"] = "Sir Luca",
["SOLDIER_PALADINS_4_NAME"] = "Sir Timo",
["SOLDIER_PALADINS_5_NAME"] = "Sir Ralf",
["SOLDIER_PALADINS_6_NAME"] = "Sir Tobias",
["SOLDIER_PALADINS_7_NAME"] = "Sir Deris",
["SOLDIER_PALADINS_8_NAME"] = "Sir Kiske",
["SOLDIER_PALADINS_9_NAME"] = "Sir Pesch",
["SOLDIER_PRIESTS_BARRACK_1_NAME"] = "Willy",
["SOLDIER_PRIESTS_BARRACK_2_NAME"] = "Henry",
["SOLDIER_PRIESTS_BARRACK_3_NAME"] = "Geoffrey",
["SOLDIER_PRIESTS_BARRACK_4_NAME"] = "Nicholas",
["SOLDIER_PRIESTS_BARRACK_5_NAME"] = "Ed",
["SOLDIER_PRIESTS_BARRACK_6_NAME"] = "Hob",
["SOLDIER_PRIESTS_BARRACK_7_NAME"] = "Odo",
["SOLDIER_PRIESTS_BARRACK_8_NAME"] = "Cedric",
["SOLDIER_PRIESTS_BARRACK_9_NAME"] = "Hal",
["SOLDIER_RANDOM_10_NAME"] = "Alvus",
["SOLDIER_RANDOM_11_NAME"] = "Borin",
["SOLDIER_RANDOM_12_NAME"] = "Hadrian",
["SOLDIER_RANDOM_13_NAME"] = "Thomas",
["SOLDIER_RANDOM_14_NAME"] = "Henry",
["SOLDIER_RANDOM_15_NAME"] = "Bryce",
["SOLDIER_RANDOM_16_NAME"] = "Rulf",
["SOLDIER_RANDOM_17_NAME"] = "Allister",
["SOLDIER_RANDOM_18_NAME"] = "Altair",
["SOLDIER_RANDOM_19_NAME"] = "Simon",
["SOLDIER_RANDOM_1_NAME"] = "Douglas",
["SOLDIER_RANDOM_20_NAME"] = "Egbert",
["SOLDIER_RANDOM_21_NAME"] = "Eldon",
["SOLDIER_RANDOM_22_NAME"] = "Garrett",
["SOLDIER_RANDOM_23_NAME"] = "Godwin",
["SOLDIER_RANDOM_24_NAME"] = "Gordon",
["SOLDIER_RANDOM_25_NAME"] = "Jerald",
["SOLDIER_RANDOM_26_NAME"] = "Kelvin",
["SOLDIER_RANDOM_27_NAME"] = "Lando",
["SOLDIER_RANDOM_28_NAME"] = "Maddox",
["SOLDIER_RANDOM_29_NAME"] = "Peyton",
["SOLDIER_RANDOM_2_NAME"] = "Dan McKill",
["SOLDIER_RANDOM_30_NAME"] = "Ramsey",
["SOLDIER_RANDOM_31_NAME"] = "Raymond",
["SOLDIER_RANDOM_32_NAME"] = "Robert",
["SOLDIER_RANDOM_33_NAME"] = "Sawyer",
["SOLDIER_RANDOM_34_NAME"] = "Silas",
["SOLDIER_RANDOM_35_NAME"] = "Stuart",
["SOLDIER_RANDOM_36_NAME"] = "Tanner",
["SOLDIER_RANDOM_37_NAME"] = "Usher",
["SOLDIER_RANDOM_38_NAME"] = "Wallace",
["SOLDIER_RANDOM_39_NAME"] = "Wesley",
["SOLDIER_RANDOM_3_NAME"] = "James Lee",
["SOLDIER_RANDOM_40_NAME"] = "Willard",
["SOLDIER_RANDOM_4_NAME"] = "Jar Johson",
["SOLDIER_RANDOM_5_NAME"] = "Phil",
["SOLDIER_RANDOM_6_NAME"] = "Robin",
["SOLDIER_RANDOM_7_NAME"] = "William",
["SOLDIER_RANDOM_8_NAME"] = "Martin",
["SOLDIER_RANDOM_9_NAME"] = "Arthur",
["SOLDIER_REINFORCEMENTS_F_1_NAME"] = "Ataina",
["SOLDIER_REINFORCEMENTS_F_2_NAME"] = "Maucil",
["SOLDIER_REINFORCEMENTS_F_3_NAME"] = "Gulica",
["SOLDIER_REINFORCEMENTS_F_4_NAME"] = "Rogas",
["SOLDIER_REINFORCEMENTS_M_10_NAME"] = "Podgie",
["SOLDIER_REINFORCEMENTS_M_1_NAME"] = "Gabini",
["SOLDIER_REINFORCEMENTS_M_2_NAME"] = "O'Bell",
["SOLDIER_REINFORCEMENTS_M_3_NAME"] = "Kent",
["SOLDIER_REINFORCEMENTS_M_4_NAME"] = "Jendars",
["SOLDIER_REINFORCEMENTS_M_5_NAME"] = "Jarlosc",
["SOLDIER_REINFORCEMENTS_M_6_NAME"] = "Astong",
["SOLDIER_REINFORCEMENTS_M_7_NAME"] = "Buigell",
["SOLDIER_REINFORCEMENTS_M_8_NAME"] = "Clane",
["SOLDIER_REINFORCEMENTS_M_9_NAME"] = "Magus",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_1_NAME"] = "Dench",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_2_NAME"] = "Smith",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_3_NAME"] = "Andrews",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_4_NAME"] = "Thompson",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_5_NAME"] = "Taylor",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_1_NAME"] = "McCartney",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_2_NAME"] = "McKellen",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_3_NAME"] = "Hopkins",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_4_NAME"] = "Caine",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_5_NAME"] = "Kingsley",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_10_NAME"] = "Víbora",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_1_NAME"] = "Fang",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_2_NAME"] = "Blade",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_3_NAME"] = "Claw",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_4_NAME"] = "Talon",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_5_NAME"] = "Edgee",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_6_NAME"] = "Shiv",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_7_NAME"] = "Foice",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_8_NAME"] = "Adaga",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_9_NAME"] = "Ferroada",
["SOLDIER_REINFORCEMENTS_SPECIAL_DARK_ARMY_1_NAME"] = "Chamacorvos Sombrio",
["SOLDIER_REINFORCEMENTS_SPECIAL_LINIREA_1_NAME"] = "Cavaleiro Exemplar",
["SOLDIER_STAGE_10_YMCA_BIKER_NAME"] = "Glenn",
["SOLDIER_STAGE_10_YMCA_CONSTRUCTOR_NAME"] = "David",
["SOLDIER_STAGE_10_YMCA_INDIO_NAME"] = "Felipe",
["SOLDIER_STAGE_10_YMCA_POLICIA_NAME"] = "Victor",
["SOLDIER_STAGE_15_DENAS_NAME"] = "Rei Denas",
["SOLDIER_TOWER_DARK_ELF_1_NAME"] = "Filraen",
["SOLDIER_TOWER_DARK_ELF_2_NAME"] = "Faeryl",
["SOLDIER_TOWER_DARK_ELF_3_NAME"] = "Gurina",
["SOLDIER_TOWER_DARK_ELF_4_NAME"] = "Jhalass",
["SOLDIER_TOWER_DARK_ELF_5_NAME"] = "Solenzar",
["SOLDIER_TOWER_DARK_ELF_6_NAME"] = "Tebryn",
["SOLDIER_TOWER_DARK_ELF_7_NAME"] = "Vierna",
["SOLDIER_TOWER_DARK_ELF_8_NAME"] = "Zyn",
["SOLDIER_TOWER_DARK_ELF_9_NAME"] = "Elerra",
["SOLDIER_TOWER_DWARF_10_NAME"] = "Babbi",
["SOLDIER_TOWER_DWARF_1_NAME"] = "Pippi",
["SOLDIER_TOWER_DWARF_2_NAME"] = "Ginni",
["SOLDIER_TOWER_DWARF_3_NAME"] = "Merri",
["SOLDIER_TOWER_DWARF_4_NAME"] = "Lorri",
["SOLDIER_TOWER_DWARF_5_NAME"] = "Talli",
["SOLDIER_TOWER_DWARF_6_NAME"] = "Danni",
["SOLDIER_TOWER_DWARF_7_NAME"] = "Getti",
["SOLDIER_TOWER_DWARF_8_NAME"] = "Daffi",
["SOLDIER_TOWER_DWARF_9_NAME"] = "Bibbi",
["SOLDIER_TOWER_ELVEN_BARRACK_1_NAME"] = "Elandil",
["SOLDIER_TOWER_ELVEN_BARRACK_2_NAME"] = "Puck",
["SOLDIER_TOWER_ELVEN_BARRACK_3_NAME"] = "Thas",
["SOLDIER_TOWER_ELVEN_BARRACK_4_NAME"] = "Kastore",
["SOLDIER_TOWER_ELVEN_BARRACK_5_NAME"] = "Elric",
["SOLDIER_TOWER_ELVEN_BARRACK_6_NAME"] = "Elaith",
["SOLDIER_TOWER_NECROMANCER_SKELETON_GOLEM_NAME"] = "Golem de Ossos",
["SOLDIER_TOWER_NECROMANCER_SKELETON_NAME"] = "Esqueleto",
["SOLDIER_TOWER_PANDAS_FEMALE_1_NAME"] = "Yan",
["SOLDIER_TOWER_PANDAS_FEMALE_2_NAME"] = "Qingzhao",
["SOLDIER_TOWER_PANDAS_FEMALE_3_NAME"] = "Hui",
["SOLDIER_TOWER_PANDAS_FEMALE_4_NAME"] = "Ailing",
["SOLDIER_TOWER_PANDAS_MALE_1_NAME"] = "Tzu",
["SOLDIER_TOWER_PANDAS_MALE_2_NAME"] = "Qian",
["SOLDIER_TOWER_PANDAS_MALE_3_NAME"] = "Xueqin",
["SOLDIER_TOWER_PANDAS_MALE_4_NAME"] = "Nai'an",
["SOLDIER_TOWER_PANDAS_MALE_5_NAME"] = "Xun",
["SOLDIER_TOWER_PANDAS_MALE_6_NAME"] = "Xingjian",
["SOLDIER_TOWER_PANDAS_MALE_7_NAME"] = "Wei",
["SOLDIER_TOWER_PANDAS_MALE_8_NAME"] = "Chen",
["SOLDIER_TOWER_ROCKET_GUNNERS_10_NAME"] = "Fortus",
["SOLDIER_TOWER_ROCKET_GUNNERS_1_NAME"] = "Axl",
["SOLDIER_TOWER_ROCKET_GUNNERS_2_NAME"] = "Rose",
["SOLDIER_TOWER_ROCKET_GUNNERS_3_NAME"] = "Slash",
["SOLDIER_TOWER_ROCKET_GUNNERS_4_NAME"] = "Hudson",
["SOLDIER_TOWER_ROCKET_GUNNERS_5_NAME"] = "Izzy",
["SOLDIER_TOWER_ROCKET_GUNNERS_6_NAME"] = "Duff",
["SOLDIER_TOWER_ROCKET_GUNNERS_7_NAME"] = "Adler",
["SOLDIER_TOWER_ROCKET_GUNNERS_8_NAME"] = "Dizzy",
["SOLDIER_TOWER_ROCKET_GUNNERS_9_NAME"] = "Ferrer",
["SOLDIER_ZHU_APPRENTICE_NAME"] = "Zhu Bajie",
["SPECIAL_ARBOREAN_BARRACK_DESCRIPTION"] = "Convoque 3 soldados arbóreos que lutam contra inimigos em seu caminho.",
["SPECIAL_ARBOREAN_BARRACK_NAME"] = "Cidadãos Arbóreos",
["SPECIAL_ARBOREAN_HONEY_DESCRIPTION"] = "O apicultor assume sua posição, comandando suas abelhas para desacelerar e danificar inimigos com mel pegajoso!",
["SPECIAL_ARBOREAN_HONEY_NAME"] = "Apicultor Arbóreo",
["SPECIAL_ARBOREAN_OLDTREE_DESCRIPTION"] = "O sujeito mal-humorado solta um enorme tronco rolante que esmaga inimigos em seu caminho.",
["SPECIAL_ARBOREAN_OLDTREE_NAME"] = "Árvore Velha",
["SPECIAL_ARBOREAN_SENTINELS_SPEARMEN_DESCRIPTION"] = "Ágeis protetoras da floresta.",
["SPECIAL_ARBOREAN_SENTINELS_SPEARMEN_NAME"] = "Lança-espinho Arborean",
["SPECIAL_PRIESTS_SOLDIERS_DESCRIPTION"] = "Cultistas redimidos que se transformam em abominações ao morrer.",
["SPECIAL_PRIESTS_SOLDIERS_NAME"] = "Cultistas Cegos",
["SPECIAL_REPAIR_HOLDER_DRAGON_DESCRIPTION"] = "Apague as chamas para liberar a torre instantaneamente.",
["SPECIAL_REPAIR_HOLDER_DRAGON_NAME"] = "Envolto em chamas",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_EARTH_DESCRIPTION"] = "Aumenta a saúde das unidades da torre.\nInvoca até 3 Guerreiros de Pedra.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_EARTH_NAME"] = "Elemental Holder: Earth",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_FIRE_DESCRIPTION"] = "Adiciona dano extra à torre construída.\nOcasionalmente mata um inimigo instantaneamente.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_FIRE_NAME"] = "Elemental Holder: Fire",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_METAL_DESCRIPTION"] = "Reduz o custo de construção.\nGera ouro dos inimigos.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_METAL_NAME"] = "Elemental Holder: Metal",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WATER_DESCRIPTION"] = "Cura constantemente as unidades aliadas próximas.\nTeletransporta os inimigos de volta ao longo do caminho.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WATER_NAME"] = "Elemental Holder: Water",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WOOD_DESCRIPTION"] = "Adiciona alcance extra à torre construída.\nOcasionalmente gera raízes persistentes que retardam os inimigos.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WOOD_NAME"] = "Elemental Holder: Wood",
["SPECIAL_REPAIR_HOLDER_SEA_OF_TREES_DESCRIPTION"] = "Limpe os escombros para habilitar esta posição estratégica. ",
["SPECIAL_REPAIR_HOLDER_SEA_OF_TREES_NAME"] = "Escombros ",
["SPECIAL_REPAIR_HOLDER_SPIDERS_DESCRIPTION"] = "Liberte o portador das teias para ativar este ponto estratégico.",
["SPECIAL_REPAIR_HOLDER_SPIDERS_NAME"] = "Portador Enredado",
["SPECIAL_REPAIR_OVERSEER_DESCRIPTION"] = "Repila os tentáculos para desbloquear esta posição estratégica.",
["SPECIAL_REPAIR_OVERSEER_NAME"] = "Tentáculos",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_1_DESCRIPTION"] = "Contrate um Elfo Mercenário para ajudar na batalha. Revive a cada 10 segundos.",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_1_NAME"] = "Elfos Mercenários I",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_2_DESCRIPTION"] = "Contrate até 2 Elfos Mercenários para ajudar na batalha. Revivem a cada 10 segundos.",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_2_NAME"] = "Elfos Mercenários II",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_3_DESCRIPTION"] = "Contrate até 3 Elfos Mercenários para ajudar na batalha. Revivem a cada 10 segundos.",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_3_NAME"] = "Elfos Mercenários III",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_1"] = "Lança raios de magia que destroem as ilusões de Mydrias e a impedem de criar mais por alguns segundos.",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_2"] = "Invoca 2 Guardiões Demoníacos que percorrem o caminho lutando contra inimigos.",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_3"] = "Prende Denas, impedindo-o de se mover e atacar.",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_1"] = "Impacto de Almas",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_2"] = "Proles Infernais",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_3"] = "Grilhões Mágicos",
["START BATTLE!"] = "COMEÇAR GUERRA!",
["START HERE!"] = "COMECE AQUI!",
["STRATEGY BASICS!"] = "ESTRATÉGIAS BÁSICAS!",
["Select by tapping on the portrait or hero unit."] = "Toque no retrato ou na unidade-herói para selecionar",
["Sell Tower"] = "Vender Torre",
["Sell this tower and get a %s GP refund."] = "Venda esta torre e ganhe %s PO de volta.",
["Shows level, health and experience."] = "Exibe nível, vida e experiência",
["Special abilities"] = "Habilidades especiais",
["Support your soldiers with ranged towers!"] = "Apoie seus soldados com torres de distância!",
["Survival mode!"] = "Modo sobrevivência!",
["TAP_TO_START"] = "Toque para iniciar",
["TAUNT_BOSS_PIG_FROM_POOL_0001"] = "Vou fazer você guinchar!",
["TAUNT_BOSS_PIG_FROM_POOL_0002"] = "Diga 'bacon' novamente. Eu te desafio em dobro!",
["TAUNT_BOSS_PIG_FROM_POOL_0003"] = "Humanos estão de volta ao cardápio, rapazes!",
["TAUNT_BOSS_PIG_FROM_POOL_0004"] = "Anda logo! Estou com fome.",
["TAUNT_BOSS_PIG_FROM_POOL_0005"] = "Vou gostar de assistir você morrer.",
["TAUNT_BOSS_PIG_FROM_POOL_0006"] = "Eu sei, sou o pior.",
["TAUNT_LVL30_BOSS_ABILITY_01"] = "Banqueteiem-se, meus filhos!",
["TAUNT_LVL30_BOSS_ABILITY_02"] = "Aguenta firme! MWAHAHAHA!",
["TAUNT_LVL30_BOSS_ABILITY_03"] = "Pelo Culto!",
["TAUNT_LVL30_BOSS_ABILITY_04"] = "Refeições saborosas para todos!",
["TAUNT_LVL30_BOSS_ABILITY_05"] = "Meu sentido aranha está tinindo!",
["TAUNT_LVL30_BOSS_ABILITY_06"] = "Ajoelhe-se diante de mim, Aliança!",
["TAUNT_LVL30_BOSS_ABILITY_07"] = "Minha casa, minhas regras!",
["TAUNT_LVL30_BOSS_ABILITY_08"] = "Ninguém escapa da minha teia!",
["TAUNT_LVL30_BOSS_ABILITY_09"] = "Morra, praga humanoide!",
["TAUNT_LVL30_BOSS_ABILITY_10"] = "Puxando suas cordas!",
["TAUNT_LVL30_BOSS_ABILITY_11"] = "Matem todos!",
["TAUNT_LVL30_BOSS_INTRO_01"] = "Finalmente! Os assassinos de minhas irmãs mostram suas caras…",
["TAUNT_LVL30_BOSS_INTRO_02"] = "Em memória dos meus irmãos Sarelgaz e Mactans…",
["TAUNT_LVL30_BOSS_INTRO_03"] = "Todos vocês acabarão de joelhos, me adorando!",
["TAUNT_LVL30_BOSS_PREFIGHT_01"] = "Chega disso...",
["TAUNT_LVL30_BOSS_PREFIGHT_02"] = "Vocês não passam de insetos insignificantes...",
["TAUNT_LVL30_BOSS_PREFIGHT_03"] = "Enredados na teia da Rainha!",
["TAUNT_LVL32_BOSS_ABILITY_01"] = "Tolos! Eu controlo a chama divina, o Fogo Samadhi!",
["TAUNT_LVL32_BOSS_ABILITY_02"] = "Chamas ardentes surgem dos céus!",
["TAUNT_LVL32_BOSS_ABILITY_03"] = "Temam o verdadeiro fogo em sua forma mais pura!",
["TAUNT_LVL32_BOSS_ABILITY_04"] = "Corpos e almas queimam igualmente!",
["TAUNT_LVL32_BOSS_FIGHT_01"] = "O fogo dentro de mim nunca morrerá!",
["TAUNT_LVL32_BOSS_FINAL_01"] = "Minha chama está se apagando...\nmas eu ainda tenho meu dragão...",
["TAUNT_LVL32_BOSS_INTRO_01"] = "Então você tem um exército?",
["TAUNT_LVL32_BOSS_INTRO_02"] = "Eu tenho um dragão! Ha ha ha ha!",
["TAUNT_LVL32_BOSS_PREFIGHT_01"] = "Chega! Agora é a minha vitória!",
["TAUNT_LVL32_BOSS_PREFIGHT_02"] = "Admirem minha verdadeira forma!",
["TAUNT_LVL34_BOSS_BOSSFIGHT_01"] = "Ok então, eu sei exatamente o que precisamos. Mais de mim. Eu, eu, eu...",
["TAUNT_LVL34_BOSS_DEATH_01"] = "Isso não pode ser… Não importa, meu marido fará vocês pagarem…",
["TAUNT_LVL34_BOSS_INTRO_01"] = "Seus macacos! Têm a ousadia de vir aqui depois do que fizeram ao meu filho?",
["TAUNT_LVL34_BOSS_WAVES_01"] = "Provem o meu poder, tolos insolentes!",
["TAUNT_LVL34_BOSS_WAVES_02"] = "O fim está próximo!",
["TAUNT_LVL35_BOSS_DEATH_01"] = "E assim cai o meu reinado... em sangue.",
["TAUNT_LVL35_BOSS_DESTROY_HOUSE_01"] = "Mmm, isso foi caro. Hora de mostrar poder de fogo!",
["TAUNT_LVL35_BOSS_DESTROY_HOUSE_02"] = "Ah, o som da perseverança. Hora de se hidratar, senhora!",
["TAUNT_LVL35_BOSS_DESTROY_HOUSE_03"] = "Grrr! Submeta-se à minha vulgar demonstração de poder!",
["TAUNT_LVL35_BOSS_INTRO_01"] = "Humanos insignificantes, alegrem-se enquanto ainda estiverem entre os vivos.",
["TAUNT_LVL35_BOSS_INTRO_02"] = "É hora da nova ordem.",
["TAUNT_LVL35_BOSS_INTRO_03"] = "Argh, estou gritando por vingança!",
["TAUNT_LVL35_BOSS_PREFIGHT_01"] = "Muito bem, então vou te mostrar por que MATAR é o meu negócio!",
["TAUNT_STAGE02_RAELYN_0001"] = "Vamos fazer isso.",
["TAUNT_STAGE02_VEZNAN_0001"] = "Aqui vêm eles. Eu ajudarei suas forças insignificantes...",
["TAUNT_STAGE02_VEZNAN_0002"] = "...quero dizer, um dos meus melhores soldados vai fazer isso. HA!",
["TAUNT_STAGE02_VEZNAN_0003"] = "HA HA HA!",
["TAUNT_STAGE06_BOSS_PIG_PREBATTLE_0001"] = "Tudo bem... Farei isso eu mesmo.",
["TAUNT_STAGE06_BOSS_PIG_RESPONSE_0001"] = "Relaxe, tudo está sob controle.",
["TAUNT_STAGE06_CULTIST_GREETING_0001"] = "Vejo que você está muito confortável aí...",
["TAUNT_STAGE06_CULTIST_GREETING_0002"] = "...melhor cumprir a sua parte do acordo.",
["TAUNT_STAGE11_CULTIST_LEADER_0001"] = "É bom que você tenha chegado tão longe...",
["TAUNT_STAGE11_CULTIST_LEADER_0002"] = "...mas você não pode parar o inevitável!",
["TAUNT_STAGE11_CULTIST_LEADER_0003"] = "CHEGA!!!",
["TAUNT_STAGE11_CULTIST_LEADER_0004"] = "É hora de você SE CURVAR diante de nós!",
["TAUNT_STAGE11_CULTIST_LEADER_0005"] = "Grrr... isso não é o fim!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0001"] = "Um novo mundo nos espera.",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0002"] = "Você subestima meu poder.",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0003"] = "Oculus Poculus!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0004"] = "Ouça o som da inevitabilidade!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0005"] = "Sou malvada? Sim, sou!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0006"] = "O Onividente nos abençoa!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0001"] = "Seu fim está próximo!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0002"] = "Meus olhos foram abertos!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0003"] = "Diga \"olá\" para meus amigos do vácuo!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0004"] = "Oculus Poculus!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0005"] = "Escória fraca e patética!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0006"] = "O Onividente nos abençoa!",
["TAUNT_STAGE11_VEZNAN_0001"] = "Denas, meu amigo. Há quanto tempo não nos vemos!",
["TAUNT_STAGE15_CULTIST_0001"] = "Está perto... Eu posso senti-lo despertando!",
["TAUNT_STAGE15_CULTIST_0002"] = "Uma nova era está próxima. Seus esforços serão em vão!",
["TAUNT_STAGE15_CULTIST_0003"] = "Grrr... sua aliança é poderosa.",
["TAUNT_STAGE15_CULTIST_0004"] = "Mas eu vou mostrar a você o que é verdadeiro poder!",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0001"] = "Tolos! Vocês vieram para morrer. ",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0002"] = "Renda-se perante o seu olhar!",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0003"] = "Você se tornará um verdadeiro crente.",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0004"] = "Aliança ou não, você está condenado!",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0005"] = "No Vácuo não há vida. Apenas morte",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0006"] = "Pare de desperdiçar meu tempo!",
["TAUNT_STAGE15_DENAS_0001"] = "Tenho uma dívida a saldar. Esta luta eu não vou perder!",
["TAUNT_STAGE16_DENAS_AFTER_BOSSFIGHT_0001"] = "Não viu isso chegando, hein?",
["TAUNT_STAGE18_ERIDAN_FIGHT_0001"] = "Sangue foi derramada esta noite.",
["TAUNT_STAGE18_ERIDAN_FIGHT_0002"] = "Confiamos em Elynie.",
["TAUNT_STAGE18_ERIDAN_FIGHT_0003"] = "Gnillur speek Edihnori!",
["TAUNT_STAGE18_ERIDAN_FIGHT_0004"] = "Sou incapaz de falhar.",
["TAUNT_STAGE18_ERIDAN_FIGHT_0005"] = "Aredhel prevalecerá!",
["TAUNT_STAGE18_ERIDAN_FIGHT_0006"] = "Esses não são elfos comuns!",
["TAUNT_STAGE18_ERIDAN_FIGHT_0007"] = "Está contando?",
["TAUNT_STAGE18_ERIDAN_FIGHT_0008"] = "Deixe-os vir!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0001"] = "Você tem meu arco!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0002"] = "Aja com rapidez!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0003"] = "Aos seus postos!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0004"] = "Mantenham os olhos abertos!",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0001"] = "Chega de aquecimento!",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0002"] = "Vocês provaram ser um incômodo...",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0003"] = "Que comece a verdadeira batalha!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0001"] = "Entreguem suas almas!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0002"] = "Os elfos se erguerão novamente!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0003"] = "Se tem algo que eu levanto... são os mortos!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0004"] = "Temam meus filhos do além!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0005"] = "Meu exército é interminável!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0006"] = "Devolverei a glória ao meu povo!",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0001"] = "Ah, a poderosa Aliança veio de visita!",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0002"] = "Chegaram bem a tempo de ver o véu se levantar.",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0003"] = "Vamos ver se podem enfrentar o poder da morte!",
["TAUNT_STAGE22_BOSS_CROCS_BEFORE_BOSSFIGHT_0001"] = "Livre enfim para devorar...",
["TAUNT_STAGE22_BOSS_CROCS_BEFORE_BOSSFIGHT_0002"] = "TUDO!!!!!",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0001"] = "Chega de intromissões!",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0002"] = "Grymbeard vai ensinar algumas boas maneiras para vocês.",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0003"] = "Todos a bordo, AHAHAHA!",
["TAUNT_STAGE25_BOSS_MACHINIST_END_0001"] = "Palhaços insolentes!",
["TAUNT_STAGE25_BOSS_MACHINIST_END_0002"] = "Vocês nunca vão me pegar, HAHAHA!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0001"] = "Não! Ainda tem mais...",
["TAUNT_STAGE26_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0002"] = "PELAS MINHAS BARBAS!!!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0001"] = "Vocês não são páreo para este exército!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0002"] = "Grymbeard não está em perigo.",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0003"] = "Grymbeard É o perigo!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0004"] = "Será que um louco seria capaz de fazer isso?",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0005"] = "O mundo se curvará diante de Grymbeard!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0001"] = "A paciência de Grymbeard chegou no limite.",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0002"] = "Vocês vão ver algo realmente sério!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0003"] = "Grymbeard não precisa de ninguém além de si mesmo!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0004"] = "Vão se apressar?!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0001"] = "Vocês e sua maldita Aliança intrometida!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0002"] = "Vou ensinar vocês a não mexerem...",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0003"] = "...com o anão PRINCIPAL!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0001"] = "Destruam todas as réplicas que quiserem, eu só farei mais.",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0002"] = "Se quiser algo bem feito, faça você mesmo.",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0003"] = "Oh Grymbeard, você é um gênio!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0004"] = "Você não vai escapar com isso!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0005"] = "Vocês estão mesmo tentando?",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0006"] = "Acham que podem superar minhas criações?",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0001"] = "Acho que vocês não se cansaram de mim...",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0002"] = "...e agora querem enfrentar o anão SUPREMO?",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0003"] = "Fiquem à vontade para tentar.",
["TAUNT_TUTORIAL_ARBOREAN_ALL_0001"] = "Continue assim! Nós acreditamos em você.",
["TAUNT_TUTORIAL_ARBOREAN_BARRACK_0001"] = "Aqui, construa um quartel!",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_1_NAME"] = "Limblliam",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_2_NAME"] = "Henry Tentáculo",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_3_NAME"] = "Geoffrey Tentáculo",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_4_NAME"] = "Tentaclas",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_5_NAME"] = "Tedtacle",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_6_NAME"] = "Holimb",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_7_NAME"] = "Tentodo",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_8_NAME"] = "Limbdric",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_9_NAME"] = "Halimb",
["TERMS_OF_SERVICE_LINK"] = "Termos de serviço",
["TIP_TITLE"] = "Dica:",
["TOWER_ARBOREAN_EMISSARY_1_DESCRIPTION"] = "Os Arboreans tornam seus inimigos mais vulneráveis usando a poderosa magia da natureza",
["TOWER_ARBOREAN_EMISSARY_1_NAME"] = "Emissário Arborean I",
["TOWER_ARBOREAN_EMISSARY_2_DESCRIPTION"] = "Os Arboreans tornam seus inimigos mais vulneráveis usando a poderosa magia da natureza",
["TOWER_ARBOREAN_EMISSARY_2_NAME"] = "Emissário Arborean II",
["TOWER_ARBOREAN_EMISSARY_3_DESCRIPTION"] = "Os Arboreans tornam seus inimigos mais vulneráveis usando a poderosa magia da natureza",
["TOWER_ARBOREAN_EMISSARY_3_NAME"] = "Emissário Arborean III",
["TOWER_ARBOREAN_EMISSARY_4_DESCRIPTION"] = "Os Arboreans tornam seus inimigos mais vulneráveis usando a poderosa magia da natureza",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_1_DESCRIPTION"] = "Invoca espíritos que curam %$towers.arborean_emissary.gift_of_nature.s_heal[1]%$ de saúde por segundo durante %$towers.arborean_emissary.gift_of_nature.duration[1]%$ segundos para aliados em uma área",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_1_NAME"] = "DOM DA NATUREZA",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_2_DESCRIPTION"] = "Invoca espíritos que curam %$towers.arborean_emissary.gift_of_nature.s_heal[2]%$ de saúde por %$towers.arborean_emissary.gift_of_nature.duration[2]%$ segundos para aliados numa área.",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_2_NAME"] = "DOM DA NATUREZA",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_3_DESCRIPTION"] = "Invoca espíritos que curam %$towers.arborean_emissary.gift_of_nature.s_heal[3]%$ de saúde por %$towers.arborean_emissary.gift_of_nature.duration[3]%$ segundos para aliados numa área.",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_3_NAME"] = "DOM DA NATUREZA",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_NAME"] = "DOM DA NATUREZA",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_NOTE"] = "Nunca mexa com o Verde.",
["TOWER_ARBOREAN_EMISSARY_4_NAME"] = "Emissário Arborean IV",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_1_DESCRIPTION"] = "Faz crescer %$towers.arborean_emissary.wave_of_roots.max_targets[1]%$ raízes ao longo do caminho, causando %$towers.arborean_emissary.wave_of_roots.s_damage[1]%$ de dano verdadeiro e atordoando inimigos por %$towers.arborean_emissary.wave_of_roots.mod_duration[1]%$ segundos.",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_1_NAME"] = "APERTO DE ESPINHEIROS",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_2_DESCRIPTION"] = "Faz crescer %$towers.arborean_emissary.wave_of_roots.max_targets[2]%$ raízes ao longo do caminho, causando %$towers.arborean_emissary.wave_of_roots.s_damage[2]%$ de dano verdadeiro e atordoando inimigos por %$towers.arborean_emissary.wave_of_roots.mod_duration[2]%$ segundos.",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_2_NAME"] = "APERTO DE ESPINHEIROS",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_3_DESCRIPTION"] = "Faz crescer %$towers.arborean_emissary.wave_of_roots.max_targets[3]%$ raízes ao longo do caminho, causando %$towers.arborean_emissary.wave_of_roots.s_damage[3]%$ de dano verdadeiro e atordoando inimigos por %$towers.arborean_emissary.wave_of_roots.mod_duration[3]%$ segundos.",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_3_NAME"] = "APERTO DE ESPINHEIROS",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_NAME"] = "APERTO DE ESPINHEIROS",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_NOTE"] = "Pise com cuidado.",
["TOWER_ARBOREAN_EMISSARY_DESC"] = "Quando provocados, os Arboreans têm sido conhecidos por usarem sua magia para marcar e enfraquecer seus inimigos.",
["TOWER_ARBOREAN_EMISSARY_NAME"] = "Emissário Arborean",
["TOWER_ARBOREAN_SENTINELS_DESCRIPTION"] = "Ágeis protetoras da floresta.",
["TOWER_ARBOREAN_SENTINELS_NAME"] = "Lanças-espinho Arborean",
["TOWER_ARCANE_WIZARD_1_DESCRIPTION"] = "Bem versados nas artes mágicas, esses feiticeiros estão sempre prontos para uma luta. ",
["TOWER_ARCANE_WIZARD_1_NAME"] = "Feiticeiros arcanos I ",
["TOWER_ARCANE_WIZARD_2_DESCRIPTION"] = "Bem versados nas artes mágicas, esses feiticeiros estão sempre prontos para uma luta.",
["TOWER_ARCANE_WIZARD_2_NAME"] = "Feiticeiros arcanos II ",
["TOWER_ARCANE_WIZARD_3_DESCRIPTION"] = "Bem versados nas artes mágicas, esses feiticeiros estão sempre prontos para uma luta.",
["TOWER_ARCANE_WIZARD_3_NAME"] = "Feiticeiros arcanos III ",
["TOWER_ARCANE_WIZARD_4_DESCRIPTION"] = "Bem versados nas artes mágicas, esses feiticeiros estão sempre prontos para uma luta.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_1_DESCRIPTION"] = "Lança um raio que mata instantaneamente o alvo. Bosses e mini-bosses recebem %$towers.arcane_wizard.disintegrate.boss_damage[1]%$ de dano mágico em vez disso.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_1_NAME"] = "DESINTEGRAR ",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_2_DESCRIPTION"] = "Reduz o tempo de recarga para %$towers.arcane_wizard.disintegrate.cooldown[2]%$ segundos. Dano a chefes e mini-chefes agora é %$towers.arcane_wizard.disintegrate.boss_damage[2]%$.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_2_NAME"] = "DESINTEGRAR ",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_3_DESCRIPTION"] = "Reduz o tempo de recarga para %$towers.arcane_wizard.disintegrate.cooldown[3]%$ segundos. O dano a chefes e mini-chefes é agora de %$towers.arcane_wizard.disintegrate.boss_damage[3]%$.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_3_NAME"] = "DESINTEGRAR ",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_NAME"] = "DESINTEGRAR ",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_NOTE"] = "Pó ao pó. ",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_1_DESCRIPTION"] = "Aumenta o dano das torres próximas em %$towers.arcane_wizard.empowerment.s_damage_factor[1]%$%.",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_1_NAME"] = "Fortalecimento",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_2_DESCRIPTION"] = "Aumenta o dano das torres próximas em %$towers.arcane_wizard.empowerment.s_damage_factor[2]%$%.",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_2_NAME"] = "Fortalecimento",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_3_DESCRIPTION"] = "Aumenta o dano das torres próximas em %$towers.arcane_wizard.empowerment.s_damage_factor[3]%$%.",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_3_NAME"] = "Fortalecimento",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_NAME"] = "Fortalecimento",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_NOTE"] = "Poder ilimitado.",
["TOWER_ARCANE_WIZARD_4_NAME"] = "Feiticeiros arcanos IV ",
["TOWER_ARCANE_WIZARD_DESC"] = "Acessando a magia pura, os feiticeiros Linireanos possuem poder suficiente para destruir completamente seus inimigos. ",
["TOWER_ARCANE_WIZARD_NAME"] = "Feiticeiros arcanos ",
["TOWER_BALLISTA_1_DESCRIPTION"] = "Uma ótima adição à guerra dos peles-verdes, é um milagre que ainda não tenha se despedaçado.",
["TOWER_BALLISTA_1_NAME"] = "Posto de Balista I",
["TOWER_BALLISTA_2_DESCRIPTION"] = "Uma ótima adição à guerra dos peles-verdes, é um milagre que ainda não tenha se despedaçado.",
["TOWER_BALLISTA_2_NAME"] = "Posto de Balista II",
["TOWER_BALLISTA_3_DESCRIPTION"] = "Uma ótima adição à guerra dos peles-verdes, é um milagre que ainda não tenha se despedaçado.",
["TOWER_BALLISTA_3_NAME"] = "Posto de Balista III",
["TOWER_BALLISTA_4_DESCRIPTION"] = "Uma ótima adição à guerra dos peles-verdes, é um milagre que ainda não tenha se despedaçado.",
["TOWER_BALLISTA_4_NAME"] = "Posto de Balista IV",
["TOWER_BALLISTA_4_SKILL_BOMB_1_DESCRIPTION"] = "Dispara uma bomba feita de sucata a grande distância, causando %$towers.ballista.skill_bomb.damage_min[1]%$-%$towers.ballista.skill_bomb.damage_max[1]%$ de dano físico. Ela reduz a velocidade dos inimigos por %$towers.ballista.skill_bomb.duration[1]%$ segundos.",
["TOWER_BALLISTA_4_SKILL_BOMB_1_NAME"] = "BOMBA DE SUCATA",
["TOWER_BALLISTA_4_SKILL_BOMB_2_DESCRIPTION"] = "A bomba de sucata causa %$towers.ballista.skill_bomb.damage_min[2]%$-%$towers.ballista.skill_bomb.damage_max[2]%$ de dano físico. Ela retarda os inimigos por %$towers.ballista.skill_bomb.duration[1]%$ segundos.",
["TOWER_BALLISTA_4_SKILL_BOMB_2_NAME"] = "BOMBA DE SUCATA",
["TOWER_BALLISTA_4_SKILL_BOMB_3_DESCRIPTION"] = "A bomba de sucata causa %$towers.ballista.skill_bomb.damage_min[3]%$-%$towers.ballista.skill_bomb.damage_max[3]%$ de dano físico. Ela retarda os inimigos por %$towers.ballista.skill_bomb.duration[1]%$ segundos.",
["TOWER_BALLISTA_4_SKILL_BOMB_3_NAME"] = "BOMBA DE SUCATA",
["TOWER_BALLISTA_4_SKILL_BOMB_NOTE"] = "Olha a frente!",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_1_DESCRIPTION"] = "O último tiro da torre causa %$towers.ballista.skill_final_shot.s_damage_factor[1]%$% mais dano e atordoa o alvo por %$towers.ballista.skill_final_shot.s_stun%$ segundo.",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_1_NAME"] = "PREGO FINAL",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_2_DESCRIPTION"] = "O último tiro causa %$towers.ballista.skill_final_shot.s_damage_factor[2]%$% mais dano e atordoa o alvo por %$towers.ballista.skill_final_shot.s_stun%$ segundo.",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_2_NAME"] = "PREGO FINAL",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_3_DESCRIPTION"] = "O último tiro causa %$towers.ballista.skill_final_shot.s_damage_factor[3]%$% mais dano e atordoa o alvo por %$towers.ballista.skill_final_shot.s_stun%$ segundo.",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_3_NAME"] = "PREGO FINAL",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_NOTE"] = "Isso foi um em um milhão, garoto!",
["TOWER_BALLISTA_DESC"] = "Extremamente entusiasmados com a guerra, os goblins fizeram um esforço extra para garantir que não precisariam usar um arco novamente.",
["TOWER_BALLISTA_NAME"] = "Posto de Balista",
["TOWER_BARREL_1_DESCRIPTION"] = "Os barris de poção dos nortenhos são uma arma poderosa contra hordas de inimigos.",
["TOWER_BARREL_1_NAME"] = "Mestres Fermentadores I",
["TOWER_BARREL_2_DESCRIPTION"] = "Os barris de poção dos nortenhos são uma arma poderosa contra hordas de inimigos.",
["TOWER_BARREL_2_NAME"] = "Mestres Fermentadores II",
["TOWER_BARREL_3_DESCRIPTION"] = "Os barris de poção dos nortenhos são uma arma poderosa contra hordas de inimigos.",
["TOWER_BARREL_3_NAME"] = "Mestres Fermentadores III",
["TOWER_BARREL_4_DESCRIPTION"] = "Os barris de poção dos nortenhos são uma arma poderosa contra hordas de inimigos.",
["TOWER_BARREL_4_NAME"] = "Mestres Fermentadores IV",
["TOWER_BARREL_4_SKILL_BARREL_1_DESCRIPTION"] = "Lança um barril nocivo que causa de %$towers.barrel.skill_barrel.explosion.damage_min[1]%$ a %$towers.barrel.skill_barrel.explosion.damage_max[1]%$ de dano físico. O barril deixa um veneno que causa %$towers.barrel.skill_barrel.poison.s_damage%$ de dano verdadeiro por segundo durante %$towers.barrel.skill_barrel.poison.duration%$ segundos. ",
["TOWER_BARREL_4_SKILL_BARREL_1_NAME"] = "LOTE RUIM",
["TOWER_BARREL_4_SKILL_BARREL_2_DESCRIPTION"] = "A explosão do barril de veneno causa de %$towers.barrel.skill_barrel.explosion.damage_min[2]%$ a %$towers.barrel.skill_barrel.explosion.damage_max[2]%$ de dano físico. O veneno do barril causa %$towers.barrel.skill_barrel.poison.s_damage%$ de dano verdadeiro por segundo durante %$towers.barrel.skill_barrel.poison.duration%$ segundos.",
["TOWER_BARREL_4_SKILL_BARREL_2_NAME"] = "LOTE RUIM",
["TOWER_BARREL_4_SKILL_BARREL_3_DESCRIPTION"] = "A explosão do barril de veneno causa de %$towers.barrel.skill_barrel.explosion.damage_min[3]%$ a %$towers.barrel.skill_barrel.explosion.damage_max[3]%$ de dano físico. O veneno do barril causa %$towers.barrel.skill_barrel.poison.s_damage%$ de dano verdadeiro por segundo durante %$towers.barrel.skill_barrel.poison.duration%$ segundos.",
["TOWER_BARREL_4_SKILL_BARREL_3_NAME"] = "LOTE RUIM",
["TOWER_BARREL_4_SKILL_BARREL_NOTE"] = "Apenas para os corajosos!",
["TOWER_BARREL_4_SKILL_WARRIOR_1_DESCRIPTION"] = "Invoca um guerreiro potencializado para lutar no caminho. Tem %$towers.barrel.skill_warrior.entity.hp_max[1]%$ de saúde e causa %$towers.barrel.skill_warrior.entity.damage_min[1]%$-%$towers.barrel.skill_warrior.entity.damage_max[1]%$ de dano físico.",
["TOWER_BARREL_4_SKILL_WARRIOR_1_NAME"] = "ELIXIR DA FORÇA",
["TOWER_BARREL_4_SKILL_WARRIOR_2_DESCRIPTION"] = "O guerreiro tem %$towers.barrel.skill_warrior.entity.hp_max[2]%$ de saúde e causa %$towers.barrel.skill_warrior.entity.damage_min[2]%$-%$towers.barrel.skill_warrior.entity.damage_max[2]%$ de dano físico.",
["TOWER_BARREL_4_SKILL_WARRIOR_2_NAME"] = "ELIXIR DA FORÇA",
["TOWER_BARREL_4_SKILL_WARRIOR_3_DESCRIPTION"] = "O guerreiro tem %$towers.barrel.skill_warrior.entity.hp_max[3]%$ de saúde e causa %$towers.barrel.skill_warrior.entity.damage_min[3]%$-%$towers.barrel.skill_warrior.entity.damage_max[3]%$ de dano físico.",
["TOWER_BARREL_4_SKILL_WARRIOR_3_NAME"] = "ELIXIR DA FORÇA",
["TOWER_BARREL_4_SKILL_WARRIOR_NOTE"] = "Tem gosto de vitória!",
["TOWER_BARREL_DESC"] = "Os nortistas são especialistas na arte de fazer poções e usam suas bebidas em batalha para lutar contra inimigos.",
["TOWER_BARREL_NAME"] = "Mestres Fermentadores",
["TOWER_BARREL_WARRIOR_NAME"] = "Halfdan o Obtuso",
["TOWER_BROKEN_DESCRIPTION"] = "Esta torre está danificada, pague ouro para repará-la.",
["TOWER_BROKEN_NAME"] = "Torre Danificada",
["TOWER_CROCS_EATEN_DESCRIPTION"] = "Reconstruir magicamente a torre para sua forma original.",
["TOWER_CROCS_EATEN_NAME"] = "Restos da torre",
["TOWER_DARK_ELF_1_DESCRIPTION"] = "Não importa a distância ou a força do inimigo, suas flechas são sempre certeiras.",
["TOWER_DARK_ELF_1_NAME"] = "Arqueiros Crepusculares I",
["TOWER_DARK_ELF_2_DESCRIPTION"] = "Não importa a distância ou a força do inimigo, suas flechas são sempre certeiras.",
["TOWER_DARK_ELF_2_NAME"] = "Arqueiros Crepusculares II",
["TOWER_DARK_ELF_3_DESCRIPTION"] = "Não importa a distância ou a força do inimigo, suas flechas são sempre certeiras.",
["TOWER_DARK_ELF_3_NAME"] = "Arqueiros crepusculares III",
["TOWER_DARK_ELF_4_DESCRIPTION"] = "Não importa a distância ou a força do inimigo, suas flechas são sempre certeiras.",
["TOWER_DARK_ELF_4_NAME"] = "Arqueiros crepusculares IV",
["TOWER_DARK_ELF_4_SKILL_BUFF_1_DESCRIPTION"] = "Cada vez que a torre mata um inimigo, seu dano de ataque aumenta em %$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$.",
["TOWER_DARK_ELF_4_SKILL_BUFF_1_NAME"] = "EMOÇÃO DA CAÇA",
["TOWER_DARK_ELF_4_SKILL_BUFF_2_DESCRIPTION"] = "Cada vez que a torre mata um inimigo, seu dano de ataque aumenta em %$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$.",
["TOWER_DARK_ELF_4_SKILL_BUFF_2_NAME"] = "EMOÇÃO DA CAÇA",
["TOWER_DARK_ELF_4_SKILL_BUFF_3_DESCRIPTION"] = "Cada vez que a torre mata um inimigo, seu dano de ataque aumenta em %$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$.",
["TOWER_DARK_ELF_4_SKILL_BUFF_3_NAME"] = "EMOÇÃO DA CAÇA",
["TOWER_DARK_ELF_4_SKILL_BUFF_NOTE"] = "¡Tally-ho!",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_1_DESCRIPTION"] = "Invoca dois Assediadores Crepusculares. Eles têm %$towers.dark_elf.soldier.hp[1]%$ de vida e causam %$towers.dark_elf.soldier.basic_attack.damage_min[1]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[1]%$ de dano físico.",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_1_NAME"] = "ESPADAS DE REFORÇO",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_2_DESCRIPTION"] = "Os Assediadores Crepusculares agora têm %$towers.dark_elf.soldier.hp[2]%$ de vida e causam %$towers.dark_elf.soldier.basic_attack.damage_min[2]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[2]%$ de dano físico.",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_2_NAME"] = "ESPADAS DE REFORÇO",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_3_DESCRIPTION"] = "Os Assediadores Crepusculares agora têm %$towers.dark_elf.soldier.hp[3]%$ de vida e causam %$towers.dark_elf.soldier.basic_attack.damage_min[3]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[3]%$ de dano físico.",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_3_NAME"] = "ESPADAS DE REFORÇO",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_NOTE"] = "Amigos estão chegando para brincar.",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_DESCRIPTION"] = "Muda o foco da torre para o inimigo que está mais perto da saída.",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_NAME"] = "Foco Inimigo: Mais Próximo",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_NOTE"] = "Não deixe que escape!",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_DESCRIPTION"] = "Muda o foco da torre para o inimigo com mais vida.",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_NAME"] = "Foco Inimigo: Vida Alta",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_NOTE"] = "Vá atrás do maior!",
["TOWER_DARK_ELF_DESC"] = "Arqueiros especializados na caça de inimigos poderosos a grandes distâncias, aprimorando seus disparos com energia sombria.",
["TOWER_DARK_ELF_NAME"] = "Arqueiros Crepusculares",
["TOWER_DEMON_PIT_1_DESCRIPTION"] = "Travessos e perigosos, esses demônios estão sempre à procura de encrenca.",
["TOWER_DEMON_PIT_1_NAME"] = "Fosso Demoníaco I",
["TOWER_DEMON_PIT_2_DESCRIPTION"] = "Travessos e perigosos, esses demônios estão sempre à procura de encrenca.",
["TOWER_DEMON_PIT_2_NAME"] = "Fosso Demoníaco II",
["TOWER_DEMON_PIT_3_DESCRIPTION"] = "Travessos e perigosos, esses demônios estão sempre à procura de encrenca.",
["TOWER_DEMON_PIT_3_NAME"] = "Fosso Demoníaco III",
["TOWER_DEMON_PIT_4_BIG_DEMON_1_DESCRIPTION"] = "Gera um enorme diabrete com %$towers.demon_pit.big_guy.hp_max[1]%$ de saúde que causa %$towers.demon_pit.big_guy.melee_attack.damage_min[1]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[1]%$ de dano físico. Ao explodir, causa %$towers.demon_pit.big_guy.explosion_damage[1]%$ de dano.",
["TOWER_DEMON_PIT_4_BIG_DEMON_1_NAME"] = "CHEFÃO",
["TOWER_DEMON_PIT_4_BIG_DEMON_2_DESCRIPTION"] = "O Grande Diabrete tem %$towers.demon_pit.big_guy.hp_max[2]%$ de saúde e causa %$towers.demon_pit.big_guy.melee_attack.damage_min[2]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[2]%$ de dano físico. A explosão causa %$towers.demon_pit.big_guy.explosion_damage[2]%$ de dano.",
["TOWER_DEMON_PIT_4_BIG_DEMON_2_NAME"] = "CHEFÃO",
["TOWER_DEMON_PIT_4_BIG_DEMON_3_DESCRIPTION"] = "O Grande Diabrete tem %$towers.demon_pit.big_guy.hp_max[3]%$ de saúde e causa %$towers.demon_pit.big_guy.melee_attack.damage_min[3]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[3]%$ de dano físico. A explosão causa %$towers.demon_pit.big_guy.explosion_damage[3]%$ de dano.",
["TOWER_DEMON_PIT_4_BIG_DEMON_3_NAME"] = "CHEFÃO",
["TOWER_DEMON_PIT_4_BIG_DEMON_NAME"] = "CHEFÃO",
["TOWER_DEMON_PIT_4_BIG_DEMON_NOTE"] = "Apenas tentando relaxar.",
["TOWER_DEMON_PIT_4_DESCRIPTION"] = "Travessos e perigosos, esses demônios estão sempre à procura de encrenca.",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_1_DESCRIPTION"] = "A explosão dos diabretes agora causa %$towers.demon_pit.master_exploders.s_damage_increase[1]%$% mais dano e queima os inimigos, causando %$towers.demon_pit.master_exploders.s_total_burning_damage_min[1]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[1]%$ de dano verdadeiro por segundo durante %$towers.demon_pit.master_exploders.s_burning_duration[1]%$ segundos. ",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_1_NAME"] = "MESTRES EXPLOSIVOS",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_2_DESCRIPTION"] = "A explosão dos diabretes causa %$towers.demon_pit.master_exploders.s_damage_increase[2]%$% mais dano. A queimadura causa %$towers.demon_pit.master_exploders.s_total_burning_damage_min[2]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[2]%$ de dano verdadeiro por segundo durante %$towers.demon_pit.master_exploders.s_burning_duration[2]%$ segundos.",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_2_NAME"] = "MESTRES EXPLOSIVOS",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_3_DESCRIPTION"] = "A explosão dos diabretes causa %$towers.demon_pit.master_exploders.s_damage_increase[3]%$% mais dano. A queimadura causa %$towers.demon_pit.master_exploders.s_total_burning_damage_min[3]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[3]%$ de dano verdadeiro por segundo durante %$towers.demon_pit.master_exploders.s_burning_duration[3]%$ segundos.",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_3_NAME"] = "MESTRES EXPLOSIVOS",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_NAME"] = "MESTRES EXPLOSIVOS",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_NOTE"] = "Só um idiota faria este trabalho.",
["TOWER_DEMON_PIT_4_NAME"] = "Fosso Demoníaco IV",
["TOWER_DEMON_PIT_DESC"] = "Surgindo das profundezas da lava, esses diabretes não hesitam em se atirar no caminho dos inimigos.",
["TOWER_DEMON_PIT_NAME"] = "Fosso Demoníaco",
["TOWER_DEMON_PIT_SOLDIER_BIG_GUY_NAME"] = "Grandalhão",
["TOWER_DEMON_PIT_SOLDIER_NAME"] = "Diabrete",
["TOWER_DWARF_1_DESCRIPTION"] = "Embora sejam tão curtas quanto seus pavios, nada passa vivo por suas linhas.",
["TOWER_DWARF_1_NAME"] = "Esquadrão de Canhoneiras I",
["TOWER_DWARF_2_DESCRIPTION"] = "Embora sejam tão curtas quanto seus pavios, nada passa vivo por suas linhas.",
["TOWER_DWARF_2_NAME"] = "Esquadrão de Canhoneiras II",
["TOWER_DWARF_3_DESCRIPTION"] = "Embora sejam tão curtas quanto seus pavios, nada passa vivo por suas linhas.",
["TOWER_DWARF_3_NAME"] = "Esquadrão de Canhoneiras III",
["TOWER_DWARF_4_DESCRIPTION"] = "Embora sejam tão curtas quanto seus pavios, nada passa vivo por suas linhas.",
["TOWER_DWARF_4_FORMATION_1_DESCRIPTION"] = "Adicione uma terceira Canhoneira ao esquadrão.",
["TOWER_DWARF_4_FORMATION_1_NAME"] = "FILEIRAS CRESCENTES",
["TOWER_DWARF_4_FORMATION_2_DESCRIPTION"] = "Adicione uma quarta Canhoneira ao esquadrão.",
["TOWER_DWARF_4_FORMATION_2_NAME"] = "FILEIRAS CRESCENTES",
["TOWER_DWARF_4_FORMATION_3_DESCRIPTION"] = "Adicione uma quinta Canhoneira ao esquadrão.",
["TOWER_DWARF_4_FORMATION_3_NAME"] = "FILEIRAS CRESCENTES",
["TOWER_DWARF_4_FORMATION_NOTE"] = "As garotas só querem armas.",
["TOWER_DWARF_4_INCENDIARY_AMMO_1_DESCRIPTION"] = "Dispare um explosivo que causa %$towers.dwarf.incendiary_ammo.damages_min[1]%$ - %$towers.dwarf.incendiary_ammo.damages_max[1]%$ de dano e queima inimigos na área, causando %$towers.dwarf.incendiary_ammo.burn.s_damage[1]%$ de dano ao longo de %$towers.dwarf.incendiary_ammo.burn.duration%$ segundos.",
["TOWER_DWARF_4_INCENDIARY_AMMO_1_NAME"] = "MUNIÇÃO INCENDIÁRIA",
["TOWER_DWARF_4_INCENDIARY_AMMO_2_DESCRIPTION"] = "Dispare um explosivo que causa %$towers.dwarf.incendiary_ammo.damages_min[2]%$ - %$towers.dwarf.incendiary_ammo.damages_max[2]%$ de dano e queima inimigos na área, causando %$towers.dwarf.incendiary_ammo.burn.s_damage[2]%$ de dano ao longo de %$towers.dwarf.incendiary_ammo.burn.duration%$ segundos.",
["TOWER_DWARF_4_INCENDIARY_AMMO_2_NAME"] = "MUNIÇÃO INCENDIÁRIA",
["TOWER_DWARF_4_INCENDIARY_AMMO_3_DESCRIPTION"] = "Dispare um explosivo que causa %$towers.dwarf.incendiary_ammo.damages_min[3]%$ - %$towers.dwarf.incendiary_ammo.damages_max[3]%$ de dano e queima inimigos na área, causando %$towers.dwarf.incendiary_ammo.burn.s_damage[3]%$ de dano ao longo de %$towers.dwarf.incendiary_ammo.burn.duration%$ segundos.",
["TOWER_DWARF_4_INCENDIARY_AMMO_3_NAME"] = "MUNIÇÃO INCENDIÁRIA",
["TOWER_DWARF_4_INCENDIARY_AMMO_NOTE"] = "Não brinquem com fogo!",
["TOWER_DWARF_4_NAME"] = "Esquadrão de Canhoneiras IV",
["TOWER_DWARF_DESC"] = "Atiradoras especialistas com um espírito de corpo incomparável, enviadas do norte para controlar o uso inadequado da tecnologia.",
["TOWER_DWARF_NAME"] = "Esquadrão de Canhoneiras",
["TOWER_ELVEN_STARGAZERS_DESC"] = "Invocando as energias do cosmos, os Astrônomos Elfo podem lutar contra muitos inimigos ao mesmo tempo.",
["TOWER_ELVEN_STARGAZERS_NAME"] = "Astrônomos Elfo",
["TOWER_FLAMESPITTER_1_DESCRIPTION"] = "Seu fogo pode ser facilmente comparado com o de um dragão, espalhando terror entre os malvados. ",
["TOWER_FLAMESPITTER_1_NAME"] = "Lança-chamas Anão I ",
["TOWER_FLAMESPITTER_2_DESCRIPTION"] = "Seu fogo pode ser facilmente comparado com o de um dragão, espalhando terror entre os malvados. ",
["TOWER_FLAMESPITTER_2_NAME"] = "Lança-chamas Anão II ",
["TOWER_FLAMESPITTER_3_DESCRIPTION"] = "Seu fogo pode ser facilmente comparado com o de um dragão, espalhando terror entre os malvados. ",
["TOWER_FLAMESPITTER_3_NAME"] = "Lança-chamas Anão III ",
["TOWER_FLAMESPITTER_4_DESCRIPTION"] = "Seu fogo pode ser facilmente comparado com o de um dragão, espalhando terror entre os malvados. ",
["TOWER_FLAMESPITTER_4_NAME"] = " Lança-chamas Anão IV ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_1_DESCRIPTION"] = "Lança uma bomba flamejante que causa %$towers.flamespitter.skill_bomb.s_damage[1]%$ de dano físico e queima inimigos por %$towers.flamespitter.skill_bomb.burning.s_damage%$ de dano verdadeiro por segundo durante %$towers.flamespitter.skill_bomb.burning.duration%$ segundos. ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_1_NAME"] = "TRILHA FLAMEJANTE ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_2_DESCRIPTION"] = "A bomba flamejante causa %$towers.flamespitter.skill_bomb.s_damage[2]%$ de dano físico. A queimadura causa %$towers.flamespitter.skill_bomb.burning.s_damage%$ de dano verdadeiro por segundo durante %$towers.flamespitter.skill_bomb.burning.duration%$ segundos. ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_2_NAME"] = "TRILHA FLAMEJANTE ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_3_DESCRIPTION"] = "A bomba flamejante causa %$towers.flamespitter.skill_bomb.s_damage[3]%$ de dano físico. A queimadura causa %$towers.flamespitter.skill_bomb.burning.s_damage%$ de dano verdadeiro por segundo durante %$towers.flamespitter.skill_bomb.burning.duration%$ segundos. ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_3_NAME"] = "TRILHA FLAMEJANTE ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_NOTE"] = "Abrasador e selvagem. ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_1_DESCRIPTION"] = "Colunas de fogo irrompem do caminho causando %$towers.flamespitter.skill_columns.s_damage_out[1]%$-%$towers.flamespitter.skill_columns.s_damage_in[1]%$ de dano físico e atordoando inimigos por %$towers.flamespitter.skill_columns.s_stun%$ segundo. ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_1_NAME"] = "TOCHAS ARDENTES ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_2_DESCRIPTION"] = "As colunas de fogo causam de %$towers.flamespitter.skill_columns.s_damage_out[2]%$-%$towers.flamespitter.skill_columns.s_damage_in[2]%$ de dano físico e atordoam inimigos por %$towers.flamespitter.skill_columns.s_stun%$ segundo. ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_2_NAME"] = "TOCHAS ARDENTES ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_3_DESCRIPTION"] = "As colunas de fogo causam de %$towers.flamespitter.skill_columns.s_damage_out[3]%$-%$towers.flamespitter.skill_columns.s_damage_in[3]%$ de dano físico e atordoam inimigos por %$towers.flamespitter.skill_columns.s_stun%$ segundo. ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_3_NAME"] = "TOCHAS ARDENTES ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_NOTE"] = "Cuidado onde pisa! ",
["TOWER_FLAMESPITTER_DESC"] = "Levando o calor da forja para a batalha, os anões emprestam sua resoluta determinação à aliança. ",
["TOWER_FLAMESPITTER_NAME"] = "Lança-chamas Anão ",
["TOWER_GHOST_1_DESCRIPTION"] = "Agora você os vê, agora não. Agora você está morto.",
["TOWER_GHOST_1_NAME"] = "Espectros Sombrios I",
["TOWER_GHOST_2_DESCRIPTION"] = "Agora você os vê, agora não. Agora você está morto.",
["TOWER_GHOST_2_NAME"] = "Espectros Sombrios II",
["TOWER_GHOST_3_DESCRIPTION"] = "Agora você os vê, agora não. Agora você está morto.",
["TOWER_GHOST_3_NAME"] = "Espectros Sombrios III",
["TOWER_GHOST_4_DESCRIPTION"] = "Agora você os vê, agora não. Agora você está morto.",
["TOWER_GHOST_4_EXTRA_DAMAGE_1_DESCRIPTION"] = "Os espectros causam %$towers.ghost.extra_damage.s_damage[1]%$% de dano extra após passarem %$towers.ghost.extra_damage.cooldown_start%$ segundos em combate.",
["TOWER_GHOST_4_EXTRA_DAMAGE_1_NAME"] = "SUCÇÃO DE ALMAS",
["TOWER_GHOST_4_EXTRA_DAMAGE_2_DESCRIPTION"] = "Os espectros causam %$towers.ghost.extra_damage.s_damage[2]%$% de dano extra após passarem %$towers.ghost.extra_damage.cooldown_start%$ segundos em combate.",
["TOWER_GHOST_4_EXTRA_DAMAGE_2_NAME"] = "SUCÇÃO DE ALMAS",
["TOWER_GHOST_4_EXTRA_DAMAGE_3_DESCRIPTION"] = "Os espectros causam %$towers.ghost.extra_damage.s_damage[3]%$% de dano extra após passarem %$towers.ghost.extra_damage.cooldown_start%$ segundos em combate.",
["TOWER_GHOST_4_EXTRA_DAMAGE_3_NAME"] = "SUCÇÃO DE ALMAS",
["TOWER_GHOST_4_EXTRA_DAMAGE_NOTE"] = "Exposição não recomendada.",
["TOWER_GHOST_4_NAME"] = "Espectros Sombrios IV",
["TOWER_GHOST_4_SOUL_ATTACK_1_DESCRIPTION"] = "Espectros derrotados lançam-se contra um inimigo próximo, infligindo %$towers.ghost.soul_attack.s_damage[1]%$ de dano verdadeiro, reduzindo sua velocidade e cortando pela metade seu dano de ataque.",
["TOWER_GHOST_4_SOUL_ATTACK_1_NAME"] = "TEMOR IMORTAL",
["TOWER_GHOST_4_SOUL_ATTACK_2_DESCRIPTION"] = "Espectros derrotados lançam-se contra um inimigo próximo, infligindo %$towers.ghost.soul_attack.s_damage[2]%$ de dano verdadeiro, reduzindo sua velocidade e cortando pela metade seu dano de ataque.",
["TOWER_GHOST_4_SOUL_ATTACK_2_NAME"] = "TEMOR IMORTAL",
["TOWER_GHOST_4_SOUL_ATTACK_3_DESCRIPTION"] = "Espectros derrotados lançam-se contra um inimigo próximo, causando %$towers.ghost.soul_attack.s_damage[3]%$ de dano verdadeiro, reduzindo sua velocidade e cortando pela metade seu dano de ataque.",
["TOWER_GHOST_4_SOUL_ATTACK_3_NAME"] = "TEMOR IMORTAL",
["TOWER_GHOST_4_SOUL_ATTACK_NOTE"] = "Você vem conosco!",
["TOWER_GHOST_DESC"] = "Espectros que lutam mesmo após a morte. Seu poder permite que se movam através das sombras e surpreendam inimigos.",
["TOWER_GHOST_NAME"] = "Espectros Sombrios",
["TOWER_HERMIT_TOAD_1_DESCRIPTION"] = "Um pouco de magia, um pouco de força bruta, o que for necessário para se livrar de intrusos indesejados.",
["TOWER_HERMIT_TOAD_1_NAME"] = "Eremita do Pântano I",
["TOWER_HERMIT_TOAD_2_DESCRIPTION"] = "Um pouco de magia, um pouco de força bruta, o que for necessário para se livrar de intrusos indesejados.",
["TOWER_HERMIT_TOAD_2_NAME"] = "Eremita do Pântano II",
["TOWER_HERMIT_TOAD_3_DESCRIPTION"] = "Um pouco de magia, um pouco de força bruta, o que for necessário para se livrar de intrusos indesejados.",
["TOWER_HERMIT_TOAD_3_NAME"] = "Eremita do Pântano III",
["TOWER_HERMIT_TOAD_4_DESCRIPTION"] = "Um pouco de magia, um pouco de força bruta, o que for necessário para se livrar de intrusos indesejados.",
["TOWER_HERMIT_TOAD_4_INSTAKILL_1_DESCRIPTION"] = "A cada %$towers.hermit_toad.power_instakill.cooldown[1]%$ segundos ele usa sua língua para devorar um inimigo.",
["TOWER_HERMIT_TOAD_4_INSTAKILL_1_NAME"] = "Língua Pegajosa",
["TOWER_HERMIT_TOAD_4_JUMP_1_DESCRIPTION"] = "A cada %$towers.hermit_toad.power_jump.cooldown[1]%$ segundos, o eremita pula alto no céu, esmagando os inimigos, causando %$towers.hermit_toad.power_jump.damage_min[1]%$ de dano e atordoando-os por %$towers.hermit_toad.power_jump.stun_duration[1]%$ segundos ao aterrissar.",
["TOWER_HERMIT_TOAD_4_JUMP_1_NAME"] = "Esmagador Terrestre",
["TOWER_HERMIT_TOAD_4_NAME"] = "Eremita do Pântano IV",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_1_DESCRIPTION"] = "A cada %$towers.hermit_toad.power_instakill.cooldown[1]%$ segundos, ele usa sua língua para devorar um inimigo.",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_1_NAME"] = "Língua Pegajosa I",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_NOTE"] = "Negócio pegajoso.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_1_DESCRIPTION"] = "A cada %$towers.hermit_toad.power_jump.cooldown[1]%$ segundos, o eremita salta alto no céu, esmagando os inimigos, causando %$towers.hermit_toad.power_jump.damage_min[1]%$ de dano e atordoando-os por %$towers.hermit_toad.power_jump.stun_duration[1]%$ segundos ao aterrissar.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_1_NAME"] = "Esmagador Terrestre I",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_2_DESCRIPTION"] = "A cada %$towers.hermit_toad.power_jump.cooldown[2]%$ segundos, o eremita salta alto no céu, esmagando os inimigos, causando %$towers.hermit_toad.power_jump.damage_min[2]%$ de dano e atordoando-os por %$towers.hermit_toad.power_jump.stun_duration[2]%$ segundos ao aterrissar.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_2_NAME"] = "Esmagador Terrestre II",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_3_DESCRIPTION"] = "A cada %$towers.hermit_toad.power_jump.cooldown[3]%$ segundos, o eremita salta alto no céu, esmagando os inimigos, causando %$towers.hermit_toad.power_jump.damage_min[3]%$ de dano e atordoando-os por %$towers.hermit_toad.power_jump.stun_duration[3]%$ segundos ao aterrissar.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_3_NAME"] = "Esmagador Terrestre III",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_NOTE"] = "Pronto para a equipe de vôlei de praia",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_DESCRIPTION"] = "O eremita muda para uma postura física.",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_NAME"] = "Pântano comum",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_NOTE"] = "Ficando sujo!",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_DESCRIPTION"] = "O eremita muda para uma postura mágica.",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_NAME"] = "Lagoa Mágica",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_NOTE"] = "Poder Ilimitado!!",
["TOWER_HERMIT_TOAD_DESC"] = "Um mago sapo gigante com uma habilidade para cuspir bolas de muco. Tudo o que ele quer é um pouco de paz e tranquilidade para seus banhos de lagoa. NÃO o perturbe.",
["TOWER_HERMIT_TOAD_NAME"] = "Eremita do Pântano",
["TOWER_NECROMANCER_1_DESCRIPTION"] = "Com seu domínio sobre a morte, os Necromantes colhem o caos que semeiam no campo de batalha.",
["TOWER_NECROMANCER_1_NAME"] = "Necromante I",
["TOWER_NECROMANCER_2_DESCRIPTION"] = "Com seu domínio sobre a morte, os Necromantes colhem o caos que semeiam no campo de batalha.",
["TOWER_NECROMANCER_2_NAME"] = "Necromante II",
["TOWER_NECROMANCER_3_DESCRIPTION"] = "Com seu domínio sobre a morte, os Necromantes colhem o caos que semeiam no campo de batalha.",
["TOWER_NECROMANCER_3_NAME"] = "Necromante III",
["TOWER_NECROMANCER_4_DESCRIPTION"] = "Com seu domínio sobre a morte, os Necromantes colhem o caos que semeiam no campo de batalha.",
["TOWER_NECROMANCER_4_NAME"] = "Necromante IV",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_1_DESCRIPTION"] = "Coloca um totem que dura %$towers.necromancer.skill_debuff.aura_duration[1]%$ segundos, amaldiçoando inimigos e dando aos esqueletos %$towers.necromancer.skill_debuff.s_damage_factor[1]%$% de dano de ataque extra.",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_1_NAME"] = "EFÍGIE ÓSSEA",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_2_DESCRIPTION"] = "O totem dá aos esqueletos %$towers.necromancer.skill_debuff.s_damage_factor[2]%$% de dano de ataque extra. O tempo de recarga é reduzido para %$towers.necromancer.skill_debuff.cooldown[2]%$ segundos.",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_2_NAME"] = "EFÍGIE ÓSSEA",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_3_DESCRIPTION"] = "O totem dá aos esqueletos %$towers.necromancer.skill_debuff.s_damage_factor[3]%$% de dano de ataque extra. O tempo de recarga é reduzido para %$towers.necromancer.skill_debuff.cooldown[3]%$ segundos.",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_3_NAME"] = "EFÍGIE ÓSSEA",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_NOTE"] = "Exército de ossos fortes!",
["TOWER_NECROMANCER_4_SKILL_RIDER_1_DESCRIPTION"] = "Invoca um Cavaleiro da Morte no caminho que causa %$towers.necromancer.skill_rider.s_damage[1]%$ de dano verdadeiro a qualquer inimigo que ele atravessar.",
["TOWER_NECROMANCER_4_SKILL_RIDER_1_NAME"] = "CAVALEIRO DA MORTE",
["TOWER_NECROMANCER_4_SKILL_RIDER_2_DESCRIPTION"] = "O Cavaleiro da Morte causa %$towers.necromancer.skill_rider.s_damage[2]%$ de dano verdadeiro.",
["TOWER_NECROMANCER_4_SKILL_RIDER_2_NAME"] = "CAVALEIRO DA MORTE",
["TOWER_NECROMANCER_4_SKILL_RIDER_3_DESCRIPTION"] = "O Cavaleiro da Morte causa %$towers.necromancer.skill_rider.s_damage[3]%$ de dano verdadeiro.",
["TOWER_NECROMANCER_4_SKILL_RIDER_3_NAME"] = "CAVALEIRO DA MORTE",
["TOWER_NECROMANCER_4_SKILL_RIDER_NOTE"] = "Uma passagem só de ida...",
["TOWER_NECROMANCER_DESC"] = "Empunhando a forma mais sombria de magia, os Necromantes usam seus inimigos como parte das fileiras de um exército sem fim.",
["TOWER_NECROMANCER_NAME"] = "Necromante",
["TOWER_PALADIN_COVENANT_1_DESCRIPTION"] = "Ferozes e dedicados, os paladinos trabalham arduamente para proteger o reino do perigo.",
["TOWER_PALADIN_COVENANT_1_NAME"] = "Pacto dos Paladinos I",
["TOWER_PALADIN_COVENANT_2_DESCRIPTION"] = "Ferozes e dedicados, os paladinos trabalham arduamente para proteger o reino do perigo.",
["TOWER_PALADIN_COVENANT_2_NAME"] = "Pacto dos Paladinos II",
["TOWER_PALADIN_COVENANT_3_DESCRIPTION"] = "Ferozes e dedicados, os paladinos trabalham arduamente para proteger o reino do perigo.",
["TOWER_PALADIN_COVENANT_3_NAME"] = "Pacto dos Paladinos III",
["TOWER_PALADIN_COVENANT_4_DESCRIPTION"] = "Ferozes e dedicados, os paladinos trabalham arduamente para proteger o reino do perigo.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_1_DESCRIPTION"] = "Quando os soldados atingem %$towers.paladin_covenant.healing_prayer.health_trigger_factor[1]%$% de sua saúde, tornam-se invencíveis e restauram %$towers.paladin_covenant.healing_prayer.s_healing[1]%$ de saúde por segundo por %$towers.paladin_covenant.healing_prayer.duration%$ segundos.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_1_NAME"] = "ORAÇÃO DE CURA",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_2_DESCRIPTION"] = "A cura aumenta para %$towers.paladin_covenant.healing_prayer.s_healing[2]%$ de saúde por segundo por %$towers.paladin_covenant.healing_prayer.duration%$ segundos.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_2_NAME"] = "ORAÇÃO DE CURA",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_3_DESCRIPTION"] = "A cura aumenta para %$towers.paladin_covenant.healing_prayer.s_healing[3]%$ de saúde por segundo por %$towers.paladin_covenant.healing_prayer.duration%$ segundos.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_3_NAME"] = "ORAÇÃO DE CURA",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_NAME"] = "ORAÇÃO DE CURA",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_NOTE"] = "Dever até à morte.",
["TOWER_PALADIN_COVENANT_4_LEAD_1_DESCRIPTION"] = "Substitui um dos paladinos por um Veterano da Guarda, o que concede aos aliados próximos um aumento de %$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$% no dano de ataque.",
["TOWER_PALADIN_COVENANT_4_LEAD_1_NAME"] = "LIDERAR PELO EXEMPLO",
["TOWER_PALADIN_COVENANT_4_LEAD_2_DESCRIPTION"] = "Substitui um dos paladinos por um Veterano da Guarda, o que concede aos aliados próximos um aumento de %$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$% no dano de ataque.",
["TOWER_PALADIN_COVENANT_4_LEAD_2_NAME"] = "LIDERAR PELO EXEMPLO",
["TOWER_PALADIN_COVENANT_4_LEAD_3_DESCRIPTION"] = "Substitui um dos paladinos por um Veterano da Guarda, o que concede aos aliados próximos um aumento de %$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$% no dano de ataque.",
["TOWER_PALADIN_COVENANT_4_LEAD_3_NAME"] = "LIDERAR PELO EXEMPLO",
["TOWER_PALADIN_COVENANT_4_LEAD_NAME"] = "LIDERAR PELO EXEMPLO",
["TOWER_PALADIN_COVENANT_4_LEAD_NOTE"] = "Pelo rei, pela terra, pelas montanhas.",
["TOWER_PALADIN_COVENANT_4_NAME"] = "Pacto dos Paladinos IV",
["TOWER_PALADIN_COVENANT_DESC"] = "Os paladinos são a espinha dorsal das forças de elite de Linirea, usando seu poder divino para se protegerem e se curarem na batalha.",
["TOWER_PALADIN_COVENANT_NAME"] = "Pacto dos Paladinos",
["TOWER_PANDAS_1_DESCRIPTION"] = "Com domínio elemental e determinação inabalável, os Mestres lutarão incansavelmente para preservar o equilíbrio natural do mundo.",
["TOWER_PANDAS_1_NAME"] = "Mestres do Bambu I",
["TOWER_PANDAS_2_DESCRIPTION"] = "Com domínio elemental e determinação inabalável, os Mestres lutarão incansavelmente para preservar o equilíbrio natural do mundo.",
["TOWER_PANDAS_2_NAME"] = "Mestres do Bambu II",
["TOWER_PANDAS_3_DESCRIPTION"] = "Com domínio elemental e determinação inabalável, os Mestres lutarão incansavelmente para preservar o equilíbrio natural do mundo.",
["TOWER_PANDAS_3_NAME"] = "Mestres do Bambu III",
["TOWER_PANDAS_4_DESCRIPTION"] = "Com domínio elemental e determinação inabalável, os Mestres lutarão incansavelmente para preservar o equilíbrio natural do mundo.",
["TOWER_PANDAS_4_FIERY"] = "Kawoosh",
["TOWER_PANDAS_4_FIERY_1_DESCRIPTION"] = "Lança um projétil de fogo que causa %$towers.pandas.soldier.teleport.damage_min[1]%$-%$towers.pandas.soldier.teleport.damage_max[1]%$ de dano verdadeiro e teleporta os inimigos atingidos para trás no caminho.",
["TOWER_PANDAS_4_FIERY_1_NAME"] = "Chama Abissal",
["TOWER_PANDAS_4_FIERY_2_DESCRIPTION"] = "Lança um projétil de fogo que causa %$towers.pandas.soldier.teleport.damage_min[2]%$-%$towers.pandas.soldier.teleport.damage_max[2]%$ de dano verdadeiro e teletransporta os inimigos atingidos para trás no caminho.",
["TOWER_PANDAS_4_FIERY_2_NAME"] = "Chama Abissal",
["TOWER_PANDAS_4_HAT"] = "Um chapéu para acertar todos",
["TOWER_PANDAS_4_HAT_1_DESCRIPTION"] = "Arremessa seu chapéu afiado em um inimigo, ricocheteando entre os inimigos e causando %$towers.pandas.soldier.hat.damage_levels[1].min%$-%$towers.pandas.soldier.hat.damage_levels[1].max%$ de dano a cada acerto.",
["TOWER_PANDAS_4_HAT_1_NAME"] = "Truque do Chapéu",
["TOWER_PANDAS_4_HAT_2_DESCRIPTION"] = "Arremessa seu chapéu afiado em um inimigo, ricocheteando entre os inimigos e causando %$towers.pandas.soldier.hat.damage_levels[2].min%$-%$towers.pandas.soldier.hat.damage_levels[2].max%$ de dano a cada acerto.",
["TOWER_PANDAS_4_HAT_2_NAME"] = "Truque do Chapéu",
["TOWER_PANDAS_4_NAME"] = "Mestres do Bambu IV",
["TOWER_PANDAS_4_THUNDER"] = "Panda Kombate",
["TOWER_PANDAS_4_THUNDER_1_DESCRIPTION"] = "Lança raios em uma pequena área, cada um causando %$towers.pandas.soldier.thunder.damage_min[1]%$-%$towers.pandas.soldier.thunder.damage_max[1]%$ de dano em área e atordoando brevemente os inimigos atingidos.",
["TOWER_PANDAS_4_THUNDER_1_NAME"] = "Sobrecarga Elétrica",
["TOWER_PANDAS_4_THUNDER_2_DESCRIPTION"] = "Lança raios em uma pequena área, cada um causando %$towers.pandas.soldier.thunder.damage_min[2]%$-%$towers.pandas.soldier.thunder.damage_max[2]%$ de dano em área e atordoando brevemente os inimigos atingidos.",
["TOWER_PANDAS_4_THUNDER_2_NAME"] = "Sobrecarga Elétrica",
["TOWER_PANDAS_DESC"] = "Misturando habilidade marcial com afinidade elemental, este trio de pandas destrói inimigos e continua sendo uma ameaça mesmo quando parece derrotado.",
["TOWER_PANDAS_NAME"] = "Mestres do Bambu",
["TOWER_PANDAS_RETREAT_DESCRIPTION"] = "Recolhe os pandas em pé para o refúgio por 8 segundos.",
["TOWER_PANDAS_RETREAT_NAME"] = "Retirada Tática",
["TOWER_PANDAS_RETREAT_NOTE"] = "A discrição é a melhor parte da valentia.",
["TOWER_RAY_1_DESCRIPTION"] = "Formas perigosas e contaminadas de magia nunca impediram os magos malignos de perseguirem propósitos nefastos.",
["TOWER_RAY_1_NAME"] = "Canalizador Arcano I",
["TOWER_RAY_2_DESCRIPTION"] = "Formas perigosas e contaminadas de magia nunca impediram os magos malignos de perseguirem propósitos nefastos.",
["TOWER_RAY_2_NAME"] = "Canalizador Arcano II",
["TOWER_RAY_3_DESCRIPTION"] = "Formas perigosas e contaminadas de magia nunca impediram os magos malignos de perseguirem propósitos nefastos.",
["TOWER_RAY_3_NAME"] = "Canalizador Arcano III",
["TOWER_RAY_4_CHAIN_1_DESCRIPTION"] = "O raio mágico agora se estende a %$towers.ray.skill_chain.s_max_enemies%$ inimigos adicionais, retardando-os e causando %$towers.ray.skill_chain.damage_mult[1]%$% do dano mágico total a cada alvo.",
["TOWER_RAY_4_CHAIN_1_NAME"] = "EXCESSO DE PODER",
["TOWER_RAY_4_CHAIN_2_DESCRIPTION"] = "O raio mágico agora se estende a %$towers.ray.skill_chain.s_max_enemies%$ inimigos adicionais, retardando-os e causando %$towers.ray.skill_chain.damage_mult[2]%$% do dano mágico total a cada alvo.",
["TOWER_RAY_4_CHAIN_2_NAME"] = "EXCESSO DE PODER",
["TOWER_RAY_4_CHAIN_3_DESCRIPTION"] = "O raio mágico agora se estende por %$towers.ray.skill_chain.s_max_enemies%$ inimigos adicionais, diminuindo a velocidade deles e causando %$towers.ray.skill_chain.damage_mult[3]%$% do dano mágico total a cada alvo.",
["TOWER_RAY_4_CHAIN_3_NAME"] = "Sobrecarga de poder",
["TOWER_RAY_4_CHAIN_NOTE"] = "Há dor suficiente para todos.",
["TOWER_RAY_4_DESCRIPTION"] = "Formas perigosas e contaminadas de magia nunca impediram os magos malignos de perseguirem propósitos nefastos.",
["TOWER_RAY_4_NAME"] = "Canalizador Arcano IV",
["TOWER_RAY_4_SHEEP_1_DESCRIPTION"] = "Transforma um inimigo próximo em uma ovelha indefesa. A ovelha tem %$towers.ray.skill_sheep.sheep.hp_mult%$% da saúde do alvo.",
["TOWER_RAY_4_SHEEP_1_NAME"] = "ENCANTAMENTO DE MUTAÇÃO",
["TOWER_RAY_4_SHEEP_2_DESCRIPTION"] = "Transforma um inimigo próximo em uma ovelha indefesa. A ovelha tem %$towers.ray.skill_sheep.sheep.hp_mult%$% da saúde do alvo.",
["TOWER_RAY_4_SHEEP_2_NAME"] = "ENCANTAMENTO DE MUTAÇÃO",
["TOWER_RAY_4_SHEEP_3_DESCRIPTION"] = "Transforma um inimigo próximo em uma ovelha indefesa. A ovelha tem %$towers.ray.skill_sheep.sheep.hp_mult%$% da saúde do alvo.",
["TOWER_RAY_4_SHEEP_3_NAME"] = "ENCANTAMENTO DE MUTAÇÃO",
["TOWER_RAY_4_SHEEP_NOTE"] = "Sinceramente, você está melhor agora.",
["TOWER_RAY_DESC"] = "Os aprendizes de Vez'nan usam seu poder corrompido para lançar um raio escuro de aflição sobre seus inimigos.",
["TOWER_RAY_NAME"] = "Canalizador Arcano",
["TOWER_ROCKET_GUNNERS_1_DESCRIPTION"] = "Equipados com a mais recente tecnologia do Exército das Trevas, os atiradores patrulham os céus.",
["TOWER_ROCKET_GUNNERS_1_NAME"] = "Atiradores de Foguetes I",
["TOWER_ROCKET_GUNNERS_2_DESCRIPTION"] = "Equipados com a mais recente tecnologia do Exército das Trevas, os atiradores patrulham os céus.",
["TOWER_ROCKET_GUNNERS_2_NAME"] = "Atiradores de Foguetes II",
["TOWER_ROCKET_GUNNERS_3_DESCRIPTION"] = "Equipados com a mais recente tecnologia do Exército das Trevas, os atiradores patrulham os céus.",
["TOWER_ROCKET_GUNNERS_3_NAME"] = "Atiradores de Foguetes III",
["TOWER_ROCKET_GUNNERS_4_DESCRIPTION"] = "Equipados com a mais recente tecnologia do Exército das Trevas, os atiradores patrulham os céus.",
["TOWER_ROCKET_GUNNERS_4_NAME"] = "Atiradores de Foguetes IV",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_1_DESCRIPTION"] = "Cada ataque destrói %$towers.rocket_gunners.soldier.phosphoric.armor_reduction[1]%$% da armadura do inimigo e causa dano em área.",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_1_NAME"] = "REVESTIMENTO FOSFÓRICO",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_2_DESCRIPTION"] = "Cada ataque destrói %$towers.rocket_gunners.soldier.phosphoric.armor_reduction[2]%$% da armadura do inimigo e causa %$towers.rocket_gunners.soldier.phosphoric.damage_area_min[2]%$-%$towers.rocket_gunners.soldier.phosphoric.damage_area_max[2]%$ de dano em área.",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_2_NAME"] = "REVESTIMENTO FOSFÓRICO",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_3_DESCRIPTION"] = "Cada ataque destrói %$towers.rocket_gunners.soldier.phosphoric.armor_reduction[3]%$% da armadura do inimigo e causa %$towers.rocket_gunners.soldier.phosphoric.damage_area_min[3]%$-%$towers.rocket_gunners.soldier.phosphoric.damage_area_max[3]%$ de dano em área.",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_3_NAME"] = "REVESTIMENTO FOSFÓRICO",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_NOTE"] = "Balas temperadas com maldade.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_1_DESCRIPTION"] = "Lança um míssil que mata instantaneamente um alvo com até %$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[1]%$ de saúde.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_1_NAME"] = "MÍSSEIS STINGER",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_2_DESCRIPTION"] = "Reduz o tempo de recarga para %$towers.rocket_gunners.sting_missiles.cooldown[2]%$ segundos. Agora, pode mirar inimigos com até %$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[2]%$ de saúde.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_2_NAME"] = "MÍSSEIS STINGER",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_3_DESCRIPTION"] = "Reduz o tempo de recarga para %$towers.rocket_gunners.sting_missiles.cooldown[3]%$ segundos. Agora, pode mirar inimigos com até %$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[3]%$ de saúde.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_3_NAME"] = "MÍSSEIS STINGER",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_NOTE"] = "Desvia disso!",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_DESCRIPTION"] = "Os Atiradores pousam no chão e não podem bloquear os inimigos.",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_NAME"] = "Decolagem",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_NOTE"] = "Ao infinito e além!",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_DESCRIPTION"] = "Os Atiradores pousam no chão e podem bloquear os inimigos.",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_NAME"] = "Aterrissagem",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_NOTE"] = "A águia pousou!",
["TOWER_ROCKET_GUNNERS_DESC"] = "Estas tropas especiais podem se manter tanto no solo quanto no ar, desencadeando seu armamento avançado sobre inimigos desprevenidos.",
["TOWER_ROCKET_GUNNERS_NAME"] = "Atiradores de Foguetes",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_1"] = "Esta torre está incluída na Campanha de Ameaça Colossal.",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_2"] = "Esta torre está incluída na campanha Jornada de Wukong.",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_1"] = "Campanha de Ameaça Colossal",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_2"] = "Campanha A Jornada de Wukong",
["TOWER_ROOM_EQUIPPED_TOWERS_TITLE"] = "Torres equipadas",
["TOWER_ROOM_GET_DLC"] = "PEGUE",
["TOWER_ROOM_LABEL_ROSTER_THUMB_NEW"] = "Novo!",
["TOWER_ROOM_SKILLS_TITLE"] = "Habilidades",
["TOWER_ROYAL_ARCHERS_1_DESCRIPTION"] = "Leais até o fim, os Arqueiros Reais protegem as forças de Linirea à distância. ",
["TOWER_ROYAL_ARCHERS_1_NAME"] = "Arqueiros Reais I ",
["TOWER_ROYAL_ARCHERS_2_DESCRIPTION"] = "Leais até o fim, os Arqueiros Reais protegem as forças de Linirea à distância. ",
["TOWER_ROYAL_ARCHERS_2_NAME"] = "Arqueiros Reais II ",
["TOWER_ROYAL_ARCHERS_3_DESCRIPTION"] = "Leais até o fim, os Arqueiros Reais protegem as forças de Linirea à distância. ",
["TOWER_ROYAL_ARCHERS_3_NAME"] = "Arqueiros Reais III ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_1_DESCRIPTION"] = "Dispara três flechas potencializadas que causam dano físico de %$towers.royal_archers.armor_piercer.damage_min[1]%$-%$towers.royal_archers.armor_piercer.damage_max[1]%$ e ignoram %$towers.royal_archers.armor_piercer.armor_penetration[1]%$% da armadura do inimigo. ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_1_NAME"] = "PERFURADOR DE ARMADURAS ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_2_DESCRIPTION"] = "Lança três flechas potencializadas que causam de %$towers.royal_archers.armor_piercer.damage_min[2]%$ a %$towers.royal_archers.armor_piercer.damage_max[2]%$ de dano físico, ignorando %$towers.royal_archers.armor_piercer.armor_penetration[2]%$% da armadura do inimigo. ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_2_NAME"] = "PERFURADOR DE ARMADURAS ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_3_DESCRIPTION"] = "Lança três flechas potencializadas que causam de %$towers.royal_archers.armor_piercer.damage_min[3]%$ a %$towers.royal_archers.armor_piercer.damage_max[3]%$ de dano físico, ignorando %$towers.royal_archers.armor_piercer.armor_penetration[3]%$% da armadura do inimigo. ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_3_NAME"] = "PERFURADOR DE ARMADURAS ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_NAME"] = "PERFURADOR DE ARMADURA ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_NOTE"] = "Estamos de olho em você. ",
["TOWER_ROYAL_ARCHERS_4_DESCRIPTION"] = "Leais até o fim, os Arqueiros Reais protegem as forças de Linirea à distância. ",
["TOWER_ROYAL_ARCHERS_4_NAME"] = "Arqueiros Reais IV ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_1_DESCRIPTION"] = "Invoca uma águia que ataca os inimigos no caminho, causando %$towers.royal_archers.rapacious_hunter.damage_min[1]%$-%$towers.royal_archers.rapacious_hunter.damage_max[1]%$ de dano físico.",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_1_NAME"] = "CAÇADOR RAPINANTE",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_2_DESCRIPTION"] = "A águia causa de %$towers.royal_archers.rapacious_hunter.damage_min[2]%$ a %$towers.royal_archers.rapacious_hunter.damage_max[2]%$ de dano físico. ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_2_NAME"] = "CAÇADOR RAPINANTE",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_3_DESCRIPTION"] = "A águia causa de %$towers.royal_archers.rapacious_hunter.damage_min[3]%$ a %$towers.royal_archers.rapacious_hunter.damage_max[3]%$ de dano físico. ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_3_NAME"] = "CAÇADOR RAPINANTE",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_NAME"] = "CAÇADOR RAPINANTE",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_NOTE"] = "O olho da águia está escondendo algo trágico. ",
["TOWER_ROYAL_ARCHERS_DESC"] = "Os arqueiros mais poderosos do reino, também são renomados por serem assistidos por águias de guerra. ",
["TOWER_ROYAL_ARCHERS_NAME"] = "Arqueiros Reais ",
["TOWER_SAND_1_DESCRIPTION"] = "Sua habilidade com a lâmina lançada é suficiente para assustar qualquer mercenário cheio de si mesmo.",
["TOWER_SAND_1_NAME"] = "Sentinelas das Dunas I",
["TOWER_SAND_2_DESCRIPTION"] = "Sua habilidade com a lâmina lançada é suficiente para assustar qualquer mercenário cheio de si mesmo.",
["TOWER_SAND_2_NAME"] = "Sentinelas das Dunas II",
["TOWER_SAND_3_DESCRIPTION"] = "A habilidade deles com a lâmina lançada é suficiente para assustar qualquer mercenário cheio de si.",
["TOWER_SAND_3_NAME"] = "Sentinelas das Dunas III",
["TOWER_SAND_4_DESCRIPTION"] = "A habilidade deles com a lâmina lançada é suficiente para assustar qualquer mercenário cheio de si.",
["TOWER_SAND_4_NAME"] = "Sentinelas das Dunas IV",
["TOWER_SAND_4_SKILL_BIG_BLADE_1_DESCRIPTION"] = "Lança lâminas giratórias pelo caminho que causam de %$towers.sand.skill_big_blade.s_damage_min[1]%$-%$towers.sand.skill_big_blade.s_damage_max[1]%$ de dano físico por segundo por %$towers.sand.skill_big_blade.duration[1]%$ segundos.",
["TOWER_SAND_4_SKILL_BIG_BLADE_1_NAME"] = "TURBILHÃO DA PERDIÇÃO",
["TOWER_SAND_4_SKILL_BIG_BLADE_2_DESCRIPTION"] = "As lâminas giratórias causam %$towers.sand.skill_big_blade.s_damage_min[2]%$-%$towers.sand.skill_big_blade.s_damage_max[2]%$ de dano físico por segundo por %$towers.sand.skill_big_blade.duration[2]%$ segundos.",
["TOWER_SAND_4_SKILL_BIG_BLADE_2_NAME"] = "TURBILHÃO DA PERDIÇÃO",
["TOWER_SAND_4_SKILL_BIG_BLADE_3_DESCRIPTION"] = "As lâminas giratórias causam de %$towers.sand.skill_big_blade.s_damage_min[3]%$ a %$towers.sand.skill_big_blade.s_damage_max[3]%$ de dano físico por segundo por %$towers.sand.skill_big_blade.duration[3]%$ segundos.",
["TOWER_SAND_4_SKILL_BIG_BLADE_3_NAME"] = "TURBILHÃO DA PERDIÇÃO",
["TOWER_SAND_4_SKILL_BIG_BLADE_NOTE"] = "Você me faz girar e girar, baby.",
["TOWER_SAND_4_SKILL_GOLD_1_DESCRIPTION"] = "Lança uma lâmina saltitante que causa %$towers.sand.skill_gold.s_damage[1]%$ de dano físico aos inimigos. Qualquer alvo morto pela lâmina rende %$towers.sand.skill_gold.gold_extra[1]%$ de ouro bônus. ",
["TOWER_SAND_4_SKILL_GOLD_1_NAME"] = "CAÇADOR DE RECOMPENSAS",
["TOWER_SAND_4_SKILL_GOLD_2_DESCRIPTION"] = "A lâmina causa %$towers.sand.skill_gold.s_damage[2]%$ de dano físico. Uma morte concede %$towers.sand.skill_gold.gold_extra[2]%$ de ouro extra. ",
["TOWER_SAND_4_SKILL_GOLD_2_NAME"] = "CAÇADOR DE RECOMPENSAS",
["TOWER_SAND_4_SKILL_GOLD_3_DESCRIPTION"] = "A lâmina causa %$towers.sand.skill_gold.s_damage[3]%$ de dano físico. Uma morte concede %$towers.sand.skill_gold.gold_extra[3]%$ de ouro extra.",
["TOWER_SAND_4_SKILL_GOLD_3_NAME"] = "CAÇADOR DE RECOMPENSAS",
["TOWER_SAND_4_SKILL_GOLD_NOTE"] = "O panfleto diz morto OU vivo.",
["TOWER_SAND_DESC"] = "Vindas de Hammerhold, as Sentinelas das Dunas podem ser as habitantes mais letais do deserto.",
["TOWER_SAND_NAME"] = "Sentinelas das Dunas",
["TOWER_SELL"] = "Vender Torre",
["TOWER_SPARKING_GEODE_1_DESCRIPTION"] = "Invocador de tempestades e portador do caos certificado. Cuidado com seu consumo de energia.",
["TOWER_SPARKING_GEODE_1_NAME"] = "Colosso Tempestuoso I",
["TOWER_SPARKING_GEODE_2_DESCRIPTION"] = "Invocador de tempestades e portador do caos certificado. Cuidado com seu consumo de energia.",
["TOWER_SPARKING_GEODE_2_NAME"] = "Colosso Tempestuoso II",
["TOWER_SPARKING_GEODE_3_DESCRIPTION"] = "Invocador de tempestades e portador do caos certificado. Cuidado com seu consumo de energia.",
["TOWER_SPARKING_GEODE_3_NAME"] = "Colosso Tempestuoso III",
["TOWER_SPARKING_GEODE_4_CRISTALIZE"] = "Trovão!",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_1_DESCRIPTION"] = "A cada %$towers.sparking_geode.crystalize.cooldown[1]%$ segundos, cristaliza %$towers.sparking_geode.crystalize.max_targets[1]%$ inimigos dentro do alcance, atordoando-os e fazendo com que recebam %$towers.sparking_geode.crystalize.s_received_damage_factor[1]%$% mais de dano.",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_1_NAME"] = "Cristalização",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_2_DESCRIPTION"] = "A cada %$towers.sparking_geode.crystalize.cooldown[2]%$ segundos, cristaliza %$towers.sparking_geode.crystalize.max_targets[2]%$ inimigos dentro do alcance, atordoando-os e fazendo com que recebam %$towers.sparking_geode.crystalize.s_received_damage_factor[2]%$% mais dano.",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_2_NAME"] = "Cristalização",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_3_DESCRIPTION"] = "A cada %$towers.sparking_geode.crystalize.cooldown[3]%$ segundos, cristaliza %$towers.sparking_geode.crystalize.max_targets[3]%$ inimigos dentro do alcance, atordoando-os e fazendo com que recebam %$towers.sparking_geode.crystalize.s_received_damage_factor[3]%$% mais dano.",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_3_NAME"] = "Cristalização",
["TOWER_SPARKING_GEODE_4_CRYSTALIZE_1_DESCRIPTION"] = "A cada %$towers.sparking_geode.crystalize.cooldown[1]%$ segundos, cristaliza %$towers.sparking_geode.crystalize.max_targets[1]%$ inimigos dentro do alcance, atordoando-os e fazendo com que sofram %$towers.sparking_geode.crystalize.s_received_damage_factor[1]%$% a mais de dano.",
["TOWER_SPARKING_GEODE_4_CRYSTALIZE_1_NAME"] = "Cristalização",
["TOWER_SPARKING_GEODE_4_DESCRIPTION"] = "Invocador de tempestades e causador de caos certificado. Tenha cuidado com seu consumo de energia.",
["TOWER_SPARKING_GEODE_4_NAME"] = "Colosso Tempestuoso IV",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST"] = "Mais difícil, melhor, mais rápido, mais forte.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_1_DESCRIPTION"] = "A cada %$towers.sparking_geode.spike_burst.cooldown[1]%$ segundos, o Colosso invoca um campo elétrico que causa dano e reduz a velocidade dos inimigos próximos por %$towers.sparking_geode.spike_burst.duration[1]%$ segundos.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_1_NAME"] = "Descarga Elétrica",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_2_DESCRIPTION"] = "A cada %$towers.sparking_geode.spike_burst.cooldown[2]%$ segundos, o Colosso invoca um campo elétrico que causa dano e reduz a velocidade dos inimigos próximos por %$towers.sparking_geode.spike_burst.duration[2]%$ segundos.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_2_NAME"] = "Descarga Elétrica",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_3_DESCRIPTION"] = "A cada %$towers.sparking_geode.spike_burst.cooldown[3]%$ segundos, o Colosso invoca um campo elétrico que causa dano e reduz a velocidade dos inimigos próximos por %$towers.sparking_geode.spike_burst.duration[3]%$ segundos.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_3_NAME"] = "Descarga Elétrica",
["TOWER_SPARKING_GEODE_DESC"] = "Originário de uma raça antiga e pacífica, esse ser poderoso segue seu instinto protetor e usa seus poderes elétricos para lutar pela Aliança, atacando com a fúria de uma tempestade.",
["TOWER_SPARKING_GEODE_NAME"] = "Colosso Tempestuoso",
["TOWER_STAGE_13_SUNRAY_NAME"] = "Torre Raio Sombrio",
["TOWER_STAGE_13_SUNRAY_REPAIR_DESCRIPTION"] = "Repare a torre para usar seus poderes destrutivos.",
["TOWER_STAGE_13_SUNRAY_REPAIR_NAME"] = "Reparar",
["TOWER_STAGE_17_WEIRDWOOD_NAME"] = "Bosquestranho",
["TOWER_STAGE_18_ELVEN_BARRACK_DESCRIPTION"] = "Elfos contratados para lutar.",
["TOWER_STAGE_18_ELVEN_BARRACK_NAME"] = "Mercenários Elfos",
["TOWER_STAGE_20_ARBOREAN_BARRACK_DESCRIPTION"] = "Chame o povo arbóreo para lutar.",
["TOWER_STAGE_20_ARBOREAN_BARRACK_NAME"] = "Cidadãos Arbóreos",
["TOWER_STAGE_20_ARBOREAN_HONEY_DESCRIPTION"] = "Convoque o grande comandante das abelhas.",
["TOWER_STAGE_20_ARBOREAN_HONEY_NAME"] = "Apicultor Arbóreo",
["TOWER_STAGE_20_ARBOREAN_OLDTREE_DESCRIPTION"] = "Peça ajuda à árvore antiga.",
["TOWER_STAGE_20_ARBOREAN_OLDTREE_NAME"] = "Árvore Velha",
["TOWER_STAGE_22_ARBOREAN_MAGES_NAME"] = "Mago Arbóreo",
["TOWER_STAGE_28_PRIESTS_BARRACK_DESCRIPTION"] = "Cultistas redimidos que levam sua feitiçaria para o campo de batalha e se transformam em abominações ao morrer.",
["TOWER_STAGE_28_PRIESTS_BARRACK_NAME"] = "Crentes do sem olhos",
["TOWER_STARGAZER_1_DESCRIPTION"] = "Os Observadores Estelares procuram aproveitar a poderosa magia de além do reino terrestre.",
["TOWER_STARGAZER_1_NAME"] = "Astrônomos Elfo I",
["TOWER_STARGAZER_2_DESCRIPTION"] = "Os Observadores Estelares procuram aproveitar a poderosa magia de além do reino terrestre.",
["TOWER_STARGAZER_2_NAME"] = "Astrônomos Elfo II",
["TOWER_STARGAZER_3_DESCRIPTION"] = "Os Observadores Estelares procuram aproveitar a poderosa magia de além do reino terrestre.",
["TOWER_STARGAZER_3_NAME"] = "Astrônomos Elfo III",
["TOWER_STARGAZER_4_DESCRIPTION"] = "Os Observadores Estelares procuram aproveitar a poderosa magia de além do reino terrestre.",
["TOWER_STARGAZER_4_EVENT_HORIZON_1_DESCRIPTION"] = "Teleporta até %$towers.elven_stargazers.teleport.max_targets[1]%$ inimigos de volta pelo caminho.",
["TOWER_STARGAZER_4_EVENT_HORIZON_1_NAME"] = "HORIZONTE DE EVENTOS",
["TOWER_STARGAZER_4_EVENT_HORIZON_2_DESCRIPTION"] = "Teleporta até %$towers.elven_stargazers.teleport.max_targets[2]%$ inimigos mais para trás no caminho. ",
["TOWER_STARGAZER_4_EVENT_HORIZON_2_NAME"] = "HORIZONTE DE EVENTOS",
["TOWER_STARGAZER_4_EVENT_HORIZON_3_DESCRIPTION"] = "Teleporta até %$towers.elven_stargazers.teleport.max_targets[3]%$ inimigos ainda mais para trás pelo caminho.",
["TOWER_STARGAZER_4_EVENT_HORIZON_3_NAME"] = "HORIZONTE DE EVENTOS",
["TOWER_STARGAZER_4_EVENT_HORIZON_NAME"] = "HORIZONTE DE EVENTOS",
["TOWER_STARGAZER_4_EVENT_HORIZON_NOTE"] = "Desaparecer, aparecer.",
["TOWER_STARGAZER_4_NAME"] = "Astrônomos Elfo IV",
["TOWER_STARGAZER_4_RISING_STAR_1_DESCRIPTION"] = "Os inimigos abatidos pela torre explodem em uma erupção de %$towers.elven_stargazers.stars_death.stars[1]%$ estrelas que causam %$towers.elven_stargazers.stars_death.damage_min[1]%$-%$towers.elven_stargazers.stars_death.damage_max[1]%$ de dano mágico aos inimigos.",
["TOWER_STARGAZER_4_RISING_STAR_1_NAME"] = "ESTRELA EM ASCENSÃO",
["TOWER_STARGAZER_4_RISING_STAR_2_DESCRIPTION"] = "A quantidade de estrelas aumenta para %$towers.elven_stargazers.stars_death.stars[2]%$. As estrelas causam dano mágico de %$towers.elven_stargazers.stars_death.damage_min[2]%$-%$towers.elven_stargazers.stars_death.damage_max[2]%$ .",
["TOWER_STARGAZER_4_RISING_STAR_2_NAME"] = "ESTRELA EM ASCENSÃO",
["TOWER_STARGAZER_4_RISING_STAR_3_DESCRIPTION"] = "A quantidade de estrelas aumenta para %$towers.elven_stargazers.stars_death.stars[3]%$. As estrelas causam dano mágico de %$towers.elven_stargazers.stars_death.damage_min[3]%$-%$towers.elven_stargazers.stars_death.damage_max[3]%$ .",
["TOWER_STARGAZER_4_RISING_STAR_3_NAME"] = "ESTRELA EM ASCENSÃO",
["TOWER_STARGAZER_4_RISING_STAR_NAME"] = "ESTRELA EM ASCENSÃO",
["TOWER_STARGAZER_4_RISING_STAR_NOTE"] = "É uma revolução estelar!",
["TOWER_TRICANNON_1_DESCRIPTION"] = "Uma canção de amor devastadora para a guerra e um espetáculo temível para inimigos e aliados igualmente.",
["TOWER_TRICANNON_1_NAME"] = "Tricannon I",
["TOWER_TRICANNON_2_DESCRIPTION"] = "Uma canção de amor devastadora para a guerra e um espetáculo temível para inimigos e aliados igualmente.",
["TOWER_TRICANNON_2_NAME"] = "Tricannon II",
["TOWER_TRICANNON_3_DESCRIPTION"] = "Uma canção de amor devastadora para a guerra e um espetáculo temível para inimigos e aliados igualmente.",
["TOWER_TRICANNON_3_NAME"] = "Tricannon III",
["TOWER_TRICANNON_4_BOMBARDMENT_1_DESCRIPTION"] = "Lança bombas rapidamente numa área ampla, cada uma causando %$towers.tricannon.bombardment.damage_min[1]%$-%$towers.tricannon.bombardment.damage_max[1]%$ de dano físico.",
["TOWER_TRICANNON_4_BOMBARDMENT_1_NAME"] = "BOMBARDEIO",
["TOWER_TRICANNON_4_BOMBARDMENT_2_DESCRIPTION"] = "Lança mais bombas em uma área mais ampla, cada uma causando %$towers.tricannon.bombardment.damage_min[2]%$-%$towers.tricannon.bombardment.damage_max[2]%$ de dano físico",
["TOWER_TRICANNON_4_BOMBARDMENT_2_NAME"] = "BOMBARDEIO",
["TOWER_TRICANNON_4_BOMBARDMENT_3_DESCRIPTION"] = "Lança ainda mais bombas numa área mais ampla, cada uma causando %$towers.tricannon.bombardment.damage_min[3]%$-%$towers.tricannon.bombardment.damage_max[3]%$ de dano físico",
["TOWER_TRICANNON_4_BOMBARDMENT_3_NAME"] = "BOMBARDEIO",
["TOWER_TRICANNON_4_BOMBARDMENT_NAME"] = "BOMBARDEIO",
["TOWER_TRICANNON_4_BOMBARDMENT_NOTE"] = "Vamos falar sobre escalabilidade.",
["TOWER_TRICANNON_4_DESCRIPTION"] = "Uma canção de amor devastadora para a guerra e um espetáculo temível para inimigos e aliados igualmente.",
["TOWER_TRICANNON_4_NAME"] = "Tricannon IV",
["TOWER_TRICANNON_4_OVERHEAT_1_DESCRIPTION"] = "Os canhões da Tricannon ficam vermelhos de calor por %$towers.tricannon.overheat.duration[1]%$ segundos, fazendo com que as bombas queimem o chão e causem %$towers.tricannon.overheat.decal.effect.s_damage[1]%$ de dano verdadeiro por segundo aos inimigos.",
["TOWER_TRICANNON_4_OVERHEAT_1_NAME"] = "SOBREAQUECIMENTO",
["TOWER_TRICANNON_4_OVERHEAT_2_DESCRIPTION"] = "Cada área escaldante causa %$towers.tricannon.overheat.decal.effect.s_damage[2]%$ de dano verdadeiro por segundo. A duração é aumentada para %$towers.tricannon.overheat.duration[2]%$ segundos.",
["TOWER_TRICANNON_4_OVERHEAT_2_NAME"] = "SOBREAQUECIMENTO",
["TOWER_TRICANNON_4_OVERHEAT_3_DESCRIPTION"] = "Cada área escaldante causa %$towers.tricannon.overheat.decal.effect.s_damage[3]%$ de dano verdadeiro por segundo. A duração é aumentada para %$towers.tricannon.overheat.duration[3]%$ segundos.",
["TOWER_TRICANNON_4_OVERHEAT_3_NAME"] = "SUPERAQUECIMENTO",
["TOWER_TRICANNON_4_OVERHEAT_NAME"] = "SUPERAQUECIMENTO",
["TOWER_TRICANNON_4_OVERHEAT_NOTE"] = "Estamos em brasa.",
["TOWER_TRICANNON_DESC"] = "O Exército das Trevas traz uma nova definição para a guerra moderna, chovendo fogo e destruição graças aos seus múltiplos canhões.",
["TOWER_TRICANNON_NAME"] = "Tricannon",
["TUTORIAL_hero_room_hero_points_desc"] = "Ganhe Pontos de Herói subindo de nível cada herói no combate.",
["TUTORIAL_hero_room_hero_points_title"] = "Pontos de Herói",
["TUTORIAL_hero_room_power_desc"] = "Use Pontos de Herói para comprar e melhorar poderes para o seu herói.",
["TUTORIAL_hero_room_power_title"] = "Poderes de Herói",
["TUTORIAL_hero_room_tutorial_navigate_desc"] = "Navegue por diferentes heróis.",
["TUTORIAL_hero_room_tutorial_select_desc"] = "Equipe os heróis que você quer usar no campo de batalha.",
["TUTORIAL_item_room_buy_desc"] = "Use suas Gemas para comprar itens que o ajudem no campo de batalha.",
["TUTORIAL_item_room_buy_title"] = "Compra de Itens",
["TUTORIAL_item_room_tutorial_equip_desc"] = "Use cada slot para equipar seus itens. Arraste para trocar a ordem deles!",
["TUTORIAL_item_room_tutorial_navigate_desc"] = "Navegue pelos vários itens disponíveis.",
["TUTORIAL_tower_room_power_desc"] = "Estas habilidades estão disponíveis assim que a torre atinge o nível IV.",
["TUTORIAL_tower_room_power_title"] = "Habilidades de Nível IV",
["TUTORIAL_tower_room_tutorial_equip_desc"] = "Equipe novas torres para experimentar diferentes combinações.",
["TUTORIAL_tower_room_tutorial_navigate_desc"] = "Navegue pelas diferentes torres.",
["TUTORIAL_tower_room_tutorial_slots_desc"] = "Use cada slot para equipar suas torres. Arraste para trocar a ordem deles!",
["TUTORIAL_upgrade_room_tooltip_buy_desc"] = "Use Pontos para comprar atualizações para seus poderes, torres e heróis.",
["TUTORIAL_upgrade_room_tooltip_souls_desc"] = "Ganhe Pontos de Melhora completando etapas da campanha.",
["TUTORIAL_upgrade_room_tooltip_souls_title"] = "Pontos de Melhora",
["Tap to continue"] = "Toque e continue",
["Touch on the path to move the hero."] = "Toque no caminho para mover o herói.",
["Tower construction"] = "Construção de torres",
["Typography"] = "Tipografia",
["UPDATE_POPUP"] = "ATUALIZAR",
["UPDATING_CLOUDSAVE_MESSAGE"] = "Atualizando jogos salvos na nuvem ...",
["UPGRADES"] = "MELHORIAS",
["UPGRADES AND HEROES RESTRICTIONS!"] = "RESTRIÇÕES DE HERÓIS E MELHORIAS!",
["Use the earned hero points to train your hero!"] = "Use os pontos de herói para treinar seu herói!",
["Use the earned stars to improve your towers and powers!"] = "Use as estrelas conquistadas para melhorar torres e poderes!",
["VICTORY"] = "VITÓRIA",
["Veteran"] = "Veterano",
["Victory!"] = "Vitória!",
["Voice Talent"] = "Ator de Voz",
["WAVE_TOOLTIP_TAP_AGAIN"] = "TOQUE DE NOVO PARA ADIANTAR",
["WAVE_TOOLTIP_TITLE"] = "ONDA CHEGANDO",
["We would like to thank"] = "Agradecimentos Especiais",
["Yes"] = "Sim",
["You must log in to Google Play game services to track achievements."] = "Você deve fazer login nos serviços de jogos do Google Play para rastrear conquistas.",
["You must watch the whole video."] = "Você precisa assistir ao vídeo completo",
["You will no longer be tracking achievements."] = "Você não vai mais acompanhar as conquistas.",
["_manually_included_characters"] = "$ ¥ ￥ ƒ ₩ € ™ × $ zł ¢ £ ¤ ¥ ƒ ден дин лв. ؋ ৳ ฿ ლ ₡ ₣ ₤ ₥ ₦ ₨ ₩ ₪ ₫ € ₭ ₮ ₱ ₲ ₴ ₵ ₹ ₺ ₽ ﷼",
["alliance_close_to_home_DESCRIPTION"] = "Concede ouro extra no início da fase.",
["alliance_close_to_home_NAME"] = "RESERVAS COMPARTILHADAS",
["alliance_corageous_stand_DESCRIPTION"] = "Cada torre LINIREANA construída aumenta os pontos de saúde dos heróis.",
["alliance_corageous_stand_NAME"] = "POSTURA CORAJOSA",
["alliance_display_of_true_might_dark_DESCRIPTION"] = "Os Feitiços de Herói do Exército Negro agora também diminuem a velocidade de todos os inimigos na tela.",
["alliance_display_of_true_might_dark_NAME"] = "MALDIÇÃO SINISTRA",
["alliance_display_of_true_might_linirea_DESCRIPTION"] = "Os Feitiços de Herói de Linirea agora também curam e ressuscitam todas as unidades aliadas.",
["alliance_display_of_true_might_linirea_NAME"] = "BÊNÇÃO DE VITALIDADE",
["alliance_flux_altering_coils_DESCRIPTION"] = "Substitui todas as bandeiras de saída por pilares arcânicos que teleportam inimigos próximos de volta.",
["alliance_flux_altering_coils_NAME"] = "PILARES ARCÂNICOS",
["alliance_friends_of_the_crown_DESCRIPTION"] = "Cada herói LINIREANO equipado reduz o custo de construir e melhorar torres.",
["alliance_friends_of_the_crown_NAME"] = "AMIGOS DA COROA",
["alliance_merciless_DESCRIPTION"] = "Cada torre do EXÉRCITO DAS TREVAS construída aumenta o dano de ataque dos heróis.",
["alliance_merciless_NAME"] = "DEFESA IMPLACÁVEL",
["alliance_seal_of_punishment_DESCRIPTION"] = "Substitui o ponto de defesa por um selo mágico que danifica inimigos que passam por cima dele.",
["alliance_seal_of_punishment_NAME"] = "SELO DE PUNIÇÃO",
["alliance_shady_company_DESCRIPTION"] = "Cada herói do EXÉRCITO DAS TREVAS equipado aumenta o dano de ataque das torres.",
["alliance_shady_company_NAME"] = "COMPANHIA SOMBRIA",
["alliance_shared_reserves_DESCRIPTION"] = "Concede ouro extra no início da fase.",
["alliance_shared_reserves_NAME"] = "RESERVAS COMPARTILHADAS",
["baloon start battle iphone"] = "Toque duas vezes para convocar a onda",
["build defensive towers along the road to stop them."] = "construa torres de defesa pela estrada para impedi-los.",
["build towers to defend the road."] = "construa torres para defender a estrada.",
["check the stage description to see:"] = "verifique na descrição da fase:",
["deals area damage"] = "causa dano em área",
["don't let enemies past this point."] = "não deixe inimigos passarem daqui.",
["earn gold by killing enemies."] = "ganhe ouro matando inimigos.",
["good rate of fire"] = "boa cadência",
["heroes_desperate_effort_DESCRIPTION"] = "Os ataques dos heróis ignoram 10% da resistência dos inimigos.",
["heroes_desperate_effort_NAME"] = "CONHEÇA TEU INIMIGO",
["heroes_lethal_focus_DESCRIPTION"] = "Os heróis causam dano crítico em 20% de seus ataques.",
["heroes_lethal_focus_NAME"] = "FOCO LETAL",
["heroes_limit_pushing_DESCRIPTION"] = "Após usar cada Feitiço de Herói cinco vezes, seu tempo de recarga será imediatamente reiniciado. ",
["heroes_limit_pushing_NAME"] = "FORÇANDO O LIMITE",
["heroes_lone_wolves_DESCRIPTION"] = "Os heróis ganham mais experiência quando estão longe um do outro.",
["heroes_lone_wolves_NAME"] = "LOBOS SOLITÁRIOS",
["heroes_nimble_physique_DESCRIPTION"] = "Os heróis desviam 20% dos ataques dos inimigos.",
["heroes_nimble_physique_NAME"] = "FÍSICO ÁGIL",
["heroes_unlimited_vigor_DESCRIPTION"] = "Reduz todos os tempos de recarga dos Feitiços do Herói.",
["heroes_unlimited_vigor_NAME"] = "VIGOR ILIMITADO",
["heroes_visual_learning_DESCRIPTION"] = "Os heróis têm 10% a mais de armadura quando estão perto um do outro. ",
["heroes_visual_learning_NAME"] = "MÃO AMIGA",
["high damage, armor piercing"] = "alto dano, perfura armadura",
["iron and heroic challenges may have restrictions on upgrades!"] = "desafios heroicos e ferrenhos podem ter restrições de melhorias!",
["max lvl allowed"] = "nv. máx.",
["multi-shot, armor piercing"] = "múltiplos tiros, perfura armadura",
["no heroes"] = "sem heróis",
["pause popup"] = "JOGO PAUSADO",
["protect your lands from the enemy attacks."] = "Proteja suas terras de ataques inimigos.",
["rally range"] = "alcance de guarda",
["ready for action!"] = "pronto pra ação!",
["reinforcements_intense_workout_DESCRIPTION"] = "Melhora a saúde e a duração dos Reforços.",
["reinforcements_intense_workout_NAME"] = "TREINO INTENSO",
["reinforcements_master_blacksmiths_DESCRIPTION"] = "Melhora o dano de ataque e a armadura dos Reforços.",
["reinforcements_master_blacksmiths_NAME"] = "MESTRES FERREIROS",
["reinforcements_night_veil_DESCRIPTION"] = "Os Arqueiros das Sombras têm alcance e velocidade de ataque aumentados.",
["reinforcements_night_veil_NAME"] = "ARCOS CINZENTOS",
["reinforcements_power_trio_DESCRIPTION"] = "Chamar os Reforços agora também invoca um Cavaleiro Exemplar.",
["reinforcements_power_trio_NAME"] = "CAVALEIRO EXEMPLAR",
["reinforcements_power_trio_dark_DESCRIPTION"] = "Chamar os Reforços também invoca um Chamacorvos Sombrio.",
["reinforcements_power_trio_dark_NAME"] = "CHAMACORVOS SOMBRIO",
["reinforcements_rebel_militia_DESCRIPTION"] = "Os reforços são substituídos por Rebeldes Linireanos, lutadores duráveis que usam grandes conjuntos de armadura.",
["reinforcements_rebel_militia_NAME"] = "MILÍCIA LINIREA",
["reinforcements_shadow_archer_DESCRIPTION"] = "Os reforços são substituídos por Arqueiros das Sombras, atacando de longe e mirando unidades voadoras.",
["reinforcements_shadow_archer_NAME"] = "ORDEM DAS SOMBRAS",
["reinforcements_thorny_armor_DESCRIPTION"] = "Os Rebeldes Linireanos refletem parte do dano de ataques corpo a corpo inimigos.",
["reinforcements_thorny_armor_NAME"] = "ARMADURA CRAVEJADA",
["resists damage from"] = "Resiste dano de",
["select the rally point control"] = "escolha o controle de ponto de guarda",
["select the tower you want to build!"] = "escolha a torre que quer construir!",
["select where you want to move your soldiers"] = "escolha aonde deseja mover seus soldados",
["soldiers block enemies"] = "soldados bloqueiam inimigos",
["some enemies enjoy different levels of magic resistance that protects them against magical attacks."] = "Alguns inimigos possuem diferentes níveis de resistência mágica, que os protegem contra ataques mágicos.",
["some enemies wear armor of different strengths that protects them against non-magical attacks."] = "Alguns inimigos usam armaduras de níveis diferentes, que os protegem contra ataques não-mágicos.",
["tap these!"] = "toque nestes!",
["tap to continue..."] = "toque e continue...",
["tap twice to call wave"] = "Toque duas vezes para convocar a onda",
["this is a strategic point."] = "este é um ponto estratégico.",
["towers_favorite_customer_DESCRIPTION"] = "Ao comprar o nível final de uma habilidade, reduza seu custo em 50%.",
["towers_favorite_customer_NAME"] = "CLIENTE FAVORITO",
["towers_golden_time_DESCRIPTION"] = "Aumenta o ouro de bônus por chamar uma onda mais cedo.",
["towers_golden_time_NAME"] = "TEMPO DE OURO",
["towers_improved_formulas_DESCRIPTION"] = "Maximiza o dano de TODAS as explosões das torres e aumenta sua área de efeito.",
["towers_improved_formulas_NAME"] = "FÓRMULAS MELHORADAS",
["towers_keen_accuracy_DESCRIPTION"] = "Reduz o tempo de recarga de TODAS as habilidades da torre em 20%.",
["towers_keen_accuracy_NAME"] = "FERVOR DE BATALHA",
["towers_royal_training_DESCRIPTION"] = "Reduz o tempo de reaparecimento das unidades da torre e o tempo de recarga dos Reforços.",
["towers_royal_training_NAME"] = "CHAMADA PARA AÇÃO",
["towers_scoping_mechanism_DESCRIPTION"] = "Aumenta o alcance de ataque de TODAS as torres em 10%.",
["towers_scoping_mechanism_NAME"] = "MECANISMO DE ESCOPO",
["towers_war_rations_DESCRIPTION"] = "Aumenta a saúde de TODAS as unidades de torre em 10%.",
["towers_war_rations_NAME"] = "RACIONAMENTO DE GUERRA",
["towers_wise_investment_DESCRIPTION"] = "As torres agora reembolsam 90% do seu custo quando vendidas.",
["towers_wise_investment_NAME"] = "INVESTIMENTO SÁBIO",
["wOOt!"] = "OPA!",
["you can adjust your soldiers rally point to make them defend a different area."] = "você pode ajustar o ponto de guarda dos soldados para que defendam áreas diferentes.",
}
