-- ------------------------------------------------
-- -- WARNING: DO NOT EDIT BY HAND                 
-- -- Generated by kr-i18n/tools/strings-export.lua
-- ------------------------------------------------
return {
["!!!COMMENT_LOCALIZATION_SOURCE"] = "Keywords + testers (fiew for kr2)",
["%d Life"] = "%d жизнь",
["%d Lives"] = "%d жизни(ей)",
["%i sec."] = "%i сек.",
["- if heroes are allowed"] = "- разрешены ли герои",
["- max upgrade level allowed"] = "- максимальный разрешенный уровень улучшений",
["A good challenge!"] = "Это настоящий вызов!",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_1_NAME"] = "Вилли-Отродье",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_2_NAME"] = "Генри-Отродье",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_3_NAME"] = "Джеффри-Отродье",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_4_NAME"] = "Разложенный Николас",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_5_NAME"] = "Эдотродье",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_6_NAME"] = "Хоботродье",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_7_NAME"] = "Одотродье",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_8_NAME"] = "Седрик-Отродье",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_9_NAME"] = "Хальботродье",
["ACHIEVEMENT"] = "ДОСТИЖЕНИЕ",
["ACHIEVEMENTS"] = "ДОСТИЖЕНИЯ",
["ACHIEVEMENTS_TITLE"] = "ДОСТИЖЕНИЯ",
["ACHIEVEMENT_AGE_OF_HEROES_DESCRIPTION"] = "Выиграйте все испытания кампании в Героическом режиме.",
["ACHIEVEMENT_AGE_OF_HEROES_NAME"] = "Время Героев",
["ACHIEVEMENT_ALL_THE_SMALL_THINGS_DESCRIPTION"] = "Уничтожьте 182 Моргателя.",
["ACHIEVEMENT_ALL_THE_SMALL_THINGS_NAME"] = "Это Все Мелочи",
["ACHIEVEMENT_ARACHNED_DESCRIPTION"] = "Победите Мигэйл, Паучью Королеву.",
["ACHIEVEMENT_ARACHNED_NAME"] = "Не в Сети",
["ACHIEVEMENT_A_COON_OF_SURPRISES_DESCRIPTION"] = "Помогите Фредо сбежать.",
["ACHIEVEMENT_A_COON_OF_SURPRISES_NAME"] = "Кокон Сюрпризов",
["ACHIEVEMENT_A_TEST_OF_PROWESS_DESCRIPTION"] = "Выиграйте этап с 3 звёздами.",
["ACHIEVEMENT_A_TEST_OF_PROWESS_NAME"] = "Испытание Мастерства",
["ACHIEVEMENT_BREAKER_OF_CHAINS_DESCRIPTION"] = "Спасите четырех эльфов в Карминных Шахтах.",
["ACHIEVEMENT_BREAKER_OF_CHAINS_NAME"] = "Разрушитель Цепей",
["ACHIEVEMENT_BUTTERTENTACLES_DESCRIPTION"] = "Завершите Вырвиглазную Башню, не позволив Мидриас опутать ваших солдат.",
["ACHIEVEMENT_BUTTERTENTACLES_NAME"] = "Скользкая Дорожка",
["ACHIEVEMENT_BYE_BYE_BEAUTIFUL_DESCRIPTION"] = "Победите Провидицу Мидриас.",
["ACHIEVEMENT_BYE_BYE_BEAUTIFUL_NAME"] = "Прощай, Красавица",
["ACHIEVEMENT_CIRCLE_OF_LIFE_DESCRIPTION"] = "Посетите посвящение новорожденного Древесника.",
["ACHIEVEMENT_CIRCLE_OF_LIFE_NAME"] = "Круг Жизни",
["ACHIEVEMENT_CLEANSE_THE_KING_DESCRIPTION"] = "Спасите Короля Линерии.",
["ACHIEVEMENT_CLEANSE_THE_KING_NAME"] = "Слава Королю!",
["ACHIEVEMENT_CLEANUP_IS_OPTIONAL_DESCRIPTION"] = "Завершите Разоренные Окраины, не убирая обломки со стратегических точек.",
["ACHIEVEMENT_CLEANUP_IS_OPTIONAL_NAME"] = "Уборка Необязательна",
["ACHIEVEMENT_CONJUNTIVICTORY_DESCRIPTION"] = "Победите Наблюдателя.",
["ACHIEVEMENT_CONJUNTIVICTORY_NAME"] = "Не в Бровь, а в Глаз",
["ACHIEVEMENT_CONQUEROR_OF_THE_VOID_DESCRIPTION"] = "Получите 3 звезды на каждом этапе Пустоты за Гранью.",
["ACHIEVEMENT_CONQUEROR_OF_THE_VOID_NAME"] = "Покоритель Пустоты",
["ACHIEVEMENT_CRAFTING_IN_THE_MINES_DESCRIPTION"] = "Соберите все три куска свинины в Логове Дикозверей",
["ACHIEVEMENT_CRAFTING_IN_THE_MINES_NAME"] = "Крафтим в Шахтах",
["ACHIEVEMENT_CROWD_CONTROL_DESCRIPTION"] = "Пройдите Долину Порчи, не допуская появления Колоссов Плоти из ямы.",
["ACHIEVEMENT_CROWD_CONTROL_NAME"] = "Контроль Толпы",
["ACHIEVEMENT_CROW_SCARER_DESCRIPTION"] = "Прогоните всех ворон в Мрачной Долине.",
["ACHIEVEMENT_CROW_SCARER_NAME"] = "Пугало",
["ACHIEVEMENT_CRYSTAL_CLEAR_DESCRIPTION"] = "Получите 3 звезды на каждом этапе Проклятого Каньона.",
["ACHIEVEMENT_CRYSTAL_CLEAR_NAME"] = "Кристально Чисто",
["ACHIEVEMENT_DARK_LIEUTENANT_DESCRIPTION"] = "Достигните 10 уровня с Раэлин.",
["ACHIEVEMENT_DARK_LIEUTENANT_NAME"] = "Темный Лейтенант",
["ACHIEVEMENT_DARK_RUTHLESSNESS_DESCRIPTION"] = "Выиграйте этап, используя только башни и героев Армии Тьмы.",
["ACHIEVEMENT_DARK_RUTHLESSNESS_NAME"] = "Темная Беспощадность",
["ACHIEVEMENT_DISTURBING_THE_PEACE_DESCRIPTION"] = "Нарушьте обеденный перерыв рабочих в Лике Превосходства.",
["ACHIEVEMENT_DISTURBING_THE_PEACE_NAME"] = "Нарушитель Спокойствия",
["ACHIEVEMENT_DLC1_WIN_BOSS_DESCRIPTION"] = "Победите Гримборода и остановите постройку военной машины.",
["ACHIEVEMENT_DLC1_WIN_BOSS_NAME"] = "Самоволка",
["ACHIEVEMENT_DLC2_GATHER_ENVELOPS_DESCRIPTION"] = "Соберите 8 хунбао на острове Бурь.",
["ACHIEVEMENT_DLC2_GATHER_ENVELOPS_NAME"] = "Желаем вам богатства и процветания.",
["ACHIEVEMENT_DLC2_WIN_BOSS_KING_DESCRIPTION"] = "Победите Короля Демона Быка в его крепости.",
["ACHIEVEMENT_DLC2_WIN_BOSS_KING_NAME"] = "Возвращение Царя Обезьян",
["ACHIEVEMENT_DLC2_WIN_BOSS_PRINCESS_DESCRIPTION"] = "Победите Принцессу Железный Веер и её водную армию.",
["ACHIEVEMENT_DLC2_WIN_BOSS_PRINCESS_NAME"] = "Поднимается зловещий ветер",
["ACHIEVEMENT_DLC2_WIN_BOSS_REDBOY_DESCRIPTION"] = "Победите Красного Мальчика и его огненную армию.",
["ACHIEVEMENT_DLC2_WIN_BOSS_REDBOY_NAME"] = "Всё изменилось...",
["ACHIEVEMENT_DOMO_ARIGATO_DESCRIPTION"] = "Раздавите 20 врагов гигантским кулаком в Сердце Колосса.",
["ACHIEVEMENT_DOMO_ARIGATO_NAME"] = "Домо Аригато",
["ACHIEVEMENT_FACTORY_STRIKE_DESCRIPTION"] = "Пройдите Конвейер Безумия, не позволив Гримбороду запустить производство. ",
["ACHIEVEMENT_FACTORY_STRIKE_NAME"] = "Забастовка на Фабрике",
["ACHIEVEMENT_FIELD_TRIP_RUINER_DESCRIPTION"] = "Потушите костер туриста.",
["ACHIEVEMENT_FIELD_TRIP_RUINER_NAME"] = "Экскурсия Сорвана",
["ACHIEVEMENT_FOREST_PROTECTOR_DESCRIPTION"] = "Достигните 10 уровня с Ниру.",
["ACHIEVEMENT_FOREST_PROTECTOR_NAME"] = "Лесной Страж",
["ACHIEVEMENT_GARBAGE_DISPOSAL_DESCRIPTION"] = "Убейте 10 Безумных Жестянщиков, прежде чем они смогут сделать Ломовых Дронов.",
["ACHIEVEMENT_GARBAGE_DISPOSAL_NAME"] = "Вынос Мусора",
["ACHIEVEMENT_GEM_SPILLER_DESCRIPTION"] = "Разбейте все ведра с магическими кристаллами.",
["ACHIEVEMENT_GEM_SPILLER_NAME"] = "Расточитель Драгоценностей",
["ACHIEVEMENT_GET_THE_PARTY_STARTED_DESCRIPTION"] = "Решите головоломку и призовите группу.",
["ACHIEVEMENT_GET_THE_PARTY_STARTED_NAME"] = "Да Начнется Веселье",
["ACHIEVEMENT_GIFT_OF_LIFE_DESCRIPTION"] = "Освободите подопытного в Зале Клонирования.",
["ACHIEVEMENT_GIFT_OF_LIFE_NAME"] = "Дар Жизни",
["ACHIEVEMENT_GREENLIT_ALLIES_DESCRIPTION"] = "Призовите 10 Древесников-копейщиков.",
["ACHIEVEMENT_GREENLIT_ALLIES_NAME"] = "Зеленый Свет Союзникам",
["ACHIEVEMENT_HAIL_TO_THE_K_BABY_DESCRIPTION"] = "Найдите короля крокодилов.",
["ACHIEVEMENT_HAIL_TO_THE_K_BABY_NAME"] = "Слава Кроколю, Детка!",
["ACHIEVEMENT_HEARTLESS_VICTORY_DESCRIPTION"] = "Пройдите Сердце Леса, не используя силы Древесников.",
["ACHIEVEMENT_HEARTLESS_VICTORY_NAME"] = "Бессердечная Победа",
["ACHIEVEMENT_INTO_THE_OGREVERSE_DESCRIPTION"] = "Раскройте тайны загадочного человека-паука.",
["ACHIEVEMENT_INTO_THE_OGREVERSE_NAME"] = "Недружелюбный Сосед",
["ACHIEVEMENT_IRONCLAD_DESCRIPTION"] = "Выиграйте все испытания кампании в Железном режиме.",
["ACHIEVEMENT_IRONCLAD_NAME"] = "Человек из Стали",
["ACHIEVEMENT_ITS_A_SECRET_TO_EVERYONE_DESCRIPTION"] = "Помогите Ланку поймать 5 рубинов.",
["ACHIEVEMENT_ITS_A_SECRET_TO_EVERYONE_NAME"] = "Секрет для Маленькой Компании",
["ACHIEVEMENT_KEPT_YOU_WAITING_DESCRIPTION"] = "Найдите скрытного солдата в Сердце Колосса.",
["ACHIEVEMENT_KEPT_YOU_WAITING_NAME"] = "Заждался, Да?",
["ACHIEVEMENT_LEARNING_THE_ROPES_DESCRIPTION"] = "Закончите обучение с 3 звёздами.",
["ACHIEVEMENT_LEARNING_THE_ROPES_NAME"] = "Осваивая Азы",
["ACHIEVEMENT_LINIREAN_RESISTANCE_DESCRIPTION"] = "Выиграйте этап, используя только башни и героев Линерии.",
["ACHIEVEMENT_LINIREAN_RESISTANCE_NAME"] = "Линерийское Сопротивление",
["ACHIEVEMENT_LUCAS_SPIDER_DESCRIPTION"] = "Развеселите Лукуса.",
["ACHIEVEMENT_LUCAS_SPIDER_NAME"] = "Паучок Лукус",
["ACHIEVEMENT_MASTER_TACTICIAN_DESCRIPTION"] = "Завершите кампанию на сложности Безумец.",
["ACHIEVEMENT_MASTER_TACTICIAN_NAME"] = "Гений-Тактик",
["ACHIEVEMENT_MECHANICAL_BURNOUT_DESCRIPTION"] = "Дайте топлива Вечному Двига́телю у Врат Черностали.",
["ACHIEVEMENT_MECHANICAL_BURNOUT_NAME"] = "Потребляйт Дрова, Превращайт их в Энергия...",
["ACHIEVEMENT_MIGHTY_III_DESCRIPTION"] = "Убейте 10000 врагов.",
["ACHIEVEMENT_MIGHTY_III_NAME"] = "Могучий III",
["ACHIEVEMENT_MIGHTY_II_DESCRIPTION"] = "Убейте 3000 врагов.",
["ACHIEVEMENT_MIGHTY_II_NAME"] = "Могучий II",
["ACHIEVEMENT_MIGHTY_I_DESCRIPTION"] = "Убейте 500 врагов.",
["ACHIEVEMENT_MIGHTY_I_NAME"] = "Могучий I",
["ACHIEVEMENT_MOST_DELICIOUS_DESCRIPTION"] = "Дайте немного меда Бигги-Древеснику.",
["ACHIEVEMENT_MOST_DELICIOUS_NAME"] = "Вкусняшка",
["ACHIEVEMENT_NATURES_WRATH_DESCRIPTION"] = "Убейте 30 врагов, используя Сердце Древесников.",
["ACHIEVEMENT_NATURES_WRATH_NAME"] = "Гнев Природы",
["ACHIEVEMENT_NONE_SHALL_PASS_DESCRIPTION"] = "Пройдите Логово Дикозверей, не позволив лишним врагам пройти через ворота.",
["ACHIEVEMENT_NONE_SHALL_PASS_NAME"] = "Никто не Пройдет!",
["ACHIEVEMENT_NOT_A_MOMENT_TO_WASTE_DESCRIPTION"] = "Вызовите 15 волн заранее.",
["ACHIEVEMENT_NOT_A_MOMENT_TO_WASTE_NAME"] = "Нельзя Терять ни Минуты",
["ACHIEVEMENT_NO_FLY_ZONE_DESCRIPTION"] = "Убейте 50 Парящих Пауков.",
["ACHIEVEMENT_NO_FLY_ZONE_NAME"] = "Бесполетная Зона",
["ACHIEVEMENT_OBLITERATE_DESCRIPTION"] = "Найдите части запретного робота в каждой стадии Колоссальной Угрозы.",
["ACHIEVEMENT_OBLITERATE_NAME"] = "Уничтожить!",
["ACHIEVEMENT_ONE_SHOT_TOWER_DESCRIPTION"] = "Уничтожьте 10 врагов одним лазером башни Темного Луча.",
["ACHIEVEMENT_ONE_SHOT_TOWER_NAME"] = "Выстрел на Славу",
["ACHIEVEMENT_OUTBACK_BARBEQUICK_DESCRIPTION"] = "Победите Кровожада на сложности Безумец до того, как он прыгнет.",
["ACHIEVEMENT_OUTBACK_BARBEQUICK_NAME"] = "Увяз в Земле",
["ACHIEVEMENT_OVER_THE_EDGE_DESCRIPTION"] = "Столкните Древесников с вершин деревьев.",
["ACHIEVEMENT_OVER_THE_EDGE_NAME"] = "Игра Окончена",
["ACHIEVEMENT_OVINE_JOURNALISM_DESCRIPTION"] = "Найдите Журналиста Овечкина на каждом участке кампании.",
["ACHIEVEMENT_OVINE_JOURNALISM_NAME"] = "Овечья Журналистика",
["ACHIEVEMENT_PEST_CONTROL_DESCRIPTION"] = "Убейте 300 Взглядышей.",
["ACHIEVEMENT_PEST_CONTROL_NAME"] = "Дезинсекция",
["ACHIEVEMENT_PLAYFUL_FRIENDS_DESCRIPTION"] = "Сыграйте в \"нору\" со всеми Древесниками в Сердце Леса.",
["ACHIEVEMENT_PLAYFUL_FRIENDS_NAME"] = "Игривые Друзья",
["ACHIEVEMENT_PORKS_OFF_THE_MENU_DESCRIPTION"] = "Победите Кровожада.",
["ACHIEVEMENT_PORKS_OFF_THE_MENU_NAME"] = "Свинины нет в Меню",
["ACHIEVEMENT_PROMOTION_DENIED_DESCRIPTION"] = "Убейте 30 Жрецов Культа, прежде чем они превратятся в Отродья.",
["ACHIEVEMENT_PROMOTION_DENIED_NAME"] = "В Повышении Отказано",
["ACHIEVEMENT_ROCK_BEATS_ROCK_DESCRIPTION"] = "Заставьте статую проиграть самой себе.",
["ACHIEVEMENT_ROCK_BEATS_ROCK_NAME"] = "Камень Бьет... Камень?",
["ACHIEVEMENT_ROOM_achievement_claim"] = "Забрать награду!",
["ACHIEVEMENT_ROYAL_CAPTAIN_DESCRIPTION"] = "Достигните 10 уровня с Веспером.",
["ACHIEVEMENT_ROYAL_CAPTAIN_NAME"] = "Королевский Капитан",
["ACHIEVEMENT_RUNEQUEST_DESCRIPTION"] = "Активируйте все шесть рун в Вечносветлом Лесу.",
["ACHIEVEMENT_RUNEQUEST_NAME"] = "Рунный Квест",
["ACHIEVEMENT_RUST_IN_PEACE_DESCRIPTION"] = "Пройдите уровень, не позволив Одержимым Доспехам воскреснуть.",
["ACHIEVEMENT_RUST_IN_PEACE_NAME"] = "Ржавейся с Миром",
["ACHIEVEMENT_SAVIOUR_OF_THE_FOREST_DESCRIPTION"] = "Выиграйте уровень, не потеряв цветы Древесников.",
["ACHIEVEMENT_SAVIOUR_OF_THE_FOREST_NAME"] = "Спаситель Леса",
["ACHIEVEMENT_SAVIOUR_OF_THE_GREEN_DESCRIPTION"] = "Получите 3 звезды на каждом этапе Вечносветлого Леса.",
["ACHIEVEMENT_SAVIOUR_OF_THE_GREEN_NAME"] = "Спаситель Природы",
["ACHIEVEMENT_SCRAMBLED_EGGS_DESCRIPTION"] = "Убейте 50 Крошек-кроков, прежде чем они вылупятся",
["ACHIEVEMENT_SCRAMBLED_EGGS_NAME"] = "Яйца Всмятку",
["ACHIEVEMENT_SEASONED_GENERAL_DESCRIPTION"] = "Завершите кампанию на сложности Ветеран.",
["ACHIEVEMENT_SEASONED_GENERAL_NAME"] = "Новоиспеченный Генерал",
["ACHIEVEMENT_SEE_YA_LATER_ALLIGATOR_DESCRIPTION"] = "Победите Абоминора, Пожирателя Миров",
["ACHIEVEMENT_SEE_YA_LATER_ALLIGATOR_NAME"] = "До Свидания, Крокозябра",
["ACHIEVEMENT_SHUT_YOUR_MOUTH_DESCRIPTION"] = "Пройдите Лик Превосходства, не позволив Гримбороду поджечь ваши башни.",
["ACHIEVEMENT_SHUT_YOUR_MOUTH_NAME"] = "Рот Закрой, Тепло не Трать!",
["ACHIEVEMENT_SIGNATURE_TECHNIQUES_DESCRIPTION"] = "Используйте заклинания героев 500 раз.",
["ACHIEVEMENT_SIGNATURE_TECHNIQUES_NAME"] = "Авторские Приемы",
["ACHIEVEMENT_SILVER_FOR_MONSTERS_DESCRIPTION"] = "Помогите Герхарту убить Лешего.",
["ACHIEVEMENT_SILVER_FOR_MONSTERS_NAME"] = "Серебро для Монстров",
["ACHIEVEMENT_SMOOTH_OPER_GATOR_DESCRIPTION"] = "Помогите дружелюбному аллигатору завести свою лодку.",
["ACHIEVEMENT_SMOOTH_OPER_GATOR_NAME"] = "Опытный Опер-гатор",
["ACHIEVEMENT_SPECTRAL_FURY_DESCRIPTION"] = "Победите Навиру и остановите нашествие Ревенантов.",
["ACHIEVEMENT_SPECTRAL_FURY_NAME"] = "Призрачная Ярость",
["ACHIEVEMENT_STARLIGHT_DESCRIPTION"] = "Помогите Фредо и Сэмми сбежать от Гигантского Паука.",
["ACHIEVEMENT_STARLIGHT_NAME"] = "Звездный Свет",
["ACHIEVEMENT_TAKE_ME_HOME_DESCRIPTION"] = "Верните гоблина Риффа в его родное измерение.",
["ACHIEVEMENT_TAKE_ME_HOME_NAME"] = "Забери Меня с Собой",
["ACHIEVEMENT_THE_CAVALRY_IS_HERE_DESCRIPTION"] = "Призовите 1000 подкреплений.",
["ACHIEVEMENT_THE_CAVALRY_IS_HERE_NAME"] = "Кавалерия прибыла!",
["ACHIEVEMENT_TIPPING_THE_SCALES_DESCRIPTION"] = "Бросьте Робин Вуда в реку.",
["ACHIEVEMENT_TIPPING_THE_SCALES_NAME"] = "Пальцем на Весы",
["ACHIEVEMENT_TREE_HUGGER_DESCRIPTION"] = "Пройдите Мглистые Развалины хотя бы с одним живым Стоеросом.",
["ACHIEVEMENT_TREE_HUGGER_NAME"] = "Древолюб",
["ACHIEVEMENT_TURN_A_BLIND_EYE_DESCRIPTION"] = "Убейте 100 Порождений Скверны, находящихся под эффектом Взгляда.",
["ACHIEVEMENT_TURN_A_BLIND_EYE_NAME"] = "На это Закроем Глаза",
["ACHIEVEMENT_UNBOUND_VICTORY_DESCRIPTION"] = "Пройдите Зловещий Перекресток, не допустив превращения Кошмаров в Закованные Кошмары.",
["ACHIEVEMENT_UNBOUND_VICTORY_NAME"] = "Свободен ото Всех Оков",
["ACHIEVEMENT_UNENDING_RICHES_DESCRIPTION"] = "Соберите в общей сложности 150000 золота.",
["ACHIEVEMENT_UNENDING_RICHES_NAME"] = "Несметные Богатства",
["ACHIEVEMENT_UNTAMED_BEAST_DESCRIPTION"] = "Достигните 10 уровня с Гримсоном.",
["ACHIEVEMENT_UNTAMED_BEAST_NAME"] = "Неукротимый Зверь",
["ACHIEVEMENT_WAR_MASONRY_DESCRIPTION"] = "Постройте 100 башен.",
["ACHIEVEMENT_WAR_MASONRY_NAME"] = "Военный Камнестрой",
["ACHIEVEMENT_WEIRDER_THINGS_DESCRIPTION"] = "Помогите Эрни и Дастону отразить атаку Моргателей на Отравленных Угодьях.",
["ACHIEVEMENT_WEIRDER_THINGS_NAME"] = "Очень Страшные Дела",
["ACHIEVEMENT_WE_ARE_ALL_MAD_HERE_DESCRIPTION"] = "Найдите хитрого кота на каждом этапе кампании Неугасимая Ярость.",
["ACHIEVEMENT_WE_ARE_ALL_MAD_HERE_NAME"] = "Все Мы Здесь не в Своем Уме...",
["ACHIEVEMENT_WE_RE_NOT_GONNA_TAKE_IT_DESCRIPTION"] = "Убейте 15 Коварных Сестер, прежде чем они смогут призвать Кошмар.",
["ACHIEVEMENT_WE_RE_NOT_GONNA_TAKE_IT_NAME"] = "Хватит Это Терпеть!",
["ACHIEVEMENT_WOBBA_LUBBA_DUB_DUB_DESCRIPTION"] = "Почините портальную пушку Ника и Марти.",
["ACHIEVEMENT_WOBBA_LUBBA_DUB_DUB_NAME"] = "Вобба-Лабба-Даб-Даб!",
["ACHIEVEMENT_YOU_SHALL_NOT_CAST_DESCRIPTION"] = "Спасите Короля Денаса на сложности Безумец, не позволяя Провидице Мидриас создавать иллюзии.",
["ACHIEVEMENT_YOU_SHALL_NOT_CAST_NAME"] = "Ты Не Призовешь!",
["ADS_MESSAGE_OK"] = "ОК",
["ADS_MESSAGE_TITLE"] = "БОЛЬШЕ КРИСТАЛЛОВ",
["ADS_NO_REWARD_VIDEO_AVAILABLE"] = "Сейчас просмотр видео недоступен. Повторите попытку позднее.",
["ADS_REWARD_EARNED"] = "За просмотр видео вы получаете кристаллы (%i шт.)",
["ADVANCED TOWERS"] = "ПРОДВИНУТЫЕ БАШНИ",
["ALERT_VERSION"] = "Доступна более новая версия игры. Пожалуйста, загрузите ее из магазина.",
["ALL FOR"] = "ВСЕ ЗА",
["ARCHER TOWER"] = "БАШНЯ ЛУЧНИКОВ",
["ARE YOU SURE YOU WANT TO QUIT?"] = "ТОЧНО ХОТИТЕ ВЫЙТИ?",
["ARMORED ENEMIES!"] = "ВРАГИ В ДОСПЕХАХ!",
["ARTILLERY"] = "АРТИЛЛЕРИЯ",
["Achievements"] = "Достижения",
["BARRACKS"] = "КАЗАРМА",
["BASIC TOWERS"] = "БАЗОВЫЕ БАШНИ",
["BEST VALUE"] = "ЛУЧШАЯ ЦЕНА",
["BOSS_BULL_KING_DESCRIPTION"] = "Безжалостный и авторитарный лидер, ветеран войны и прагматичный стратег. Известен своей огромной силой, злопамятным характером и военным мастерством.",
["BOSS_BULL_KING_EXTRA"] = "-Высокая броня и сопротивление магии\n- Сильный оглушающий эффект по области на юниты и башни",
["BOSS_BULL_KING_NAME"] = "Царь Демонов-Бык",
["BOSS_CORRUPTED_DENAS_DESCRIPTION"] = "Поверженный король Линерии, превращенный в грозное чудовище темными силами Культа Наблюдателя.",
["BOSS_CORRUPTED_DENAS_EXTRA"] = "- Создает Взглядышей",
["BOSS_CORRUPTED_DENAS_NAME"] = "Оскверненный Денас",
["BOSS_CROCS_DESCRIPTION"] = "Голод во плоти, это древнее существо может поглотить весь мир, если его не остановить. ",
["BOSS_CROCS_EXTRA"] = "- Поедает башни\n- Эволюционирует при утолении голода\n- Призывает Крошек-кроков",
["BOSS_CROCS_LVL1_DESCRIPTION"] = "Голод во плоти, это древнее существо может поглотить весь мир, если его не остановить. ",
["BOSS_CROCS_LVL1_EXTRA"] = "- Поедает башни\n- Эволюционирует при утолении голода\n- Призывает Крошек-кроков",
["BOSS_CROCS_LVL1_NAME"] = "Абоминор",
["BOSS_CROCS_LVL2_DESCRIPTION"] = "Голод во плоти, это древнее существо может поглотить весь мир, если его не остановить. ",
["BOSS_CROCS_LVL2_EXTRA"] = "- Поедает башни\n- Эволюционирует при утолении голода\n- Призывает Крошек-кроков",
["BOSS_CROCS_LVL2_NAME"] = "Абоминор",
["BOSS_CROCS_LVL3_DESCRIPTION"] = "Голод во плоти, это древнее существо может поглотить весь мир, если его не остановить. ",
["BOSS_CROCS_LVL3_EXTRA"] = "- Поедает башни\n- Эволюционирует при утолении голода\n- Призывает Крошек-кроков",
["BOSS_CROCS_LVL3_NAME"] = "Абоминор",
["BOSS_CROCS_LVL4_DESCRIPTION"] = "Голод во плоти, это древнее существо может поглотить весь мир, если его не остановить. ",
["BOSS_CROCS_LVL4_EXTRA"] = "- Поедает башни\n- Эволюционирует при утолении голода\n- Призывает Крошек-кроков",
["BOSS_CROCS_LVL4_NAME"] = "Абоминор",
["BOSS_CROCS_LVL5_DESCRIPTION"] = "Голод во плоти, это древнее существо может поглотить весь мир, если его не остановить. ",
["BOSS_CROCS_LVL5_EXTRA"] = "- Поедает башни\n- Эволюционирует при утолении голода\n- Призывает Крошек-кроков",
["BOSS_CROCS_LVL5_NAME"] = "Абоминор",
["BOSS_CROCS_NAME"] = "Абоминор",
["BOSS_CULT_LEADER_DESCRIPTION"] = "Нынешний лидер Культа, Мидриас действует как правая рука Надблюдателя, организуя вторжения в другие миры.",
["BOSS_CULT_LEADER_EXTRA"] = "- Высокая броня и сопротивление магии, если не блокируется\n - Высокий урон по площади",
["BOSS_CULT_LEADER_NAME"] = "Провидица Мидриас",
["BOSS_GRYMBEARD_DESCRIPTION"] = "Самовлюбленный гном с бредом величия. Столь же опасен, сколь и невменяем.",
["BOSS_GRYMBEARD_EXTRA"] = "- Запускает ракетный кулак против солдат игрока",
["BOSS_GRYMBEARD_NAME"] = "Гримбород",
["BOSS_MACHINIST_DESCRIPTION"] = "За рулем своего последнего изобретения, Гримбород обрушивает на своих врагов дождь из огня и металла.",
["BOSS_MACHINIST_EXTRA"] = "- Летает\n- Стреляет металлоломом в солдат",
["BOSS_MACHINIST_NAME"] = "Гримбород",
["BOSS_NAVIRA_DESCRIPTION"] = "Отвернувшийся от света и воззвавший к запретным силам смерти, Навира желает вернуть эльфам былое могущество.",
["BOSS_NAVIRA_EXTRA"] = "- Отключает башни огненными шарами\n- Превращается в неостановимое торнадо",
["BOSS_NAVIRA_NAME"] = "Навира",
["BOSS_PIG_DESCRIPTION"] = "Его королевское величество, бесподобный король Дикозверей, первый самопровозглашенный, крушащий своих врагов гигантской булавой.",
["BOSS_PIG_EXTRA"] = "- Прыгает на большие расстояния по тропам",
["BOSS_PIG_NAME"] = "Кровожад",
["BOSS_PRINCESS_IRON_FAN_DESCRIPTION"] = "Элегантная, но смертельно опасная Принцесса Железный Веер владеет легендарным Железным Веером, способным гасить пламя и вызывать бури.",
["BOSS_PRINCESS_IRON_FAN_EXTRA"] = "- Клонирует себя\n- Закрывает героев во флаконе\n- Превращает башни в спаунеры врагов",
["BOSS_PRINCESS_IRON_FAN_NAME"] = "Princess Iron Fan",
["BOSS_REDBOY_TEEN_DESCRIPTION"] = "Огненный и горделивый молодой князь-демон. Вспыльчивый, самоуверенный и безжалостно амбициозный. Мастер Огня Самадхи и искусный копейщик.",
["BOSS_REDBOY_TEEN_EXTRA"] = "- Мощная атака по площади\n- Приказывает своему дракону оглушать башни",
["BOSS_REDBOY_TEEN_NAME"] = "Red Boy",
["BOSS_SPIDER_QUEEN_DESCRIPTION"] = "Древняя Паучья Королева, первобытная сила, пробудившаяся ото сна, дабы вернуть то, что принадлежит ей по праву. ",
["BOSS_SPIDER_QUEEN_EXTRA"] = "- Оглушает башни\n- Высасывает жизнь из солдат игрока\n- Призывает Пауков-кровопийц\n- Кидает паутину вам в глаза",
["BOSS_SPIDER_QUEEN_NAME"] = "Мигэйл",
["BRIEFING_LEVEL_WARNING"] = "Эта кампания имеет высокий уровень сложности.",
["BUTTON_BUG_CRASH"] = "ОШИБКА В ИГРЕ",
["BUTTON_BUG_OTHER"] = "ДРУГОЕ",
["BUTTON_BUG_REPORT"] = "ОШИБКА",
["BUTTON_BUY"] = "КУПИТЬ",
["BUTTON_BUY_UPGRADE"] = "КУПИТЬ УЛУЧШЕНИЕ",
["BUTTON_CLOSE"] = "ЗАКРЫТЬ",
["BUTTON_CONFIRM"] = "ПОДТВЕРДИТЬ",
["BUTTON_CONTINUE"] = "ПРОДОЛЖИТЬ",
["BUTTON_DISABLE"] = "Отключить",
["BUTTON_DONE"] = "ГОТОВО",
["BUTTON_ENDLESS_QUIT"] = "ВЫЙТИ",
["BUTTON_ENDLESS_TRYAGAIN"] = "ПОВТОРИТЬ",
["BUTTON_GET_GEMS"] = "КУПИТЬ ПРЕДМЕТЫ",
["BUTTON_LEVEL_SELECT_FIGHT"] = "СРАЖАТЬСЯ! ",
["BUTTON_LOST_CONTENT"] = "УТРАЧЕННЫЙ КОНТЕНТ",
["BUTTON_MAIN_MENU"] = "ГЛАВНОЕ МЕНЮ",
["BUTTON_NO"] = "НЕТ",
["BUTTON_OK"] = "OK!",
["BUTTON_QUIT"] = "ВЫЙТИ",
["BUTTON_RESET"] = "СБРОС",
["BUTTON_RESTART"] = "ЗАНОВО",
["BUTTON_UNDO"] = "ОТМЕНА",
["BUTTON_YES"] = "ДА",
["BUY UPGRADES!"] = "КУПИТЬ УЛУЧШЕНИЯ!",
["Basic Tower Types"] = "Основные типы башен",
["CARD_REWARDS_CAMPAIGN"] = "Новая кампания!",
["CARD_REWARDS_DLC_1"] = "Колоссальная Угроза",
["CARD_REWARDS_DLC_2"] = "Путешествие Укуна",
["CARD_REWARDS_HERO"] = "НОВЫЙ ГЕРОЙ!",
["CARD_REWARDS_TOWER"] = "НОВАЯ БАШНЯ!",
["CARD_REWARDS_TOWER_LEVEL"] = "НОВЫЙ УРОВЕНЬ БАШЕН!",
["CARD_REWARDS_TOWER_LEVEL_PREFIX"] = "УР.",
["CARD_REWARDS_UPDATE_01"] = "Неугасимая Ярость",
["CARD_REWARDS_UPDATE_02"] = "Древний Голод",
["CARD_REWARDS_UPDATE_03"] = "Арахнофобия",
["CARD_REWARDS_UPGRADES"] = "ОЧКИ УЛУЧШЕНИЙ!",
["CArmor0"] = "Нет",
["CArmor1"] = "Слабая",
["CArmor2"] = "Средняя",
["CArmor3"] = "Хорошая",
["CArmor4"] = "Мощная",
["CArmor9"] = "Неуязвимость",
["CArmorSmall0"] = "Нет",
["CArmorSmall1"] = "Сл.",
["CArmorSmall2"] = "Ср.",
["CArmorSmall3"] = "Хор.",
["CArmorSmall4"] = "Мощ.",
["CArmorSmall9"] = "Неу.",
["CHALLENGE_RULE_DIFFICULTY_CASUAL"] = "Новичок",
["CHALLENGE_RULE_DIFFICULTY_IMPOSSIBLE"] = "Безумец",
["CHALLENGE_RULE_DIFFICULTY_NORMAL"] = "Боец",
["CHALLENGE_RULE_DIFFICULTY_VETERAN"] = "Ветеран",
["CHANGE_LANGUAGE_QUESTION"] = "Хотите изменить языковые настройки?",
["CINEMATICS_TAP_TO_CONTINUE"] = "Нажмите, чтобы продолжить...",
["CINEMATICS_TAP_TO_CONTINUE_KR1"] = "Нажмите, чтобы продолжить...",
["CINEMATICS_TAP_TO_CONTINUE_KR2"] = "Нажмите, чтобы продолжить...",
["CINEMATICS_TAP_TO_CONTINUE_KR3"] = "Нажмите, чтобы продолжить...",
["CINEMATICS_TAP_TO_CONTINUE_KR5"] = "Нажмите, чтобы продолжить...",
["CLAIM_GIFT"] = "Получить подарок ",
["CLOUDSYNC_PLEASE_WAIT"] = "Обновление облачного сохранения ...",
["CLOUD_DIALOG_NO"] = "Нет",
["CLOUD_DIALOG_OK"] = "ОК",
["CLOUD_DIALOG_YES"] = "Да",
["CLOUD_DOWNLOAD_QUESTION"] = "Скачать сохранение из iCloud?",
["CLOUD_DOWNLOAD_TITLE"] = "Скачать из iCloud",
["CLOUD_SAVE"] = "Облачное Сохранение",
["CLOUD_SAVE_DISABLE_EXTRA"] = "Внимание! Вы можете потерять свой игровой прогресс, если игра будет удалена.",
["CLOUD_SAVE_DISABLE_GENERIC_DESCRIPTION"] = "Вы действительно хотите отключить сохранение игрового прогресса в облаке?",
["CLOUD_SAVE_OFF"] = "Облако выключено",
["CLOUD_SAVE_ON"] = "Облако активно",
["CLOUD_UPLOAD_QUESTION"] = "Загрузить текущий игровой прогресс в iCloud?",
["CLOUD_UPLOAD_TITLE"] = "Загрузить в iCloud",
["COMIC_10_1_KR5_KR5"] = "Выпустите меня! Я делаю лучшее для королевства!",
["COMIC_10_2_KR5_KR5"] = "Прекращай это богохульство, брат. Не таков эльфийский путь.",
["COMIC_10_3_KR5_KR5"] = "Благодарю, мой старый ученик. Дальше мы сами.",
["COMIC_10_4_KR5_KR5"] = "Позже, в лагере...",
["COMIC_10_5_KR5_KR5"] = "Значит... ты уверен, что Вез'нану можно верить?",
["COMIC_10_6_KR5_KR5"] = "Мы не спускаем с него глаз...",
["COMIC_10_7_KR5_KR5"] = "...но пока он, похоже, на нашей стороне.",
["COMIC_10_8_KR5_KR5"] = "Хе-хе. Пока что...",
["COMIC_11_1_KR5_KR5"] = "Болото пробудилось...",
["COMIC_11_2_KR5_KR5"] = "...оно будто следит за нами...",
["COMIC_11_3_KR5_KR5"] = "...подкрадывается...",
["COMIC_11_4_KR5_KR5"] = "...жаждет поглотить нас.",
["COMIC_11_5_KR5_KR5"] = "Берегись!",
["COMIC_11_6_KR5_KR5"] = "Нас атакуют!",
["COMIC_11_7_KR5_KR5"] = "Лети, светлячок! Наше спасение в твоей скорости!",
["COMIC_12_1_KR5_KR5"] = "Ошибкой было просто запирать тебя. Во второй раз я ее не допущу.",
["COMIC_12_2_KR5_KR5"] = "НЕЕЕЕЕЕЕЕЕЕЕЕТ!!!",
["COMIC_12_3_KR5_KR5"] = "Я изгоняю тебя навек!",
["COMIC_12_4_KR5_KR5"] = "Кхе!",
["COMIC_12_5_KR5_KR5"] = "Кхе-кхе!",
["COMIC_12_6_KR5_KR5"] = "Мм... Похоже, я потеряла хватку.",
["COMIC_13_1_KR5_KR5"] = "Они называли меня безумцем.",
["COMIC_13_2_KR5_KR5"] = "Говорили, что создать такое оружие невозможно.",
["COMIC_13_3_KR5_KR5"] = "Но скоро они поймут, насколько ошибались...",
["COMIC_13_4_KR5_KR5"] = "...и склонятся пред гением Гримборода!",
["COMIC_14_1_KR5_KR5"] = "Что будем делать с ними?",
["COMIC_14_2_KR5_KR5"] = "Предоставьте их мне!",
["COMIC_14_3_KR5_KR5"] = "Я знаю, куда их пристроить.",
["COMIC_14_4_KR5_KR5"] = "Значит вот как?",
["COMIC_14_5_KR5_KR5"] = "Оставишь Гримборода гнить в тюрьме?!",
["COMIC_14_6_KR5_KR5"] = "Напротив, мой низкорослый друг...",
["COMIC_14_7_KR5_KR5"] = "...у меня большие планы на твою большую голову!",
["COMIC_15_10_KR5_KR5"] = "…но не в лучшем состоянии.",
["COMIC_15_1_KR5_KR5"] = "Где-то в горах.",
["COMIC_15_2_KR5_KR5"] = "Эй, Гоблин!",
["COMIC_15_3_KR5_KR5"] = "Давай-ка поработай!",
["COMIC_15_4_KR5_KR5"] = "Доставь вот это сообщение.",
["COMIC_15_5_KR5_KR5"] = "Нам нужно отправить больше разведчиков. Мы не можем спать спокойно, пока эти сектанты разгуливают повсюду.",
["COMIC_15_6_KR5_KR5"] = "Мы могли бы отправить несколько светлячков на помощь, они...",
["COMIC_15_7_KR5_KR5"] = "Милорд! Срочные новости!",
["COMIC_15_8_KR5_KR5"] = "Что ж...",
["COMIC_15_9_KR5_KR5"] = "Наши разведчики нашлись...",
["COMIC_16_1_KR5_KR5"] = "За меня отомстят!",
["COMIC_16_2_KR5_KR5"] = "Моя сестра... ЧТО?!",
["COMIC_17_10_KR5_KR5"] = "Если мы их не остановим, они уничтожат все королевства!",
["COMIC_17_11_KR5_KR5"] = "Мы должны ему помочь!",
["COMIC_17_12_KR5_KR5"] = "О, конечно, конечно.",
["COMIC_17_13_KR5_KR5"] = "Да-да…",
["COMIC_17_1_KR5_KR5"] = "Прекрасный день, не так ли?",
["COMIC_17_2_KR5_KR5"] = "Я мог бы привыкнуть к этому миру.",
["COMIC_17_3_KR5_KR5"] = "Лучше не стоит.",
["COMIC_17_4_KR5_KR5"] = "Солнце, это ты?! Мог бы просто помахать, знаешь ли…",
["COMIC_17_5_KR5_KR5"] = "Друзья, случилось нечто ужасное...",
["COMIC_17_6_KR5_KR5"] = "Я мирно медитировал в своей черепахе, когда…",
["COMIC_17_7_KR5_KR5"] = "Три Демонических Короля появились из ниоткуда!",
["COMIC_17_8_KR5_KR5"] = "Само собой, я сражался доблестно, но...",
["COMIC_17_9_KR5_KR5"] = "Они бесчестно забрали мои небесные сферы!",
["COMIC_18_1_KR5_KR5"] = "У берегов логова Царя Демона Быка...",
["COMIC_18_2_KR5_KR5"] = "Цель обнаружена!",
["COMIC_18_3_KR5_KR5"] = "Давайте взорвём эту крепость!",
["COMIC_18_4_KR5_KR5"] = "Мои стены смеются над вашими камешками!",
["COMIC_18_5_KR5_KR5"] = "За Линирею!",
["COMIC_18_6_KR5_KR5"] = "Назад, парни! Нам нужен здесь прорыв!",
["COMIC_19_1_KR5_KR5"] = "Небесные сферы не могут оставаться в вашей опеке, это абсурд!",
["COMIC_19_2_KR5_KR5"] = "Да, будь осторожен с этим, приятель.",
["COMIC_19_3_KR5_KR5"] = "Ты был очень мудрым, благородная обезьяна!",
["COMIC_19_4_KR5_KR5"] = "Что мне с вами тремя делать?",
["COMIC_1_1_KR5"] = "Прошло несколько месяцев с тех пор, как мы прибыли на эти земли в поисках нашего пропавшего короля...",
["COMIC_1_2B_KR5"] = "...После того, как он был изгнан Вез'наном, темным волшебником.",
["COMIC_1_4_KR5"] = "Мы нашли место и разбили лагерь, чтобы собраться с силами...",
["COMIC_1_5_KR5"] = "...в мире...",
["COMIC_1_8_KR5"] = "...Но, похоже, ему пришел конец.",
["COMIC_2_1_KR5"] = "УРААА!",
["COMIC_2_3_KR5"] = "Вез'нан?!",
["COMIC_2_4a_KR5"] = "Спокойно... Я пришел...",
["COMIC_2_4b_KR5"] = "...договорится.",
["COMIC_2_5_KR5"] = "После того, во что ты превратил наше королевство?!",
["COMIC_2_6_KR5"] = "Денасу нужно было раскрыть глаза.",
["COMIC_2_7_KR5"] = "Он отказывался видеть угрозу, нависшую над королевством.",
["COMIC_2_8_1_KR5"] = "Но давайте найдем вашего короля...",
["COMIC_2_8_2_KR5"] = "...и положим конец этой угрозе...",
["COMIC_2_8b_KR5"] = "...вместе.",
["COMIC_3_1_KR5"] = "Ох ты! Что это тут у нас?",
["COMIC_3_2_KR5"] = "Могучий меч Элини!",
["COMIC_3_3_KR5"] = "Ауч!",
["COMIC_3_4a_KR5"] = "Ну конечно...",
["COMIC_3_4b_KR5"] = "Кончай тратить время!",
["COMIC_3_5a_KR5"] = "Агх... но он ближе, чем ты думаешь.",
["COMIC_3_5b_KR5"] = "Наш король все еще не найден.",
["COMIC_3_6_KR5"] = "Однако... битва может быть не из легких.",
["COMIC_4_10a_KR5"] = "Ха! Я всегда прав!",
["COMIC_4_10b_KR5"] = "Итак... что теперь?",
["COMIC_4_11_KR5"] = "У нас могут быть разногласия...",
["COMIC_4_12_KR5"] = "Но великий враг для нас един.",
["COMIC_4_1_KR5"] = "Элини...",
["COMIC_4_2_KR5"] = "Придай ему сил!",
["COMIC_4_4_KR5"] = "Ааыыууургх!",
["COMIC_4_7a_KR5"] = "Я смотрю, \"отпуск\" делает с тобой чудеса!",
["COMIC_4_7b_KR5"] = "ТЫ!!!",
["COMIC_4_8_KR5"] = "Тебе следует ответить за свои проделки!",
["COMIC_4_9_KR5"] = "Но ты был прав.",
["COMIC_5_1_KR2"] = "Победа!",
["COMIC_5_1_KR5_KR5"] = "Вам, червям, не остановить...",
["COMIC_5_2_KR2"] = "Победа!",
["COMIC_5_2_KR5_KR5"] = "...НОВЫЙ МИР!",
["COMIC_5_6_KR5_KR5"] = "Оно проснулось!",
["COMIC_5_7a_KR5_KR5"] = "Что же, вот он...",
["COMIC_5_7b_KR5_KR5"] = "...последний бой.",
["COMIC_6_1a_KR5_KR5"] = "Вы храбры, чтобы бросать мне вызов...",
["COMIC_6_1b_KR5_KR5"] = "... однако, эта штука явно лишняя!",
["COMIC_6_4_KR5_KR5"] = "Эй!",
["COMIC_6_5_KR5_KR5"] = "Ты, космический слизень...",
["COMIC_6_6_KR5_KR5"] = "...не сомневайся в МОЕЙ силе!!!",
["COMIC_6_8_KR5_KR5"] = "Приготовьтесь. Я не смогу держать его вечно!",
["COMIC_7_1_KR5_KR5"] = "НЕТ!!! Этого не может... быть!!!",
["COMIC_7_3_KR5_KR5"] = "Итак... что теперь?",
["COMIC_7_4a_KR5_KR5"] = "Ну, моя миссия выполнена...",
["COMIC_7_4b_KR5_KR5"] = "...и я думаю, им нужен их король.",
["COMIC_7_5_2_KR2"] = "Не-а!",
["COMIC_7_6_KR5_KR5"] = "До встречи, дорогой враг.",
["COMIC_7_7_KR5_KR5"] = "Позже, в Вечносветлом Лесу...",
["COMIC_8_1_KR5_KR5"] = "Ах, наконец-то!",
["COMIC_8_2_KR5_KR5"] = "Это могущество вновь...",
["COMIC_8_4_KR5_KR5"] = "... МОЁ! ",
["COMIC_8_5_KR5_KR5"] = "МУА-ХА-ХА-ХА-ХА!",
["COMIC_9_1_KR5_KR5"] = "Не так давно нас, эльфов, почитали за нашу магию и стать...",
["COMIC_9_2_KR5_KR5"] = "...пока наша священная реликвия не была осквернена и мы не стали тенью самих себя.",
["COMIC_9_3_KR5_KR5"] = "Но с этой армией, я верну нам былую славу...",
["COMIC_9_4_KR5_KR5"] = "...и возглавлю новый мир, где правят эльфы!",
["COMIC_BALLOON_0002_KR1"] = "Победа!",
["COMIC_BALLOON_02_KR1"] = "Победа!",
["COMIC_balloon_0002_KR1"] = "Победа!",
["COMMAND YOUR TROOPS!"] = "КОМАНДУЙТЕ ВОЙСКАМИ!",
["CONFIRM_EXIT"] = "Выйти?",
["CONFIRM_RESTART"] = "Перезапустить?",
["CONTROLLER_STAGE_16_OVERSEER_DESCRIPTION"] = "Межпространственное чудовище, которое вторгается в другие миры и завоевывает их, чтобы поглотить их энергию. Должно быть остановлено любой ценой.",
["CONTROLLER_STAGE_16_OVERSEER_EXTRA"] = "- Меняет местами башни игрока\n- Призывает Взглядышей\n- Уничтожает стратегические точки",
["CONTROLLER_STAGE_16_OVERSEER_NAME"] = "Наблюдатель",
["CREDITS_COPYRIGHT"] = "© Ironhide Game Studio, 2014. Все права защищены.",
["CREDITS_POWERED_BY"] = "Создатели:",
["CREDITS_SUBTITLE_01"] = "(в алфавитном порядке)",
["CREDITS_SUBTITLE_07"] = "(в алфавитном порядке)",
["CREDITS_SUBTITLE_09"] = "(в алфавитном порядке)",
["CREDITS_SUBTITLE_16"] = "(в алфавитном порядке)",
["CREDITS_TEXT_18"] = "Родным, друзьям и преданным игрокам,",
["CREDITS_TEXT_18_2"] = "которые поддерживали нас все эти долгие годы.",
["CREDITS_TITLE_01"] = "Креативные директора и исполнительные продюсеры",
["CREDITS_TITLE_01_CREATIVE_DIRECTORS"] = "Творческие директора",
["CREDITS_TITLE_01_EXECUTIVE_PRODUCERS"] = "Исполнительные продюсеры",
["CREDITS_TITLE_02"] = "Ведущий гейм-дизайнер",
["CREDITS_TITLE_02_LEAD_GAME_DESIGNERS"] = "Ведущие гейм-дизайнеры",
["CREDITS_TITLE_03"] = "Гейм-дизайнеры",
["CREDITS_TITLE_03_GAME_DESIGNER"] = "Гейм-дизайнер",
["CREDITS_TITLE_04"] = "Сценарист",
["CREDITS_TITLE_04_STORY_WRITERS"] = "Сценаристы",
["CREDITS_TITLE_05"] = "Авторы текстов",
["CREDITS_TITLE_06"] = "Ведущий программист",
["CREDITS_TITLE_06_LEAD_PROGRAMMERS"] = "Ведущие программисты",
["CREDITS_TITLE_07"] = "Программисты",
["CREDITS_TITLE_08"] = "Главный художник",
["CREDITS_TITLE_09"] = "Художники",
["CREDITS_TITLE_10"] = "Художник комиксов",
["CREDITS_TITLE_11"] = "Автор комиксов",
["CREDITS_TITLE_12"] = "Технический дизайнер",
["CREDITS_TITLE_13"] = "Звукорежиссеры",
["CREDITS_TITLE_14"] = "Композитор",
["CREDITS_TITLE_15"] = "Озвучивание",
["CREDITS_TITLE_16"] = "Тестирование и контроль качества",
["CREDITS_TITLE_17"] = "Бета-тестирование",
["CREDITS_TITLE_18"] = "Особая благодарность",
["CREDITS_TITLE_19_PMO"] = "Управление проектами",
["CREDITS_TITLE_20_PRODUCER"] = "Продюсер",
["CREDITS_TITLE_21_MARKETING"] = "Маркетинг",
["CREDITS_TITLE_22_SPECIAL_COLLAB"] = "Особые сотрудники",
["CREDITS_TITLE_ANCIENT_HUNGER_UPDATE"] = "Древний Голод / Арахнофобия / Путешествие Укуна",
["CREDITS_TITLE_GAME_ENGINE_PROGRAMMER"] = "Программист игрового движка",
["CREDITS_TITLE_LOCALIZATION"] = "Локализация",
["CREDITS_TITLE_LOGO"] = "ИГРА",
["CRange0"] = "Небольшой",
["CRange1"] = "Средний",
["CRange2"] = "Большой",
["CRange3"] = "Существенный",
["CRange4"] = "Огромный",
["CReload0"] = "Очень медленно",
["CReload1"] = "Медленно",
["CReload2"] = "Средне",
["CReload3"] = "Быстро",
["CReload4"] = "Очень быстро",
["CSpeed0"] = "Медленно",
["CSpeed1"] = "Средне",
["CSpeed2"] = "Быстро",
["C_DIFFICULTY_EASY"] = "Новичок: пройдено",
["C_DIFFICULTY_HARD"] = "Ветеран: пройдено",
["C_DIFFICULTY_IMPOSSIBLE"] = "Безумец: пройдено",
["C_DIFFICULTY_NORMAL"] = "Боец: пройдено",
["C_REWARD"] = "Награда:",
["Campaign"] = "Кампания",
["Cancel"] = "Отмена",
["Casual"] = "Новичок",
["Challenge Rules"] = "Правила испытания",
["Clear_progress"] = "Очистить прогресс",
["Community Manager"] = "Менеджер сообщества",
["Credits"] = "Создатели",
["DAYS_ABBREVIATION"] = "д",
["DELETE SLOT?"] = "Удалить ячейку?",
["DIFFICULTY_SELECTION_EASY_DESCRIPTION"] = "Новичкам в стратегиях!",
["DIFFICULTY_SELECTION_HARD_DESCRIPTION"] = "Имейте в виду, это хардкор! Играйте на свой страх и риск!",
["DIFFICULTY_SELECTION_IMPOSSIBLE_DESCRIPTION"] = "Выживет только сильнейший!",
["DIFFICULTY_SELECTION_IMPOSSIBLE_LOCKED_DESCRIPTION"] = "Пройдите кампанию, чтобы открыть этот режим",
["DIFFICULTY_SELECTION_NORMAL_DESCRIPTION"] = "Это настоящий вызов!",
["DIFFICULTY_SELECTION_NOTE"] = "Вы всегда можете изменить уровень сложности при выборе этапа.",
["DIFFICULTY_SELECTION_TITLE"] = "Выберите уровень сложности!",
["DISCOUNT"] = "СКИДКА",
["DLC_OWNED"] = "КУПЛЕННЫЙ",
["Difficulty Level"] = "Уровень сложности",
["ELITE STAGE!"] = "ЭЛИТНЫЙ ЭТАП!",
["ENEMY_ACOLYTE_DESCRIPTION"] = "Низкорослые и покорные, Послушники полагаются на численное превосходство в бою.",
["ENEMY_ACOLYTE_EXTRA"] = "- Призывает щупальце при смерти",
["ENEMY_ACOLYTE_NAME"] = "Послушник Культа",
["ENEMY_ACOLYTE_SPECIAL"] = "Призывает щупальце при смерти",
["ENEMY_ACOLYTE_TENTACLE_DESCRIPTION"] = "В качестве последнего средства Послушники жертвуют свою жизнь Наблюдателю, порождая смертоносные щупальца.",
["ENEMY_ACOLYTE_TENTACLE_EXTRA"] = "- Появляется из мертвых Послушников.",
["ENEMY_ACOLYTE_TENTACLE_NAME"] = "Щупальце Послушника",
["ENEMY_AMALGAM_DESCRIPTION"] = "Чудовища, созданные из плоти и почвы Пустоты за Гранью. Несмотря на свою медлительность, эти гиганты несут хаос на поле боя.",
["ENEMY_AMALGAM_EXTRA"] = "- Мини-босс\n- Умирая, взрывается",
["ENEMY_AMALGAM_NAME"] = "Колосс Плоти",
["ENEMY_ANIMATED_ARMOR_DESCRIPTION"] = "Потрепанные реликвии битв давно минувших, теперь одержимые фантомами, что вновь ведут их в бой. ",
["ENEMY_ANIMATED_ARMOR_EXTRA"] = "- После разрушения может быть воскрешен фантомом",
["ENEMY_ANIMATED_ARMOR_NAME"] = "Одержимые Доспехи",
["ENEMY_ARMORED_NIGHTMARE_DESCRIPTION"] = "Облаченные в доспехи благодаря магии Культа, эти Кошмары сломя голову бросаются в битву. ",
["ENEMY_ARMORED_NIGHTMARE_EXTRA"] = "- Высокая броня\n- При уничтожении превращается в Кошмар",
["ENEMY_ARMORED_NIGHTMARE_NAME"] = "Закованный Кошмар",
["ENEMY_ARMORED_NIGHTMARE_SPECIAL"] = "При уничтожении превращается в Кошмар.",
["ENEMY_ASH_SPIRIT_DESCRIPTION"] = "Могущественные духи превратились в пугающих монстров, рождённых из лавы, пепла и скорби.",
["ENEMY_ASH_SPIRIT_EXTRA"] = "- Высокое здоровье\n- Высокая броня\n- Восстанавливает здоровье на пылающей земле",
["ENEMY_ASH_SPIRIT_NAME"] = "Ash Spirit",
["ENEMY_BALLOONING_SPIDER_DESCRIPTION"] = "Быстрые и хитрые пауки, умеющие избегать неприятностей.",
["ENEMY_BALLOONING_SPIDER_EXTRA"] = "- Начинает летать при встрече с солдатами\n- Средняя броня",
["ENEMY_BALLOONING_SPIDER_FLYER_DESCRIPTION"] = "Быстрые и хитрые пауки, умеющие избегать неприятностей.",
["ENEMY_BALLOONING_SPIDER_FLYER_EXTRA"] = "- Начинает летать при встрече с солдатами\n- Средняя броня",
["ENEMY_BALLOONING_SPIDER_FLYER_NAME"] = "Парящий Паук",
["ENEMY_BALLOONING_SPIDER_NAME"] = "Парящий Паук",
["ENEMY_BANE_WOLF_DESCRIPTION"] = "Искаженные волки, охотящиеся на тех, кому не хватает прыти сбежать.",
["ENEMY_BANE_WOLF_EXTRA"] = "- Ускоряется с получением урона",
["ENEMY_BANE_WOLF_NAME"] = "Гибельный Волк",
["ENEMY_BEAR_VANGUARD_DESCRIPTION"] = "Большие, широкоплечие и злобные, они кладут своих врагов пачками.",
["ENEMY_BEAR_VANGUARD_EXTRA"] = "- Высокая броня\n- Впадает в ярость, когда рядом умирает другой Медведь",
["ENEMY_BEAR_VANGUARD_NAME"] = "Медведь-авангардец",
["ENEMY_BEAR_VANGUARD_SPECIAL"] = "Впадает в бешенство, когда рядом умирает другой медведь.",
["ENEMY_BEAR_WOODCUTTER_DESCRIPTION"] = "Склонен засыпать на посту, но когда просыпается, дела становятся серьезными.",
["ENEMY_BEAR_WOODCUTTER_EXTRA"] = "- Высокая броня\n- Впадает в ярость, когда рядом умирает Медведь",
["ENEMY_BEAR_WOODCUTTER_NAME"] = "Медведь-лесоруб",
["ENEMY_BIG_TERRACOTA_DESCRIPTION"] = "Антропоморфный комок грязи, рожденный слиянием нескольких душ, движимых жаждой убийства.",
["ENEMY_BIG_TERRACOTA_EXTRA"] = "- Ближний бой",
["ENEMY_BIG_TERRACOTA_NAME"] = "Иллюзорная приманка монстра",
["ENEMY_BLAZE_RAIDER_DESCRIPTION"] = "Гордые и крепкие капитаны, посвящённые в Путь Огня, владеющие змеевидными копьями, чтобы перехитрить врагов.",
["ENEMY_BLAZE_RAIDER_EXTRA"] = "- Слабая броня\n- Особая атака на горящей земле",
["ENEMY_BLAZE_RAIDER_NAME"] = "Огненный Рейдер",
["ENEMY_BLINKER_DESCRIPTION"] = "С их пронзающим взглядом и крыльями, как у летучих мышей, Моргатели охотятся на ничего не подозревающих врагов.",
["ENEMY_BLINKER_EXTRA"] = "- Оглушает солдат игрока",
["ENEMY_BLINKER_NAME"] = "Моргатель Пустоты",
["ENEMY_BLINKER_SPECIAL"] = "Оглушает солдат игрока.",
["ENEMY_BOSS_BULL_KING_NAME"] = "Bull Demon King",
["ENEMY_BOSS_CORRUPTED_DENAS_NAME"] = "Оскверненный Денас",
["ENEMY_BOSS_CROCS_2_NAME"] = "Абоминор Веномус",
["ENEMY_BOSS_CROCS_3_NAME"] = "Абоминор Игнис",
["ENEMY_BOSS_CROCS_NAME"] = "Абоминор",
["ENEMY_BOSS_CULT_LEADER_NAME"] = "Провидица Мидриас",
["ENEMY_BOSS_DEFORMED_GRYMBEARD_NAME"] = "Обезображенный Гримбород",
["ENEMY_BOSS_GRYMBEARD_NAME"] = "Гримбород",
["ENEMY_BOSS_MACHINIST_NAME"] = "Гримбород",
["ENEMY_BOSS_NAVIRA_NAME"] = "Навира",
["ENEMY_BOSS_OVERSEER_NAME"] = "Наблюдатель",
["ENEMY_BOSS_PIG_NAME"] = "Кровожад",
["ENEMY_BOSS_PRINCESS_IRON_FAN_CLONE_NAME"] = "Клон Принцессы Железный Веер",
["ENEMY_BOSS_PRINCESS_IRON_FAN_NAME"] = "Princess Iron Fan",
["ENEMY_BOSS_REDBOY_TEEN_NAME"] = "Красный Мальчик",
["ENEMY_BOSS_SPIDER_QUEEN_NAME"] = "Мигэйл",
["ENEMY_BRUTE_WELDER_DESCRIPTION"] = "Эти работящие парни используют свои горелки на ничего не подозревающих врагах.",
["ENEMY_BRUTE_WELDER_EXTRA"] = "- Отключает башню после смерти",
["ENEMY_BRUTE_WELDER_NAME"] = "Дьявольский Сварщик",
["ENEMY_BURNING_TREANT_DESCRIPTION"] = "Деревянные создания со злыми намерениями, рожденные в пылающем лесу.",
["ENEMY_BURNING_TREANT_EXTRA"] = "- Урон по области\n- Оставляет пылающую землю при атаке",
["ENEMY_BURNING_TREANT_NAME"] = "Burning Treant",
["ENEMY_CITIZEN_1_DESCRIPTION"] = "Зловещие рыбаки, служащие Принцессе и пробирающиеся через чёрный рынок.",
["ENEMY_CITIZEN_1_EXTRA"] = "- Слабый",
["ENEMY_CITIZEN_1_NAME"] = "Старый торговец рыбой",
["ENEMY_CITIZEN_2_DESCRIPTION"] = "Зловещие рыбаки, служащие Принцессе, пробираются через чёрный рынок.",
["ENEMY_CITIZEN_2_EXTRA"] = "- Слабый",
["ENEMY_CITIZEN_2_NAME"] = "Рыбак из Блэквотера",
["ENEMY_CITIZEN_3_DESCRIPTION"] = "Зловещие рыбаки, служащие Принцессе и пробирающиеся через чёрный рынок.",
["ENEMY_CITIZEN_3_EXTRA"] = "- Слабый",
["ENEMY_CITIZEN_3_NAME"] = "Контрабандист чернил",
["ENEMY_CITIZEN_4_DESCRIPTION"] = "Зловещие рыбаки, служащие Принцессе и пробирающиеся через чёрный рынок.",
["ENEMY_CITIZEN_4_EXTRA"] = "- Слабый",
["ENEMY_CITIZEN_4_NAME"] = "Браконьер Прилива",
["ENEMY_COMMON_CLONE_DESCRIPTION"] = "Не примечательный, не уникальный, почти такой же, как и оригинал.",
["ENEMY_COMMON_CLONE_EXTRA"] = "- Бездумно наступает",
["ENEMY_COMMON_CLONE_NAME"] = "Клон",
["ENEMY_CORRUPTED_ELF_DESCRIPTION"] = "Поднятые из могил эльфы, что охотятся на врагов издалека. И в смерти они не знают промаха. ",
["ENEMY_CORRUPTED_ELF_EXTRA"] = "- Порождает фантома после смерти",
["ENEMY_CORRUPTED_ELF_NAME"] = "Ревенант-странник",
["ENEMY_CORRUPTED_STALKER_DESCRIPTION"] = "Облачные Охотники, прирученные Послушниками, теперь служащие ездовыми животными Культа.",
["ENEMY_CORRUPTED_STALKER_EXTRA"] = "- Летает",
["ENEMY_CORRUPTED_STALKER_NAME"] = "Прирученный Охотник",
["ENEMY_CORRUPTED_STALKER_SPECIAL"] = "Летает.",
["ENEMY_CROCS_BASIC_DESCRIPTION"] = "Гордый воин-крок, все еще в начале своего жизненного пути и всего в нескольких калориях от превращения в машину для убийства, которой он жаждет себя видеть. ",
["ENEMY_CROCS_BASIC_EGG_DESCRIPTION"] = "Новорожденные и неугомонные. Фраза \"они так быстро растут\" появилась благодаря этим полным сюрпризов малышам. ",
["ENEMY_CROCS_BASIC_EGG_EXTRA"] = "- Нельзя остановить\n- Низкая броня\n- Вырастает в Гатора через несколько секунд ",
["ENEMY_CROCS_BASIC_EGG_NAME"] = "Крошка-крок",
["ENEMY_CROCS_BASIC_EXTRA"] = "- Ближняя атака",
["ENEMY_CROCS_BASIC_NAME"] = "Гатор",
["ENEMY_CROCS_EGG_SPAWNER_DESCRIPTION"] = "Эта крокодилиха носит гнездо, полное неприятностей! Каждые несколько шагов она сбрасывает яйца, из которых вылупливаются орды Крошек-кроков. Это как передвижной детский сад, но только более кусачий!",
["ENEMY_CROCS_EGG_SPAWNER_EXTRA"] = "- Порождает Крошек-кроков",
["ENEMY_CROCS_EGG_SPAWNER_NAME"] = "Гнездящийся Гатор",
["ENEMY_CROCS_FLIER_DESCRIPTION"] = "Хитрые Кроки, которые в своем желании обмануть естественный отбор приделали себе крылья, чтобы получить преимущество в воздухе.",
["ENEMY_CROCS_FLIER_EXTRA"] = "- Летает",
["ENEMY_CROCS_FLIER_NAME"] = "Kрылатый Крок",
["ENEMY_CROCS_HYDRA_DESCRIPTION"] = "Одна голова – хорошо, а две – лучше, и гидры это доказывают. Существует старый миф о трехглавом звере, подобном этому, но он, вероятно, ложь.",
["ENEMY_CROCS_HYDRA_EXTRA"] = "- Отращивает третью голову при смерти\n- Выплевывает на тропу яд",
["ENEMY_CROCS_HYDRA_NAME"] = "Гидра",
["ENEMY_CROCS_QUICKFEET_GATOR_NAME"] = "Быстроног",
["ENEMY_CROCS_RANGED_DESCRIPTION"] = "Быстрые и ловкие ящерицы-охотники, которые расправляются с врагами на расстоянии с помощью рогаток.",
["ENEMY_CROCS_RANGED_EXTRA"] = "- Быстрая скорость\n- Дальняя атака",
["ENEMY_CROCS_RANGED_NAME"] = "Ящерострел",
["ENEMY_CROCS_SHAMAN_DESCRIPTION"] = "Волшебные создания, обладающие большой важностью среди Кроков. В конце концов, для хладнокровной расы возможность предвидеть превратности судьбы является вопросом жизни и смерти. ",
["ENEMY_CROCS_SHAMAN_EXTRA"] = " -Дальний магический урон\n- Высокое сопротивление магии\n- Исцеляет других Кроков\n- Оглушает башни",
["ENEMY_CROCS_SHAMAN_NAME"] = "Мудрый Крок",
["ENEMY_CROCS_TANK_DESCRIPTION"] = "Краеугольный камень сил Кроков с менталитетом \"лучшая защита – это нападение\". Они умыкнули парочку панцирей и стали использовать их, как они посчитали, наилучшим образом.",
["ENEMY_CROCS_TANK_EXTRA"] = "- Высокое здоровье\n- Высокая броня\n- Вращается при блокировании",
["ENEMY_CROCS_TANK_NAME"] = "Ящертанк",
["ENEMY_CRYSTAL_GOLEM_DESCRIPTION"] = "Пропитанные потусторонней магией из своих кристаллов, эти каменные изваяния почти несокрушимы.",
["ENEMY_CRYSTAL_GOLEM_EXTRA"] = "- Мини-босс\n- Очень высокая броня",
["ENEMY_CRYSTAL_GOLEM_NAME"] = "Кристальный Голем",
["ENEMY_CULTBROOD_DESCRIPTION"] = "Наполовину пауки, наполовину изуродованные культисты, они бросаются в бой без страха и жалости.",
["ENEMY_CULTBROOD_EXTRA"] = "- Быстрый\n- Ядовитая атака\n- Если враг умирает от яда, появляется новый Культорожденный",
["ENEMY_CULTBROOD_NAME"] = "Культорожденный",
["ENEMY_CUTTHROAT_RAT_DESCRIPTION"] = "Хитрые и коварные по своей натуре, крысы являются искусными убийцами и лазутчиками.",
["ENEMY_CUTTHROAT_RAT_EXTRA"] = "- Быстрая скорость\n- Становится невидимым после удара по врагу",
["ENEMY_CUTTHROAT_RAT_NAME"] = "Крыс-головорез",
["ENEMY_CUTTHROAT_RAT_SPECIAL"] = "Становится невидимым после удара по врагу.",
["ENEMY_DARKSTEEL_ANVIL_DESCRIPTION"] = "Гномья версия барабанов войны. Чем тяжелее наковальня, тем звучнее клич.",
["ENEMY_DARKSTEEL_ANVIL_EXTRA"] = "- Усиливает броню и скорость своим союзникам ",
["ENEMY_DARKSTEEL_ANVIL_NAME"] = "Наковальня Черностали",
["ENEMY_DARKSTEEL_FIST_DESCRIPTION"] = "Механически усовершенствован, чтобы гнуть металл, но вместо этого бъет всех вокруг.",
["ENEMY_DARKSTEEL_FIST_EXTRA"] = "- Особая атака оглушает солдат игрока",
["ENEMY_DARKSTEEL_FIST_NAME"] = "Кулак Черностали",
["ENEMY_DARKSTEEL_GUARDIAN_DESCRIPTION"] = "Прочные боевые экзоскелеты, управляемые воинами-гномами и питаемые огненными моторами. Вот что значит быть одетым сногсшибательно.",
["ENEMY_DARKSTEEL_GUARDIAN_EXTRA"] = "- Мини-босс\n- При низком здоровье приходит в ярость",
["ENEMY_DARKSTEEL_GUARDIAN_NAME"] = "Страж Черностали",
["ENEMY_DARKSTEEL_HAMMERER_DESCRIPTION"] = "Воины, столь же грубые, сколь предпочитаемое ими оружие.",
["ENEMY_DARKSTEEL_HAMMERER_EXTRA"] = " ",
["ENEMY_DARKSTEEL_HAMMERER_NAME"] = "Молотобоец Черностали",
["ENEMY_DARKSTEEL_HULK_DESCRIPTION"] = "Исступленные, с расплавленной сталью вместо крови в венах, они самые тяжелые среди гномов.",
["ENEMY_DARKSTEEL_HULK_EXTRA"] = "- На низком здоровье разбегается и таранит солдат, нанося урон ",
["ENEMY_DARKSTEEL_HULK_NAME"] = "Громила Черностали",
["ENEMY_DARKSTEEL_SHIELDER_DESCRIPTION"] = "Закрываясь за огромными щитами, они идут в наступление, расталкивая врагов на пути. ",
["ENEMY_DARKSTEEL_SHIELDER_EXTRA"] = "- После поражения превращается в Молотобойца",
["ENEMY_DARKSTEEL_SHIELDER_NAME"] = "Щитоносец Черностали",
["ENEMY_DEATHWOOD_DESCRIPTION"] = "Стоеросы, оскверненные злыми духами, что бродят по лесу, неся разрушение. ",
["ENEMY_DEATHWOOD_EXTRA"] = "- Мини-босс\n- Метает проклятые желуди, наносящие урон по площади",
["ENEMY_DEATHWOOD_NAME"] = "Смерторос",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_DESCRIPTION"] = "Плод неукротимого высокомерия Гримборода. Сила его ума соизмерима лишь с его уродством.",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_EXTRA"] = "- Летает\n- Магический щит",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_NAME"] = "Обезображенный Клон",
["ENEMY_DEMON_MINOTAUR_DESCRIPTION"] = "Полулюди-полубыки — гибридные демоны с сокрушительной атакой тараном. Они не знают пощады.",
["ENEMY_DEMON_MINOTAUR_EXTRA"] = "- Атака с разбега\n- Невозможно мгновенно убить",
["ENEMY_DEMON_MINOTAUR_NAME"] = "Demon Minotaur",
["ENEMY_DOOM_BRINGER_DESCRIPTION"] = "Грозные воины, несущие погибель любой ценой.",
["ENEMY_DOOM_BRINGER_EXTRA"] = "- Оглушает башни",
["ENEMY_DOOM_BRINGER_NAME"] = "Doombringer",
["ENEMY_DRAINBROOD_DESCRIPTION"] = "Древний паук с смертоносным укусом. Некоторые полагают, что именно он является главным виновником заражения кристаллами других пауков.",
["ENEMY_DRAINBROOD_EXTRA"] = "- Кристаллизует врагов, поглощая их жизненную силу",
["ENEMY_DRAINBROOD_NAME"] = "Паук-кровопийца",
["ENEMY_DREADEYE_VIPER_DESCRIPTION"] = "Покрывающие стрелы собственным ядом, Гадюки – смертельно опасные враги на расстоянии.",
["ENEMY_DREADEYE_VIPER_EXTRA"] = "- Низкая защита от магии\n- Ядовитые атаки",
["ENEMY_DREADEYE_VIPER_NAME"] = "Гадюка-смертострел",
["ENEMY_DREADEYE_VIPER_SPECIAL"] = "Атакует ядовитыми стрелами.",
["ENEMY_DUST_CRYPTID_DESCRIPTION"] = "Ранее – отрада для глаз, а теперь – кошмар во плоти для тех, кто забрел слишком далеко. ",
["ENEMY_DUST_CRYPTID_EXTRA"] = "- Летает\n- Оставляет облако пыльцы, защищающее врагов от урона",
["ENEMY_DUST_CRYPTID_NAME"] = "Пыльцевой Криптид",
["ENEMY_EVOLVING_SCOURGE_DESCRIPTION"] = "Они могут показаться почти милыми на первый взгляд, но если позволить Бедствиям полакомиться мертвой добычей, дела быстро пойдут наперекосяк.",
["ENEMY_EVOLVING_SCOURGE_EXTRA"] = "- Пожирает павших бойцов для эволюции в более сильную форму\n - Под воздействием Взгляда мгновенно переходит в финальную форму",
["ENEMY_EVOLVING_SCOURGE_NAME"] = "Растущее Бедствие",
["ENEMY_FAN_GUARD_DESCRIPTION"] = "Сильные и чрезвычайно универсальные воительницы, умеющие как причинять боль, так и защищаться с помощью своих магических вееров.",
["ENEMY_FAN_GUARD_EXTRA"] = "- Имеет среднюю броню и магическое сопротивление, пока не заблокирован.",
["ENEMY_FAN_GUARD_NAME"] = "Fan Guard",
["ENEMY_FIRE_FOX_DESCRIPTION"] = "Неуловимые и милые лисы, рожденные из огня. Слишком быстрые и непредсказуемые, чтобы их приручить.",
["ENEMY_FIRE_FOX_EXTRA"] = "- Низкое сопротивление магии\n- Быстрее на горящей земле\n- При смерти оставляет горящую землю",
["ENEMY_FIRE_FOX_NAME"] = "Огненная Лиса",
["ENEMY_FIRE_PHOENIX_DESCRIPTION"] = "Мифические летающие существа, питающиеся самим огнём. Они живут и умирают в пылающем пламени.",
["ENEMY_FIRE_PHOENIX_EXTRA"] = "- Летающий\n- При смерти оставляет горящую землю",
["ENEMY_FIRE_PHOENIX_NAME"] = "Zhuque",
["ENEMY_FLAME_GUARD_DESCRIPTION"] = "Стремясь заслужить одобрение своих наставников, эти низкоранговые ученики преуспевают в обращении с короткими клинками.",
["ENEMY_FLAME_GUARD_EXTRA"] = "- Особая атака на пылающей земле",
["ENEMY_FLAME_GUARD_NAME"] = "Страж Пламени",
["ENEMY_GALE_WARRIOR_DESCRIPTION"] = "Грациозные и стильные, эти воины были выбраны своей принцессой и готовы умереть за неё.",
["ENEMY_GALE_WARRIOR_EXTRA"] = "- Средняя броня\n- Вызывает кровотечение каждые 3 атаки",
["ENEMY_GALE_WARRIOR_NAME"] = "Gale Warrior",
["ENEMY_GLAREBROOD_CRYSTAL_NAME"] = "Кристалл Взглядорожденных",
["ENEMY_GLARELING_DESCRIPTION"] = "Если не отнестись к ним всерьез, эти мелкие твари одолеют даже самую сильную армию.",
["ENEMY_GLARELING_EXTRA"] = "- Высокая скорость",
["ENEMY_GLARELING_NAME"] = "Взглядыш",
["ENEMY_GLARENWARDEN_DESCRIPTION"] = "Эти мерзкие пауки – результат сращивания Взглядорожденных, сделавшего их сильнее и выносливее, чем когда-либо.",
["ENEMY_GLARENWARDEN_EXTRA"] = "- Высокая броня\n- Кража жизни при атаке",
["ENEMY_GLARENWARDEN_NAME"] = "Взглядохранитель",
["ENEMY_GOLDEN_EYED_DESCRIPTION"] = "Колоссальный зверь, чей рёв вселяет страх в сердца врагов.",
["ENEMY_GOLDEN_EYED_EXTRA"] = "- Мини-босс\n- Увеличивает скорость передвижения союзников",
["ENEMY_GOLDEN_EYED_NAME"] = "Golden-Eyed Beast",
["ENEMY_HARDENED_HORROR_DESCRIPTION"] = "Эта порода Ужасов обладает заточенными клинками вместо рук, которыми прорубает путь через тела врагов.",
["ENEMY_HARDENED_HORROR_EXTRA"] = "- Под воздействием Взгляда катится на высокой скорости и не может быть заблокирован",
["ENEMY_HARDENED_HORROR_NAME"] = "Саблерукий Ужас",
["ENEMY_HELLFIRE_WARLOCK_DESCRIPTION"] = "Крайне опасные колдуны, мастера по призыву существ и пламени из глубин ада.",
["ENEMY_HELLFIRE_WARLOCK_EXTRA"] = "- Бросает огненные шары\n- Призывает Девятихвостую Лису",
["ENEMY_HELLFIRE_WARLOCK_NAME"] = "Hellfire Warlock",
["ENEMY_HOG_INVADER_DESCRIPTION"] = "Грязные и неорганизованные нарушители спокойствия. Основная масса армии диких зверей.",
["ENEMY_HOG_INVADER_EXTRA"] = "- Низкое здоровье",
["ENEMY_HOG_INVADER_NAME"] = "Кабан-захватчик",
["ENEMY_HYENA5_DESCRIPTION"] = "Ужасные бойцы, которые любят пировать на костях павших врагов.",
["ENEMY_HYENA5_EXTRA"] = "- Средняя броня\n- Лечится, поедая павших солдат",
["ENEMY_HYENA5_NAME"] = "Гиена-гнилоклык",
["ENEMY_HYENA5_SPECIAL"] = "Исцеляется, поедая убитых солдат.",
["ENEMY_KILLERTILE_DESCRIPTION"] = "Могучие крушители, развившие навыки смертельного укуса благодаря годам боевого опыта (или курице).",
["ENEMY_KILLERTILE_EXTRA"] = "- Высокое здоровье\n- Высокий урон",
["ENEMY_KILLERTILE_NAME"] = "Смертилия",
["ENEMY_LESSER_EYE_DESCRIPTION"] = "Злые глаза, парящие над полем боя и действующие в качестве разведчиков Мерзких Рассадников.",
["ENEMY_LESSER_EYE_EXTRA"] = "- Летает",
["ENEMY_LESSER_EYE_NAME"] = "Малый Глаз",
["ENEMY_LESSER_SISTER_DESCRIPTION"] = "Благодаря своей зловещей магии Коварные Сестры впускают Кошмары в материальный мир.",
["ENEMY_LESSER_SISTER_EXTRA"] = "- Высокая защита от магии\n- Призывает Кошмары ",
["ENEMY_LESSER_SISTER_NAME"] = "Коварная Сестра",
["ENEMY_LESSER_SISTER_NIGHTMARE_DESCRIPTION"] = "Бесплотные тени, сотканные книгой заклинаний Сестер Культа.",
["ENEMY_LESSER_SISTER_NIGHTMARE_EXTRA"] = "- Не может быть выбран в качестве цели, если не блокируется в ближнем бою",
["ENEMY_LESSER_SISTER_NIGHTMARE_NAME"] = "Кошмар",
["ENEMY_LESSER_SISTER_SPECIAL"] = "Призывает Кошмары",
["ENEMY_MACHINIST_DESCRIPTION"] = "Одержимый шестеренками и турбинами, этот гном сделал промышленную автоматизацию и войну смыслом своей жизни.",
["ENEMY_MACHINIST_EXTRA"] = "- Управляет конвейером, который создает Часовых",
["ENEMY_MACHINIST_NAME"] = "Гримбород",
["ENEMY_MAD_TINKERER_DESCRIPTION"] = "Жестянщики не особо заботятся о чем либо, помимо сборки всякой ерунды из мусора и палок.",
["ENEMY_MAD_TINKERER_EXTRA"] = "- Создает дронов из металлолома, оставленного другими врагами",
["ENEMY_MAD_TINKERER_NAME"] = "Безумный Жестянщик",
["ENEMY_MINDLESS_HUSK_DESCRIPTION"] = "Из-за своего внешнего вида Оболочки кажутся слабыми врагами, однако внутри каждой из них таится сюрприз.",
["ENEMY_MINDLESS_HUSK_EXTRA"] = "- Оставляет Взглядыша после смерти",
["ENEMY_MINDLESS_HUSK_NAME"] = "Безмозглая Оболочка",
["ENEMY_NINE_TAILED_FOX_DESCRIPTION"] = "Таинственные создания, красивые и могущественные. Пронесутся сквозь врагов, как бушующий костёр.",
["ENEMY_NINE_TAILED_FOX_EXTRA"] = "- Среднее сопротивление магии\n- Телепортируется вперёд, оглушая врагов по прибытии\n- Урон по области",
["ENEMY_NINE_TAILED_FOX_NAME"] = "Девятихвостый Лис",
["ENEMY_NOXIOUS_HORROR_DESCRIPTION"] = "Похожие на амфибий существа, плюющие ядовитой желчью в свою добычу. Также опасны вблизи.",
["ENEMY_NOXIOUS_HORROR_EXTRA"] = "- Под воздействием Взгляда получает сопротивление магии и ядовитую ауру",
["ENEMY_NOXIOUS_HORROR_NAME"] = "Ядовитый Плевун",
["ENEMY_PALACE_GUARD_DESCRIPTION"] = "Малоодарённые новобранцы, чья единственная мотивация — исполнить желания своей Принцессы.",
["ENEMY_PALACE_GUARD_EXTRA"] = "- Ближний бой\n- Лёгкая броня",
["ENEMY_PALACE_GUARD_NAME"] = "Дворцовая стража",
["ENEMY_PUMPKIN_WITCH_DESCRIPTION"] = "Враг, превращенный в Тыквика. Легко давится.",
["ENEMY_PUMPKIN_WITCH_EXTRA"] = "- Нельзя остановить",
["ENEMY_PUMPKIN_WITCH_FLYING_DESCRIPTION"] = "Враг, превращенный в Тыквика. Легко давится.",
["ENEMY_PUMPKIN_WITCH_FLYING_EXTRA"] = "- Нельзя остановить",
["ENEMY_PUMPKIN_WITCH_FLYING_NAME"] = "Тыквик",
["ENEMY_PUMPKIN_WITCH_NAME"] = "Тыквик",
["ENEMY_QIONGQI_DESCRIPTION"] = "Свирепые летающие львы, атакующие с силой молнии. Короли бури.",
["ENEMY_QIONGQI_EXTRA"] = "- Летающий\n- Очень высокий урон\n- Среднее сопротивление магии",
["ENEMY_QIONGQI_NAME"] = "Цюнци",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_DESCRIPTION"] = "После долгих лет доставок куриц своим собратьям они стали настолько быстрыми, что иногда даже забывают принести курицу.",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_EXTRA"] = "- Быстрая скорость\n- Дальняя атака\n-  Может кинуть куриную ногу Гатору, позволяя ему эволюционировать",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_NAME"] = "Быстроног",
["ENEMY_QUICKFEET_GATOR_DESCRIPTION"] = "После долгих лет доставок куриц своим собратьям они стали настолько быстрыми, что иногда даже забывают принести курицу.",
["ENEMY_QUICKFEET_GATOR_EXTRA"] = "- Быстрая скорость\n- Дальняя атака\n-  Может кинуть куриную ногу Гатору, позволяя ему эволюционировать",
["ENEMY_QUICKFEET_GATOR_NAME"] = "Быстроног",
["ENEMY_REVENANT_HARVESTER_DESCRIPTION"] = "Жрицы древности, скитающиеся по лесам и распространяющие свое слово через духов.",
["ENEMY_REVENANT_HARVESTER_EXTRA"] = "- Превращает ближайших фантомов в Жниц",
["ENEMY_REVENANT_HARVESTER_NAME"] = "Ревенант-жница",
["ENEMY_REVENANT_SOULCALLER_DESCRIPTION"] = "Эльфийские мистики, наполненные магией смерти, восставшие из земли, чтобы призывать души умерших.",
["ENEMY_REVENANT_SOULCALLER_EXTRA"] = "- Отключает башни\n- Призывает фантомов",
["ENEMY_REVENANT_SOULCALLER_NAME"] = "Ревенант-духовод",
["ENEMY_RHINO_DESCRIPTION"] = "Живой таран, без оглядки несущийся по полю боя.",
["ENEMY_RHINO_EXTRA"] = "- Мини-босс\n- Таранит солдат игрока",
["ENEMY_RHINO_NAME"] = "Носорог-разрушитель",
["ENEMY_RHINO_SPECIAL"] = "Таранит солдат.",
["ENEMY_ROLLING_SENTRY_DESCRIPTION"] = "Даже будучи сбитыми, они продолжают охотиться на земле.",
["ENEMY_ROLLING_SENTRY_EXTRA"] = "- После уничтожения превращается в металлолом\n- Дальняя атака",
["ENEMY_ROLLING_SENTRY_NAME"] = "Катящийся Часовой",
["ENEMY_SCRAP_DRONE_DESCRIPTION"] = "Наскоро собранная штуковина, с единственной целью – мешать врагам.",
["ENEMY_SCRAP_DRONE_EXTRA"] = "- Летает",
["ENEMY_SCRAP_DRONE_NAME"] = "Ломовой Дрон",
["ENEMY_SCRAP_SPEEDSTER_DESCRIPTION"] = "Громкие и надоедливые, с жаждой скорости",
["ENEMY_SCRAP_SPEEDSTER_EXTRA"] = "- После уничтожения превращается в металлолом",
["ENEMY_SCRAP_SPEEDSTER_NAME"] = "Ломовой Бегун",
["ENEMY_SKUNK_BOMBARDIER_DESCRIPTION"] = "Выводя свои природные токсины на новый уровень, скунсы несут хаос во вражеские ряды.",
["ENEMY_SKUNK_BOMBARDIER_EXTRA"] = "- Низкая скорость\n- Среднее сопротивление магии\n- Атаки ослабляют солдат игрока.\n- Умирая, взрывается.",
["ENEMY_SKUNK_BOMBARDIER_NAME"] = "Скунс-бомбардир",
["ENEMY_SKUNK_BOMBARDIER_SPECIAL"] = "Атаки ослабляют солдат игрока. Взрывается при смерти, нанося урон.",
["ENEMY_SMALL_STALKER_DESCRIPTION"] = "Оскверненные магией Культа, эти Облачные Охотники телепортируются по полю боя и сеют хаос.",
["ENEMY_SMALL_STALKER_EXTRA"] = "- Получая урон, телепортируется вперед",
["ENEMY_SMALL_STALKER_NAME"] = "Оскверненный Охотник",
["ENEMY_SMALL_STALKER_SPECIAL"] = "Телепортируется на короткое расстояние, избегая атак.",
["ENEMY_SPECTER_DESCRIPTION"] = "Рабы даже после истления их тела, обреченные преследовать живых. ",
["ENEMY_SPECTER_EXTRA"] = "- Взаимодействует с другими врагами и объектами",
["ENEMY_SPECTER_NAME"] = "Фантом",
["ENEMY_SPIDEAD_DESCRIPTION"] = "Прямые потомки Паучьей Королевы Мигэйл, эти пауки всегда находят способ раздражать – даже после смерти.",
["ENEMY_SPIDEAD_EXTRA"] = "- Высокое сопротивление магии\n- При смерти создаёт паутину",
["ENEMY_SPIDEAD_NAME"] = "Прядущая Дочь",
["ENEMY_SPIDERLING_DESCRIPTION"] = "Пауки, усиленные магией Культа. Быстрые и яростные. Кусаются.",
["ENEMY_SPIDERLING_EXTRA"] = "- Высокая скорость\n- Низкое сопротивление магии",
["ENEMY_SPIDERLING_NAME"] = "Взглядорожденный",
["ENEMY_SPIDER_PRIEST_DESCRIPTION"] = "Опутанные \"заботой\" их нового бога, жрецы вступают в бой, пуская в ход темную магию.",
["ENEMY_SPIDER_PRIEST_EXTRA"] = "- Высокая защита от магии\n- Превращается во Взглядохранителя на пороге смерти",
["ENEMY_SPIDER_PRIEST_NAME"] = "Жрец Паутины",
["ENEMY_SPIDER_SISTER_DESCRIPTION"] = "Будучи преданными последователями Паучьей Королевы, они используют магию, чтобы призывать ее потомство.",
["ENEMY_SPIDER_SISTER_EXTRA"] = "- Высокое сопротивление магии\n- Призывает Взглядорожденных",
["ENEMY_SPIDER_SISTER_NAME"] = "Паучья Сестра",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_DESCRIPTION"] = "Теневые двойники, которых Мидриас использует для вмешательства в битву.",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_EXTRA"] = "- Защищает врагов от урона\n- Захватывает башни темными щупальцами",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_NAME"] = "Иллюзия Мидриас",
["ENEMY_STORM_ELEMENTAL_DESCRIPTION"] = "Могущественные элементали, порождённые тайфунами, молниями и гневом. Дальний родственник Пепельного Духа.",
["ENEMY_STORM_ELEMENTAL_EXTRA"] = "- Высокая броня\n- Дальний бой\n- Оглушает ближайшую башню при смерти",
["ENEMY_STORM_ELEMENTAL_NAME"] = "Дух Бури",
["ENEMY_STORM_SPIRIT_DESCRIPTION"] = "Маленькие драконы скачут сквозь грозовые облака, ловко уклоняясь от опасностей и врагов.",
["ENEMY_STORM_SPIRIT_EXTRA"] = "- Летающий\n- Низкое сопротивление магии\n- Бросается вперёд при получении урона",
["ENEMY_STORM_SPIRIT_NAME"] = "Штормовой Дракелинг",
["ENEMY_SURVEILLANCE_SENTRY_DESCRIPTION"] = "Гномья конструкция, созданная, чтобы следить за врагами с высоты.",
["ENEMY_SURVEILLANCE_SENTRY_EXTRA"] = "- Летает\n- После уничтожения превращается в Катящегося Часового",
["ENEMY_SURVEILLANCE_SENTRY_NAME"] = "Летающий Часовой",
["ENEMY_SURVEYOR_HARPY_DESCRIPTION"] = "В поисках падали стервятники следуют за Дикозверьми повсюду.",
["ENEMY_SURVEYOR_HARPY_EXTRA"] = "- Летает",
["ENEMY_SURVEYOR_HARPY_NAME"] = "Стервятник-патрульный",
["ENEMY_SURVEYOR_HARPY_SPECIAL"] = "Летает.",
["ENEMY_TERRACOTA_DESCRIPTION"] = "Материализованные тени, служащие отвлечением.",
["ENEMY_TERRACOTA_EXTRA"] = "- Ближний бой",
["ENEMY_TERRACOTA_NAME"] = "Иллюзорная приманка",
["ENEMY_TOWER_RAY_SHEEP_DESCRIPTION"] = "Бееееее.",
["ENEMY_TOWER_RAY_SHEEP_EXTRA"] = "- Неблокируема",
["ENEMY_TOWER_RAY_SHEEP_FLYING_DESCRIPTION"] = "Бееееее.",
["ENEMY_TOWER_RAY_SHEEP_FLYING_EXTRA"] = "- Летает",
["ENEMY_TOWER_RAY_SHEEP_FLYING_NAME"] = "Летающая Овца",
["ENEMY_TOWER_RAY_SHEEP_NAME"] = "Овца",
["ENEMY_TURTLE_SHAMAN_DESCRIPTION"] = "Мирные на вид, но гнилые душой, шаманы поддерживают Дикозверей невредимыми и готовыми к бою.",
["ENEMY_TURTLE_SHAMAN_EXTRA"] = "- Низкая скорость\n- Высокое здоровье\n- Высокая защита от магии\n- Лечит врагов",
["ENEMY_TURTLE_SHAMAN_NAME"] = "Черепаха-шаман",
["ENEMY_TURTLE_SHAMAN_SPECIAL"] = "Лечит врагов.",
["ENEMY_TUSKED_BRAWLER_DESCRIPTION"] = "Более крепкие, нежели захватчики, и носящие хлипкую броню. Всегда готовы к хорошей драке.",
["ENEMY_TUSKED_BRAWLER_EXTRA"] = "- Низкая броня",
["ENEMY_TUSKED_BRAWLER_NAME"] = "Клыкастый Борец",
["ENEMY_UNBLINDED_ABOMINATION_DESCRIPTION"] = "Полностью развращенные жрецы Культа, известные своей свирепостью в бою.",
["ENEMY_UNBLINDED_ABOMINATION_EXTRA"] = "- Пожирает бойцов с низким уровнем здоровья",
["ENEMY_UNBLINDED_ABOMINATION_NAME"] = "Отродье Культа",
["ENEMY_UNBLINDED_ABOMINATION_SPECIAL"] = "Может поглотить солдата с низким уровнем здоровья.",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_DESCRIPTION"] = "После порабощения эльфов некоторые Отродья были назначены следить за тем, чтобы работа в шахтах шла гладко.",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_EXTRA"] = "- Убейте, чтобы освободить эльфов",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_NAME"] = "Отродье-надсмотрщик",
["ENEMY_UNBLINDED_PRIEST_DESCRIPTION"] = "Между занятиями молитвами и оккультизмом, жрецы вступают в бой, пуская в ход темную магию.",
["ENEMY_UNBLINDED_PRIEST_EXTRA"] = "- Высокая защита от магии\n- Превращается в Отродье на пороге смерти",
["ENEMY_UNBLINDED_PRIEST_NAME"] = "Жрец Культа",
["ENEMY_UNBLINDED_PRIEST_SPECIAL"] = "При низком уровне здоровья превращается в Отродье.",
["ENEMY_UNBLINDED_SHACKLER_DESCRIPTION"] = "Повелевающие оскверненной магией с помощью кристаллов, вросших в их руки, Оковщики – страшные враги в ближнем бою.",
["ENEMY_UNBLINDED_SHACKLER_EXTRA"] = "- Среднее сопротивление магии\n- Сковывает башни при низком здоровье",
["ENEMY_UNBLINDED_SHACKLER_NAME"] = "Оковщик",
["ENEMY_UNBLINDED_SHACKLER_SPECIAL"] = "Сковывает башни, не позволяя им атаковать.",
["ENEMY_VILE_SPAWNER_DESCRIPTION"] = "Устремляя свои многочисленные летающие глаза на врагов, Мерзкие Рассадники способны видеть везде и всюду.",
["ENEMY_VILE_SPAWNER_EXTRA"] = "- Призывает Малые Глаза",
["ENEMY_VILE_SPAWNER_NAME"] = "Мерзкий Рассадник",
["ENEMY_WATER_SORCERESS_DESCRIPTION"] = "Видавшие виды заклинатели стихий, использующие силу воды, чтобы лечить союзников и поражать врагов издали.",
["ENEMY_WATER_SORCERESS_EXTRA"] = "- Дальний бой\n- Средняя магическая сопротивляемость\n- Лечит союзников",
["ENEMY_WATER_SORCERESS_NAME"] = "Water Master",
["ENEMY_WATER_SPIRIT_DESCRIPTION"] = "Бездушные водные сущности накатывают неумолимыми волнами, яростно разоряя побережья.",
["ENEMY_WATER_SPIRIT_EXTRA"] = "- Низкая магическая сопротивляемость\n- Может появляться из воды",
["ENEMY_WATER_SPIRIT_NAME"] = "Water Spirit",
["ENEMY_WATER_SPIRIT_SPAWNLESS_DESCRIPTION"] = "Бездушные водные сущности накатывают неумолимыми волнами, яростно разоряя побережья.",
["ENEMY_WATER_SPIRIT_SPAWNLESS_EXTRA"] = "- Низкая магическая сопротивляемость\n- Может появляться из воды",
["ENEMY_WATER_SPIRIT_SPAWNLESS_NAME"] = "Water Spirit",
["ENEMY_WUXIAN_DESCRIPTION"] = "Могущественные и выносливые волшебники, уничтожающие своих врагов магией.",
["ENEMY_WUXIAN_EXTRA"] = "- Дальний бой\n- Средняя броня\n- Специальная атака на пылающей земле",
["ENEMY_WUXIAN_NAME"] = "Wuxian",
["ERROR_MESSAGE_GENERIC"] = "Ой! Что-то пошло не так.",
["Earn huge bonus points and gold by calling waves earlier!"] = "Получайте дополнительные очки и золото, призывая волны раньше!",
["FIRST_WEEK_PACK"] = "Подарок ",
["FULLADS_BONUS_REWARDS_TITLE"] = "БОНУСНЫЕ НАГРАДЫ!",
["FULLADS_BUTTON_BUY"] = "КУПИТЬ",
["FULLADS_BUTTON_CLAIM"] = "ПОСМОТРИТЕ РЕКЛАМУ И ПОЛУЧИТЕ ЭТИ НАГРАДЫ!",
["FULLADS_BUTTON_CLAIM_SHORT"] = "ПОЛУЧИТЬ!",
["FULLADS_BUTTON_HIRE"] = "НАНЯТЬ",
["FULLADS_BUTTON_INFO"] = "ИНФОРМАЦИЯ",
["FULLADS_BUTTON_PLAY"] = "ПОСМОТРИТЕ РЕКЛАМУ И ПОЛУЧИТЕ ЭТИ НАГРАДЫ!",
["FULLADS_BUTTON_PLAY_SHORT"] = "ИГРАТЬ!",
["FULLADS_BUTTON_SPIN"] = "СМОТРИТЕ РЕКЛАМУ, ЧТОБЫ ПОКРУТИТЬ КОЛЕСО!",
["FULLADS_BUTTON_SPIN_SHORT"] = "КРУТИТЬ!",
["FULLADS_BUTTON_UNLOCK"] = "ОТКРЫТЬ",
["FULLADS_DEFEAT_ENDLESS_REWARDS_TITLE"] = "УТЕШИТЕЛЬНЫЙ ПРИЗ!",
["FULLADS_DEFEAT_REWARDS_TITLE"] = "УТЕШИТЕЛЬНЫЙ ПРИЗ!",
["FULLADS_GNOME_REWARDS_TITLE"] = "НАГРАДЫ ГНОМА!",
["FULLADS_MAP_CROWNS_DESCRIPTION"] = "Используйте короны, чтобы нанять героя на день.",
["FULLADS_MAP_GEMS_DESCRIPTION"] = "Используйте кристаллы, чтобы покупать предметы и открывать героев навсегда.",
["FULLADS_MAP_HEROROOM_HELP_CROWNS"] = "Нанять на один день",
["FULLADS_MAP_HEROROOM_HELP_GEMS"] = "Купить навсегда",
["FULLADS_MAP_STARS_DESCRIPTION"] = "Используйте звезды, чтобы покупать улучшения.",
["FULLADS_VICTORY_CLAIM_BONUS"] = "СМОТРЕТЬ РЕКЛАМУ ЗА БОНУС: %sX",
["FULLADS_VICTORY_REWARDS_TITLE"] = "НАГРАДЫ ЗА ПОБЕДУ!",
["FULLADS_WHEEL_PROBABILITIES_TITLE"] = "Вероятность вознаграждения",
["FULLADS_WHEEL_REWARDS_TITLE"] = "КОЛЕСО ФОРТУНЫ!",
["FULLADS_YOUR_REWARDS_TITLE"] = "ВАШИ НАГРАДЫ!",
["FULLADS_YOUR_REWARD_TITLE"] = "ВАША НАГРАДА!",
["Face an endless unrelenting enemy force and try to defeat as many as possible to comete for the best score!"] = "Сразитесь с бесчисленными полчищами врагов и постарайтесь убить как можно больше в борьбе за лучший результат!",
["Face an endless unrelenting enemy force and try to defeat as many as possible to compete for the best score!"] = "Сразитесь с бесчисленными полчищами врагов и постарайтесь убить как можно больше в борьбе за лучший результат!",
["Failed to load Rewarded Video, first session !"] = "Не удалось загрузить видео",
["Failed to load Rewarded Video, internal error !"] = "Не удалось загрузить видео",
["Failed to load Rewarded Video, missing location parameter !"] = "Не удалось загрузить видео",
["Failed to load Rewarded Video, network error !"] = "Не удалось загрузить видео",
["Failed to load Rewarded Video, no Internet connection !"] = "Не удалось загрузить видео",
["Failed to load Rewarded Video, no ad found !"] = "Не удалось загрузить видео",
["Failed to load Rewarded Video, session not started !"] = "Не удалось загрузить видео",
["Failed to load Rewarded Video, too many connections !"] = "Не удалось загрузить видео",
["Failed to load Rewarded Video, unknown error !"] = "Не удалось загрузить видео",
["Failed to load Rewarded Video, wrong orientation !"] = "Не удалось загрузить видео",
["GAME PAUSED"] = "ПАУЗА",
["GAME_TITLE_KR5"] = "Королевская Лихорадка 5: Альянс",
["GEMS_BARREL_NAME"] = "БОЧОНОК КРИСТАЛЛОВ",
["GEMS_CHEST_NAME"] = "СУНДУК КРИСТАЛЛОВ",
["GEMS_HANDFUL_NAME"] = "ПРИГОРШНЯ КРИСТАЛЛОВ",
["GEMS_MOUNTAIN_NAME"] = "ГОРА КРИСТАЛЛОВ",
["GEMS_POUCH_NAME"] = "МЕШОК КРИСТАЛЛОВ",
["GEMS_WAGON_NAME"] = "ТЕЛЕГА КРИСТАЛЛОВ",
["GET_ALL_AWESOME_HEROES"] = "ПОЛУЧИТЕ ВСЕХ КРУТЫХ ГЕРОЕВ",
["GET_THIS_AWESOME"] = "ПОЛУЧИТЕ ЭТОГО\nКРУТОГО ГЕРОЯ",
["GET_THIS_AWESOME_2"] = "ПОЛУЧИТЕ ЭТИХ\nКРУТЫХ ГЕРОЕВ",
["GET_THIS_AWESOME_3"] = "ПОЛУЧИТЕ ЭТИХ\nКРУТЫХ ГЕРОЕВ",
["GIFT_CLAIMED"] = "Подарок получен! ",
["GOOGLE_PLAY"] = "GOOGLE PLAY",
["Got it!"] = "Ясно!",
["HERO LEVEL UP!"] = "УРОВЕНЬ ГЕРОЯ ПОВЫШЕН!",
["HERO ROOM"] = "ГЕРОИ",
["HERO UNLOCKED!"] = "ОТКРЫТ ГЕРОЙ!",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_1"] = "Призывает грифонов, которые кружат над областью в течение %$heroes.hero_bird.ultimate.bird.duration[2]%$ секунд, атакуя врагов и нанося %$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[2]%$ урона за удар.",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_2"] = "Призывает грифонов, которые кружат над областью в течение %$heroes.hero_bird.ultimate.bird.duration[3]%$ секунд, атакуя врагов и нанося %$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[3]%$ урона за удар.",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_3"] = "Призывает грифонов, которые кружат над областью в течение %$heroes.hero_bird.ultimate.bird.duration[4]%$ секунд, атакуя врагов и нанося %$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[4]%$ урона за удар.",
["HERO_BIRD_BIRDS_OF_PREY_MENUBOTTOM_DESCRIPTION"] = "Призывает грифонов, которые летают над территорией, атакуя врагов.",
["HERO_BIRD_BIRDS_OF_PREY_MENUBOTTOM_NAME"] = "Хищные Птицы",
["HERO_BIRD_BIRDS_OF_PREY_TITLE"] = "ХИЩНЫЕ ПТИЦЫ",
["HERO_BIRD_CLASS"] = "Мастер-наездник",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_1"] = "Подрывает над головами врагов взрывчатку, нанося каждому %$heroes.hero_bird.cluster_bomb.explosion_damage_min[1]%$ урона и поджигая землю на %$heroes.hero_bird.cluster_bomb.fire_duration[1]%$ секунд. Горение наносит %$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$ урона в течение 3 секунд.",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_2"] = "Подрывает над головами врагов взрывчатку, нанося каждому %$heroes.hero_bird.cluster_bomb.explosion_damage_min[2]%$ урона и поджигая землю на %$heroes.hero_bird.cluster_bomb.fire_duration[2]%$ секунд. Горение наносит %$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$ урона в течение 3 секунд.",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_3"] = "Подрывает над головами врагов взрывчатку, нанося каждому %$heroes.hero_bird.cluster_bomb.explosion_damage_min[3]%$ урона и поджигая землю на %$heroes.hero_bird.cluster_bomb.fire_duration[3]%$ секунд. Горение наносит %$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$ урона в течение 3 секунд.",
["HERO_BIRD_CLUSTER_BOMB_TITLE"] = "КОВРОВАЯ БОМБАРДИРОВКА",
["HERO_BIRD_DESC"] = "Отважный всадник на грифоне лавиной стали и огня влетает в бой. Хотя Броден с неохотой присоединился к Альянсу после того, как Армия Тьмы вторглась в его дом, он согласился помочь в уничтожении Культа, чтобы восстановить статус-кво в Линерии.",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_1"] = "Грифон пикирует на землю, поглощая врага со здоровьем до %$heroes.hero_bird.eat_instakill.hp_max[1]%$ .",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_2"] = "Грифон пикирует на землю, поглощая врага со здоровьем до %$heroes.hero_bird.eat_instakill.hp_max[2]%$ .",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_3"] = "Грифон пикирует на землю, поглощая врага со здоровьем до %$heroes.hero_bird.eat_instakill.hp_max[3]%$ .",
["HERO_BIRD_EAT_INSTAKILL_TITLE"] = "ОХОТНИЧЬЕ ПИКЕ",
["HERO_BIRD_GATTLING_DESCRIPTION_1"] = "Накрывает врага ураганом пуль, нанося %$heroes.hero_bird.gattling.s_damage_min[1]%$-%$heroes.hero_bird.gattling.s_damage_max[1]%$ физического урона.",
["HERO_BIRD_GATTLING_DESCRIPTION_2"] = "Накрывает врага ураганом пуль, нанося %$heroes.hero_bird.gattling.s_damage_min[2]%$-%$heroes.hero_bird.gattling.s_damage_max[2]%$ физического урона.",
["HERO_BIRD_GATTLING_DESCRIPTION_3"] = "Накрывает врага ураганом пуль, нанося %$heroes.hero_bird.gattling.s_damage_min[3]%$-%$heroes.hero_bird.gattling.s_damage_max[3%$ физического урона.",
["HERO_BIRD_GATTLING_TITLE"] = "ПОКАЗАТЕЛЬНЫЙ РАССТРЕЛ",
["HERO_BIRD_NAME"] = "Броден",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_1"] = "Грифон издает оглушительный крик, парализуя врагов на %$heroes.hero_bird.shout_stun.stun_duration[1]%$ секунду и замедляя их на %$heroes.hero_bird.shout_stun.slow_duration[1]%$ секунды после этого.",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_2"] = "Грифон издает оглушительный крик, парализуя врагов на %$heroes.hero_bird.shout_stun.stun_duration[2]%$ секунды и замедляя их на %$heroes.hero_bird.shout_stun.slow_duration[2]%$ секунды после этого.",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_3"] = "Грифон издает оглушительный крик, парализуя врагов на %$heroes.hero_bird.shout_stun.stun_duration[3]%$ секунды и замедляя их на %$heroes.hero_bird.shout_stun.slow_duration[3]%$ секунд после этого.",
["HERO_BIRD_SHOUT_STUN_TITLE"] = "УЖАСАЮЩИЙ КРИК",
["HERO_BUILDER_CLASS"] = "Главный Прораб",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_1"] = "Строит временную башню, которая атакует проходящих врагов в течение %$heroes.hero_builder.defensive_turret.duration[1]%$ секунд, нанося %$heroes.hero_builder.defensive_turret.attack.damage_min[1]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[1]%$ физического урона за атаку.",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_2"] = "Строит временную башню, которая атакует проходящих врагов в течение %$heroes.hero_builder.defensive_turret.duration[2]%$ секунд, нанося %$heroes.hero_builder.defensive_turret.attack.damage_min[2]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[2]%$ физического урона за атаку.",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_3"] = "Строит временную башню, которая атакует проходящих врагов в течение %$heroes.hero_builder.defensive_turret.duration[3]%$ секунд, нанося %$heroes.hero_builder.defensive_turret.attack.damage_min[3]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[3]%$ физического урона за атаку.",
["HERO_BUILDER_DEFENSIVE_TURRET_TITLE"] = "ОБОРОНИТЕЛЬНАЯ ВЫШКА",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_1"] = "Быстро вращает свою деревянную балку, нанося %$heroes.hero_builder.demolition_man.s_damage_min[1]%$-%$heroes.hero_builder.demolition_man.s_damage_max[1]%$ физического урона врагам вокруг себя.",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_2"] = "Быстро вращает свою деревянную балку, нанося %$heroes.hero_builder.demolition_man.s_damage_min[2]%$-%$heroes.hero_builder.demolition_man.s_damage_max[2]%$ физического урона врагам вокруг себя.",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_3"] = "Быстро вращает свою деревянную балку, нанося %$heroes.hero_builder.demolition_man.s_damage_min[3]%$-%$heroes.hero_builder.demolition_man.s_damage_max[3]%$ физического урона врагам вокруг себя.",
["HERO_BUILDER_DEMOLITION_MAN_TITLE"] = "КРУШИТЕЛЬ",
["HERO_BUILDER_DESC"] = "Годы, проведенные за возведением оборонительных сооружений Линерии, научили Торреса кое-чему о военном деле. Теперь, когда все королевство в опасности (и когда ему надоело наблюдать со стороны) он использует все свои инструменты и опыт в схватке. ",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_1"] = "Торрес делает перерыв на перекус, восстанавливая себе %$heroes.hero_builder.lunch_break.heal_hp[1]%$ здоровья.",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_2"] = "Торрес делает перерыв на перекус, восстанавливая себе %$heroes.hero_builder.lunch_break.heal_hp[2]%$ здоровья.",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_3"] = "Торрес делает перерыв на перекус, восстанавливая себе %$heroes.hero_builder.lunch_break.heal_hp[3]%$ здоровья.",
["HERO_BUILDER_LUNCH_BREAK_TITLE"] = "ОБЕДЕННЫЙ ПЕРЕРЫВ",
["HERO_BUILDER_NAME"] = "Торрес",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_1"] = "Вызывает двух строителей, которые сражаются на его стороне в течение %$heroes.hero_builder.overtime_work.soldier.duration%$ секунд.",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_2"] = "Строители теперь имеют %$heroes.hero_builder.overtime_work.soldier.hp_max[2]%$ здоровья и наносят %$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_max[2]%$ физического урона. Они сражаются в течение %$heroes.hero_builder.overtime_work.soldier.duration%$ секунд.",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_3"] = "Строители теперь имеют %$heroes.hero_builder.overtime_work.soldier.hp_max[3]%$ здоровья и наносят %$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_max[3]%$ физического урона. Они сражаются в течение %$heroes.hero_builder.overtime_work.soldier.duration%$ секунд.",
["HERO_BUILDER_OVERTIME_WORK_TITLE"] = "МУЖИКИ ЗА РАБОТОЙ",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_1"] = "Сбрасывает огромный стальной шар на путь, нанося %$heroes.hero_builder.ultimate.damage[2]%$ физического урона и оглушая врагов на %$heroes.hero_builder.ultimate.stun_duration[2]%$ секунд.",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_2"] = "Сбрасывает огромный стальной шар на путь, нанося %$heroes.hero_builder.ultimate.damage[3]%$ физического урона и оглушая врагов на %$heroes.hero_builder.ultimate.stun_duration[3]%$ секунд.",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_3"] = "Сбрасывает огромный стальной шар на путь, нанося %$heroes.hero_builder.ultimate.damage[4]%$ физического урона и оглушая врагов на %$heroes.hero_builder.ultimate.stun_duration[4]%$ секунд.",
["HERO_BUILDER_WRECKING_BALL_MENUBOTTOM_DESCRIPTION"] = "Сбрасывает шар-молот на путь, нанося урон врагам.",
["HERO_BUILDER_WRECKING_BALL_MENUBOTTOM_NAME"] = "Шар-молот",
["HERO_BUILDER_WRECKING_BALL_TITLE"] = "ШАР-МОЛОТ",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_1"] = "Сильвара раскрывает свою истинную сущность на %$heroes.hero_dragon_arb.ultimate.duration[2]%$ секунд. В течение этого времени она увеличивает свои урон, скорость и защиту на %$heroes.hero_dragon_arb.ultimate.s_bonuses[2]%$% и усиливает некоторые из своих способностей.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_2"] = "Сильвара раскрывает свою истинную сущность на %$heroes.hero_dragon_arb.ultimate.duration[3]%$ секунд. В течение этого времени она увеличивает свои урон, скорость и защиту на %$heroes.hero_dragon_arb.ultimate.s_bonuses[3]%$% и усиливает некоторые из своих способностей.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_3"] = "Сильвара раскрывает свою истинную сущность на %$heroes.hero_dragon_arb.ultimate.duration[4]%$ секунд. В течение этого времени она увеличивает свои урон, скорость и защиту на %$heroes.hero_dragon_arb.ultimate.s_bonuses[4]%$% и усиливает некоторые из своих способностей.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_MENUBOTTOM_DESCRIPTION"] = "Раскрывает истинную сущность Сильвары.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_MENUBOTTOM_NAME"] = "Истинная Натура",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_TITLE"] = "ИСТИННАЯ НАТУРА",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_1"] = "Сильвара периодически оставляет на земле корни и ростки, которые позже превращает в Древесников. Они сражаются в течение %$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[1]%$ секунд. Во время действия Истинной Натуры призывает более сильных Древесников.",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_2"] = "Сильвара периодически оставляет на земле корни и ростки, которые позже превращает в Древесников. Они сражаются в течение %$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[2]%$ секунд. Во время действия Истинной Натуры призывает более сильных Древесников.",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_3"] = "Сильвара периодически оставляет на земле корни и ростки, которые позже превращает в Древесников. Они сражаются в течение %$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[3]%$ секунд. Во время действия Истинной Натуры призывает более сильных Древесников.",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_TITLE"] = "ЗОВ ЛЕСА",
["HERO_DRAGON_ARB_CLASS"] = "Дух Природы",
["HERO_DRAGON_ARB_DESC"] = "Дракон природы и покровительница Древесников. Она взращивает леса своим дыханием и дарует ветер своими крыльями. Как и сама природа, она может быть как заботливой, так и карающей. Главное – не мусорите!",
["HERO_DRAGON_ARB_NAME"] = "Сильвара",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_1"] = "Каждые %$heroes.hero_dragon_arb.thorn_bleed.cooldown[1]%$ секунд Сильвара производит особую атаку, наносящую урон врагам в зависимости от их скорости. Во время действия Истинной Натуры эта атака имеет %$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[1]%$% шанс мгновенно убить врага.",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_2"] = "Каждые %$heroes.hero_dragon_arb.thorn_bleed.cooldown[2]%$ секунд Сильвара производит особую атаку, наносящую урон врагам в зависимости от их скорости. Во время действия Истинной Натуры эта атака имеет %$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[2]%$% шанс мгновенно убить врага.",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_3"] = "Каждые %$heroes.hero_dragon_arb.thorn_bleed.cooldown[3]%$ секунд Сильвара производит особую атаку, наносящую урон врагам в зависимости от их скорости. Во время действия Истинной Натуры эта атака имеет %$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[3]%$% шанс мгновенно убить врага.",
["HERO_DRAGON_ARB_THORN BLEED_TITLE"] = "ШИПАСТОЕ ДЫХАНИЕ",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_1"] = "Увеличивает урон ближайших башен на %$heroes.hero_dragon_arb.tower_runes.s_damage_factor[1]%$% на протяжении %$heroes.hero_dragon_arb.tower_runes.duration[1]%$ секунд.",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_2"] = "Увеличивает урон ближайших башен на %$heroes.hero_dragon_arb.tower_runes.s_damage_factor[2]%$% на протяжении %$heroes.hero_dragon_arb.tower_runes.duration[2]%$ секунд.",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_3"] = "Увеличивает урон ближайших башен на %$heroes.hero_dragon_arb.tower_runes.s_damage_factor[3]%$% на протяжении %$heroes.hero_dragon_arb.tower_runes.duration[3]%$ секунд.",
["HERO_DRAGON_ARB_TOWER RUNES_TITLE"] = "ГЛУБОКИЕ КОРНИ",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_1"] = "На %$heroes.hero_dragon_arb.tower_plants.duration[1]%$ секунд призывает растения рядом с башнями. В зависимости от принадлежности башен, они становятся ядовитыми растениями, наносящими урон и замедляющими, или лечащими растениями, исцеляющими союзников.",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_2"] = "На %$heroes.hero_dragon_arb.tower_plants.duration[2]%$ секунд призывает растения рядом с башнями. В зависимости от принадлежности башен, они становятся ядовитыми растениями, наносящими урон и замедляющими, или лечащими растениями, исцеляющими союзников.",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_3"] = "На %$heroes.hero_dragon_arb.tower_plants.duration[3]%$ секунд призывает растения рядом с башнями. В зависимости от принадлежности башен, они становятся ядовитыми растениями, наносящими урон и замедляющими, или лечащими растениями, исцеляющими союзников.",
["HERO_DRAGON_ARB_TOWER_PLANTS_TITLE"] = "ДАР ЖИЗНИ",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_1"] = "Выпускает %$heroes.hero_dragon_bone.burst.proj_count[1]%$ магических снарядов, каждый из которых наносит %$heroes.hero_dragon_bone.burst.damage_min[1]%$-%$heroes.hero_dragon_bone.burst.damage_max[1]%$ чистого урона и заражает врагов чумой.",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_2"] = "Выпускает %$heroes.hero_dragon_bone.burst.proj_count[2]%$ магических снарядов, каждый из которых наносит %$heroes.hero_dragon_bone.burst.damage_min[2]%$-%$heroes.hero_dragon_bone.burst.damage_max[2]%$ чистого урона и заражает врагов чумой.",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_3"] = "Выпускает %$heroes.hero_dragon_bone.burst.proj_count[3]%$ магических снарядов, каждый из которых наносит %$heroes.hero_dragon_bone.burst.damage_min[3]%$-%$heroes.hero_dragon_bone.burst.damage_max[3]%$ чистого урона и заражает врагов чумой.",
["HERO_DRAGON_BONE_BURST_TITLE"] = "РАСПОЛЗАЮЩЕЕСЯ ПОВЕТРИЕ",
["HERO_DRAGON_BONE_CLASS"] = "Драколич",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_1"] = "Накрывает область ядовитым облаком, которое заражает врагов чумой и замедляет их на %$heroes.hero_dragon_bone.cloud.duration[1]%$ секунд.",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_2"] = "Накрывает область ядовитым облаком, которое заражает врагов чумой и замедляет их на %$heroes.hero_dragon_bone.cloud.duration[2]%$ секунд.",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_3"] = "Накрывает область ядовитым облаком, которое заражает врагов чумой и замедляет их на %$heroes.hero_dragon_bone.cloud.duration[3]%$ секунд.",
["HERO_DRAGON_BONE_CLOUD_TITLE"] = "ЧУМНОЕ ОБЛАКО",
["HERO_DRAGON_BONE_DESC"] = "После освобождения Вез'наном во время его завоевательного похода, Кощей предложил вернуть долг, используя свои силы, дабы рыскать по землям в поисках магов, что могли бы нести угрозу планам Темного Лорда.",
["HERO_DRAGON_BONE_NAME"] = "Кощей",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_1"] = "С размаху приземляется на поле боя, нанося врагам %$heroes.hero_dragon_bone.nova.damage_min[1]%$-%$heroes.hero_dragon_bone.nova.damage_max[1]%$ взрывного урона и заражая их чумой.",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_2"] = "С размаху приземляется на поле боя, нанося врагам %$heroes.hero_dragon_bone.nova.damage_min[2]%$-%$heroes.hero_dragon_bone.nova.damage_max[2]%$ взрывного урона и заражая их чумой.",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_3"] = "С размаху приземляется на поле боя, нанося врагам %$heroes.hero_dragon_bone.nova.damage_min[3]%$-%$heroes.hero_dragon_bone.nova.damage_max[3]%$ взрывного урона и заражая их чумой.",
["HERO_DRAGON_BONE_NOVA_TITLE"] = "ВОЛНА ХВОРИ",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_1"] = "Бьет врагов %$heroes.hero_dragon_bone.rain.bones_count[1]%$ костяными шипами, нанося %$heroes.hero_dragon_bone.rain.damage_min[1]%$-%$heroes.hero_dragon_bone.rain.damage_max[1]%$ чистого урона и ненадолго оглушая их.",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_2"] = "Бьет врагов %$heroes.hero_dragon_bone.rain.bones_count[2]%$ костяными шипами, нанося %$heroes.hero_dragon_bone.rain.damage_min[2]%$-%$heroes.hero_dragon_bone.rain.damage_max[2]%$ чистого урона и ненадолго оглушая их.",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_3"] = "Бьет врагов %$heroes.hero_dragon_bone.rain.bones_count[3]%$ костяными шипами, нанося %$heroes.hero_dragon_bone.rain.damage_min[3]%$-%$heroes.hero_dragon_bone.rain.damage_max[3]%$ чистого урона и ненадолго оглушая их.",
["HERO_DRAGON_BONE_RAIN_TITLE"] = "ПОЗВОНКОВЫЙ ДОЖДЬ",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_1"] = "Призывает двух костяных драконов. Каждый из них имеет %$heroes.hero_dragon_bone.ultimate.dog.hp[2]%$ здоровья и наносит %$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[2]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[2]%$ физического урона.",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_2"] = "Призывает двух костяных драконов. Каждый из них имеет %$heroes.hero_dragon_bone.ultimate.dog.hp[3]%$ здоровья и наносит %$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[3]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[3]%$ физического урона.",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_3"] = "Призывает двух костяных драконов. Каждый из них имеет %$heroes.hero_dragon_bone.ultimate.dog.hp[4]%$ здоровья и наносит %$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[4]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[4]%$ физического урона.",
["HERO_DRAGON_BONE_RAISE_DRAKES_MENUBOTTOM_DESCRIPTION"] = "Призывает двух костяных драконов. ",
["HERO_DRAGON_BONE_RAISE_DRAKES_MENUBOTTOM_NAME"] = "Воскрешение Драконов",
["HERO_DRAGON_BONE_RAISE_DRAKES_TITLE"] = "ВОСКРЕШЕНИЕ ДРАКОНОВ",
["HERO_DRAGON_GEM_CLASS"] = "Несломленный",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_1"] = "Заключает врага в кристалл на несколько секунд. Затем кристалл взрывается, мгновенно убивая цель и нанося %$heroes.hero_dragon_gem.crystal_instakill.s_damage[1]%$ чистого урона по площади.",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_2"] = "Заключает врага в кристалл на несколько секунд. Затем кристалл взрывается, мгновенно убивая цель и нанося %$heroes.hero_dragon_gem.crystal_instakill.s_damage[2]%$ чистого урона по площади.",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_3"] = "Заключает врага в кристалл на несколько секунд. Затем кристалл взрывается, мгновенно убивая цель и нанося %$heroes.hero_dragon_gem.crystal_instakill.s_damage[3]%$ чистого урона по площади.",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_TITLE"] = "ГРАНАТОВАЯ ГРОБНИЦА",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_1"] = "На %$heroes.hero_dragon_gem.crystal_totem.duration[1]%$ секунд бросает кристалл, который снижает скорость врагов на %$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$% и наносит им %$heroes.hero_dragon_gem.crystal_totem.s_damage[1]%$ магического урона каждые 1 секунды.",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_2"] = "На %$heroes.hero_dragon_gem.crystal_totem.duration[2]%$ секунд бросает кристалл, который снижает скорость врагов на %$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$% и наносит им %$heroes.hero_dragon_gem.crystal_totem.s_damage[2]%$ магического урона каждые 1 секунды.",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_3"] = "На %$heroes.hero_dragon_gem.crystal_totem.duration[3]%$ секунд бросает кристалл, который снижает скорость врагов на %$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$% и наносит им %$heroes.hero_dragon_gem.crystal_totem.s_damage[3]%$ магического урона каждые 1 секунды.",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_TITLE"] = "СИЛОВОЙ ПРОВОДНИК",
["HERO_DRAGON_GEM_DESC"] = "Спокойная жизнь Космира была потревожена, когда Культ начал свою деятельность в Проклятом Каньоне. Желая избавиться от нарушителей, дракон заключил сделку с Вез'наном, чтобы присоединиться к Альянсу против общего врага.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_1"] = "Вызывает %$heroes.hero_dragon_gem.ultimate.max_shards[2]%$ кристальных залпа, наносящих %$heroes.hero_dragon_gem.ultimate.damage_min[2]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[2]%$ чистого урона врагам в зоне поражения.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_2"] = "Вызывает %$heroes.hero_dragon_gem.ultimate.max_shards[3]%$ кристальных залпов, наносящих %$heroes.hero_dragon_gem.ultimate.damage_min[3]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[3]%$ чистого урона врагам в зоне поражения.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_3"] = "Вызывает %$heroes.hero_dragon_gem.ultimate.max_shards[4]%$ кристальных залпов, наносящих %$heroes.hero_dragon_gem.ultimate.damage_min[4]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[4]%$ чистого урона врагам в зоне поражения.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_MENUBOTTOM_DESCRIPTION"] = "Атакует врагов залпами кристаллов.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_MENUBOTTOM_NAME"] = "КРИСТАЛЬНАЯ ЛАВИНА",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_TITLE"] = "КРИСТАЛЬНАЯ ЛАВИНА",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_1"] = "Создает кристаллические шипы на путях рядом с собой, нанося каждому пораженному врагу %$heroes.hero_dragon_gem.floor_impact.damage_min[1]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[1]%$ физического урона.",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_2"] = "Создает кристаллические шипы на путях рядом с собой, нанося каждому пораженному врагу %$heroes.hero_dragon_gem.floor_impact.damage_min[2]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[2]%$ физического урона.",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_3"] = "Создает кристаллические шипы на путях рядом с собой, нанося каждому пораженному врагу %$heroes.hero_dragon_gem.floor_impact.damage_min[3]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[3]%$ физического урона.",
["HERO_DRAGON_GEM_FLOOR_IMPACT_TITLE"] = "ОСКОЛКИ ПРИЗМЫ",
["HERO_DRAGON_GEM_NAME"] = "Космир",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_1"] = "Замуровывает в кристаллах группу врагов, оглушая их на %$heroes.hero_dragon_gem.stun.duration[1]%$ секунды.",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_2"] = "Замуровывает в кристаллах группу врагов, оглушая их на %$heroes.hero_dragon_gem.stun.duration[2]%$ секунды.",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_3"] = "Замуровывает в кристаллах группу врагов, оглушая их на %$heroes.hero_dragon_gem.stun.duration[3]%$ секунды.",
["HERO_DRAGON_GEM_STUN_TITLE"] = "ПАРАЛИЗУЮЩЕЕ ДЫХАНИЕ",
["HERO_HUNTER_BEASTS_DESCRIPTION_1"] = "Призывает двух летучих мышей, которые атакуют ближайших врагов в течение %$heroes.hero_hunter.beasts.duration[1]%$ секунд, нанося %$heroes.hero_hunter.beasts.damage_min[1]%$-%$heroes.hero_hunter.beasts.damage_max[1]%$ физического урона. Каждая летучая мышь имеет шанс украсть %$heroes.hero_hunter.beasts.gold_to_steal[1]%$ золота у своей цели.",
["HERO_HUNTER_BEASTS_DESCRIPTION_2"] = "Призывает двух летучих мышей, которые атакуют ближайших врагов в течение %$heroes.hero_hunter.beasts.duration[2]%$ секунд, нанося %$heroes.hero_hunter.beasts.damage_min[2]%$-%$heroes.hero_hunter.beasts.damage_max[2]%$ физического урона. Каждая летучая мышь имеет шанс украсть %$heroes.hero_hunter.beasts.gold_to_steal[2]%$ золота у своей цели.",
["HERO_HUNTER_BEASTS_DESCRIPTION_3"] = "Призывает двух летучих мышей, которые атакуют ближайших врагов в течение %$heroes.hero_hunter.beasts.duration[3]%$ секунд, нанося %$heroes.hero_hunter.beasts.damage_min[3]%$-%$heroes.hero_hunter.beasts.damage_max[3]%$ физического урона. Каждая летучая мышь имеет шанс украсть %$heroes.hero_hunter.beasts.gold_to_steal[3]%$ золота у своей цели.",
["HERO_HUNTER_BEASTS_TITLE"] = "СУМЕРЕЧНЫЕ ЗВЕРИ",
["HERO_HUNTER_CLASS"] = "Серебряная Охотница",
["HERO_HUNTER_DESC"] = "Рожденная от союза вампира и известного охотника на чудовищ, Аня следует по стопам своего отца, борясь с порождениями тьмы. Неустанная охота на культистов привела ее в южные земли, к Альянсу.",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_1"] = "Каждый 7-й удар Ани в ближнем бою наносит %$heroes.hero_hunter.heal_strike.damage_min[1]%$-%$heroes.hero_hunter.heal_strike.damage_max[1]%$ чистого урона и лечит ее на %$heroes.hero_hunter.heal_strike.heal_factor[1]%$% от максимального здоровья ее цели.",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_2"] = "Каждый 7-й удар Ани в ближнем бою наносит %$heroes.hero_hunter.heal_strike.damage_min[2]%$-%$heroes.hero_hunter.heal_strike.damage_max[2]%$ чистого урона и лечит ее на %$heroes.hero_hunter.heal_strike.heal_factor[2]%$% от максимального здоровья ее цели.",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_3"] = "Каждый 7-й удар Ани в ближнем бою наносит %$heroes.hero_hunter.heal_strike.damage_min[2]%$-%$heroes.hero_hunter.heal_strike.damage_max[2]%$ чистого урона и лечит ее на %$heroes.hero_hunter.heal_strike.heal_factor[2]%$% от максимального здоровья ее цели.",
["HERO_HUNTER_HEAL_STRIKE_TITLE"] = "ВАМПИРСКИЙ КОГОТЬ",
["HERO_HUNTER_NAME"] = "Аня",
["HERO_HUNTER_RICOCHET_DESCRIPTION_1"] = "Аня обращается в туман и атакует %$heroes.hero_hunter.ricochet.s_bounces[1]%$ врагов, нанося каждому из них %$heroes.hero_hunter.ricochet.damage_min[1]%$-%$heroes.hero_hunter.ricochet.damage_max[1]%$ физического урона.",
["HERO_HUNTER_RICOCHET_DESCRIPTION_2"] = "Аня обращается в туман и атакует %$heroes.hero_hunter.ricochet.s_bounces[2]%$ врагов, нанося каждому из них %$heroes.hero_hunter.ricochet.damage_min[2]%$-%$heroes.hero_hunter.ricochet.damage_max[2]%$ физического урона.",
["HERO_HUNTER_RICOCHET_DESCRIPTION_3"] = "Аня обращается в туман и атакует %$heroes.hero_hunter.ricochet.s_bounces[3]%$ врагов, нанося каждому из них %$heroes.hero_hunter.ricochet.damage_min[3]%$-%$heroes.hero_hunter.ricochet.damage_max[3]%$ физического урона.",
["HERO_HUNTER_RICOCHET_TITLE"] = "ШАГ В ТУМАН",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_1"] = "Стреляет по всем врагам вокруг, нанося каждому из них %$heroes.hero_hunter.shoot_around.s_damage_min[1]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[1]%$ чистого урона.",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_2"] = "Стреляет по всем врагам вокруг, нанося каждому из них %$heroes.hero_hunter.shoot_around.s_damage_min[2]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[2]%$ чистого урона.",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_3"] = "Стреляет по всем врагам вокруг, нанося каждому из них %$heroes.hero_hunter.shoot_around.s_damage_min[3]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[3]%$ чистого урона.",
["HERO_HUNTER_SHOOT_AROUND_TITLE"] = "СЕРЕБРЯНЫЙ ШТОРМ",
["HERO_HUNTER_SPIRIT_DESCRIPTION_1"] = "Вызывает проекцию Данте, которая наносит %$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[2]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[2]%$ чистого урона в секунду в течение %$heroes.hero_hunter.ultimate.duration%$ секунд. Воскрешает Аню, если ее тело рядом.",
["HERO_HUNTER_SPIRIT_DESCRIPTION_2"] = "Вызывает проекцию Данте, которая наносит %$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[3]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[3]%$ чистого урона в секунду в течение %$heroes.hero_hunter.ultimate.duration%$ секунд. Воскрешает Аню, если ее тело рядом.",
["HERO_HUNTER_SPIRIT_DESCRIPTION_3"] = "Вызывает проекцию Данте, которая наносит %$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[4]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[4]%$ чистого урона в секунду в течение %$heroes.hero_hunter.ultimate.duration%$ секунд. Воскрешает Аню, если ее тело рядом.",
["HERO_HUNTER_SPIRIT_MENUBOTTOM_DESCRIPTION"] = "Вызывает привидение Данте, которое атакует и замедляет врагов.",
["HERO_HUNTER_SPIRIT_MENUBOTTOM_NAME"] = "Помощь Охотника",
["HERO_HUNTER_SPIRIT_TITLE"] = "ПОМОЩЬ ОХОТНИКА",
["HERO_HUNTER_ULTIMATE_ENTITY_NAME"] = "Проекция Данте",
["HERO_LAVA_CLASS"] = "Расплавленный Гнев",
["HERO_LAVA_DESC"] = "Огненное и разрушительное существо с очень горячим нравом было разбужено действиями Гримборода. Поскольку диалог – не его конек, оно пробьет ряды врага, пока не утихомирится, чтобы вновь заснуть спокойно. ",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_1"] = "Бросает лавовый шар, который наносит %$heroes.hero_lava.double_trouble.s_damage[1]%$ взрывного урона врагам и на %$heroes.hero_lava.double_trouble.soldier.duration%$ секунд превращается в горящую Головешку, имеющую %$heroes.hero_lava.double_trouble.soldier.hp_max[1]%$ здоровья и сражающуюся с врагами.",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_2"] = "Бросает лавовый шар, который наносит %$heroes.hero_lava.double_trouble.s_damage[2]%$ взрывного урона врагам и на %$heroes.hero_lava.double_trouble.soldier.duration%$ секунд превращается в горящую Головешку, имеющую %$heroes.hero_lava.double_trouble.soldier.hp_max[2]%$ здоровья и сражающуюся с врагами.",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_3"] = "Бросает лавовый шар, который наносит %$heroes.hero_lava.double_trouble.s_damage[3]%$ взрывного урона врагам и на %$heroes.hero_lava.double_trouble.soldier.duration%$ секунд превращается в горящую Головешку, имеющую %$heroes.hero_lava.double_trouble.soldier.hp_max[3]%$ здоровья и сражающуюся с врагами.",
["HERO_LAVA_DOUBLE_TROUBLE_SOLDIER_NAME"] = "Головешка",
["HERO_LAVA_DOUBLE_TROUBLE_TITLE"] = "ДВОЙНЫЕ НЕПРИЯТНОСТИ",
["HERO_LAVA_HOTHEADED_DESCRIPTION_1"] = "При воскрешении Кратау усиливает урон ближайших башен на %$heroes.hero_lava.hotheaded.s_damage_factors[1]%$% на %$heroes.hero_lava.hotheaded.durations[1]%$ секунд.",
["HERO_LAVA_HOTHEADED_DESCRIPTION_2"] = "При воскрешении Кратау усиливает урон ближайших башен на %$heroes.hero_lava.hotheaded.s_damage_factors[2]%$% на %$heroes.hero_lava.hotheaded.durations[2]%$ секунд.",
["HERO_LAVA_HOTHEADED_DESCRIPTION_3"] = "При воскрешении Кратау усиливает урон ближайших башен на %$heroes.hero_lava.hotheaded.s_damage_factors[3]%$% на %$heroes.hero_lava.hotheaded.durations[3]%$ секунд.",
["HERO_LAVA_HOTHEADED_TITLE"] = "ГОРЯЧАЯ ГОЛОВА",
["HERO_LAVA_NAME"] = "Кратау",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_1"] = "Несколько раз ударяет врага, нанося %$heroes.hero_lava.temper_tantrum.s_damage_min[1]%$-%$heroes.hero_lava.temper_tantrum.s_damage_max[1]%$ физического урона и оглушая цель на %$heroes.hero_lava.temper_tantrum.duration[1]%$ секунды.",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_2"] = "Несколько раз ударяет врага, нанося %$heroes.hero_lava.temper_tantrum.s_damage_min[2]%$-%$heroes.hero_lava.temper_tantrum.s_damage_max[2]%$ физического урона и оглушая цель на %$heroes.hero_lava.temper_tantrum.duration[2]%$ секунды.",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_3"] = "Несколько раз ударяет врага, нанося %$heroes.hero_lava.temper_tantrum.s_damage_min[3]%$-%$heroes.hero_lava.temper_tantrum.s_damage_max[3]%$ физического урона и оглушая цель на %$heroes.hero_lava.temper_tantrum.duration[3]%$ секунды.",
["HERO_LAVA_TEMPER_TANTRUM_TITLE"] = "ВСПЫШКА ГНЕВА",
["HERO_LAVA_ULTIMATE_DESCRIPTION_1"] = "Запускает %$heroes.hero_lava.ultimate.fireball_count[2]%$ лавовых бомбы на тропу, каждая наносит %$heroes.hero_lava.ultimate.bullet.s_damage[2]%$ чистого урона по площади и поджигает врагов на %$heroes.hero_lava.ultimate.bullet.scorch.duration%$ секунд.",
["HERO_LAVA_ULTIMATE_DESCRIPTION_2"] = "Запускает %$heroes.hero_lava.ultimate.fireball_count[3]%$ лавовых бомбы на тропу, каждая наносит %$heroes.hero_lava.ultimate.bullet.s_damage[3]%$ чистого урона по площади и поджигает врагов на %$heroes.hero_lava.ultimate.bullet.scorch.duration%$ секунд.",
["HERO_LAVA_ULTIMATE_DESCRIPTION_3"] = "Запускает %$heroes.hero_lava.ultimate.fireball_count[4]%$ лавовых бомб на тропу, каждая наносит %$heroes.hero_lava.ultimate.bullet.s_damage[4]%$ чистого урона по площади и поджигает врагов на %$heroes.hero_lava.ultimate.bullet.scorch.duration%$ секунд.",
["HERO_LAVA_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "Запускает лавовые бомбы на тропу, поджигая землю.",
["HERO_LAVA_ULTIMATE_MENUBOTTOM_NAME"] = "Выброс Ярости",
["HERO_LAVA_ULTIMATE_TITLE"] = "ВЫБРОС ЯРОСТИ",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_1"] = "Превращается в огненный вихрь, нанося врагам вокруг %$heroes.hero_lava.wild_eruption.s_damage[1]%$ чистого урона в секунду и поджигая их на %$heroes.hero_lava.wild_eruption.duration[1]%$ секунды.",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_2"] = "Превращается в огненный вихрь, нанося врагам вокруг %$heroes.hero_lava.wild_eruption.s_damage[2]%$ чистого урона в секунду и поджигая их на %$heroes.hero_lava.wild_eruption.duration[2]%$ секунды.",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_3"] = "Превращается в огненный вихрь, нанося врагам вокруг %$heroes.hero_lava.wild_eruption.s_damage[3]%$ чистого урона в секунду и поджигая их на %$heroes.hero_lava.wild_eruption.duration[3]%$ секунды.",
["HERO_LAVA_WILD_ERUPTION_TITLE"] = "ДИКОЕ ИЗВЕРЖЕНИЕ",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_1"] = "Призывает %$heroes.hero_lumenir.ultimate.soldier_count[1]%$ воинов света, которые на короткое время оглушают ближайших врагов и наносят %$heroes.hero_lumenir.ultimate.damage_min[1]%$-%$heroes.hero_lumenir.ultimate.damage_max[1]%$ чистого урона.",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_2"] = "Призывает %$heroes.hero_lumenir.ultimate.soldier_count[2]%$ воинов света, которые на короткое время оглушают ближайших врагов и наносят %$heroes.hero_lumenir.ultimate.damage_min[2]%$-%$heroes.hero_lumenir.ultimate.damage_max[2]%$ чистого урона.",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_3"] = "Призывает %$heroes.hero_lumenir.ultimate.soldier_count[3]%$ воинов света, которые на короткое время оглушают ближайших врагов и наносят %$heroes.hero_lumenir.ultimate.damage_min[3]%$-%$heroes.hero_lumenir.ultimate.damage_max[3]%$ чистого урона.",
["HERO_LUMENIR_ARROW_STORM_MENUBOTTOM_DESCRIPTION"] = "Призывает божественных воинов, сражающихся с врагами.",
["HERO_LUMENIR_ARROW_STORM_MENUBOTTOM_NAME"] = "Триумфальный Зов",
["HERO_LUMENIR_ARROW_STORM_TITLE"] = "ТРИУМФАЛЬНЫЙ ЗОВ",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_1"] = "Обрушивает божественный меч на самого сильного врага поблизости, нанося %$heroes.hero_lumenir.celestial_judgement.damage[1]%$ чистого урона и оглушая его на %$heroes.hero_lumenir.celestial_judgement.stun_duration[1]%$ секунды.",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_2"] = "Обрушивает божественный меч на самого сильного врага поблизости, нанося %$heroes.hero_lumenir.celestial_judgement.damage[2]%$ чистого урона и оглушая его на %$heroes.hero_lumenir.celestial_judgement.stun_duration[2]%$ секунды.",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_3"] = "Обрушивает божественный меч на самого сильного врага поблизости, нанося %$heroes.hero_lumenir.celestial_judgement.damage[3]%$ чистого урона и оглушая его на %$heroes.hero_lumenir.celestial_judgement.stun_duration[3]%$ секунды.",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_TITLE"] = "КАРА НЕБЕСНАЯ",
["HERO_LUMENIR_CLASS"] = "Несущая Свет",
["HERO_LUMENIR_DESC"] = "Паря между мирами, Люменир олицетворяет решительность и справедливость. Она – легендарная Несущая Свет, почитаемая паладинами Линерии, которым она дарует свое благословение, наделяя их великой силой для борьбы со злом.",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_1"] = "Выпускает %$heroes.hero_lumenir.fire_balls.flames_count[1]%$ шаров божественного света, которые движутся по пути, нанося урон врагам. Каждый шар наносит %$heroes.hero_lumenir.fire_balls.flame_damage_min[1]%$-%$heroes.hero_lumenir.fire_balls.flame_damage_max[1]%$ чистого урона всякому врагу, через которого проходит.",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_2"] = "Выпускает %$heroes.hero_lumenir.fire_balls.flames_count[2]%$ шаров божественного света, которые движутся по пути, нанося урон врагам. Каждый шар наносит %$heroes.hero_lumenir.fire_balls.flame_damage_min[2]%$-%$heroes.hero_lumenir.fire_balls.flame_damage_max[2]%$ чистого урона всякому врагу, через которого проходит.",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_3"] = "Выпускает %$heroes.hero_lumenir.fire_balls.flames_count[3]%$ шаров божественного света, которые движутся по пути, нанося урон врагам. Каждый шар наносит %$heroes.hero_lumenir.fire_balls.flame_damage_min[3]%$-%$heroes.hero_lumenir.fire_balls.flame_damage_max[3]%$ чистого урона всякому врагу, через которого проходит.",
["HERO_LUMENIR_FIRE_BALLS_TITLE"] = "СИЯЮЩАЯ ВОЛНА",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_1"] = "Призывает маленького дракона света, который следует за другим выбранным героем в течение %$heroes.hero_lumenir.mini_dragon.dragon.duration[1]%$ секунд. Каждый дракон наносит %$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[1]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[1]%$ физического урона за атаку.",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_2"] = "Призывает маленького дракона света, который следует за другим выбранным героем в течение %$heroes.hero_lumenir.mini_dragon.dragon.duration[2]%$ секунд. Каждый дракон наносит %$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[2]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[2]%$ физического урона за атаку.",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_3"] = "Призывает маленького дракона света, который следует за другим выбранным героем в течение %$heroes.hero_lumenir.mini_dragon.dragon.duration[3]%$ секунд. Каждый дракон наносит %$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[3]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[3]%$ физического урона за атаку.",
["HERO_LUMENIR_MINI_DRAGON_TITLE"] = "СПУТНИК СВЕТА",
["HERO_LUMENIR_NAME"] = "Люменир",
["HERO_LUMENIR_SHIELD_DESCRIPTION_1"] = "Дарует союзным войскам щит, увеличивающий их броню на %$heroes.hero_lumenir.shield.armor[1]%$% и позволяющий отражать %$heroes.hero_lumenir.shield.spiked_armor[1]%$% урона обратно врагам.",
["HERO_LUMENIR_SHIELD_DESCRIPTION_2"] = "Дарует союзным войскам щит, увеличивающий их броню на %$heroes.hero_lumenir.shield.armor[2]%$% и позволяющий отражать %$heroes.hero_lumenir.shield.spiked_armor[2]%$% урона обратно врагам.",
["HERO_LUMENIR_SHIELD_DESCRIPTION_3"] = "Дарует союзным войскам щит, увеличивающий их броню на %$heroes.hero_lumenir.shield.armor[3]%$% и позволяющий отражать %$heroes.hero_lumenir.shield.spiked_armor[3]%$% урона обратно врагам.",
["HERO_LUMENIR_SHIELD_TITLE"] = "БЛАГОСЛОВЕНИЕ ВОЗМЕЗДИЯ",
["HERO_MECHA_CLASS"] = "Ходячая Катастрофа",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_1"] = "Вызывает дирижабль гоблинов, который бомбит врагов вблизи заданной области, нанося %$heroes.hero_mecha.ultimate.ranged_attack.damage_min[2]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[2]%$ чистого урона по площади за атаку.",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_2"] = "Вызывает дирижабль гоблинов, который бомбит врагов вблизи заданной области, нанося %$heroes.hero_mecha.ultimate.ranged_attack.damage_min[3]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[3]%$ чистого урона по площади за атаку.",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_3"] = "Вызывает дирижабль гоблинов, который бомбит врагов вблизи заданной области, нанося %$heroes.hero_mecha.ultimate.ranged_attack.damage_min[4]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[4]%$ чистого урона по площади за атаку.",
["HERO_MECHA_DEATH_FROM_ABOVE_MENUBOTTOM_DESCRIPTION"] = "Вызывает дирижабль, бомбящий врагов в области.",
["HERO_MECHA_DEATH_FROM_ABOVE_MENUBOTTOM_NAME"] = "Смерть с небес",
["HERO_MECHA_DEATH_FROM_ABOVE_TITLE"] = "СМЕРТЬ С НЕБЕС",
["HERO_MECHA_DESC"] = "Созданный умом двух сумасшедших гоблинов-изобретателей и построенный на основе украденных у гномов технологий, Онагро является величайшей военной машиной Зеленокожих и внушающим ужас зрелищем для всех врагов Армии Тьмы.",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_1"] = "Призывает %$heroes.hero_mecha.goblidrones.units%$ дронов, которые атакуют врагов в течение %$heroes.hero_mecha.goblidrones.drone.duration[1]%$ секунд, нанося %$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[1]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[1]%$ физического урона за атаку.",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_2"] = "Призывает %$heroes.hero_mecha.goblidrones.units%$ дронов, которые атакуют врагов в течение %$heroes.hero_mecha.goblidrones.drone.duration[2]%$ секунд, нанося %$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[2]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[2]%$ физического урона за атаку.",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_3"] = "Призывает %$heroes.hero_mecha.goblidrones.units%$ дронов, которые атакуют врагов в течение %$heroes.hero_mecha.goblidrones.drone.duration[3]%$ секунд, нанося %$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[3]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[3]%$ физического урона за атаку.",
["HERO_MECHA_GOBLIDRONES_TITLE"] = "ГОБЛИДРОНЫ",
["HERO_MECHA_MINE_DROP_DESCRIPTION_1"] = "Стоя на месте, мех периодически оставляет на пути мины (не более %$heroes.hero_mecha.mine_drop.max_mines[1]%$ на карте одновременно). При взрыве каждая мина наносит %$heroes.hero_mecha.mine_drop.damage_min[1]%$-%$heroes.hero_mecha.mine_drop.damage_max[1]%$ взрывного урона.",
["HERO_MECHA_MINE_DROP_DESCRIPTION_2"] = "Стоя на месте, мех периодически оставляет на пути мины (не более %$heroes.hero_mecha.mine_drop.max_mines[2]%$ на карте одновременно). При взрыве каждая мина наносит %$heroes.hero_mecha.mine_drop.damage_min[2]%$-%$heroes.hero_mecha.mine_drop.damage_max[2]%$ взрывного урона.",
["HERO_MECHA_MINE_DROP_DESCRIPTION_3"] = "Стоя на месте, мех периодически оставляет на пути мины (не более %$heroes.hero_mecha.mine_drop.max_mines[3]%$ на карте одновременно). При взрыве каждая мина наносит %$heroes.hero_mecha.mine_drop.damage_min[3]%$-%$heroes.hero_mecha.mine_drop.damage_max[3]%$ взрывного урона.",
["HERO_MECHA_MINE_DROP_TITLE"] = "СБРОС МИН",
["HERO_MECHA_NAME"] = "Онагро",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_1"] = "Мех ударяет по земле, нанося %$heroes.hero_mecha.power_slam.s_damage[1]%$ физического урона врагам поблизости и на короткое время оглушая их.",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_2"] = "Мех ударяет по земле, нанося %$heroes.hero_mecha.power_slam.s_damage[2]%$ физического урона врагам поблизости и на короткое время оглушая их.",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_3"] = "Мех ударяет по земле, нанося %$heroes.hero_mecha.power_slam.s_damage[3]%$ физического урона врагам поблизости и на короткое время оглушая их.",
["HERO_MECHA_POWER_SLAM_TITLE"] = "СИЛОВОЙ УДАР",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_1"] = "Бросает бомбу, которая разливает деготь на пути, замедляя врагов на %$heroes.hero_mecha.tar_bomb.slow_factor%$% на %$heroes.hero_mecha.tar_bomb.duration[1]%$ секунд.",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_2"] = "Бросает бомбу, которая разливает деготь на пути, замедляя врагов на %$heroes.hero_mecha.tar_bomb.slow_factor%$% на %$heroes.hero_mecha.tar_bomb.duration[2]%$ секунд.",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_3"] = "Бросает бомбу, которая разливает деготь на пути, замедляя врагов на %$heroes.hero_mecha.tar_bomb.slow_factor%$% на %$heroes.hero_mecha.tar_bomb.duration[3]%$ секунд.",
["HERO_MECHA_TAR_BOMB_TITLE"] = "ДЕГТЯРНАЯ БОМБА",
["HERO_MUYRN_CLASS"] = "Хранитель Леса",
["HERO_MUYRN_DESC"] = "Несмотря на свой детский вид, ловкач Ниру защищает лес вот уже сотни лет, используя свою связь с силами природы. Он присоединился к Альянсу, чтобы положить конец растущим волнам захватчиков, угрожающих его дому.",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_1"] = "Заколдовывает всех врагов в области, уменьшая их урон на %$heroes.hero_muyrn.faery_dust.s_damage_factor[1]%$% на %$heroes.hero_muyrn.faery_dust.duration[1]%$ секунд.",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_2"] = "Заколдовывает всех врагов в области, уменьшая их урон на %$heroes.hero_muyrn.faery_dust.s_damage_factor[2]%$% на %$heroes.hero_muyrn.faery_dust.duration[2]%$ секунд.",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_3"] = "Заколдовывает всех врагов в области, уменьшая их урон на %$heroes.hero_muyrn.faery_dust.s_damage_factor[3]%$% на %$heroes.hero_muyrn.faery_dust.duration[3]%$ секунд.",
["HERO_MUYRN_FAERY_DUST_TITLE"] = "ОСЛАБЛЯЮЩИЕ ЧАРЫ",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_1"] = "В бою Ниру создает вокруг себя щит из листьев. В течение %$heroes.hero_muyrn.leaf_whirlwind.duration[1]%$ секунд щит исцеляет Ниру и наносит %$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[1]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[1]%$ магического урона в секунду.",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_2"] = "В бою Ниру создает вокруг себя щит из листьев. В течение %$heroes.hero_muyrn.leaf_whirlwind.duration[2]%$ секунд щит исцеляет Ниру и наносит %$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[2]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[2]%$ магического урона в секунду.",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_3"] = "В бою Ниру создает вокруг себя щит из листьев. В течение %$heroes.hero_muyrn.leaf_whirlwind.duration[3]%$ секунд щит исцеляет Ниру и наносит %$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[3]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[3]%$ магического урона в секунду.",
["HERO_MUYRN_LEAF_WHIRLWIND_TITLE"] = "ВИХРЬ ЛИСТВЫ",
["HERO_MUYRN_NAME"] = "Ниру",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_1"] = "Покрывает зону корнями на %$heroes.hero_muyrn.ultimate.duration[2]%$ секунд, замедляя врагов и нанося %$heroes.hero_muyrn.ultimate.s_damage_min[2]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[2]%$ чистого урона в секунду.",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_2"] = "Покрывает зону корнями на %$heroes.hero_muyrn.ultimate.duration[3]%$ секунд, замедляя врагов и нанося %$heroes.hero_muyrn.ultimate.s_damage_min[3]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[3]%$ чистого урона в секунду.",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_3"] = "Покрывает зону корнями на %$heroes.hero_muyrn.ultimate.duration[3]%$ секунд, замедляя врагов и нанося %$heroes.hero_muyrn.ultimate.s_damage_min[3]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[3]%$ чистого урона в секунду.",
["HERO_MUYRN_ROOT_DEFENDER_MENUBOTTOM_DESCRIPTION"] = "Создает корни, наносящие урон врагам и замедляющие их.",
["HERO_MUYRN_ROOT_DEFENDER_MENUBOTTOM_NAME"] = "Защитные Корни",
["HERO_MUYRN_ROOT_DEFENDER_TITLE"] = "ЗАЩИТНЫЕ КОРНИ",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_1"] = "Призывает %$heroes.hero_muyrn.sentinel_wisps.max_summons[1]%$ дружественного светлячка, который следует за Ниру в течение %$heroes.hero_muyrn.sentinel_wisps.wisp.duration[1]%$ секунд. Светлячок наносит %$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[1]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[1]%$ магического урона.",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_2"] = "Призывает %$heroes.hero_muyrn.sentinel_wisps.max_summons[2]%$ дружественных светлячков, которые следуют за Ниру в течение %$heroes.hero_muyrn.sentinel_wisps.wisp.duration[2]%$ секунд. Светлячки наносят %$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[2]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[2]%$ магического урона.",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_3"] = "Призывает %$heroes.hero_muyrn.sentinel_wisps.max_summons[3]%$ дружественных светлячков, которые следуют за Ниру в течение %$heroes.hero_muyrn.sentinel_wisps.wisp.duration[3]%$ секунд. Светлячки наносят %$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[3]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[3]%$ магического урона.",
["HERO_MUYRN_SENTINEL_WISPS_TITLE"] = "СВЕТЛЯЧКИ-ЧАСОВЫЕ",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_1"] = "Выпускает зеленый разряд энергии в направлении врага, нанося %$heroes.hero_muyrn.verdant_blast.s_damage[1]%$ магического урона.",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_2"] = "Выпускает зеленый разряд энергии в направлении врага, нанося %$heroes.hero_muyrn.verdant_blast.s_damage[2]%$ магического урона.",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_3"] = "Выпускает зеленый разряд энергии в направлении врага, нанося %$heroes.hero_muyrn.verdant_blast.s_damage[2]%$ магического урона.",
["HERO_MUYRN_VERDANT_BLAST_TITLE"] = "УДАР ЛЕСА",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_1"] = "Совершает мощный удар мечом по врагу, нанося %$heroes.hero_raelyn.brutal_slash.s_damage[1]%$ чистого урона.",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_2"] = "Совершает мощный удар мечом по врагу, нанося %$heroes.hero_raelyn.brutal_slash.s_damage[2]%$ чистого урона.",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_3"] = "Совершает мощный удар мечом по врагу, нанося %$heroes.hero_raelyn.brutal_slash.s_damage[3]%$ чистого урона.",
["HERO_RAELYN_BRUTAL_SLASH_TITLE"] = "БЕСПОЩАДНЫЙ УДАР",
["HERO_RAELYN_CLASS"] = "Темный Лейтенант",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_1"] = "Призывает в бой Темного Рыцаря, имеющего %$heroes.hero_raelyn.ultimate.entity.hp_max[2]%$ здоровья и наносящего за атаку %$heroes.hero_raelyn.ultimate.entity.damage_min[2]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[2]%$ чистого урона.",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_2"] = "Темный Рыцарь теперь имеет %$heroes.hero_raelyn.ultimate.entity.hp_max[3]%$ здоровья и наносит %$heroes.hero_raelyn.ultimate.entity.damage_min[3]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[3]%$ чистого урона.",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_3"] = "Темный Рыцарь теперь имеет %$heroes.hero_raelyn.ultimate.entity.hp_max[4]%$ здоровья и наносит %$heroes.hero_raelyn.ultimate.entity.damage_min[4]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[4]%$ чистого урона.",
["HERO_RAELYN_COMMAND_ORDERS_MENUBOTTOM_DESCRIPTION"] = "Призывает Темного Рыцаря на поле боя.",
["HERO_RAELYN_COMMAND_ORDERS_MENUBOTTOM_NAME"] = "Приказ Командира",
["HERO_RAELYN_COMMAND_ORDERS_TITLE"] = "ПРИКАЗ КОМАНДИРА",
["HERO_RAELYN_DESC"] = "Грозная Раэлин живет, чтобы вести темных рыцарей в бой на острие атаки. Ее жестокость и неумолимость принесли ей признание Вез’нана и страх линерийцев. Всегда готовая к славному бою, она была первым из добровольцев, присоединившимся к рядам Темного Волшебника.",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_1"] = "Оглушает ближайших врагов на %$heroes.hero_raelyn.inspire_fear.stun_duration[1]%$ секунд и снижает их урон на %$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[1]%$% на %$heroes.hero_raelyn.inspire_fear.damage_duration[1]%$ секунд.",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_2"] = "Оглушает ближайших врагов на %$heroes.hero_raelyn.inspire_fear.stun_duration[2]%$ секунд и снижает их урон на %$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[2]%$% на %$heroes.hero_raelyn.inspire_fear.damage_duration[2]%$ секунд.",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_3"] = "Оглушает ближайших врагов на %$heroes.hero_raelyn.inspire_fear.stun_duration[3]%$ секунд и снижает их урон на %$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[3]%$% на %$heroes.hero_raelyn.inspire_fear.damage_duration[3]%$ секунд.",
["HERO_RAELYN_INSPIRE_FEAR_TITLE"] = "ВСЕЛЯЯ СТРАХ",
["HERO_RAELYN_NAME"] = "Раэлин",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_1"] = "В течение %$heroes.hero_raelyn.onslaught.duration[1]%$ секунд Раэлин атакует быстрее и наносит %$heroes.hero_raelyn.onslaught.damage_factor[1]%$% урона от ее атаки в небольшой области вокруг основной цели.",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_2"] = "В течение %$heroes.hero_raelyn.onslaught.duration[2]%$ секунд Раэлин атакует быстрее и наносит %$heroes.hero_raelyn.onslaught.damage_factor[2]%$% урона от ее атаки в небольшой области вокруг основной цели.",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_3"] = "В течение %$heroes.hero_raelyn.onslaught.duration[3]%$ секунд Раэлин атакует быстрее и наносит %$heroes.hero_raelyn.onslaught.damage_factor[3]%$% урона от ее атаки в небольшой области вокруг основной цели.",
["HERO_RAELYN_ONSLAUGHT_TITLE"] = "НАТИСК",
["HERO_RAELYN_ULTIMATE_ENTITY_NAME"] = "Темный Рыцарь",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_1"] = "В бою Раэлин накрывает себя щитом, прочность которого основана на количестве врагов рядом с ней (%$heroes.hero_raelyn.unbreakable.shield_per_enemy[1]%$% от ее общего здоровья за каждого врага, максимум – %$heroes.hero_raelyn.unbreakable.max_targets%$ врага).",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_2"] = "В бою Раэлин накрывает себя щитом, прочность которого основана на количестве врагов рядом с ней (%$heroes.hero_raelyn.unbreakable.shield_per_enemy[2]%$% от ее общего здоровья за каждого врага, максимум – %$heroes.hero_raelyn.unbreakable.max_targets%$ врага).",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_3"] = "В бою Раэлин накрывает себя щитом, прочность которого основана на количестве врагов рядом с ней (%$heroes.hero_raelyn.unbreakable.shield_per_enemy[3]%$% от ее общего здоровья за каждого врага, максимум – %$heroes.hero_raelyn.unbreakable.max_targets%$ врага).",
["HERO_RAELYN_UNBREAKABLE_TITLE"] = "НЕСОКРУШИМАЯ",
["HERO_ROBOT_CLASS"] = "Осадный Голем",
["HERO_ROBOT_DESC"] = "Мастера-кузнецы Армии Тьмы превзошли сами себя, создав военного автоматона, которого они уместно назвали Боеголов. Оснащенный реактивными двигателями и свободный от эмоций, Боеголов бросается в бой, не различая друга и врага.",
["HERO_ROBOT_EXPLODE_DESCRIPTION_1"] = "Создает огненный взрыв, наносящий врагам %$heroes.hero_robot.explode.damage_min[1]%$-%$heroes.hero_robot.explode.damage_max[1]%$ взрывного урона и поджигая их на %$heroes.hero_robot.explode.burning_duration%$ секунды. Горение наносит %$heroes.hero_robot.explode.s_burning_damage[1]%$ урона в секунду.",
["HERO_ROBOT_EXPLODE_DESCRIPTION_2"] = "Создает огненный взрыв, наносящий врагам %$heroes.hero_robot.explode.damage_min[2]%$-%$heroes.hero_robot.explode.damage_max[2]%$ взрывного урона и поджигая их на %$heroes.hero_robot.explode.burning_duration%$ секунды. Горение наносит %$heroes.hero_robot.explode.s_burning_damage[2]%$ урона в секунду.",
["HERO_ROBOT_EXPLODE_DESCRIPTION_3"] = "Создает огненный взрыв, наносящий врагам %$heroes.hero_robot.explode.damage_min[3]%$-%$heroes.hero_robot.explode.damage_max[3]%$ взрывного урона и поджигая их на %$heroes.hero_robot.explode.burning_duration%$ секунды. Горение наносит %$heroes.hero_robot.explode.s_burning_damage[3]%$ урона в секунду.",
["HERO_ROBOT_EXPLODE_TITLE"] = "САМОСОЖЖЕНИЕ",
["HERO_ROBOT_FIRE_DESCRIPTION_1"] = "Выстреливает из пушки, полной раскаленных углей, нанося %$heroes.hero_robot.fire.damage_min[1]%$-%$heroes.hero_robot.fire.damage_max[1]%$ физического урона и замедляя врагов на %$heroes.hero_robot.fire.s_slow_duration[1]%$ секунд.",
["HERO_ROBOT_FIRE_DESCRIPTION_2"] = "Выстреливает из пушки, полной раскаленных углей, нанося %$heroes.hero_robot.fire.damage_min[2]%$-%$heroes.hero_robot.fire.damage_max[2]%$ физического урона и замедляя врагов на %$heroes.hero_robot.fire.s_slow_duration[1]%$ секунд.",
["HERO_ROBOT_FIRE_DESCRIPTION_3"] = "Выстреливает из пушки, полной раскаленных углей, нанося %$heroes.hero_robot.fire.damage_min[3]%$-%$heroes.hero_robot.fire.damage_max[3]%$ физического урона и замедляя врагов на %$heroes.hero_robot.fire.s_slow_duration[1]%$ секунд.",
["HERO_ROBOT_FIRE_TITLE"] = "ДЫМОВАЯ ЗАВЕСА",
["HERO_ROBOT_JUMP_DESCRIPTION_1"] = "Прыгает на врага, оглушая его на %$heroes.hero_robot.jump.stun_duration[1]%$ секунды и нанося %$heroes.hero_robot.jump.s_damage[1]%$ физического урона по площади.",
["HERO_ROBOT_JUMP_DESCRIPTION_2"] = "Прыгает на врага, оглушая его на %$heroes.hero_robot.jump.stun_duration[2]%$ секунды и нанося %$heroes.hero_robot.jump.s_damage[2]%$ физического урона по площади.",
["HERO_ROBOT_JUMP_DESCRIPTION_3"] = "Прыгает на врага, оглушая его на %$heroes.hero_robot.jump.stun_duration[3]%$ секунды и нанося %$heroes.hero_robot.jump.s_damage[3]%$ физического урона по площади.",
["HERO_ROBOT_JUMP_TITLE"] = "ДРОБЯЩИЙ УДАР",
["HERO_ROBOT_NAME"] = "Боеголов",
["HERO_ROBOT_TRAIN_DESCRIPTION_1"] = "Призывает боевую повозку, которая двигается по пути, нанося врагам %$heroes.hero_robot.ultimate.s_damage[2]%$ урона и поджигая их на %$heroes.hero_robot.ultimate.burning_duration%$ секунды. Горение наносит %$heroes.hero_robot.ultimate.s_burning_damage%$ урона в секунду.",
["HERO_ROBOT_TRAIN_DESCRIPTION_2"] = "Призывает боевую повозку, которая двигается по пути, нанося врагам %$heroes.hero_robot.ultimate.s_damage[3]%$ урона и поджигая их на %$heroes.hero_robot.ultimate.burning_duration%$ секунды. Горение наносит %$heroes.hero_robot.ultimate.s_burning_damage%$ урона в секунду.",
["HERO_ROBOT_TRAIN_DESCRIPTION_3"] = "Призывает боевую повозку, которая двигается по пути, нанося врагам %$heroes.hero_robot.ultimate.s_damage[4]%$ урона и поджигая их на %$heroes.hero_robot.ultimate.burning_duration%$ секунды. Горение наносит %$heroes.hero_robot.ultimate.s_burning_damage%$ урона в секунду.",
["HERO_ROBOT_TRAIN_MENUBOTTOM_DESCRIPTION"] = "Призывает боевую повозку, которая давит врагов.",
["HERO_ROBOT_TRAIN_MENUBOTTOM_NAME"] = "Моторная Голова",
["HERO_ROBOT_TRAIN_TITLE"] = "МОТОРНАЯ ГОЛОВА",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_1"] = "Наносит удар по врагу с менее чем %$heroes.hero_robot.uppercut.s_life_threshold[1]%$% здоровья, мгновенно убивая его.",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_2"] = "Наносит удар по врагу с менее чем %$heroes.hero_robot.uppercut.s_life_threshold[2]%$% здоровья, мгновенно убивая его.",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_3"] = "Наносит удар по врагу с менее чем %$heroes.hero_robot.uppercut.s_life_threshold[3]%$% здоровья, мгновенно убивая его.",
["HERO_ROBOT_UPPERCUT_TITLE"] = "ЖЕЛЕЗНЫЙ АППЕРКОТ",
["HERO_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_1"] = "Этот герой включен в кампанию \"Колоссальная Угроза\"",
["HERO_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_2"] = "Этот герой включён в кампанию «Путешествие Укуна».",
["HERO_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_1"] = "Кампания \"Колоссальная Угроза\"",
["HERO_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_2"] = "Кампания «Путешествие Укуна»",
["HERO_ROOM_EQUIPPED_HEROES"] = "Выбранные Герои",
["HERO_ROOM_GET_DLC"] = "ПОЛУЧИТЕ ЭТО",
["HERO_ROOM_LABEL_ROSTER_THUMB_NEW"] = "Новинка!",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_1"] = "Призывает магическое отражение Териен, атакующее врагов, нанося %$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[1]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[1]%$ магического урона.",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_2"] = "Призывает магическое отражение Териен, атакующее врагов, нанося %$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[2]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[2]%$ магического урона.",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_3"] = "Призывает магическое отражение Териен, атакующее врагов, нанося %$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[3]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[3]%$ магического урона.",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_ENTITY_NAME"] = "Астральное Отражение",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_TITLE"] = "АСТРАЛЬНОЕ ОТРАЖЕНИЕ",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_1"] = "Защищает союзного солдата, предотвращая до %$heroes.hero_space_elf.black_aegis.shield_base[1]%$ урона по нему. Щит взрывается через некоторое время или при истощении, нанося %$heroes.hero_space_elf.black_aegis.explosion_damage[1]%$ магического урона по площади.",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_2"] = "Защищает союзного солдата, предотвращая до %$heroes.hero_space_elf.black_aegis.shield_base[2]%$ урона по нему. Щит взрывается через некоторое время или при истощении, нанося %$heroes.hero_space_elf.black_aegis.explosion_damage[2]%$ магического урона по площади.",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_3"] = "Защищает союзного солдата, предотвращая до %$heroes.hero_space_elf.black_aegis.shield_base[3]%$ урона по нему. Щит взрывается через некоторое время или при истощении, нанося %$heroes.hero_space_elf.black_aegis.explosion_damage[3]%$ магического урона по площади.",
["HERO_SPACE_ELF_BLACK_AEGIS_TITLE"] = "ЧЕРНАЯ ЭГИДА",
["HERO_SPACE_ELF_CLASS"] = "Волшебница Пустоты",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_1"] = "Заточает группу врагов в пустоте на %$heroes.hero_space_elf.ultimate.duration[2]%$ секунд, нанося %$heroes.hero_space_elf.ultimate.damage[2]%$ урона.",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_2"] = "Заточает группу врагов в пустоте на %$heroes.hero_space_elf.ultimate.duration[3]%$ секунд, нанося %$heroes.hero_space_elf.ultimate.damage[3]%$ урона.",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_3"] = "Заточает группу врагов в пустоте на %$heroes.hero_space_elf.ultimate.duration[4]%$ секунд, нанося %$heroes.hero_space_elf.ultimate.damage[4]%$ урона.",
["HERO_SPACE_ELF_COSMIC_PRISON_MENUBOTTOM_DESCRIPTION"] = "Захватывает несколько врагов в области, нанося им урон.",
["HERO_SPACE_ELF_COSMIC_PRISON_MENUBOTTOM_NAME"] = "Космическая темница",
["HERO_SPACE_ELF_COSMIC_PRISON_TITLE"] = "КОСМИЧЕСКАЯ ТЕМНИЦА",
["HERO_SPACE_ELF_DESC"] = "Долгие годы сверстники избегали ее за вмешательства в неизвестные и потусторонние силы. Но теперь волшебница пустоты Териен стала одним из важнейших специалистов среди сил Альянса для понимания Наблюдателя и любых сил за гранью этого мира.",
["HERO_SPACE_ELF_NAME"] = "Териен",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_1"] = "Искривляет пространство вокруг всех башен на %$heroes.hero_space_elf.spatial_distortion.duration[1]%$ секунд, увеличивая их дальность на %$heroes.hero_space_elf.spatial_distortion.s_range_factor[1]%$%. ",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_2"] = "Искривляет пространство вокруг всех башен на %$heroes.hero_space_elf.spatial_distortion.duration[2]%$ секунд, увеличивая их дальность на %$heroes.hero_space_elf.spatial_distortion.s_range_factor[2]%$%. ",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_3"] = "Искривляет пространство вокруг всех башен на %$heroes.hero_space_elf.spatial_distortion.duration[3]%$ секунд, увеличивая их дальность на %$heroes.hero_space_elf.spatial_distortion.s_range_factor[3]%$%. ",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_TITLE"] = "ПРОСТРАНСТВЕННОЕ ИСКАЖЕНИЕ",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_1"] = "На %$heroes.hero_space_elf.void_rift.duration[1]%$ секунд открывает %$heroes.hero_space_elf.void_rift.cracks_amount[1]%$ трещину на пути, наносящую %$heroes.hero_space_elf.void_rift.s_damage_min[1]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[1]%$ урона в секунду врагам, стоящим на ней.",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_2"] = "На %$heroes.hero_space_elf.void_rift.duration[2]%$ секунд открывает %$heroes.hero_space_elf.void_rift.cracks_amount[2]%$ трещин на пути, каждая из которых наносит %$heroes.hero_space_elf.void_rift.s_damage_min[2]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[2]%$ урона в секунду врагам, стоящим на ней.",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_3"] = "На %$heroes.hero_space_elf.void_rift.duration[3]%$ секунд открывает %$heroes.hero_space_elf.void_rift.cracks_amount[3]%$ трещин на пути, каждая из которых наносит %$heroes.hero_space_elf.void_rift.s_damage_min[3]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[3]%$ урона в секунду врагам, стоящим на ней.",
["HERO_SPACE_ELF_VOID_RIFT_TITLE"] = "ПУСТОТНЫЙ РАЗЛОМ",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_1"] = "Призывает %$heroes.hero_spider.ultimate.spawn_amount[2]%$ пауков, которые сражаются в течение %$heroes.hero_spider.ultimate.spider.duration[2]%$ секунд, оглушая врагов при атаке.",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_2"] = "Призывает %$heroes.hero_spider.ultimate.spawn_amount[3]%$ пауков, которые сражаются в течение %$heroes.hero_spider.ultimate.spider.duration[3]%$ секунд, оглушая врагов при атаке.",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_3"] = "Призывает %$heroes.hero_spider.ultimate.spawn_amount[4]%$ пауков, которые сражаются в течение %$heroes.hero_spider.ultimate.spider.duration[4]%$ секунд, оглушая врагов при атаке.",
["HERO_SPIDER_ARACNID_SPAWNER_MENUBOTTOM_DESCRIPTION"] = "Призывает стаю оглушающих пауков.",
["HERO_SPIDER_ARACNID_SPAWNER_MENUBOTTOM_NAME"] = "Зов Охотника",
["HERO_SPIDER_ARACNID_SPAWNER_TITLE"] = "ЗОВ ОХОТНИКА",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_1"] = "Каждые %$heroes.hero_spider.area_attack.cooldown[1]%$ секунд Мизгирь заявляет о своем присутствии, оглушая ближайших врагов на %$heroes.hero_spider.area_attack.s_stun_time[1]%$ секунды.",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_2"] = "Каждые %$heroes.hero_spider.area_attack.cooldown[2]%$ секунд Мизгирь заявляет о своем присутствии, оглушая ближайших врагов на %$heroes.hero_spider.area_attack.s_stun_time[2]%$ секунды.",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_3"] = "Каждые %$heroes.hero_spider.area_attack.cooldown[3]%$ секунд Мизгирь заявляет о своем присутствии, оглушая ближайших врагов на %$heroes.hero_spider.area_attack.s_stun_time[3]%$ секунд.",
["HERO_SPIDER_AREA_ATTACK_TITLE"] = "ДЕМОРАЛИЗАЦИЯ",
["HERO_SPIDER_DESC"] = "Мизгирь – последняя выжившая из отряда Сумеречных Эльфов, которому было поручено уничтожить культ Паучьей Королевы. Смешайте темные искусства и несравненное охотничье мастрество, и вы получите одну из самых грозных убийц известных земель.",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_1"] = "Каждые %$heroes.hero_spider.instakill_melee.cooldown[1]%$ секунд Мизгирь может мгновенно убить оглушенного врага, имеющего не более %$heroes.hero_spider.instakill_melee.life_threshold[1]%$ здоровья.",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_2"] = "Каждые %$heroes.hero_spider.instakill_melee.cooldown[2]%$ секунд Мизгирь может мгновенно убить оглушенного врага, имеющего не более %$heroes.hero_spider.instakill_melee.life_threshold[2]%$ здоровья.",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_3"] = "Каждые %$heroes.hero_spider.instakill_melee.cooldown[3]%$ секунд Мизгирь может мгновенно убить оглушенного врага, имеющего не более %$heroes.hero_spider.instakill_melee.life_threshold[3]%$ здоровья.",
["HERO_SPIDER_INSTAKILL_MELEE_TITLE"] = "МЕРТВАЯ ХВАТКА",
["HERO_SPIDER_NAME"] = "Мизгирь",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_1"] = "В мгновение ока Мизгирь телепортируется к врагу с наибольшим запасом здоровья, нанося ему %$heroes.hero_spider.supreme_hunter.damage_min[1]%$-%$heroes.hero_spider.supreme_hunter.damage_max[1]%$ урона.",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_2"] = "В мгновение ока Мизгирь телепортируется к врагу с наибольшим запасом здоровья, нанося ему %$heroes.hero_spider.supreme_hunter.damage_min[2]%$-%$heroes.hero_spider.supreme_hunter.damage_max[2]%$ урона.",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_3"] = "В мгновение ока Мизгирь телепортируется к врагу с наибольшим запасом здоровья, нанося ему %$heroes.hero_spider.supreme_hunter.damage_min[3]%$-%$heroes.hero_spider.supreme_hunter.damage_max[3]%$ урона.",
["HERO_SPIDER_SUPREME_HUNTER_TITLE"] = "ТЕМНАЯ ПОСТУПЬ",
["HERO_SPIDER_TUNNELING_DESCRIPTION_1"] = "Теперь при появлении из под земли Мизгирь наносит %$heroes.hero_spider.tunneling.damage_min[1]%$-%$heroes.hero_spider.tunneling.damage_max[1]%$ урона по площади.",
["HERO_SPIDER_TUNNELING_DESCRIPTION_2"] = "Теперь при появлении из под земли Мизгирь наносит %$heroes.hero_spider.tunneling.damage_min[2]%$-%$heroes.hero_spider.tunneling.damage_max[2]%$ урона по площади.",
["HERO_SPIDER_TUNNELING_DESCRIPTION_3"] = "Теперь при появлении из под земли Мизгирь наносит %$heroes.hero_spider.tunneling.damage_min[3]%$-%$heroes.hero_spider.tunneling.damage_max[3]%$ урона по площади.",
["HERO_SPIDER_TUNNELING_TITLE"] = "РЫТЬЁ ХОДОВ",
["HERO_VENOM_CLASS"] = "Оскверненный Убийца",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_1"] = "Заполняет область липкой субстанцией, которая замедляет врагов и через несколько секунд превращается в пронзающие шипы, наносящие %$heroes.hero_venom.ultimate.s_damage[2]%$ чистого урона.",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_2"] = "Заполняет область липкой субстанцией, которая замедляет врагов и через несколько секунд превращается в пронзающие шипы, наносящие %$heroes.hero_venom.ultimate.s_damage[3]%$ чистого урона.",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_3"] = "Заполняет область липкой субстанцией, которая замедляет врагов и через несколько секунд превращается в пронзающие шипы, наносящие %$heroes.hero_venom.ultimate.s_damage[4]%$ чистого урона.",
["HERO_VENOM_CREEPING_DEATH_MENUBOTTOM_DESCRIPTION"] = "Создает на пути липкое вещество, которое замедляет врагов и наносит им урон.",
["HERO_VENOM_CREEPING_DEATH_MENUBOTTOM_NAME"] = "Ползучая Смерть",
["HERO_VENOM_CREEPING_DEATH_TITLE"] = "ПОЛЗУЧАЯ СМЕРТЬ",
["HERO_VENOM_DESC"] = "Воспротивившись превращению в отродье Культом, наемник Гримсон был заключен в темницу и оставлен гнить. Мучительный процесс даровал Гримсону способности к смене формы, которые он использовал для побега от Культа, поклявшись вернуться и отомстить.",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_1"] = "Гримсон пожирает врага с менее чем %$heroes.hero_venom.eat_enemy.hp_trigger%$% здоровья, восстанавливая при этом %$heroes.hero_venom.eat_enemy.regen[1]%$% от своего общего здоровья.",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_2"] = "Гримсон пожирает врага с менее чем %$heroes.hero_venom.eat_enemy.hp_trigger%$% здоровья, восстанавливая при этом %$heroes.hero_venom.eat_enemy.regen[2]%$% от своего общего здоровья.",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_3"] = "Гримсон пожирает врага с менее чем %$heroes.hero_venom.eat_enemy.hp_trigger%$% здоровья, восстанавливая при этом %$heroes.hero_venom.eat_enemy.regen[3]%$% от своего общего здоровья.",
["HERO_VENOM_EAT_ENEMY_TITLE"] = "СВЕЖАЯ ПЛОТЬ",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_1"] = "Распространяет на пути колючие щупальца, каждое из которых наносит %$heroes.hero_venom.floor_spikes.s_damage[1]%$ чистого урона ближайшим врагам.",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_2"] = "Распространяет на пути колючие щупальца, каждое из которых наносит %$heroes.hero_venom.floor_spikes.s_damage[2]%$ чистого урона ближайшим врагам.",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_3"] = "Распространяет на пути колючие щупальца, каждое из которых наносит %$heroes.hero_venom.floor_spikes.s_damage[3]%$ чистого урона ближайшим врагам.",
["HERO_VENOM_FLOOR_SPIKES_TITLE"] = "СМЕРТОНОСНЫЕ ШИПЫ",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_1"] = "Когда его здоровье опускается ниже %$heroes.hero_venom.inner_beast.trigger_hp%$%, Гримсон преобразуется полностью, увеличивая наносимый урон на %$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[1]%$% и восстанавливая %$heroes.hero_venom.inner_beast.basic_melee.regen_health%$% от общего здоровья за удар на протяжении %$heroes.hero_venom.inner_beast.duration%$ секунд.",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_2"] = "Когда его здоровье опускается ниже %$heroes.hero_venom.inner_beast.trigger_hp%$%, Гримсон преобразуется полностью, увеличивая наносимый урон на %$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[2]%$% и восстанавливая %$heroes.hero_venom.inner_beast.basic_melee.regen_health%$% от общего здоровья за удар на протяжении %$heroes.hero_venom.inner_beast.duration%$ секунд.",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_3"] = "Когда его здоровье опускается ниже %$heroes.hero_venom.inner_beast.trigger_hp%$%, Гримсон преобразуется полностью, увеличивая наносимый урон на %$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[3]%$% и восстанавливая %$heroes.hero_venom.inner_beast.basic_melee.regen_health%$% от общего здоровья за удар на протяжении %$heroes.hero_venom.inner_beast.duration%$ секунд.",
["HERO_VENOM_INNER_BEAST_TITLE"] = "ЗВЕРЬ ВНУТРИ",
["HERO_VENOM_NAME"] = "Гримсон",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_1"] = "Поражает врага на расстоянии, нанося %$heroes.hero_venom.ranged_tentacle.s_damage[1]%$ физического урона с %$heroes.hero_venom.ranged_tentacle.bleed_chance[1]%$% шансом вызвать кровотечение. Кровотечение наносит %$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$ урона в секунду на протяжении %$heroes.hero_venom.ranged_tentacle.bleed_duration[1]%$ секунд.",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_2"] = "Поражает врага на расстоянии, нанося %$heroes.hero_venom.ranged_tentacle.s_damage[2]%$ физического урона с %$heroes.hero_venom.ranged_tentacle.bleed_chance[2]%$% шансом вызвать кровотечение. Кровотечение наносит %$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$ урона в секунду на протяжении %$heroes.hero_venom.ranged_tentacle.bleed_duration[2]%$ секунд.",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_3"] = "Поражает врага на расстоянии, нанося %$heroes.hero_venom.ranged_tentacle.s_damage[3]%$ физического урона с %$heroes.hero_venom.ranged_tentacle.bleed_chance[3]%$% шансом вызвать кровотечение. Кровотечение наносит %$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$ урона в секунду на протяжении %$heroes.hero_venom.ranged_tentacle.bleed_duration[3]%$ секунд.",
["HERO_VENOM_RANGED_TENTACLE_TITLE"] = "СЕРДЦЕЕД",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_1"] = "Обрушивает на зону %$heroes.hero_vesper.ultimate.s_spread[2]%$ стрел, каждая из которых наносит %$heroes.hero_vesper.ultimate.damage[2]%$ физического урона врагам.",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_2"] = "Обрушивает на зону %$heroes.hero_vesper.ultimate.s_spread[3]%$ стрел, каждая из которых наносит %$heroes.hero_vesper.ultimate.damage[3]%$ физического урона врагам.",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_3"] = "Обрушивает на зону %$heroes.hero_vesper.ultimate.s_spread[4]%$ стрел, каждая из которых наносит %$heroes.hero_vesper.ultimate.damage[4]%$ физического урона врагам.",
["HERO_VESPER_ARROW_STORM_MENUBOTTOM_DESCRIPTION"] = "Обрушивает на область стрелы, нанося урон врагам.",
["HERO_VESPER_ARROW_STORM_MENUBOTTOM_NAME"] = "Град Стрел",
["HERO_VESPER_ARROW_STORM_TITLE"] = "ГРАД СТРЕЛ",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_1"] = "Выпускает стрелу, оглушающую врага на %$heroes.hero_vesper.arrow_to_the_knee.stun_duration[1]%$ секунды и наносящую %$heroes.hero_vesper.arrow_to_the_knee.s_damage[1]%$ физического урона.",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_2"] = "Выпускает стрелу, оглушающую врага на %$heroes.hero_vesper.arrow_to_the_knee.stun_duration[2]%$ секунды и наносящую %$heroes.hero_vesper.arrow_to_the_knee.s_damage[2]%$ физического урона.",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_3"] = "Выпускает стрелу, оглушающую врага на %$heroes.hero_vesper.arrow_to_the_knee.stun_duration[3]%$ секунду и наносящую %$heroes.hero_vesper.arrow_to_the_knee.s_damage[3]%$ физического урона.",
["HERO_VESPER_ARROW_TO_THE_KNEE_TITLE"] = "СТРЕЛА В КОЛЕНО",
["HERO_VESPER_CLASS"] = "Kоролевский Капитан",
["HERO_VESPER_DESC"] = "Искусно владея как мечом, так и луком, Веспер завоевал свое место командира линерийских сил. После падения Линерии и исчезновения короля Денаса он собрал все войска, которые смог, и начал поход, чтобы вернуть пропавшего правителя.",
["HERO_VESPER_DISENGAGE_DESCRIPTION_1"] = "Когда его здоровье опускается ниже %$heroes.hero_vesper.disengage.hp_to_trigger%$%, Веспер уклоняется от следующей ближней атаки, отпрыгивая назад. Затем он выпускает три стрелы, каждая из которых наносит %$heroes.hero_vesper.disengage.s_damage[1]%$ физического урона ближайшим врагам.",
["HERO_VESPER_DISENGAGE_DESCRIPTION_2"] = "Когда его здоровье опускается ниже %$heroes.hero_vesper.disengage.hp_to_trigger%$%, Веспер уклоняется от следующей ближней атаки, отпрыгивая назад. Затем он выпускает три стрелы, каждая из которых наносит %$heroes.hero_vesper.disengage.s_damage[2]%$ физического урона ближайшим врагам.",
["HERO_VESPER_DISENGAGE_DESCRIPTION_3"] = "Когда его здоровье опускается ниже %$heroes.hero_vesper.disengage.hp_to_trigger%$%, Веспер уклоняется от следующей ближней атаки, отпрыгивая назад. Затем он выпускает три стрелы, каждая из которых наносит %$heroes.hero_vesper.disengage.s_damage[3]%$ физического урона ближайшим врагам.",
["HERO_VESPER_DISENGAGE_TITLE"] = "ОТСТУПЛЕНИЕ",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_1"] = "Трижды ударяет врага, нанося %$heroes.hero_vesper.martial_flourish.s_damage[1]%$ физического урона.",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_2"] = "Трижды ударяет врага, нанося %$heroes.hero_vesper.martial_flourish.s_damage[2]%$ физического урона.",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_3"] = "Трижды ударяет врага, нанося %$heroes.hero_vesper.martial_flourish.s_damage[3]%$ физического урона.",
["HERO_VESPER_MARTIAL_FLOURISH_TITLE"] = "МАСТЕРСТВО БОЯ",
["HERO_VESPER_NAME"] = "Веспер",
["HERO_VESPER_RICOCHET_DESCRIPTION_1"] = "Выпускает стрелу, отскакивающую между %$heroes.hero_vesper.ricochet.s_bounces[1]%$ врагами, каждый раз нанося %$heroes.hero_vesper.ricochet.s_damage[1]%$ физического урона.",
["HERO_VESPER_RICOCHET_DESCRIPTION_2"] = "Выпускает стрелу, отскакивающую между %$heroes.hero_vesper.ricochet.s_bounces[2]%$ врагами, каждый раз нанося %$heroes.hero_vesper.ricochet.s_damage[2]%$ физического урона.",
["HERO_VESPER_RICOCHET_DESCRIPTION_3"] = "Выпускает стрелу, отскакивающую между %$heroes.hero_vesper.ricochet.s_bounces[3]%$ врагами, каждый раз нанося %$heroes.hero_vesper.ricochet.s_damage[3]%$ физического урона.",
["HERO_VESPER_RICOCHET_TITLE"] = "РИКОШЕТ",
["HERO_WITCH_CLASS"] = "Ведьма-шутница",
["HERO_WITCH_DESC"] = "Хотя она и любит удивлять путников по Волшебному Лесу забавными и безвредными фокусами, те, кто представляет угрозу лесам или ее собратьям-карликам, вскоре выяснят, что за игривой улыбкой скрывается безжалостная ведьма, с которой следует считаться.",
["HERO_WITCH_DISENGAGE_DESCRIPTION_1"] = "Когда ее здоровье опускается ниже %$heroes.hero_witch.disengage.hp_to_trigger%$%, Стрэги телепортируется назад, оставляя сражаться приманку вместо себя. Приманка имеет %$heroes.hero_witch.disengage.decoy.hp_max[1]%$ здоровья и взрывается при уничтожении, оглушая врагов на %$heroes.hero_witch.disengage.decoy.explotion.stun_duration[1]%$ секунду.",
["HERO_WITCH_DISENGAGE_DESCRIPTION_2"] = "Когда ее здоровье опускается ниже %$heroes.hero_witch.disengage.hp_to_trigger%$%, Стрэги телепортируется назад, оставляя сражаться приманку вместо себя. Приманка имеет %$heroes.hero_witch.disengage.decoy.hp_max[2]%$ здоровья и взрывается при уничтожении, оглушая врагов на %$heroes.hero_witch.disengage.decoy.explotion.stun_duration[2]%$ секунды.",
["HERO_WITCH_DISENGAGE_DESCRIPTION_3"] = "Когда ее здоровье опускается ниже %$heroes.hero_witch.disengage.hp_to_trigger%$%, Стрэги телепортируется назад, оставляя сражаться приманку вместо себя. Приманка имеет %$heroes.hero_witch.disengage.decoy.hp_max[3]%$ здоровья и взрывается при уничтожении, оглушая врагов на %$heroes.hero_witch.disengage.decoy.explotion.stun_duration[3]%$ секунды.",
["HERO_WITCH_DISENGAGE_TITLE"] = "ОЧАРОВЫВАЮЩАЯ ОБМАНКА",
["HERO_WITCH_NAME"] = "Стреги",
["HERO_WITCH_PATH_AOE_DESCRIPTION_1"] = "Разливает гигантское зелье на пути, нанося %$heroes.hero_witch.skill_path_aoe.s_damage[1]%$ магического урона по площади и замедляя врагов на %$heroes.hero_witch.skill_path_aoe.duration[1]%$ секунд.",
["HERO_WITCH_PATH_AOE_DESCRIPTION_2"] = "Разливает гигантское зелье на пути, нанося %$heroes.hero_witch.skill_path_aoe.s_damage[2]%$ магического урона по площади и замедляя врагов на %$heroes.hero_witch.skill_path_aoe.duration[2]%$ секунд.",
["HERO_WITCH_PATH_AOE_DESCRIPTION_3"] = "Разливает гигантское зелье на пути, нанося %$heroes.hero_witch.skill_path_aoe.s_damage[2]%$ магического урона по площади и замедляя врагов на %$heroes.hero_witch.skill_path_aoe.duration[2]%$ секунд.",
["HERO_WITCH_PATH_AOE_TITLE"] = "РАССЕЧЬ ВОЗДУХ И РАЗЛИТЬ",
["HERO_WITCH_POLYMORPH_DESCRIPTION_1"] = "Превращает врага в Тыквика на %$heroes.hero_witch.skill_polymorph.duration[1]%$ секунд. Тыквик имеет %$heroes.hero_witch.skill_polymorph.pumpkin.hp[1]%$% здоровья цели.",
["HERO_WITCH_POLYMORPH_DESCRIPTION_2"] = "Превращает врага в Тыквика на %$heroes.hero_witch.skill_polymorph.duration[2]%$ секунд. Тыквик имеет %$heroes.hero_witch.skill_polymorph.pumpkin.hp[2]%$% здоровья цели.",
["HERO_WITCH_POLYMORPH_DESCRIPTION_3"] = "Превращает врага в Тыквика на %$heroes.hero_witch.skill_polymorph.duration[3]%$ секунд. Тыквик имеет %$heroes.hero_witch.skill_polymorph.pumpkin.hp[3]%$% здоровья цели.",
["HERO_WITCH_POLYMORPH_TITLE"] = "ОВОЩЕНИЕ!",
["HERO_WITCH_SOLDIERS_DESCRIPTION_1"] = "Призывает %$heroes.hero_witch.skill_soldiers.soldiers_amount[1]%$ кошку сражаться с врагами. Кошка имеет %$heroes.hero_witch.skill_soldiers.soldier.hp_max[1]%$ здоровья и наносит %$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[1]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[1]%$ физического урона.",
["HERO_WITCH_SOLDIERS_DESCRIPTION_2"] = "Призывает %$heroes.hero_witch.skill_soldiers.soldiers_amount[2]%$ кошек сражаться с врагами. Кошки имеют %$heroes.hero_witch.skill_soldiers.soldier.hp_max[2]%$ здоровья и наносят %$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[2]%$ физического урона.",
["HERO_WITCH_SOLDIERS_DESCRIPTION_3"] = "Призывает %$heroes.hero_witch.skill_soldiers.soldiers_amount[3]%$ кошек сражаться с врагами. Кошки имеют %$heroes.hero_witch.skill_soldiers.soldier.hp_max[3]%$ здоровья и наносят %$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[3]%$ физического урона.",
["HERO_WITCH_SOLDIERS_TITLE"] = "НОЧНЫЕ ФУРИИ",
["HERO_WITCH_ULTIMATE_DESCRIPTION_1"] = "Телепортирует %$heroes.hero_witch.ultimate.max_targets[2]%$ врагов назад, усыпляя их на %$heroes.hero_witch.ultimate.duration[2]%$ секунды.",
["HERO_WITCH_ULTIMATE_DESCRIPTION_2"] = "Телепортирует %$heroes.hero_witch.ultimate.max_targets[3]%$ врагов назад, усыпляя их на %$heroes.hero_witch.ultimate.duration[3]%$ секунды.",
["HERO_WITCH_ULTIMATE_DESCRIPTION_3"] = "Телепортирует %$heroes.hero_witch.ultimate.max_targets[4]%$ врагов назад, усыпляя их на %$heroes.hero_witch.ultimate.duration[4]%$ секунд.",
["HERO_WITCH_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "Телепортирует врагов назад, ненадолго усыпляя их.",
["HERO_WITCH_ULTIMATE_MENUBOTTOM_NAME"] = "Назад, в Кровать!",
["HERO_WITCH_ULTIMATE_TITLE"] = "НАЗАД, В КРОВАТЬ!",
["HERO_WUKONG_CLASS"] = "Царь Обезьян",
["HERO_WUKONG_DESC"] = "Рождённый из небесного камня Инь и Ян, Сунь Укун получил силу, ловкость и бессмертие. Но короли-демоны украли у него сферы силы. Теперь легендарный обманщик восстаёт, чтобы вернуть их, пока не стало слишком поздно.",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_1"] = "Падает и увеличивает посох Цзиньгу, чтобы растоптать врага, мгновенно убивая его и нанося %$heroes.hero_wukong.giant_staff.area_damage.damage_min[1]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[1]%$ урона по области вокруг цели.",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_2"] = "Спотыкается и увеличивает Цзиньгу Банг, чтобы растоптать врага, мгновенно убивая его и нанося %$heroes.hero_wukong.giant_staff.area_damage.damage_min[2]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[2]%$ урона по области вокруг цели.",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_3"] = "Спотыкается и увеличивает Цзиньгу Бан, чтобы растоптать врага, мгновенно убивая его и нанося %$heroes.hero_wukong.giant_staff.area_damage.damage_min[3]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[3]%$ урона по области вокруг цели.",
["HERO_WUKONG_GIANT_STAFF_TITLE"] = "Техника Цзиньгу-Бан",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_1"] = "Призывает 2 волосатых клона Сунь Укуна для сражения на его стороне. Они наносят %$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[1]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[1]%$ урона и существуют %$heroes.hero_wukong.hair_clones.soldier.duration[1]%$ секунд.",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_2"] = "Призывает 2 клона из волос Сунь Укуна, чтобы сражались на его стороне. Они наносят %$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[2]%$ урона и существуют %$heroes.hero_wukong.hair_clones.soldier.duration[2]%$ сек.",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_3"] = "Призывает 2 клонов из волос Сунь Укуна, чтобы сражались на его стороне. Они наносят %$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[3]%$–%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[3]%$ урона и существуют %$heroes.hero_wukong.hair_clones.soldier.duration[3]%$ сек.",
["HERO_WUKONG_HAIR_CLONES_TITLE"] = "Hair Clones",
["HERO_WUKONG_NAME"] = "Sun Wukong",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_1"] = "Запускает Цзиньгу Бан в воздух, разделяя его на %$heroes.hero_wukong.pole_ranged.pole_amounts[1]%$ посохов, которые падают на врагов, каждый нанося %$heroes.hero_wukong.pole_ranged.damage_min[1]%$ урона и оглушая врагов в небольшой области.",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_2"] = "Запускает Цзиньгу Бан в воздух, разделяя его на %$heroes.hero_wukong.pole_ranged.pole_amounts[2]%$ посохов, которые падают на врагов, каждый нанося %$heroes.hero_wukong.pole_ranged.damage_min[2]%$ урона и оглушая врагов в небольшой области.",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_3"] = "Запускает Цзиньгу Бан в воздух, разделяя его на %$heroes.hero_wukong.pole_ranged.pole_amounts[3]%$ посохов, которые падают на врагов, каждый нанося %$heroes.hero_wukong.pole_ranged.damage_min[3]%$ урона и оглушая врагов в небольшой области.",
["HERO_WUKONG_POLE_RANGED_TITLE"] = "Шквал Шестов",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_1"] = "Белый дракон с огромной силой врезается в землю, нанося %$heroes.hero_wukong.ultimate.damage_total[2]%$ истинного урона и оставляя зону замедления.",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_2"] = "Белый Дракон с огромной силой врезается в землю, нанося %$heroes.hero_wukong.ultimate.damage_total[3]%$ истинного урона и оставляя замедляющую зону.",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_3"] = "Белый дракон с огромной силой врезается в землю, нанося %$heroes.hero_wukong.ultimate.damage_total[4]%$ истинного урона и оставляя зону замедления.",
["HERO_WUKONG_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "Призывает Белого Дракона.",
["HERO_WUKONG_ULTIMATE_MENUBOTTOM_NAME"] = "Белый Дракон",
["HERO_WUKONG_ULTIMATE_TITLE"] = "Белый Дракон",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_1"] = "Чжу Бацзе, верный спутник Сунь Укуна, следует за ним повсюду. Наносит %$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[1]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[1]%$ урона и имеет небольшой шанс нанести мощную атаку по области.",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_2"] = "Чжу Бацзе, верный спутник Сунь Укуна, следует за ним повсюду. Наносит %$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[2]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[2]%$ урона и имеет небольшой шанс нанести мощную атаку по области.",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_3"] = "Чжу Бацзе, верный спутник Сунь Укуна, повсюду следует за ним. Наносит %$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[3]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[3]%$ урона и имеет небольшой шанс нанести мощную атаку по области.",
["HERO_WUKONG_ZHU_APPRENTICE_TITLE"] = "Ученик Чжу",
["HOURS_ABBREVIATION"] = "ч",
["Hero at your command!"] = "Герой в вашем распоряжении!",
["Heroes are elite units that can face strong enemies and support your forces."] = "Герои — это элитные бойцы, которые могут противостоять сильным врагам и оказывать поддержку вашим войскам.",
["Heroes gain experience every time they damage an enemy or use an ability."] = "Герои получают опыт при каждом нанесении урона врагу или использовании умения.",
["IAP_CONNECTION_ERROR"] = "%@: ошибка подключения",
["INCOMING NEXT WAVE!"] = "СЛЕДУЮЩАЯ ВОЛНА!",
["INCOMING WAVE"] = "НОВАЯ ВОЛНА",
["INGAME_BALLOON_BUILD_HERE"] = "Строить здесь!",
["INGAME_BALLOON_GOAL"] = "Не дайте врагам пройти за эту точку.",
["INGAME_BALLOON_GOLD"] = "Зарабатывайте золото, убивая врагов",
["INGAME_BALLOON_INCOMING"] = "СЛЕДУЮЩАЯ ВОЛНА НА ПОДХОДЕ!",
["INGAME_BALLOON_NEW_HERO"] = "Новый герой!",
["INGAME_BALLOON_NEW_POWER"] = "Новый навык!",
["INGAME_BALLOON_NOTIFICATION_TAP_HERE"] = "Нажмите здесь!",
["INGAME_BALLOON_SELECT_HERO"] = "Нажмите, чтобы выбрать!",
["INGAME_BALLOON_START_BATTLE"] = "НАЧАТЬ БИТВУ!",
["INGAME_BALLOON_TAP_HERE"] = "Нажмите на дорогу",
["INGAME_BALLOON_TAP_TO_CALL"] = "НАЖМИТЕ ДЛЯ ПРИЗЫВА РАНЬШЕ СРОКА",
["INGAME_BALLOON_TAP_TWICE_BUILD"] = "Нажмите дважды, чтобы построить башню",
["INGAME_BALLOON_TAP_TWICE_START"] = "НАЖМИТЕ ДВАЖДЫ, ЧТОБЫ НАЧАТЬ БИТВУ",
["INGAME_BALLOON_TAP_TWICE_WAVE"] = "Нажмите дважды, чтобы вызвать волну",
["INGAME_TUTORIAL1_HELP1"] = "Не дайте врагам пройти за эту точку.",
["INGAME_TUTORIAL1_HELP2"] = "Постройте башни для защиты дороги.",
["INGAME_TUTORIAL1_HELP3"] = "Зарабатывайте золото, убивая врагов.",
["INGAME_TUTORIAL1_SUBTITLE1"] = "Защищайте свои земли от вражеских атак.",
["INGAME_TUTORIAL1_SUBTITLE2"] = "Стройте оборонительные башни вдоль дороги, чтобы остановить их.",
["INGAME_TUTORIAL1_TITLE"] = "Цель",
["INGAME_TUTORIAL_GOTCHA_1"] = "Ясно!",
["INGAME_TUTORIAL_GOTCHA_2"] = "Начнем!",
["INGAME_TUTORIAL_HINT"] = "ПОДСКАЗКА",
["INGAME_TUTORIAL_INSTRUCTIONS"] = "ИНСТРУКЦИИ",
["INGAME_TUTORIAL_NEW_TIP"] = "НОВЫЙ СОВЕТ",
["INGAME_TUTORIAL_NEXT"] = "Далее!",
["INGAME_TUTORIAL_OK"] = "Ок!",
["INGAME_TUTORIAL_SKIP"] = "Пропустить!",
["INGAME_TUTORIAL_TIP_CHALLENGE"] = "ПРЕДУПРЕЖДЕНИЕ",
["ITEM_CLUSTER_BOMB_BOTTOM_DESC"] = "Как попкорн, но намного веселее и менее вкусно.",
["ITEM_CLUSTER_BOMB_BOTTOM_INFO"] = "Бомба, которая создает новые меньшие бомбы.",
["ITEM_CLUSTER_BOMB_DESC"] = "Бросьте бомбу, которая наносит урон врагам в зоне и запускает меньшие бомбы вокруг нее.",
["ITEM_CLUSTER_BOMB_NAME"] = "Кассетная Бомба",
["ITEM_DEATHS_TOUCH_BOTTOM_DESC"] = "Прекрасная вещь, дабы почувствовать себя богом... СМЕРТИ!",
["ITEM_DEATHS_TOUCH_BOTTOM_INFO"] = "Выберите. Коснитесь вашей цели. Убейте.",
["ITEM_DEATHS_TOUCH_DESC"] = "Обуздайте силу Смерти и коснитесь любого врага, чтобы мгновенно уничтожить его. Не работает на боссах и мини-боссах. ",
["ITEM_DEATHS_TOUCH_NAME"] = "Прикосновение Смерти",
["ITEM_LOOT_BOX_BOTTOM_DESC"] = "Пара таких – и ты обеспечен на всю жизнь.",
["ITEM_LOOT_BOX_BOTTOM_INFO"] = "Сбросьте на дорогу ящик, нанеся урон врагам и мгновенно получив золото.",
["ITEM_LOOT_BOX_DESC"] = "Сбросьте на дорогу ящик, нанеся урон врагам и мгновенно получив 300 золота.",
["ITEM_LOOT_BOX_NAME"] = "Лутбокс",
["ITEM_MEDICAL_KIT_BOTTOM_DESC"] = "Все для того, чтобы подлататься, генерал.",
["ITEM_MEDICAL_KIT_BOTTOM_INFO"] = "Восстанавливает игроку до 3 сердец.",
["ITEM_MEDICAL_KIT_DESC"] = "Специальный комплект, восстанавливающий игроку до 3 сердец.",
["ITEM_MEDICAL_KIT_NAME"] = "Аптечка",
["ITEM_PORTABLE_COIL_BOTTOM_DESC"] = "Бзззз! Как уж на сковородке!",
["ITEM_PORTABLE_COIL_BOTTOM_INFO"] = "Установите ловушку, которая оглушает врагов в зоне и наносит им урон.",
["ITEM_PORTABLE_COIL_DESC"] = "Установите ловушку, которая оглушает активировавших ее врагов и наносит им урон. Ее эффекты могут распространяться на ближайших врагов.",
["ITEM_PORTABLE_COIL_NAME"] = "Переносная Катушка",
["ITEM_ROOM_EQUIP"] = "Выбрать",
["ITEM_ROOM_EQUIPPED"] = "Выбран",
["ITEM_ROOM_EQUIPPED_ITEMS"] = "Выбранные предметы",
["ITEM_SCROLL_OF_SPACESHIFT_BOTTOM_DESC"] = "Не хватает времени на врагов? Больше не беспокойтесь!",
["ITEM_SCROLL_OF_SPACESHIFT_BOTTOM_INFO"] = "Телепортируйте назад группу врагов.",
["ITEM_SCROLL_OF_SPACESHIFT_DESC"] = "Телепортируйте назад до 10 врагов.",
["ITEM_SCROLL_OF_SPACESHIFT_NAME"] = "Свиток Смещения",
["ITEM_SECOND_BREATH_BOTTOM_DESC"] = "Поднимает из могилы без недостатков мертвецов.",
["ITEM_SECOND_BREATH_BOTTOM_INFO"] = "Воскрешает павших героев, исцеляет раненых и мгновенно перезаряжает способности героев.",
["ITEM_SECOND_BREATH_DESC"] = "Божественное благословение, которое воскрешает павших героев, исцеляет раненых и мгновенно перезаряжает способности героев.",
["ITEM_SECOND_BREATH_NAME"] = "Второе Дыхание",
["ITEM_SUMMON_BLACKBURN_BOTTOM_DESC"] = "Единственный. Неповторимый.",
["ITEM_SUMMON_BLACKBURN_BOTTOM_INFO"] = "Призовите могучего Блэкберна, который сразится на вашей стороне.",
["ITEM_SUMMON_BLACKBURN_DESC"] = "Призовите могучего воина-ревенанта, чтобы расправится с вашими врагами.",
["ITEM_SUMMON_BLACKBURN_NAME"] = "Шлем Блэкберна",
["ITEM_VEZNAN_WRATH_BOTTOM_DESC"] = "Дайте им вкусить толику безграничной мощи Темного Волшебника!",
["ITEM_VEZNAN_WRATH_BOTTOM_INFO"] = "Испепеляет всех врагов на поле боя.",
["ITEM_VEZNAN_WRATH_DESC"] = "Вез'нан произносит мощное заклинание, испепеляющее каждого врага на поле боя.",
["ITEM_VEZNAN_WRATH_NAME"] = "Гнев Вез'нана",
["ITEM_WINTER_AGE_BOTTOM_DESC"] = "Также полезно, если вам просто ОЧЕНЬ не нравится лето.",
["ITEM_WINTER_AGE_BOTTOM_INFO"] = "Заморозьте всех врагов на экране.",
["ITEM_WINTER_AGE_DESC"] = "Мощное заклинание, которое создает ледяные ветра, замораживающие всех врагов на несколько секунд.",
["ITEM_WINTER_AGE_NAME"] = "Ледниковый Период",
["If you enjoy using %@, would you mind taking a moment to rate it? It won't take more than a minute. Thanks for your support!"] = "Если Вам нравится %@, пожалуйста, поставьте свою оценку. Это займет у Вас не более минуты.\n Спасибо за поддержку!",
["Impossible"] = "БЕЗУМЕЦ",
["Iron Challenge"] = "Железное испытание",
["KR5_NO_GEMS"] = "У вас недостаточно кристаллов.\nХотите купить еще?",
["KR5_PURCHASE_ERROR"] = "Произошла ошибка при обработке вашей покупки.",
["KR5_RATE_US"] = "Вам нравится игра? Оцените нас в магазине!",
["LEVEL_10_HEROIC"] = "Героическое описание 10",
["LEVEL_10_HISTORY"] = "Оказывается, Культ использует добытые кристаллы для строительства зловещего артефакта прямо на пороге каньона. Воздух тяжелеет, становится трудно дышать, окружающее пространство наполнено гулом странной энергии... Прежде чем продвигаться дальше, мы должны убедиться, что артефакт будет уничтожен.",
["LEVEL_10_IRON"] = "Железное Описание 10",
["LEVEL_10_IRON_UNLOCK"] = "Будет определено",
["LEVEL_10_MODES_UPGRADES"] = "Макс. 5 уровень",
["LEVEL_10_TITLE"] = "10. Двор Храма",
["LEVEL_11_HEROIC"] = "Героическое описание 11",
["LEVEL_11_HISTORY"] = "Мы наконец выбрались из каньона, однако мы все еще далеки от конца. Теперь мы стоим перед гигантским порталом, обитым кристаллами, пока Провидица Мидриас завершает свои ритуалы. Мы не знаем, что явится извне, но мы не дрогнем. Готовьтесь!",
["LEVEL_11_IRON"] = "Железное Описание 11",
["LEVEL_11_IRON_UNLOCK"] = "Будет определено",
["LEVEL_11_MODES_UPGRADES"] = "Макс. 5 уровень",
["LEVEL_11_TITLE"] = "11. Плато Каньона",
["LEVEL_12_HEROIC"] = "Героическое описание 12",
["LEVEL_12_HISTORY"] = "Теперь, когда Денас снова с нами, мы храбро шагнули через портал в неизвестность. Этот странный мир – будто искаженное отражение Линерии, опустошенное и поглощенное чумой. Смотрите под ноги, ведь в темноте может таиться нечто куда более страшное, чем Культ.",
["LEVEL_12_IRON"] = "Железное Описание 12",
["LEVEL_12_IRON_UNLOCK"] = "Будет определено",
["LEVEL_12_MODES_UPGRADES"] = "Макс. 5 уровень",
["LEVEL_12_TITLE"] = "12. Отравленные Угодья",
["LEVEL_13_HEROIC"] = "Героическое описание 13",
["LEVEL_13_HISTORY"] = "Знакомый силуэт Храма Гроз маячит на горизонте. Путь предельно ясен: следовать за зловонием и скверной, и мы непременно найдем их источник. Нам лишь нужно выжить среди этих искаженных ужасов, которые словно поднимаются прямо из земли...",
["LEVEL_13_IRON"] = "Железное Описание 13",
["LEVEL_13_IRON_UNLOCK"] = "Будет определено",
["LEVEL_13_MODES_UPGRADES"] = "Макс. 5 уровень",
["LEVEL_13_TITLE"] = "13. Оскверненный Храм",
["LEVEL_14_HEROIC"] = "Героическое описание 14",
["LEVEL_14_HISTORY"] = "Эти проклятые существа, кажется, появляются из ниоткуда! Солдаты не знают ни сна, ни отдыха, а все, к чему мы прикасаемся, кажется живым и готовым напасть на нас, как будто сама земля сопротивляется нам со всей своей мощью. Провидица Мидриас и ее приспешники должны быть рядом.",
["LEVEL_14_IRON"] = "Железное Описание 14",
["LEVEL_14_IRON_UNLOCK"] = "Будет определено",
["LEVEL_14_MODES_UPGRADES"] = "Макс. 5 уровень",
["LEVEL_14_TITLE"] = "14. Долина Порчи",
["LEVEL_15_HEROIC"] = "Героическое описание 15",
["LEVEL_15_HISTORY"] = "Мы вышли из долины победителями, и теперь единственное, что отделяет нас от Наблюдателя, это сама Мидриас. Тогда, в каньонах, мы видели, на что она способна, но здесь, под покровительством ее хозяина, она куда страшнее. Не то чтобы опасности когда-либо останавливали нас раньше. К бою!",
["LEVEL_15_IRON"] = "Железное Описание 15",
["LEVEL_15_IRON_UNLOCK"] = "Будет определено",
["LEVEL_15_MODES_UPGRADES"] = "Макс. 5 уровень",
["LEVEL_15_TITLE"] = "15. Вырвиглазная Башня",
["LEVEL_16_HEROIC"] = "Героическое описание 16",
["LEVEL_16_HISTORY"] = "Мидриас больше нет, и Наблюдатель остается нашим последним и главным врагом. Это наш единственный шанс навсегда положить конец Культу и вторжению. Что произойдет дальше – не важно, если мы не встанем плечом к плечу в этот последний раз... Вперед!",
["LEVEL_16_IRON"] = "Железное Описание 16",
["LEVEL_16_IRON_UNLOCK"] = "Будет определено",
["LEVEL_16_MODES_UPGRADES"] = "Макс. 5 уровень",
["LEVEL_16_TITLE"] = "16. Пик Голода",
["LEVEL_17_HISTORY"] = "Окрестности некогда причудливого Волшебного Леса теперь выглядят недружелюбно и устрашающе. Ходят слухи, что орды павших эльфийских воинов и призрачных существ теперь бродят по этим землям, нападая на путешественников и оскверняя сам лес своим присутствием. Генерал, мы должны исследовать проблему. ",
["LEVEL_17_TITLE"] = "17. Мглистые Развалины",
["LEVEL_18_HISTORY"] = "Пришла весть с Заставы Глубоколесья, где немногочисленные эльфы едва держатся под натиском орд павших. Мы должны спешить на помощь им и их капитану, Эридану, пока еще не поздно. Удержав заставу, мы сможем наступать дальше и добраться до корня этого зла. ",
["LEVEL_18_TITLE"] = "18. Застава Глубоколесья",
["LEVEL_19_HISTORY"] = "Усталый Эридан указал нам путь к Храму Павших, откуда марширует орда ревенантов для захвата континента. Этим войском командует маг, известный по имени Навира, Повелитель Душ. Его нужно остановить во что бы то ни стало! ",
["LEVEL_19_TITLE"] = "19. Храм Павших",
["LEVEL_1_HEROIC"] = "Героическое Описание 1",
["LEVEL_1_HISTORY"] = "Уже несколько месяцев мы безуспешно прочесываем южные леса, не находя ни единого следа Короля Денаса. Между тем, мы подружились с Древесниками, духами природы, и встретили их воинственных соседей, Дикозверей, нападающих на нас при любой возможности.\nПокончим с этим, чтобы мы могли продолжить поиски Короля!",
["LEVEL_1_IRON"] = "Железное Описание 1",
["LEVEL_1_IRON_UNLOCK"] = "Королевские Лучники\nЗавет Паладинов",
["LEVEL_1_MODES_UPGRADES"] = "Макс. 1 уровень",
["LEVEL_1_TITLE"] = "1. Море Деревьев",
["LEVEL_20_HISTORY"] = "Мы получили срочное сообщение от Древесников на краю леса, что отчаянно взывают о помощи. Они атакованы безжалостными Кроками. Долго они не продержатся. Помните об осторожности, Генерал. Эти чешуйчатые твари остры и зубом, и умом.",
["LEVEL_20_TITLE"] = "20. Деревушка Древесников",
["LEVEL_21_HISTORY"] = "Город в безопасности! Но позже Древесники поведали, что незадолго до атаки они почувствовали, как их древняя печать ослабла. Приняв во внимание внезапное вторжение Кроков, мы погрузились в сердце болота. Мы наткнулись на старый каменный круг Древесников, выглядящий как логово... логово чего-то огромного.",
["LEVEL_21_TITLE"] = "21. Затонувшие Руины",
["LEVEL_22_HISTORY"] = "По прибытии в храм, мы подтвердили наши худшие опасения. Печать, которая долгое время защищала наши земли от Абоминора – Пожирателя Миров – была почти разрушена, сдерживаемая только благодаря отчаянной связующей магии некоторых шаманов Древесников. Генерал, остановите Абоминора, или все королевства пропадут в его бездонной пасти.",
["LEVEL_22_TITLE"] = "22. Лощина Ненасытного ",
["LEVEL_23_HISTORY"] = "Разведчики доложили о неестественных ополознях на соседних горах. Дальнейшее изучение показало, что они вызваны гномами, с которыми мы ранее не пересекались. Они собирают гигантского автоматона в недрах южного склона горы. Это дело требует расследования, Генерал. ",
["LEVEL_23_TITLE"] = "23. Врата Черностали",
["LEVEL_24_HISTORY"] = "Гномы всегда слыли изобретателями, но этот так называемый \"Клан Черностали\" доводит поклонение металлу до абсурда, выставляя отсталым даже народ Болгура. Они используют свои кузницы для максимального \"улучшения\" самих себя. Кто стоит за этим безумием? Нужно все выяснить!",
["LEVEL_24_TITLE"] = "24. Конвейер Безумия",
["LEVEL_25_HISTORY"] = "Все обернулось так, как мы и боялись. Только недра горы подобного размера могут вместить кузню, необходимую для постройки этого автоматона. Сколько же здесь гномов? Они сопротивляются нашему наступлению, но при этом не прекращают ковать и переплавлять. И что еще страннее, они все кажутся... одинаковыми? Что-то здесь не так.",
["LEVEL_25_TITLE"] = "25. Сердце Колосса",
["LEVEL_26_HISTORY"] = "Наш путь через гору и под горой приводит нас в зал, полный разных баков, и они не пустуют. Неудивительно, что эти гномы сильны числом, умом и при этом все на одно лицо. Они все – один и тот же гном, Гримбород! Он клонировал самого себя посредством извращенных наук. Генерал, мы должны положить этому конец!",
["LEVEL_26_TITLE"] = "26. Зал Клонирования",
["LEVEL_27_HISTORY"] = "Мы смогли нарушить большинство планов Черностали, но все будет напрасно, пока Гримбород разгуливает на свободе. Он наверняка работает над последними дополнениями к голове своего автоматона. Генерал, ведите войска к вершинам. Будем надеяться, что сейчас мы имеем дело с нужным гномом. ",
["LEVEL_27_TITLE"] = "27. Лик Превосходства",
["LEVEL_28_HISTORY"] = "Следуя за зацепками, оставленными нашими разведчиками, мы нашли след, ведущий к этим треклятым культистам. Похоже, они нашли нового бога для поклонения – мерзкое плетущее паутину чудовище... Культисты И пауки? Ничего хорошего из этой смеси не выйдет.",
["LEVEL_28_TITLE"] = "28. Разоренное Святилище",
["LEVEL_29_HISTORY"] = "Чем глубже мы спускаемся, тем яснее становится, что этот ужас разрастался внизу уже долгое время, выжидая момента для атаки. Судя по сгущающейся вокруг нас паутине и жуткой тьме, которая, кажется, дышит нам в спины, я бы поспорил, Генерал, что мы приближаемся к самому сердцу их логова.",
["LEVEL_29_TITLE"] = "29. Гнездовой Чертог",
["LEVEL_2_HEROIC"] = "Героическое Описание 2",
["LEVEL_2_HISTORY"] = "До нас дошли страшные вести! Сердце Леса атакуют! Мы должны вернуться и помочь Древесникам! Некоторые силы Армии Тьмы согласились присоединиться к нам на поле боя, однако будьте бдительны: хотя сейчас мы идем в одной упряжке, это может измениться в любой момент.",
["LEVEL_2_IRON"] = "Железное Описание 2",
["LEVEL_2_IRON_UNLOCK"] = "Таинственный Маг\nТрехпушка",
["LEVEL_2_MODES_UPGRADES"] = "Макс. 2 уровень",
["LEVEL_2_TITLE"] = "2. Ворота Стража",
["LEVEL_30_HISTORY"] = "Наконец-то мы добрались до логова их так называемого бога – перед нами ветхий, давно заброшенный храм, рушащийся под тяжестью собственного забытого прошлого. Достойный трон для проклятого божества. На сей раз мы удостоверимся, что живых не останется, и истребим этих паразитов, отныне и навечно.",
["LEVEL_30_TITLE"] = "30. Забытый Трон",
["LEVEL_31_HISTORY"] = "После всех сражений и испытаний мир наконец вернулся в королевства. Теперь остаётся лишь слушать шум прибоя и играть в настольные игры в ожидании старого друга. И всё же, даже несмотря на спокойствие, я задаюсь вопросом, как долго продлится этот мир...",
["LEVEL_31_TITLE"] = "31. Небесный Обезьяний Лес",
["LEVEL_32_HISTORY"] = "Наши поиски привели нас в самое сердце вулкана, где когда-то давно забытый храм воздавал почести огню.\n\nНо Великий Огненный Дракон, некогда нейтральный страж этих пылающих глубин, теперь бурлит неестественной яростью. Все признаки указывают на то, что влияние Красного Мальчика исказило его волю.",
["LEVEL_32_TITLE"] = "32. Пещера Огненного Дракона",
["LEVEL_33_HISTORY"] = "После изнурительной схватки с Красным Мальчиком мы направляемся дальше — к Острову Бури. Как только мы ступили на его землю, небо наполнили тучи с молниями и свирепые порывы ветра, воющие в странных изогнутых узорах. Но у нас нет выбора: остров хранит единственный вход во дворец Принцессы. Приготовьтесь... надвигается буря.",
["LEVEL_33_TITLE"] = "33. Остров Бури",
["LEVEL_34_HISTORY"] = "Мы можем поблагодарить Принцессу и её Железный Веер за испытания, которые мы пережили. Перейдя мост и преодолев лютые бури, мы теперь стоим в самом эпицентре. Это место осталось нетронутым — обманчиво спокойным и прекрасным. Мы не можем терять бдительность. Даже демоническая знать не встанет у нас на пути.",
["LEVEL_34_TITLE"] = "34. Око Бури",
["LEVEL_35_HISTORY"] = "Вот он, момент истины. Король-Демон Бык возвышается в своей неприступной крепости. С остатками наших войск мы идём в лобовую атаку, сочетая силу и хитрость. Мы должны ударить, прежде чем он полностью высвободит силу сфер.\nВо имя всего, что вам дорого на этой благословенной земле... Я прошу вас, стойте насмерть, Альянс!",
["LEVEL_35_TITLE"] = "35. Цитадель Короля Демонов",
["LEVEL_3_HEROIC"] = "Героическое описание 3",
["LEVEL_3_HISTORY"] = "Мы вернулись к Сердцу в последний миг, но Дикозвери уже на подходе. Занимайте позиции и готовьтесь к битве! Защитите Сердце любой ценой, иначе и лес, и Древесники наверняка погибнут.",
["LEVEL_3_IRON"] = "Железное Описание 3",
["LEVEL_3_IRON_UNLOCK"] = "Королевские Лучники\nСоюз Паладинов",
["LEVEL_3_MODES_UPGRADES"] = "Макс. 3 уровень",
["LEVEL_3_TITLE"] = "3. Сердце Леса",
["LEVEL_4_HEROIC"] = "Героическое описание 4",
["LEVEL_4_HISTORY"] = "Теперь, когда Сердце Леса в безопасности, мы должны перегруппироваться и воспользоваться полученным преимуществом. Пришло время перенести войну на территорию Дикозверей! Закрепитесь на вершине деревьев этого леса и найдите их лагерь с высоты.",
["LEVEL_4_IRON"] = "Железное Описание 4",
["LEVEL_4_IRON_UNLOCK"] = "Трехпушка\nЭмиссар Древесников",
["LEVEL_4_MODES_UPGRADES"] = "Макс. 4 уровень",
["LEVEL_4_TITLE"] = "4. Изумрудные Кроны",
["LEVEL_5_HEROIC"] = "Героическое описание 5",
["LEVEL_5_HISTORY"] = "Благодаря Вашим усилиям, мы успешно заняли высоту и обнаружили лагерь Дикозверей в древних руинах за пределами леса. Проведите войска вглубь их территории, но остерегайтесь ловушек. Мы, может, и выиграли еще одну битву, но это далеко не конец.",
["LEVEL_5_IRON"] = "Железное Описание 5",
["LEVEL_5_IRON_UNLOCK"] = "Таинственный Волшебник\nЗавет Паладинов",
["LEVEL_5_MODES_UPGRADES"] = "Макс. 5 уровень",
["LEVEL_5_TITLE"] = "5. Разоренные Окраины",
["LEVEL_6_HEROIC"] = "Героическое описание 6",
["LEVEL_6_HISTORY"] = "Возможно, мы наконец получили преимущество над Дикозверьми, однако нам все еще предстоит сразиться с их лидером, Кровожадом. Самопровозглашенный Король Дикозверей – могучий враг, так что не позвольте его выходкам одурачить вас, иначе вы встретите свой конец между его клыков.",
["LEVEL_6_IRON"] = "Железное Описание 6",
["LEVEL_6_IRON_UNLOCK"] = "Королевские Лучники\nЯма Демонов",
["LEVEL_6_MODES_UPGRADES"] = "Макс. 5 уровень",
["LEVEL_6_TITLE"] = "6. Логово Дикозверей",
["LEVEL_7_HEROIC"] = "Героическое описание 7",
["LEVEL_7_HISTORY"] = "Двигаясь по следам культистов, что помогали Дикозверям уничтожать лес, мы оказались в пустынном месте, где, как мы подозреваем, Культ осуществляет свои странные планы. Нам нужно быть осторожными: мы не знаем наверняка, с чем имеем дело, однако у них, кажется, есть несколько трюков за пазухой.",
["LEVEL_7_IRON"] = "Железное Описание 7",
["LEVEL_7_IRON_UNLOCK"] = "Нет королевских лучников",
["LEVEL_7_MODES_UPGRADES"] = "Макс. 5 уровень",
["LEVEL_7_TITLE"] = "7. Мрачная Долина",
["LEVEL_8_HEROIC"] = "Героическое описание 8",
["LEVEL_8_HISTORY"] = "Войдя на территорию Культа, мы оказались в огромной сети пещер, полных кристаллов, что излучают странную магию. Культисты добывают эти кристаллы: похоже, они используют их в качестве источника энергии. Для каких целей, мы не знаем, но помешать их деятельности – хороший способ вызвать хаос в их рядах. ",
["LEVEL_8_IRON"] = "Железное Описание 8",
["LEVEL_8_IRON_UNLOCK"] = "Трехпушка\nЗавет Паладинов",
["LEVEL_8_MODES_UPGRADES"] = "Макс. 5 уровень",
["LEVEL_8_TITLE"] = "8. Карминные Шахты",
["LEVEL_9_HEROIC"] = "Героическое описание 9",
["LEVEL_9_HISTORY"] = "Изгибы и повороты этих туннелей сводят с ума, но мы знаем, что находимся на правильном пути, ведь культисты сражаются все яростнее. По мере нашего продвижения мы сталкиваемся с все новыми ужасами, что навевает вопрос, как глубоко скверна укоренилась в рядах Культа.",
["LEVEL_9_IRON"] = "Железное Описание 9",
["LEVEL_9_IRON_UNLOCK"] = "Яма Демонов\nТаинственный Волшебник",
["LEVEL_9_MODES_UPGRADES"] = "Макс. 5 уровень",
["LEVEL_9_TITLE"] = "9. Зловещий Перекресток",
["LEVEL_DEFEAT_ADVICE"] = "ИСПОЛЬЗУЙТЕ КРИСТАЛЛЫ ДЛЯ ПОКУПКИ ПРЕДМЕТОВ И РАЗГРОМИТЕ ПРОТИВНИКОВ!",
["LEVEL_DEFEAT_GEMS_COLLECTED"] = "ВЫ СОБРАЛИ",
["LEVEL_DEFEAT_GEMS_COUNT"] = "СОБРАНО КРИСТАЛЛОВ: %i ",
["LEVEL_DEFEAT_TITLE"] = "ПОРАЖЕНИЕ!",
["LEVEL_MODE_CAMPAIGN"] = "Кампания",
["LEVEL_MODE_HEROIC"] = "Героическое испытание",
["LEVEL_MODE_HEROIC_DESCRIPTION"] = "Проверьте свои тактические способности против элитных войск врага в этом испытании, предназначенном только для самых героических защитников.",
["LEVEL_MODE_IRON"] = "Железное испытание",
["LEVEL_MODE_IRON_DESCRIPTION"] = "Это испытание для величайших воинов. Оно потребует от вас максимального использования своих навыков.",
["LEVEL_SELECT_AVAILABLE_TOWERS"] = "Доступные башни",
["LEVEL_SELECT_CHALLENGE_ONE_ELITE_WAVE"] = "1 элитная волна",
["LEVEL_SELECT_CHALLENGE_ONE_LIFE"] = "Всего 1 жизнь",
["LEVEL_SELECT_CHALLENGE_ONE_WAVE"] = "1 суперволна",
["LEVEL_SELECT_CHALLENGE_RULES"] = "Правила испытания",
["LEVEL_SELECT_CHALLENGE_SIX_ELITE_WAVE"] = "6 элитных волн",
["LEVEL_SELECT_DIFFICULTY_CASUAL"] = "НОВИЧОК",
["LEVEL_SELECT_DIFFICULTY_IMPOSSIBLE"] = "БЕЗУМЕЦ",
["LEVEL_SELECT_DIFFICULTY_NORMAL"] = "БОЕЦ",
["LEVEL_SELECT_DIFFICULTY_VETERAN"] = "ВЕТЕРАН",
["LEVEL_SELECT_GAME_MODE"] = "Режим игры",
["LEVEL_SELECT_GET_DLC"] = "ПОЛУЧИТЕ ЭТО",
["LEVEL_SELECT_HELP1"] = "Выберите режим игры!",
["LEVEL_SELECT_HELP2"] = "Выберите сложность!",
["LEVEL_SELECT_HELP3"] = "Начать Бой!",
["LEVEL_SELECT_MODE_LOCKED1"] = "Режим заблокирован",
["LEVEL_SELECT_MODE_LOCKED2"] = "Разблокируйте этот режим, завершив этот этап.",
["LEVEL_SELECT_TO_BATTLE"] = "В БОЙ!",
["LOADING"] = "ЗАГРУЗКА",
["LV22_BOSS_BEFORE_FIGHT_EAT_01"] = "Вкусно! Ха Ха Ха!",
["LV22_BOSS_BEFORE_FIGHT_EAT_02"] = "Ненавижу растения.",
["LV22_BOSS_BEFORE_FIGHT_EAT_03"] = "Ты – то, что ты ешь.",
["LV22_BOSS_BEFORE_FIGHT_EAT_04"] = "Этот кусочек был кстати.",
["LV22_BOSS_BEFORE_FIGHT_EAT_05"] = "Уже устали?",
["LV22_BOSS_BEFORE_FIGHT_EAT_06"] = "Я никогда, никогда больше не буду голодать!",
["LV22_BOSS_BEFORE_FIGHT_EAT_07"] = "Классная была башенка, ха-ха-ха!",
["LV22_BOSS_BEFORE_FIGHT_EAT_08"] = "На вкус как... свобода.",
["LV22_BOSS_INTRO_01"] = "Принесли мне закусок для первой трапезы...",
["LV22_BOSS_INTRO_02"] = "Они выглядят... хрустящими.",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_01"] = "Тебе достанутся только черви!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_02"] = "Зелень – друзья, а не еда.",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_03"] = "И ты больше есть не будешь!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_04"] = "Убирайся в свою тюрьму, монстр!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_05"] = "Ты не пожрешь!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_06"] = "Я защищу Зелень!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_07"] = "Поглядим, кто будет смеяться последним.",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_08"] = "Он все сильнее! На помощь!!!",
["LV22_MAGE_INTRO_01"] = "Захлопни пасть!",
["LV22_MAGE_INTRO_02"] = "Скорее! Долго я его не сдержу!",
["Localization Manager"] = "Менеджер по локализации",
["Log in"] = "Авторизоваться",
["Log out?"] = "Выйти из системы?",
["Login Required"] = "Требуется авторизация",
["MAGES’ GUILD"] = "ГИЛЬДИЯ МАГОВ",
["MAGIC RESISTANT ENEMIES!"] = "ВРАГИ С СОПРОТИВЛЕНИЕМ МАГИИ!",
["MAP_BALLON_BUY_UPGRADES_DESCRIPTION"] = "Используйте заработанные звезды для улучшения башен и навыков!",
["MAP_BALLON_BUY_UPGRADES_TITLE"] = "КУПИТЬ УЛУЧШЕНИЯ!",
["MAP_BALLON_HERO_LEVELUP_DESCRIPTION"] = "Используйте полученные очки героя для его тренировки!",
["MAP_BALLON_HERO_LEVELUP_TITLE"] = "УРОВЕНЬ ГЕРОЯ ПОВЫШЕН!",
["MAP_BALLON_HERO_UNLOCKED"] = "ОТКРЫТ ГЕРОЙ!",
["MAP_BALLON_START_HERE"] = "НАЧНИТЕ ЗДЕСЬ!",
["MAP_BUTTON_ACHIEVEMENTS"] = "ДОСТИЖЕНИЯ",
["MAP_BUTTON_CHALLENGES"] = "ИСПЫТАНИЯ",
["MAP_BUTTON_ENCYCLOPEDIA"] = "ЭНЦИКЛОПЕДИЯ",
["MAP_BUTTON_HERO_ROOM"] = "ГЕРОИ",
["MAP_BUTTON_ITEMS"] = "ПРЕДМЕТЫ",
["MAP_BUTTON_SHOP"] = "МАГАЗИН",
["MAP_BUTTON_TOWER_ROOM"] = "БАШНИ",
["MAP_BUTTON_UPGRADES"] = "УЛУЧШЕНИЯ",
["MAP_ENCYCLOPEDIA_STRATEGY_GUIDE"] = "РУКОВОДСТВО ПО СТРАТЕГИИ",
["MAP_ENCYCLOPEDIA_TIPS"] = "ПОДСКАЗКИ",
["MAP_HEROROOM_HELP1"] = "Выбирайте и тренируйте способности!",
["MAP_HEROROOM_HELP2"] = "Нажмите для выбора",
["MAP_HEROROOM_HELP3"] = "Улучшите навык героя!",
["MAP_HERO_ROOM_GET_IT_NOW"] = "ПОЛУЧИТЕ СЕЙЧАС!",
["MAP_HERO_ROOM_SELECT"] = "ВЫБРАТЬ",
["MAP_HERO_ROOM_SELECTED"] = "ВЫБРАН",
["MAP_HERO_ROOM_TRAIN"] = "ТРЕНИРОВАТЬ",
["MAP_HERO_ROOM_UNLOCK"] = "ОТКРОЕТСЯ НА ЭТАПЕ %d",
["MAP_HERO_ROOM_UNLOCK_10"] = "ОТКРОЕТСЯ НА ЭТАПЕ 10",
["MAP_HERO_ROOM_UNLOCK_14"] = "ОТКРОЕТСЯ НА ЭТАПЕ 14",
["MAP_HERO_ROOM_UNLOCK_15"] = "ОТКРОЕТСЯ НА ЭТАПЕ 15",
["MAP_HERO_ROOM_UNLOCK_4"] = "ОТКРОЕТСЯ НА ЭТАПЕ 4",
["MAP_HERO_ROOM_UNLOCK_7"] = "ОТКРОЕТСЯ НА ЭТАПЕ 7",
["MAP_HERO_ROOM_UNLOCK_9"] = "ОТКРОЕТСЯ НА ЭТАПЕ 9",
["MAP_HERO_ROOM_UNLOCK_AFTER_CAMPAIGN"] = "Разблокируется после завершения игры ",
["MAP_INAPPS_BUBBLE_INFO_1"] = "Получайте кристаллы за игру.",
["MAP_INAPPS_BUBBLE_INFO_2"] = "Покупайте особые предметы за кристаллы!",
["MAP_INAPPS_BUBBLE_MORE_GEMS"] = "Мало кристаллов!",
["MAP_INAPPS_BUBBLE_SUCCESSFUL"] = "Успешная\nпокупка!",
["MAP_INAPP_GEMS_GEM_SHOP_TITLE"] = "МАГАЗИН КРИСТАЛЛОВ",
["MAP_INAPP_GEM_PACK_1"] = "ПРИГОРШНЯ КРИСТАЛЛОВ",
["MAP_INAPP_GEM_PACK_2"] = "МЕШОК КРИСТАЛЛОВ",
["MAP_INAPP_GEM_PACK_3"] = "БОЧОНОК КРИСТАЛЛОВ",
["MAP_INAPP_GEM_PACK_4"] = "СУНДУК КРИСТАЛЛОВ",
["MAP_INAPP_GEM_PACK_5"] = "ТЕЛЕГА КРИСТАЛЛОВ",
["MAP_INAPP_GEM_PACK_6"] = "ГОРА КРИСТАЛЛОВ",
["MAP_INAPP_GEM_PACK_BAG"] = "Мешок кристаллов",
["MAP_INAPP_GEM_PACK_BARREL"] = "Бочонок кристаллов",
["MAP_INAPP_GEM_PACK_CHEST"] = "Сундук кристаллов",
["MAP_INAPP_GEM_PACK_FREE"] = "Бесплатные кристаллов",
["MAP_INAPP_GEM_PACK_HANDFUL"] = "Пригоршня кристаллов",
["MAP_INAPP_GEM_PACK_VAULT"] = "Погреб кристаллов",
["MAP_INAPP_GEM_PACK_WAGON"] = "Вагон кристаллов",
["MAP_INAPP_MORE_GEMS"] = "БОЛЬШЕ КРИСТАЛЛОВ",
["MAP_INAPP_TEXT_1"] = "Пригоршня кристаллов",
["MAP_INAPP_TEXT_2"] = "Мешок кристаллов",
["MAP_INAPP_TEXT_3"] = "Сундук кристаллов",
["MAP_INAPP_TEXT_4"] = "Бесплатные кристаллов",
["MAP_INAPP_TEXT_GEMS"] = "Кристаллы",
["MAP_NEW_GAMEMODE_UNLOCKED_DESCRIPTION"] = "Сражайтесь с бесконечными врагами и боритесь за лучший результат!",
["MAP_NEW_GAMEMODE_UNLOCKED_TITLE"] = "НОВОЕ ИСПЫТАНИЕ!",
["MAP_NEW_HERO_ALERT"] = "НОВЫЙ\nГЕРОЙ!",
["MAP_NEW_TOWER_ALERT"] = "НОВАЯ\nБАШНЯ!",
["MAP_TOWER_ROOM_SELECT"] = "ВЫБРАТЬ",
["MAP_TOWER_ROOM_SELECTED"] = "ВЫБРАН",
["MEDIUM"] = "СРЕДНЯЯ",
["MENU_HUD_WAVES"] = "%i/%i",
["MINUTES_ABBREVIATION"] = "м",
["MORE_GAMES"] = "Больше игр",
["Magic resistant enemies take less damage from mages."] = "Враги, имеющее сопротивление магии, получают меньше урона от магов.",
["NEW"] = "НОВОЕ",
["NEW SPECIAL POWER!"] = "НОВОЕ ОСОБОЕ УМЕНИЕ!",
["NEW TOWER UNLOCKED"] = "ОТКРЫТА НОВАЯ БАШНЯ",
["NEW TOWER UPGRADES"] = "НОВЫЕ УЛУЧШЕНИЯ БАШНИ",
["NEWS"] = "НОВОСТИ",
["NEWS_ERROR"] = "Соединение не установлено. Пожалуйста, проверьте подключение или повторите попытку позже.",
["NOTIFICATION_NEW_ENEMY_TITLE"] = "НОВЫЙ ВРАГ",
["NOTIFICATION_NEW_SPECIAL_TITLE"] = "НОВОЕ ОСОБОЕ УМЕНИЕ!",
["NOTIFICATION_NEW_TOWERS_SUB_DESCRIPTION"] = "Теперь вы можете улучшать ваши башни до уровня %d.",
["NOTIFICATION_NEW_TOWERS_SUB_TITLE"] = "ДОСТУПНЫ БАШНИ УРОВНЯ %d",
["NOTIFICATION_NEW_TOWERS_TITLE"] = "НОВЫЕ УЛУЧШЕНИЯ БАШЕН",
["NOTIFICATION_NEW_TOWER_TITLE"] = "НОВАЯ БАШНЯ РАЗБЛОКИРОВАНА",
["NOTIFICATION_armored_enemies_desc_body_1"] = "Некоторые враги носят доспехи различной прочности для защиты от физических атак.",
["NOTIFICATION_armored_enemies_desc_body_2"] = "Сопротивление урону от",
["NOTIFICATION_armored_enemies_desc_body_3"] = "Враги в доспехах получают меньше урона от башен Лучников, Казарм и Артиллерии.",
["NOTIFICATION_armored_enemies_desc_title"] = "Враги в Доспехах!",
["NOTIFICATION_armored_enemies_enemy_name"] = "Клыкастый Борец",
["NOTIFICATION_bottom_info_desc_body"] = "Вы можете посмотреть информацию о враге в любое время, нажав на него и на его портрет.",
["NOTIFICATION_bottom_info_desc_title"] = "ИНФОРМАЦИЯ О ВРАГЕ",
["NOTIFICATION_bottom_info_tap_portrait_desc"] = "Нажмите здесь, чтобы открыть снова",
["NOTIFICATION_button_ok"] = "Ок",
["NOTIFICATION_glare_desc_body"] = "Наблюдатель всматривается в битву, придавая сил ближайшим врагам своим порочным Взглядом.",
["NOTIFICATION_glare_desc_bullets"] = " - Исцеляет врагов, находящихся в зоне действия\n- Активирует уникальные способности врагов",
["NOTIFICATION_glare_desc_title"] = "Взгляд Наблюдателя",
["NOTIFICATION_hero_desc"] = "Показывает уровень, здоровье и опыт.",
["NOTIFICATION_hero_desc_baloon_1"] = "Выберите его, коснувшись героя или его портрета",
["NOTIFICATION_hero_desc_baloon_2"] = "Коснитесь или перетащите по пути, чтобы переместить героя",
["NOTIFICATION_hero_desc_body_1"] = "Герои — это элитные бойцы, которые могут противостоять сильным врагам и оказывать поддержку вашим войскам.",
["NOTIFICATION_hero_desc_body_2"] = "Герои получают опыт при каждом нанесении урона врагу или использовании умения.",
["NOTIFICATION_hero_desc_title"] = "Герой в вашем распоряжении!",
["NOTIFICATION_magic_resistant_enemies_desc_body_1"] = "Некоторые враги обладают разным уровнем сопротивления магии, что защищает их от магических атак.",
["NOTIFICATION_magic_resistant_enemies_desc_body_2"] = "Сопротивление урону от",
["NOTIFICATION_magic_resistant_enemies_desc_body_3"] = "Враги, имеющее сопротивление магии, получают меньше урона от магов.",
["NOTIFICATION_magic_resistant_enemies_desc_title"] = "Враги с Сопротивлением Магии!",
["NOTIFICATION_magic_resistant_enemies_enemy_name"] = "Черепаха-шаман",
["NOTIFICATION_rally_point_desc_body_1"] = "Вы можете изменить расположение точки сбора солдат, чтобы они защищали другую область.",
["NOTIFICATION_rally_point_desc_body_2"] = "Выберите управление точкой сбора",
["NOTIFICATION_rally_point_desc_body_3"] = "Выберите место для перемещения солдат",
["NOTIFICATION_rally_point_desc_subtitle"] = "Радиус Сбора",
["NOTIFICATION_rally_point_desc_title"] = "Командуйте войсками!",
["NOTIFICATION_special_desc_body"] = "Вы можете призвать дополнительные войска для помощи на поле боя.",
["NOTIFICATION_special_desc_bullets"] = "Подкрепления отлично подходят для задержки врагов.",
["NOTIFICATION_special_desc_title"] = "Вызов Подкрепления",
["NOTIFICATION_title_enemy"] = "Информация о Враге",
["NOTIFICATION_title_glare"] = "НОВЫЙ СОВЕТ!",
["NOTIFICATION_title_hint"] = "ГЕРОЙ РАЗБЛОКИРОВАН",
["NOTIFICATION_title_new_tip"] = "НОВЫЙ СОВЕТ",
["NOTIFICATION_title_special"] = "НАВЫК РАЗБЛОКИРОВАН",
["No"] = "Нет",
["No, Thanks"] = "Нет, спасибо",
["None"] = "Нет",
["Nope"] = "Не-а!",
["Normal"] = "Боец",
["OFF!"] = "ОТКЛ.!",
["OFFERS_END"] = "Истекает через:",
["OFFER_GET_IT_NOW"] = "ПОЛУЧИТЬ СЕЙЧАС",
["OFFER_GET_THEM_NOW"] = "ПОЛУЧИТЬ СЕЙЧАС",
["OFFER_ICON_BANNER"] = "ПРЕДЛОЖЕНИЕ",
["OFFER_OFF"] = "ОТКЛ.",
["OFFER_PACK_DESCRIPTION_ALL_HEROES"] = "ПОЛУЧИТЕ ВСЕХ ГЕРОЕВ СЕЙЧАС!",
["OFFER_PACK_DESCRIPTION_ALL_TOWERS"] = "ПОЛУЧИТЕ ВСЕ БАШНИ СЕЙЧАС!",
["OFFER_PACK_DESCRIPTION_TEXT_01"] = "Постройте мощнейшую армию с этим уникальным предложением!",
["OFFER_PACK_DESCRIPTION_TEXT_02"] = "Купить!",
["OFFER_PACK_TIMELEFT_TEXT"] = "Истекает через:",
["OFFER_PACK_TITLE_01"] = "Предложение на Хэллоуин",
["OFFER_PACK_TITLE_02"] = "Предложение на Черную пятницу",
["OFFER_PACK_TITLE_03"] = "Рождественское предложение",
["OFFER_PACK_TITLE_04"] = "Новогоднее предложение",
["OFFER_PACK_TITLE_05"] = "Весеннее предложение",
["OFFER_PACK_TITLE_06"] = "Летняя распродажа",
["OFFER_PACK_TITLE_07"] = "Предложение от Ironhide",
["OFFER_PACK_TITLE_08"] = "Предложение для начинающих",
["OFFER_PACK_TITLE_09"] = "Ограниченное предложение",
["OFFER_PACK_TITLE_ALL_HEROES"] = "МЕГА КОМПЛЕКТ ГЕРОЕВ",
["OFFER_PACK_TITLE_ALL_TOWERS"] = "МЕГА КОМПЛЕКТ БАШЕН",
["OFFER_PACK_TITLE_STARTER_PACK"] = "Предложение для начинающих",
["OFFER_REGULAR"] = "ОБЫЧНАЯ ЦЕНА",
["ONE_TIME_OFFER"] = "ОДНОРАЗОВОЕ ПРЕДЛОЖЕНИЕ!",
["ON_SALE"] = "В ПРОДАЖЕ",
["OPTIONS"] = "НАСТРОЙКИ",
["OPTIONS_PAGE_CONTROLS"] = "ПАРАМЕТРЫ",
["OPTIONS_PAGE_HELP"] = "КОНТРОЛЛЕР",
["OPTIONS_PAGE_SHORTCUTS"] = "КЛАВИАТУРА",
["OPTIONS_PAGE_VIDEO"] = "ВИДЕО",
["Objective"] = "Цель",
["Over 50 stars are recommended to face this stage."] = "Чтобы противостоять врагам на этом этапе, рекомендуется набрать более 50 звезд.",
["POPUP_CLEAR_PROGRESS_CONFIRM"] = "ВЫ УВЕРЕНЫ, ЧТО ХОТИТЕ СТЕРЕТЬ СВОЙ ПРОГРЕСС?",
["POPUP_LABEL_MAIN_MENU"] = "Главное Меню",
["POPUP_SETTINGS_LANGUAGE"] = "Язык",
["POPUP_SETTINGS_MUSIC"] = "МУЗЫКА",
["POPUP_SETTINGS_SFX"] = "ЗВУК",
["POPUP_label_error_msg"] = "Ой! Что-то пошло не так.",
["POPUP_label_error_msg2"] = "Ой! Что-то пошло не так.",
["POPUP_label_purchasing"] = "ОБРАБАТЫВАЕМ ЗАПРОС",
["POPUP_label_title_options"] = "Настройки",
["POPUP_label_version"] = "Версия 0.0.8b",
["POWER_REINFORCEMENTS_NAME"] = "Подкрепление",
["POWER_SUMMON_DESCRIPTION"] = "Призовите солдат на битву.",
["POWER_SUMMON_LARGE_DESCRIPTION"] = "Вы можете призывать подкрепления для помощи на поле боя.\n\nПодкрепления бесплатны, и их можно вызывать каждые 15 секунд.",
["POWER_SUMMON_NAME"] = "Подкрепления",
["PRICE_FREE"] = "Бесплатно",
["PRIVACY_POLICY_ASK_AGE"] = "Когда ты родился?",
["PRIVACY_POLICY_BUTTON_LINK"] = "Политика конфиденциальности",
["PRIVACY_POLICY_CONSENT_SHORT"] = "Прежде чем начать игру, подтвердите, что вы (и ваш родитель, если вы ребенок или подросток) прочитали и приняли нашу политику конфиденциальности.",
["PRIVACY_POLICY_LINK"] = "Политика конфиденциальности",
["PRIVACY_POLICY_WELCOME"] = "Добро пожаловать!",
["PROCESSING ITEMS TO RESTORE"] = "ОБРАБОТКА ТОВАРОВ ДЛЯ ВОССТАНОВЛЕНИЯ",
["PROCESSING YOUR REQUEST"] = "ОБРАБАТЫВАЕМ ЗАПРОС",
["PURCHASE_PENDING_MESSAGE"] = "Покупка находится на рассмотрении и будет доставлена после завершения оплаты или обработки.",
["PUSH_NOTIFICATIONS_PERMISSION_RATIONALE"] = "Хотели бы вы получать уведомления о распродажах продуктов и новых играх от Ironhide?",
["Produced by %s"] = "Создатели: %s",
["QUIT"] = "Выйти",
["Quit"] = "Выйти",
["RESTORE"] = "ВОССТАНОВИТЬ",
["RESTORE_PURCHASES"] = "Восстановить покупки",
["RESTORE_SLOT_ADD_GEMS_TITLE"] = "Выберите слот для добавления драгоценных камней",
["RESTORE_SLOT_PROGRESS_MSG"] = "Получение данных восстановления с сервера…",
["RESTORE_SLOT_STATS_TITLE"] = "СТАТИСТИКА",
["RESTORE_SLOT_TITLE"] = "Выберите слот для замены",
["Rate %@"] = "Оценить %@",
["Remind me later"] = "Напомнить позже",
["SALE_SCREEN_MAP_ROOMS"] = "РАСПР.",
["SECONDS_ABBREVIATION"] = "с",
["SETTINGS_LANGUAGE"] = "Язык",
["SETTINGS_SUPPORT"] = "Поддержка",
["SHOP_DESKTOP_GET_DLC_BUTTON"] = "ПОЛУЧИТЕ ЭТО",
["SHOP_DESKTOP_TITLE"] = "МАГАЗИН",
["SHOP_ROOM_BEST_VALUE_TITLE"] = "САМОЕ\nВЫГОДНОЕ",
["SHOP_ROOM_DLC_1_DESCRIPTION"] = "ОТПРАВЬТЕСЬ В НОВОЕ ЭПИЧЕСКОЕ ПРИКЛЮЧЕНИЕ! ",
["SHOP_ROOM_DLC_1_TITLE"] = "КАМПАНИЯ \"КОЛОССАЛЬНАЯ УГРОЗА\"",
["SHOP_ROOM_DLC_1_TOOLTIP_DESCRIPTION"] = "5 Новых Уровней\nНовая Башня\nНовый Герой\nБолее 10 новых врагов\n2 Битвы с Мини-Боссами\nЭпическая Битва с Боссом\n И Многое Другое...",
["SHOP_ROOM_DLC_1_TOOLTIP_TITLE"] = "КАМПАНИЯ \"КОЛОССАЛЬНАЯ УГРОЗА\"",
["SHOP_ROOM_DLC_2_DESCRIPTION"] = "ОТРАВЬТЕСЬ В ЭТО НОВОЕ ЭПИЧЕСКОЕ ПРИКЛЮЧЕНИЕ",
["SHOP_ROOM_DLC_2_TITLE"] = "КАМПАНИЯ ПУТЕШЕСТВИЯ УКУНА",
["SHOP_ROOM_MOST_POPULAR_TITLE"] = "САМОЕ\nПОПУЛЯРНОЕ",
["SLOT_CLOUD_DOWNLOADING"] = "Скачивание...",
["SLOT_CLOUD_DOWNLOAD_FAILED"] = "Не удалось скачать сохраненную игру из iCloud. Повторите попытку позже.",
["SLOT_CLOUD_DOWNLOAD_SUCCESSFUL"] = "Скачивание завершено.",
["SLOT_CLOUD_UPLOADING"] = "Загрузка...",
["SLOT_CLOUD_UPLOAD_FAILED"] = "Не удалось загрузить сохраненную игру в iCloud. Повторите попытку позже.",
["SLOT_CLOUD_UPLOAD_ICLOUD_NOT_CONFIGURED"] = "iCloud не настроен на вашем устройстве.",
["SLOT_CLOUD_UPLOAD_SUCCESSFUL"] = "Загрузка завершена.",
["SLOT_DELETE_SLOT"] = "Удалить ячейку?",
["SLOT_NAME"] = "Ячейка",
["SLOT_NEW_GAME"] = "НОВАЯ ИГРА",
["SOLDIER_ARBOREAN_BARRACK_NAME"] = "Древесник-солдат",
["SOLDIER_ARBOREAN_SENTINELS_1_NAME"] = "Балуу",
["SOLDIER_ARBOREAN_SENTINELS_2_NAME"] = "Вилла",
["SOLDIER_ARBOREAN_SENTINELS_3_NAME"] = "Йккон",
["SOLDIER_ARBOREAN_SENTINELS_4_NAME"] = "Хаави",
["SOLDIER_ARBOREAN_SENTINELS_5_NAME"] = "Плук",
["SOLDIER_ARBOREAN_SENTINELS_6_NAME"] = "Гулдд",
["SOLDIER_ARBOREAN_SENTINELS_7_NAME"] = "Тина",
["SOLDIER_ARBOREAN_SENTINELS_8_NAME"] = "Узки",
["SOLDIER_ARBOREAN_SENTINELS_9_NAME"] = "Делуу",
["SOLDIER_DRAGON_BONE_ULTIMATE_DOG_NAME"] = "Костяной Дракон",
["SOLDIER_EARTH_HOLDER_NAME"] = "Каменный Воин",
["SOLDIER_GHOST_TOWER_NAME"] = "Призрак",
["SOLDIER_HERO_BUILDER_WORKER_1_NAME"] = "Хеммар",
["SOLDIER_HERO_BUILDER_WORKER_2_NAME"] = "О'Тул",
["SOLDIER_HERO_BUILDER_WORKER_3_NAME"] = "Крюс",
["SOLDIER_HERO_BUILDER_WORKER_4_NAME"] = "Бирк",
["SOLDIER_HERO_BUILDER_WORKER_5_NAME"] = "Лаук",
["SOLDIER_HERO_BUILDER_WORKER_6_NAME"] = "О'Нейл",
["SOLDIER_HERO_BUILDER_WORKER_7_NAME"] = "Ховелс",
["SOLDIER_HERO_BUILDER_WORKER_8_NAME"] = "Вуди",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL1_NAME"] = "Страж Древесников",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL2_NAME"] = "Страж Древесников",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL3_NAME"] = "Страж Древесников",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL1_NAME"] = "Парагон Древесников",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL2_NAME"] = "Парагон Древесников",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL3_NAME"] = "Парагон Древесников",
["SOLDIER_HERO_SPIDER_ULTIMATE_NAME"] = "Паучонок",
["SOLDIER_HERO_WITCH_CAT_1_NAME"] = "Конан",
["SOLDIER_HERO_WITCH_CAT_2_NAME"] = "Альфаджор",
["SOLDIER_HERO_WITCH_CAT_3_NAME"] = "Бабиека",
["SOLDIER_HERO_WITCH_CAT_4_NAME"] = "Пелуче",
["SOLDIER_HERO_WITCH_CAT_5_NAME"] = "Пипа",
["SOLDIER_HERO_WITCH_CAT_6_NAME"] = "Ватсон",
["SOLDIER_HERO_WITCH_CAT_7_NAME"] = "Чими",
["SOLDIER_HERO_WITCH_CAT_8_NAME"] = "Пантуфла",
["SOLDIER_HERO_WITCH_DECOY_NAME"] = "Тряпич",
["SOLDIER_HERO_WUKONG_HAIR_CLONES_1_NAME"] = "Сан Викун",
["SOLDIER_HERO_WUKONG_HAIR_CLONES_2_NAME"] = "Сон Вокен",
["SOLDIER_ITEM_SUMMON_BLACKBURN_NAME"] = "Лорд Блэкберн",
["SOLDIER_PALADINS_10_NAME"] = "Сэр Йоаким",
["SOLDIER_PALADINS_11_NAME"] = "Сэр Андре",
["SOLDIER_PALADINS_12_NAME"] = "Сэр Саммет",
["SOLDIER_PALADINS_13_NAME"] = "Сэр Удо",
["SOLDIER_PALADINS_14_NAME"] = "Сэр Эрик",
["SOLDIER_PALADINS_15_NAME"] = "Сэр Брюс",
["SOLDIER_PALADINS_16_NAME"] = "Сэр Роб",
["SOLDIER_PALADINS_17_NAME"] = "Сэр Бифф",
["SOLDIER_PALADINS_18_NAME"] = "Сэр Боус",
["SOLDIER_PALADINS_1_NAME"] = "Сэр Кай",
["SOLDIER_PALADINS_2_NAME"] = "Сэр Ханси",
["SOLDIER_PALADINS_3_NAME"] = "Сэр Лука",
["SOLDIER_PALADINS_4_NAME"] = "Сэр Тимо",
["SOLDIER_PALADINS_5_NAME"] = "Сэр Ральф",
["SOLDIER_PALADINS_6_NAME"] = "Сэр Тобиас",
["SOLDIER_PALADINS_7_NAME"] = "Сэр Дерис",
["SOLDIER_PALADINS_8_NAME"] = "Сэр Киске",
["SOLDIER_PALADINS_9_NAME"] = "Сэр Пеш",
["SOLDIER_PRIESTS_BARRACK_1_NAME"] = "Вилли",
["SOLDIER_PRIESTS_BARRACK_2_NAME"] = "Генри",
["SOLDIER_PRIESTS_BARRACK_3_NAME"] = "Джеффри",
["SOLDIER_PRIESTS_BARRACK_4_NAME"] = "Николас",
["SOLDIER_PRIESTS_BARRACK_5_NAME"] = "Эд",
["SOLDIER_PRIESTS_BARRACK_6_NAME"] = "Хоб",
["SOLDIER_PRIESTS_BARRACK_7_NAME"] = "Одо",
["SOLDIER_PRIESTS_BARRACK_8_NAME"] = "Седрик",
["SOLDIER_PRIESTS_BARRACK_9_NAME"] = "Хал",
["SOLDIER_RANDOM_10_NAME"] = "Альвус",
["SOLDIER_RANDOM_11_NAME"] = "Борин",
["SOLDIER_RANDOM_12_NAME"] = "Адриан",
["SOLDIER_RANDOM_13_NAME"] = "Томас",
["SOLDIER_RANDOM_14_NAME"] = "Генри",
["SOLDIER_RANDOM_15_NAME"] = "Брайс",
["SOLDIER_RANDOM_16_NAME"] = "Рульф",
["SOLDIER_RANDOM_17_NAME"] = "Аллистер",
["SOLDIER_RANDOM_18_NAME"] = "Альтаир",
["SOLDIER_RANDOM_19_NAME"] = "Саймон",
["SOLDIER_RANDOM_1_NAME"] = "Дуглас",
["SOLDIER_RANDOM_20_NAME"] = "Эгберт",
["SOLDIER_RANDOM_21_NAME"] = "Элдон",
["SOLDIER_RANDOM_22_NAME"] = "Гарретт",
["SOLDIER_RANDOM_23_NAME"] = "Годвин",
["SOLDIER_RANDOM_24_NAME"] = "Гордон",
["SOLDIER_RANDOM_25_NAME"] = "Джеральд",
["SOLDIER_RANDOM_26_NAME"] = "Келвин",
["SOLDIER_RANDOM_27_NAME"] = "Ландо",
["SOLDIER_RANDOM_28_NAME"] = "Мэддокс",
["SOLDIER_RANDOM_29_NAME"] = "Пейтон",
["SOLDIER_RANDOM_2_NAME"] = "Дэн МакКилл",
["SOLDIER_RANDOM_30_NAME"] = "Рэмси",
["SOLDIER_RANDOM_31_NAME"] = "Раймонд",
["SOLDIER_RANDOM_32_NAME"] = "Роберт",
["SOLDIER_RANDOM_33_NAME"] = "Сойер",
["SOLDIER_RANDOM_34_NAME"] = "Сайлас",
["SOLDIER_RANDOM_35_NAME"] = "Стюарт",
["SOLDIER_RANDOM_36_NAME"] = "Таннер",
["SOLDIER_RANDOM_37_NAME"] = "Ашер",
["SOLDIER_RANDOM_38_NAME"] = "Уоллес",
["SOLDIER_RANDOM_39_NAME"] = "Уэсли",
["SOLDIER_RANDOM_3_NAME"] = "Джеймс Ли",
["SOLDIER_RANDOM_40_NAME"] = "Уиллард",
["SOLDIER_RANDOM_4_NAME"] = "Джар Джонсон",
["SOLDIER_RANDOM_5_NAME"] = "Фил",
["SOLDIER_RANDOM_6_NAME"] = "Робин",
["SOLDIER_RANDOM_7_NAME"] = "Уильям",
["SOLDIER_RANDOM_8_NAME"] = "Мартин",
["SOLDIER_RANDOM_9_NAME"] = "Артур",
["SOLDIER_REINFORCEMENTS_F_1_NAME"] = "Атайна",
["SOLDIER_REINFORCEMENTS_F_2_NAME"] = "Мауцил",
["SOLDIER_REINFORCEMENTS_F_3_NAME"] = "Гулика",
["SOLDIER_REINFORCEMENTS_F_4_NAME"] = "Рогас",
["SOLDIER_REINFORCEMENTS_M_10_NAME"] = "Поджи",
["SOLDIER_REINFORCEMENTS_M_1_NAME"] = "Габини",
["SOLDIER_REINFORCEMENTS_M_2_NAME"] = "О'Белл",
["SOLDIER_REINFORCEMENTS_M_3_NAME"] = "Кент",
["SOLDIER_REINFORCEMENTS_M_4_NAME"] = "Джендарс",
["SOLDIER_REINFORCEMENTS_M_5_NAME"] = "Жарлоск",
["SOLDIER_REINFORCEMENTS_M_6_NAME"] = "Астонг",
["SOLDIER_REINFORCEMENTS_M_7_NAME"] = "Буигелл",
["SOLDIER_REINFORCEMENTS_M_8_NAME"] = "Клейн",
["SOLDIER_REINFORCEMENTS_M_9_NAME"] = "Магус",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_1_NAME"] = "Денч",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_2_NAME"] = "Смит",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_3_NAME"] = "Эндрюс",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_4_NAME"] = "Томпсон",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_5_NAME"] = "Тейлор",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_1_NAME"] = "Маккартни",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_2_NAME"] = "Маккеллен",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_3_NAME"] = "Хопкинс",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_4_NAME"] = "Кейн",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_5_NAME"] = "Кингсли",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_10_NAME"] = "Вайпер",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_1_NAME"] = "Фанг",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_2_NAME"] = "Блэйд",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_3_NAME"] = "Клоу",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_4_NAME"] = "Тэлон",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_5_NAME"] = "Эджи",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_6_NAME"] = "Шив",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_7_NAME"] = "Сай",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_8_NAME"] = "Даггер",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_9_NAME"] = "Стинг",
["SOLDIER_REINFORCEMENTS_SPECIAL_DARK_ARMY_1_NAME"] = "Темный Вороновод",
["SOLDIER_REINFORCEMENTS_SPECIAL_LINIREA_1_NAME"] = "Парагон Рыцарь",
["SOLDIER_STAGE_10_YMCA_BIKER_NAME"] = "Гленн",
["SOLDIER_STAGE_10_YMCA_CONSTRUCTOR_NAME"] = "Дэвид",
["SOLDIER_STAGE_10_YMCA_INDIO_NAME"] = "Фелипе",
["SOLDIER_STAGE_10_YMCA_POLICIA_NAME"] = "Виктор",
["SOLDIER_STAGE_15_DENAS_NAME"] = "Король Денас",
["SOLDIER_TOWER_DARK_ELF_1_NAME"] = "Фильраен",
["SOLDIER_TOWER_DARK_ELF_2_NAME"] = "Фаэрил",
["SOLDIER_TOWER_DARK_ELF_3_NAME"] = "Гурина",
["SOLDIER_TOWER_DARK_ELF_4_NAME"] = "Джаласс",
["SOLDIER_TOWER_DARK_ELF_5_NAME"] = "Солензар",
["SOLDIER_TOWER_DARK_ELF_6_NAME"] = "Тебрин",
["SOLDIER_TOWER_DARK_ELF_7_NAME"] = "Виерна",
["SOLDIER_TOWER_DARK_ELF_8_NAME"] = "Зин",
["SOLDIER_TOWER_DARK_ELF_9_NAME"] = "Элерра",
["SOLDIER_TOWER_DWARF_10_NAME"] = "Бабби",
["SOLDIER_TOWER_DWARF_1_NAME"] = "Пиппи",
["SOLDIER_TOWER_DWARF_2_NAME"] = "Джинни",
["SOLDIER_TOWER_DWARF_3_NAME"] = "Мерри",
["SOLDIER_TOWER_DWARF_4_NAME"] = "Лорри",
["SOLDIER_TOWER_DWARF_5_NAME"] = "Талли",
["SOLDIER_TOWER_DWARF_6_NAME"] = "Данни",
["SOLDIER_TOWER_DWARF_7_NAME"] = "Гетти",
["SOLDIER_TOWER_DWARF_8_NAME"] = "Даффи",
["SOLDIER_TOWER_DWARF_9_NAME"] = "Бибби",
["SOLDIER_TOWER_ELVEN_BARRACK_1_NAME"] = "Элендиль",
["SOLDIER_TOWER_ELVEN_BARRACK_2_NAME"] = "Пак",
["SOLDIER_TOWER_ELVEN_BARRACK_3_NAME"] = "Тас",
["SOLDIER_TOWER_ELVEN_BARRACK_4_NAME"] = "Касторе",
["SOLDIER_TOWER_ELVEN_BARRACK_5_NAME"] = "Элрик",
["SOLDIER_TOWER_ELVEN_BARRACK_6_NAME"] = "Элайт",
["SOLDIER_TOWER_NECROMANCER_SKELETON_GOLEM_NAME"] = "Костяной Голем",
["SOLDIER_TOWER_NECROMANCER_SKELETON_NAME"] = "Скелет",
["SOLDIER_TOWER_PANDAS_FEMALE_1_NAME"] = "Янь",
["SOLDIER_TOWER_PANDAS_FEMALE_2_NAME"] = "Цинчжао",
["SOLDIER_TOWER_PANDAS_FEMALE_3_NAME"] = "Хуэй",
["SOLDIER_TOWER_PANDAS_FEMALE_4_NAME"] = "Айлин",
["SOLDIER_TOWER_PANDAS_MALE_1_NAME"] = "Цзу",
["SOLDIER_TOWER_PANDAS_MALE_2_NAME"] = "Цянь",
["SOLDIER_TOWER_PANDAS_MALE_3_NAME"] = "Сюэцинь",
["SOLDIER_TOWER_PANDAS_MALE_4_NAME"] = "Найань",
["SOLDIER_TOWER_PANDAS_MALE_5_NAME"] = "Сюнь",
["SOLDIER_TOWER_PANDAS_MALE_6_NAME"] = "Синцзянь",
["SOLDIER_TOWER_PANDAS_MALE_7_NAME"] = "Вэй",
["SOLDIER_TOWER_PANDAS_MALE_8_NAME"] = "Чэнь",
["SOLDIER_TOWER_ROCKET_GUNNERS_10_NAME"] = "Фортус",
["SOLDIER_TOWER_ROCKET_GUNNERS_1_NAME"] = "Аксель",
["SOLDIER_TOWER_ROCKET_GUNNERS_2_NAME"] = "Роза",
["SOLDIER_TOWER_ROCKET_GUNNERS_3_NAME"] = "Слэш",
["SOLDIER_TOWER_ROCKET_GUNNERS_4_NAME"] = "Хадсон",
["SOLDIER_TOWER_ROCKET_GUNNERS_5_NAME"] = "Иззи",
["SOLDIER_TOWER_ROCKET_GUNNERS_6_NAME"] = "Дафф",
["SOLDIER_TOWER_ROCKET_GUNNERS_7_NAME"] = "Адлер",
["SOLDIER_TOWER_ROCKET_GUNNERS_8_NAME"] = "Диззи",
["SOLDIER_TOWER_ROCKET_GUNNERS_9_NAME"] = "Феррер",
["SOLDIER_ZHU_APPRENTICE_NAME"] = "Zhu Bajie",
["SPECIAL_ARBOREAN_BARRACK_DESCRIPTION"] = "Призывает трех Древесников-солдат, которые сражаются с врагами на пути.",
["SPECIAL_ARBOREAN_BARRACK_NAME"] = "Древесники-горожане",
["SPECIAL_ARBOREAN_HONEY_DESCRIPTION"] = "Пчеловод заступает на пост, приказывая своим пчелам атаковать и замедлять врагов с помощью липкого меда!",
["SPECIAL_ARBOREAN_HONEY_NAME"] = "Древесник-пчеловод",
["SPECIAL_ARBOREAN_OLDTREE_DESCRIPTION"] = "Сварливый древень бросает массивные катящиеся бревна, которые сокрушают врагов на своем пути.",
["SPECIAL_ARBOREAN_OLDTREE_NAME"] = "Старый Дуб",
["SPECIAL_ARBOREAN_SENTINELS_SPEARMEN_DESCRIPTION"] = "Проворные защитники леса.",
["SPECIAL_ARBOREAN_SENTINELS_SPEARMEN_NAME"] = "Древесник-копейщик",
["SPECIAL_PRIESTS_SOLDIERS_DESCRIPTION"] = "Искупленные культисты, превращающиеся в Отродья при смерти.",
["SPECIAL_PRIESTS_SOLDIERS_NAME"] = "Ослепленные Культисты",
["SPECIAL_REPAIR_HOLDER_DRAGON_DESCRIPTION"] = "Потушите пламя, чтобы немедленно освободить башню.",
["SPECIAL_REPAIR_HOLDER_DRAGON_NAME"] = "Охваченный пламенем",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_EARTH_DESCRIPTION"] = "Увеличивает здоровье юнитов башни.\nПризывает до 3 Каменных воинов.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_EARTH_NAME"] = "Elemental Holder: Earth",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_FIRE_DESCRIPTION"] = "Увеличивает урон построенной башни.\nИногда мгновенно убивает врага.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_FIRE_NAME"] = "Elemental Holder: Fire",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_METAL_DESCRIPTION"] = "Снижает стоимость строительства.\nПолучает золото с врагов.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_METAL_NAME"] = "Elemental Holder: Metal",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WATER_DESCRIPTION"] = "Постоянно лечит ближайшие дружественные юниты.\nТелепортирует врагов назад по пути.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WATER_NAME"] = "Elemental Holder: Water",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WOOD_DESCRIPTION"] = "Увеличивает дальность построенной башни.\nИногда вызывает кратковременные корни, замедляющие врагов.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WOOD_NAME"] = "Elemental Holder: Wood",
["SPECIAL_REPAIR_HOLDER_SEA_OF_TREES_DESCRIPTION"] = "Уберите обломки, чтобы воспользоваться этой стратегической точкой",
["SPECIAL_REPAIR_HOLDER_SEA_OF_TREES_NAME"] = "Обломки ",
["SPECIAL_REPAIR_HOLDER_SPIDERS_DESCRIPTION"] = "Уберите паутину, чтобы задействовать эту стратегическую точку.",
["SPECIAL_REPAIR_HOLDER_SPIDERS_NAME"] = "Опутанная Точка",
["SPECIAL_REPAIR_OVERSEER_DESCRIPTION"] = "Избавьтесь от щупалец, чтобы разблокировать эту стратегическую точку.",
["SPECIAL_REPAIR_OVERSEER_NAME"] = "Щупальца",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_1_DESCRIPTION"] = "Наймите Эльфийского Наемника для битвы. Возрождается каждые десять секунд.",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_1_NAME"] = "Эльфийские Наемники I",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_2_DESCRIPTION"] = "Наймите до двух Эльфийских Наемников для битвы. Возрождаются каждые десять секунд.",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_2_NAME"] = "Эльфийские Наемники II",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_3_DESCRIPTION"] = "Наймите до трех Эльфийских Наемников для битвы. Возрождаются каждые десять секунд.",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_3_NAME"] = "Эльфийские Наемники III",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_1"] = "Выпускает магические снаряды, которые разрушают иллюзии Мидриас и не позволяют ей создавать новые на протяжении нескольких секунд.",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_2"] = "Призывает 2 Демонов-стражей, которые идут по пути и сражаются с врагами.",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_3"] = "Запирает Денаса, не позволяя ему двигаться и атаковать.",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_1"] = "Удар Души",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_2"] = "Адские Отродья",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_3"] = "Волшебные Силки",
["START BATTLE!"] = "НАЧАТЬ БОЙ!",
["START HERE!"] = "НАЧНИТЕ ЗДЕСЬ!",
["STRATEGY BASICS!"] = "ОСНОВЫ СТРАТЕГИИ!",
["Select by tapping on the portrait or hero unit."] = "Сделайте выбор, нажав на портрет или героя",
["Sell Tower"] = "Продать башню",
["Sell this tower and get a %s GP refund."] = "Продайте башню и получите золото: %s.",
["Shows level, health and experience."] = "Показывает уровень, здоровье и опыт.",
["Special abilities"] = "Особые способности",
["Support your soldiers with ranged towers!"] = "Поддержите своих солдат, размещая башни дальнего действия!",
["Survival mode!"] = "Режим выживания!",
["TAP_TO_START"] = "Нажмите, чтобы начать",
["TAUNT_BOSS_PIG_FROM_POOL_0001"] = "Ты у меня завизжишь!",
["TAUNT_BOSS_PIG_FROM_POOL_0002"] = "Скажи! Скажи еще раз! Скажи еще раз \"бекон\"!",
["TAUNT_BOSS_PIG_FROM_POOL_0003"] = "Люди снова в меню, парни!",
["TAUNT_BOSS_PIG_FROM_POOL_0004"] = "Поторопись! Я голоден.",
["TAUNT_BOSS_PIG_FROM_POOL_0005"] = "Мне будет приятно посмотреть, как ты умираешь.",
["TAUNT_BOSS_PIG_FROM_POOL_0006"] = "Я та еще деловая колбаса.",
["TAUNT_LVL30_BOSS_ABILITY_01"] = "Пируйте, дети мои!",
["TAUNT_LVL30_BOSS_ABILITY_02"] = "Повиси-ка тут! МВАХАХАХА!",
["TAUNT_LVL30_BOSS_ABILITY_03"] = "За Культ!",
["TAUNT_LVL30_BOSS_ABILITY_04"] = "Всем по кусочку!",
["TAUNT_LVL30_BOSS_ABILITY_05"] = "Моё паучье чутьё подсказывает...",
["TAUNT_LVL30_BOSS_ABILITY_06"] = "На колени перед Королевой, Альянс!",
["TAUNT_LVL30_BOSS_ABILITY_07"] = "Мой дом, мои правила!",
["TAUNT_LVL30_BOSS_ABILITY_08"] = "Никто не ускользнёт из моей паутины!",
["TAUNT_LVL30_BOSS_ABILITY_09"] = "Сгинь, человечья чума!",
["TAUNT_LVL30_BOSS_ABILITY_10"] = "Дёргаю за ваши ниточки!",
["TAUNT_LVL30_BOSS_ABILITY_11"] = "Убейте их всех!",
["TAUNT_LVL30_BOSS_INTRO_01"] = "Наконец-то! Убийцы моих сестер пожаловали...",
["TAUNT_LVL30_BOSS_INTRO_02"] = "Я отомщу за Сарельгаз и Мактанс...",
["TAUNT_LVL30_BOSS_INTRO_03"] = "И стану богом – отныне и навеки!",
["TAUNT_LVL30_BOSS_PREFIGHT_01"] = "С меня довольно...",
["TAUNT_LVL30_BOSS_PREFIGHT_02"] = "Вы лишь жалкие насекомые...",
["TAUNT_LVL30_BOSS_PREFIGHT_03"] = "Пойманные в сети Королевы!",
["TAUNT_LVL32_BOSS_ABILITY_01"] = "Глупцы! Я владею божественным пламенем — Огнём Самадхи!",
["TAUNT_LVL32_BOSS_ABILITY_02"] = "Пылающие языки пламени низвергаются с небес!",
["TAUNT_LVL32_BOSS_ABILITY_03"] = "Бойтесь истинного огня в его чистейшей форме!",
["TAUNT_LVL32_BOSS_ABILITY_04"] = "Плоть и души горят одинаково!",
["TAUNT_LVL32_BOSS_FIGHT_01"] = "Огонь во мне никогда не угаснет!",
["TAUNT_LVL32_BOSS_FINAL_01"] = "Моё пламя угасает...\nно у меня ещё есть дракон...",
["TAUNT_LVL32_BOSS_INTRO_01"] = "У тебя армия?",
["TAUNT_LVL32_BOSS_INTRO_02"] = "А у меня дракон! Ха-ха-ха-ха!",
["TAUNT_LVL32_BOSS_PREFIGHT_01"] = "Хватит! Сейчас моя очередь побеждать!",
["TAUNT_LVL32_BOSS_PREFIGHT_02"] = "Восхищайтесь моей истинной формой!",
["TAUNT_LVL34_BOSS_BOSSFIGHT_01"] = "Ладно, я знаю, что нам нужно. Больше меня. Я, я, я...",
["TAUNT_LVL34_BOSS_DEATH_01"] = "Этого не может быть… Неважно, мой муж заставит вас заплатить…",
["TAUNT_LVL34_BOSS_INTRO_01"] = "Вы, обезьяны! Осмелились прийти сюда после того, что сделали с моим сыном?",
["TAUNT_LVL34_BOSS_WAVES_01"] = "Вкусите мою силу, наглые глупцы!",
["TAUNT_LVL34_BOSS_WAVES_02"] = "Конец близок!",
["TAUNT_LVL35_BOSS_DEATH_01"] = "И так падает моё правление... в крови.",
["TAUNT_LVL35_BOSS_DESTROY_HOUSE_01"] = "Ммм, это было дорого. Пора применить огневую мощь!",
["TAUNT_LVL35_BOSS_DESTROY_HOUSE_02"] = "Ах, звук настойчивости. Время воды, мадам!",
["TAUNT_LVL35_BOSS_DESTROY_HOUSE_03"] = "Ррр! Покорись моей вульгарной демонстрации силы!",
["TAUNT_LVL35_BOSS_INTRO_01"] = "Жалкие люди, радуйтесь, пока вы ещё среди живых.",
["TAUNT_LVL35_BOSS_INTRO_02"] = "Время нового порядка.",
["TAUNT_LVL35_BOSS_INTRO_03"] = "Арргх, я кричу о мести!",
["TAUNT_LVL35_BOSS_PREFIGHT_01"] = "Хорошо, тогда я покажу тебе, почему УБИВАТЬ — это моё дело!",
["TAUNT_STAGE02_RAELYN_0001"] = "Приступим.",
["TAUNT_STAGE02_VEZNAN_0001"] = "Они идут. Я помогу вашим хилым войскам...",
["TAUNT_STAGE02_VEZNAN_0002"] = "...в смысле, одна из моих лучших солдат займется этим. ХА!",
["TAUNT_STAGE02_VEZNAN_0003"] = "ХА ХА ХА!",
["TAUNT_STAGE06_BOSS_PIG_PREBATTLE_0001"] = "Ладно... Я сделаю это сам.",
["TAUNT_STAGE06_BOSS_PIG_RESPONSE_0001"] = "Спокуха, все под контролем. ",
["TAUNT_STAGE06_CULTIST_GREETING_0001"] = "Вижу, ты хорошо устроился...",
["TAUNT_STAGE06_CULTIST_GREETING_0002"] = "...лучше бы тебе сдержать свою часть сделки.",
["TAUNT_STAGE11_CULTIST_LEADER_0001"] = "Хорошо, что тебе удалось добраться так далеко...",
["TAUNT_STAGE11_CULTIST_LEADER_0002"] = "... но нельзя остановить грядущее!",
["TAUNT_STAGE11_CULTIST_LEADER_0003"] = "ДОВОЛЬНО!!!",
["TAUNT_STAGE11_CULTIST_LEADER_0004"] = "Пришло время вам СКЛОНИТЬСЯ перед нами!",
["TAUNT_STAGE11_CULTIST_LEADER_0005"] = "Гррр... это еще не конец!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0001"] = "Нас ожидает новый мир.",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0002"] = "Ты недооцениваешь мою мощь.",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0003"] = "Окулус Покулус!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0004"] = "Вы слышите? Это – рок, неизбежность!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0005"] = "Я злая? Еще как!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0006"] = "Всевидящий благословляет нас!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0001"] = "Твой конец близок!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0002"] = "Наконец-то я вижу истину!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0003"] = "Поздоровайся с моими друзьями из пустоты!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0004"] = "Окулус Покулус!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0005"] = "Вы все жалкие отбросы!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0006"] = "Всевидящий благословляет нас!",
["TAUNT_STAGE11_VEZNAN_0001"] = "Денас, друг мой. Давно не виделись!",
["TAUNT_STAGE15_CULTIST_0001"] = "Оно близко... Я чувствую его пробуждение!",
["TAUNT_STAGE15_CULTIST_0002"] = "Грядет новая эра. Ваши усилия пойдут прахом!",
["TAUNT_STAGE15_CULTIST_0003"] = "Аргх... ваш альянс силен.",
["TAUNT_STAGE15_CULTIST_0004"] = "Но я покажу вам, что есть истинная сила!",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0001"] = "Глупцы! Вы пришли на смерть.",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0002"] = "Склонитесь перед его взором!",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0003"] = "Вы станете истинно верующими.",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0004"] = "Ваш Альянс здесь, но вы обречены!",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0005"] = "В пустоте нет жизни, только смерть!",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0006"] = "Хватит тратить мое время!",
["TAUNT_STAGE15_DENAS_0001"] = "Мне нужно сравнять счет. Этот бой я не пропущу!",
["TAUNT_STAGE16_DENAS_AFTER_BOSSFIGHT_0001"] = "Что, зрение подводит? ",
["TAUNT_STAGE18_ERIDAN_FIGHT_0001"] = "Ночью пролилась кровь.",
["TAUNT_STAGE18_ERIDAN_FIGHT_0002"] = "Наша вера – в Элини.",
["TAUNT_STAGE18_ERIDAN_FIGHT_0003"] = "Gnillur speek Edihnori!",
["TAUNT_STAGE18_ERIDAN_FIGHT_0004"] = "У меня ж глаз-алмаз.",
["TAUNT_STAGE18_ERIDAN_FIGHT_0005"] = "Аредхел выстоит!",
["TAUNT_STAGE18_ERIDAN_FIGHT_0006"] = "Это не просто странники!",
["TAUNT_STAGE18_ERIDAN_FIGHT_0007"] = "Еще ведешь счет?",
["TAUNT_STAGE18_ERIDAN_FIGHT_0008"] = "Подпустим их ближе!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0001"] = "Мой лук с тобой!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0002"] = "Действуйте быстро!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0003"] = "На позиции!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0004"] = "Смотрите в оба!",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0001"] = "Разминка окончена!",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0002"] = "Вы показали себя воистину назойливыми...",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0003"] = "Да начнется настоящая битва!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0001"] = "Я управляю всеми душами!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0002"] = "Эльфы возвысятся вновь.",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0003"] = "Я поднимаю все... даже мертвых!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0004"] = "Велением древних нечестивых сил!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0005"] = "Бойтесь детей моих могильных!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0006"] = "Я верну былую славу моему народу.",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0001"] = "Ах, могучий Альянс заглянул на огонек.",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0002"] = "Пора сбросить маски! ",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0003"] = "Узрите мощь смерти!",
["TAUNT_STAGE22_BOSS_CROCS_BEFORE_BOSSFIGHT_0001"] = "Свобода! Пришло время сожрать...",
["TAUNT_STAGE22_BOSS_CROCS_BEFORE_BOSSFIGHT_0002"] = "ВСЁЁЁЁЁЁЁЁЁЁЁ!!!!",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0001"] = "Довольно игр!",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0002"] = "Гримбород научит вас манерам. ",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0003"] = "Все на борт, АХАХАХА!",
["TAUNT_STAGE25_BOSS_MACHINIST_END_0001"] = "Вы, наглые глупцы!",
["TAUNT_STAGE25_BOSS_MACHINIST_END_0002"] = "Вам никогда меня не поймать, ХАХАХА!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0001"] = "НЕТ! Еще есть время...",
["TAUNT_STAGE26_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0002"] = "ЧЕРТ ПОБЕРИ!!!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0001"] = "Не тягаться вам с этой армией!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0002"] = "Гримбород не в опасности.",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0003"] = "Гримбород И ЕСТЬ опасность!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0004"] = "Смог бы безумец добиться подобного?",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0005"] = "Мир склонится перед Гримбородом!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0001"] = "Терпение Гримборода на исходе...",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0002"] = "Вы увидите то, что вам и не снилось!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0003"] = "Гримбороду никто не нужен, кроме Гримборода!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0004"] = "Может вы поторопитесь?!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0001"] = "Вы и ваш назойливый Альянс!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0002"] = "Я научу вас не связываться...",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0003"] = "...с ГЛАВНЫМ гномом!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0001"] = "Давите клонов сколько влезет, я просто сделаю новых.",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0002"] = "Хочешь сделать хорошо – сделай это сам.",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0003"] = "Ох, Гримбород, ты просто гений!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0004"] = "Вам не уйти отсюда в целости!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0005"] = "Вы вообще пытаетесь?",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0006"] = "Вы думаете, что можете превзойти мои творения?",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0001"] = "Я вижу, вам меня мало...",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0002"] = "И теперь вы решили сразиться с ИСХОДНЫМ гномом?",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0003"] = "Желаю удачи.",
["TAUNT_TUTORIAL_ARBOREAN_ALL_0001"] = "Продолжай! Мы верим в тебя.",
["TAUNT_TUTORIAL_ARBOREAN_BARRACK_0001"] = "Вот, постройте казарму!",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_1_NAME"] = "Культяллиам",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_2_NAME"] = "Щупальцастый Генри",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_3_NAME"] = "Щупальцастый Джеффри",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_4_NAME"] = "Тентаклас",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_5_NAME"] = "Тедтакль",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_6_NAME"] = "Рукохоб",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_7_NAME"] = "Тентодо",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_8_NAME"] = "Культядрик",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_9_NAME"] = "Щупальцастый Хал",
["TERMS_OF_SERVICE_LINK"] = "Условия обслуживания",
["TIP_TITLE"] = "Совет:",
["TOWER_ARBOREAN_EMISSARY_1_DESCRIPTION"] = "Древесники делают своих врагов более уязвимыми, используя мощную магию природы.",
["TOWER_ARBOREAN_EMISSARY_1_NAME"] = "Эмиссар Древесников I",
["TOWER_ARBOREAN_EMISSARY_2_DESCRIPTION"] = "Древесники делают своих врагов более уязвимыми, используя мощную магию природы.",
["TOWER_ARBOREAN_EMISSARY_2_NAME"] = "Эмиссар Древесников II",
["TOWER_ARBOREAN_EMISSARY_3_DESCRIPTION"] = "Древесники делают своих врагов более уязвимыми, используя мощную магию природы.",
["TOWER_ARBOREAN_EMISSARY_3_NAME"] = "Эмиссар Древесников III",
["TOWER_ARBOREAN_EMISSARY_4_DESCRIPTION"] = "Древесники делают своих врагов более уязвимыми, используя мощную магию природы.",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_1_DESCRIPTION"] = "Вызывает светлячков, которые восстанавливают %$towers.arborean_emissary.gift_of_nature.s_heal[1]%$ здоровья в секунду в течение %$towers.arborean_emissary.gift_of_nature.duration[1]%$ секунд всем союзникам в области.",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_1_NAME"] = "ДАР ПРИРОДЫ",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_2_DESCRIPTION"] = "Вызывает светлячков, которые восстанавливают %$towers.arborean_emissary.gift_of_nature.s_heal[2]%$ здоровья в секунду в течение %$towers.arborean_emissary.gift_of_nature.duration[2]%$ секунд всем союзникам в области.",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_2_NAME"] = "ДАР ПРИРОДЫ",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_3_DESCRIPTION"] = "Вызывает светлячков, которые восстанавливают %$towers.arborean_emissary.gift_of_nature.s_heal[3]%$ здоровья в секунду в течение %$towers.arborean_emissary.gift_of_nature.duration[3]%$ секунд всем союзникам в области.",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_3_NAME"] = "ДАР ПРИРОДЫ",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_NAME"] = "ДАР ПРИРОДЫ",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_NOTE"] = "Никогда не шути с Природой.",
["TOWER_ARBOREAN_EMISSARY_4_NAME"] = "Эмиссар Древесников IV",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_1_DESCRIPTION"] = "Выращивает %$towers.arborean_emissary.wave_of_roots.max_targets[1]%$ корня на пути, нанося %$towers.arborean_emissary.wave_of_roots.s_damage[1]%$ чистого урона и оглушая врагов на %$towers.arborean_emissary.wave_of_roots.mod_duration[1]%$ секунд.",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_1_NAME"] = "КОЛЮЧАЯ ХВАТКА",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_2_DESCRIPTION"] = "Выращивает %$towers.arborean_emissary.wave_of_roots.max_targets[2]%$ корней на пути, нанося %$towers.arborean_emissary.wave_of_roots.s_damage[2]%$ чистого урона и оглушая врагов на %$towers.arborean_emissary.wave_of_roots.mod_duration[2]%$ секунд.",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_2_NAME"] = "КОЛЮЧАЯ ХВАТКА",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_3_DESCRIPTION"] = "Выращивает %$towers.arborean_emissary.wave_of_roots.max_targets[3]%$ корней на пути, нанося %$towers.arborean_emissary.wave_of_roots.s_damage[3]%$ чистого урона и оглушая врагов на %$towers.arborean_emissary.wave_of_roots.mod_duration[3]%$ секунд.",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_3_NAME"] = "КОЛЮЧАЯ ХВАТКА",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_NAME"] = "КОЛЮЧАЯ ХВАТКА",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_NOTE"] = "Смотри под ноги.",
["TOWER_ARBOREAN_EMISSARY_DESC"] = "Когда их провоцируют, обыкновенно мирные Древесники используют свою магию, помечающую и ослабляющую врагов.",
["TOWER_ARBOREAN_EMISSARY_NAME"] = "Эмиссар Древесников ",
["TOWER_ARBOREAN_SENTINELS_DESCRIPTION"] = "Проворные защитники леса.",
["TOWER_ARBOREAN_SENTINELS_NAME"] = "Древесники Шипокопейщики",
["TOWER_ARCANE_WIZARD_1_DESCRIPTION"] = "Признанные мастера магических искусств, эти волшебники всегда готовы к бою.",
["TOWER_ARCANE_WIZARD_1_NAME"] = "Таинственный Волшебник I",
["TOWER_ARCANE_WIZARD_2_DESCRIPTION"] = "Признанные мастера магических искусств, эти волшебники всегда готовы к бою.",
["TOWER_ARCANE_WIZARD_2_NAME"] = "Таинственный Волшебник II",
["TOWER_ARCANE_WIZARD_3_DESCRIPTION"] = "Признанные мастера магических искусств, эти волшебники всегда готовы к бою.",
["TOWER_ARCANE_WIZARD_3_NAME"] = "Таинственный Волшебник III",
["TOWER_ARCANE_WIZARD_4_DESCRIPTION"] = "Признанные мастера магических искусств, эти волшебники всегда готовы к бою.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_1_DESCRIPTION"] = "Выпускает луч, мгновенно убивающий цель. Боссы и мини-боссы вместо этого получают %$towers.arcane_wizard.disintegrate.boss_damage[1]%$ магического урона.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_1_NAME"] = "ИСПЕПЕЛИТЬ!",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_2_DESCRIPTION"] = "Сокращает время перезарядки луча до %$towers.arcane_wizard.disintegrate.cooldown[2]%$ секунд. Урон по боссам и мини-боссам теперь составляет %$towers.arcane_wizard.disintegrate.boss_damage[2]%$.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_2_NAME"] = "ИСПЕПЕЛИТЬ!",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_3_DESCRIPTION"] = "Сокращает время перезарядки луча до %$towers.arcane_wizard.disintegrate.cooldown[3]%$ секунд. Урон по боссам и мини-боссам теперь составляет %$towers.arcane_wizard.disintegrate.boss_damage[3]%$.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_3_NAME"] = "ИСПЕПЕЛИТЬ!",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_NAME"] = "ИСПЕПЕЛИТЬ!",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_NOTE"] = "Прах к праху. ",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_1_DESCRIPTION"] = "Увеличивает урон ближайших башен на %$towers.arcane_wizard.empowerment.s_damage_factor[1]%$%.",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_1_NAME"] = "СОВЕРШЕНСТВОВАНИЕ",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_2_DESCRIPTION"] = "Увеличивает урон ближайших башен на %$towers.arcane_wizard.empowerment.s_damage_factor[2]%$%.",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_2_NAME"] = "СОВЕРШЕНСТВОВАНИЕ",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_3_DESCRIPTION"] = "Увеличивает урон ближайших башен на %$towers.arcane_wizard.empowerment.s_damage_factor[3]%$%.",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_3_NAME"] = "СОВЕРШЕНСТВОВАНИЕ",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_NAME"] = "СОВЕРШЕНСТВОВАНИЕ",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_NOTE"] = "Абсолютная власть!",
["TOWER_ARCANE_WIZARD_4_NAME"] = "Таинственный Волшебник IV",
["TOWER_ARCANE_WIZARD_DESC"] = "Посвятившие себя чистой магии, волшебники Линерии владеют достаточной силой, чтобы полностью уничтожить своих врагов. ",
["TOWER_ARCANE_WIZARD_NAME"] = "Таинственный Волшебник",
["TOWER_BALLISTA_1_DESCRIPTION"] = "Прекрасное дополнение к арсеналу Зеленокожих. Чудо, что эта штуковина еще не развалилась.",
["TOWER_BALLISTA_1_NAME"] = "Баллиста I",
["TOWER_BALLISTA_2_DESCRIPTION"] = "Прекрасное дополнение к арсеналу Зеленокожих. Чудо, что эта штуковина еще не развалилась.",
["TOWER_BALLISTA_2_NAME"] = "Баллиста II",
["TOWER_BALLISTA_3_DESCRIPTION"] = "Прекрасное дополнение к арсеналу Зеленокожих. Чудо, что эта штуковина еще не развалилась.",
["TOWER_BALLISTA_3_NAME"] = "Баллиста III",
["TOWER_BALLISTA_4_DESCRIPTION"] = "Прекрасное дополнение к арсеналу Зеленокожих. Чудо, что эта штуковина еще не развалилась.",
["TOWER_BALLISTA_4_NAME"] = "Баллиста IV",
["TOWER_BALLISTA_4_SKILL_BOMB_1_DESCRIPTION"] = "Швыряет бомбу из металлолома на большое расстояние, нанося %$towers.ballista.skill_bomb.damage_min[1]%$-%$towers.ballista.skill_bomb.damage_max[1]%$ физического урона. Обломки замедляют врагов на %$towers.ballista.skill_bomb.duration[1]%$ секунд.",
["TOWER_BALLISTA_4_SKILL_BOMB_1_NAME"] = "МУСОРНАЯ БОМБА",
["TOWER_BALLISTA_4_SKILL_BOMB_2_DESCRIPTION"] = "Мусорная бомба теперь наносит %$towers.ballista.skill_bomb.damage_min[2]%$-%$towers.ballista.skill_bomb.damage_max[2]%$ физического урона и замедляет врагов на %$towers.ballista.skill_bomb.duration[1]%$ секунд.",
["TOWER_BALLISTA_4_SKILL_BOMB_2_NAME"] = "МУСОРНАЯ БОМБА",
["TOWER_BALLISTA_4_SKILL_BOMB_3_DESCRIPTION"] = "Мусорная бомба теперь наносит %$towers.ballista.skill_bomb.damage_min[3]%$-%$towers.ballista.skill_bomb.damage_max[3]%$ физического урона и замедляет врагов на %$towers.ballista.skill_bomb.duration[1]%$ секунд.",
["TOWER_BALLISTA_4_SKILL_BOMB_3_NAME"] = "МУСОРНАЯ БОМБА",
["TOWER_BALLISTA_4_SKILL_BOMB_NOTE"] = "Берегись!",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_1_DESCRIPTION"] = "Последний выстрел башни наносит на %$towers.ballista.skill_final_shot.s_damage_factor[1]%$% больше урона и оглушает цель на %$towers.ballista.skill_final_shot.s_stun%$ секунды.",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_1_NAME"] = "ПОСЛЕДНИЙ ШТРИХ",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_2_DESCRIPTION"] = "Последний выстрел наносит на %$towers.ballista.skill_final_shot.s_damage_factor[3]%$% больше урона и оглушает цель на %$towers.ballista.skill_final_shot.s_stun%$ секунды.",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_2_NAME"] = "ПОСЛЕДНИЙ ШТРИХ",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_3_DESCRIPTION"] = "Последний выстрел наносит на %$towers.ballista.skill_final_shot.s_damage_factor[3]%$% больше урона и оглушает цель на %$towers.ballista.skill_final_shot.s_stun%$ секунды.",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_3_NAME"] = "ПОСЛЕДНИЙ ШТРИХ",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_NOTE"] = "Отличный выстрел! Один на миллион!",
["TOWER_BALLISTA_DESC"] = "Помешанные на войне, гоблины сделали все, дабы им никогда больше не пришлось брать в руки лук.",
["TOWER_BALLISTA_NAME"] = "Баллиста",
["TOWER_BARREL_1_DESCRIPTION"] = "Бочки с зельями – мощное оружие северян против орд врагов.",
["TOWER_BARREL_1_NAME"] = "Боевые Пивовары I",
["TOWER_BARREL_2_DESCRIPTION"] = "Бочки с зельями – мощное оружие северян против орд врагов.",
["TOWER_BARREL_2_NAME"] = "Боевые Пивовары II",
["TOWER_BARREL_3_DESCRIPTION"] = "Бочки с зельями – мощное оружие северян против орд врагов.",
["TOWER_BARREL_3_NAME"] = "Боевые Пивовары III",
["TOWER_BARREL_4_DESCRIPTION"] = "Бочки с зельями – мощное оружие северян против орд врагов.",
["TOWER_BARREL_4_NAME"] = "Боевые Пивовары IV",
["TOWER_BARREL_4_SKILL_BARREL_1_DESCRIPTION"] = "Бросает ядовитую бочку, наносящую %$towers.barrel.skill_barrel.explosion.damage_min[1]%$-%$towers.barrel.skill_barrel.explosion.damage_max[1]%$ физического урона. Бочка оставляет яд, наносящий %$towers.barrel.skill_barrel.poison.s_damage%$ чистого урона каждую секунду в течение %$towers.barrel.skill_barrel.poison.duration%$ секунд. ",
["TOWER_BARREL_4_SKILL_BARREL_1_NAME"] = "БРАКОВАННАЯ ПАРТИЯ",
["TOWER_BARREL_4_SKILL_BARREL_2_DESCRIPTION"] = "Взрыв бочки наносит %$towers.barrel.skill_barrel.explosion.damage_min[2]%$-%$towers.barrel.skill_barrel.explosion.damage_max[2]%$ физического урона. Яд наносит %$towers.barrel.skill_barrel.poison.s_damage%$ чистого урона в секунду в течение %$towers.barrel.skill_barrel.poison.duration%$ секунд.",
["TOWER_BARREL_4_SKILL_BARREL_2_NAME"] = "БРАКОВАННАЯ ПАРТИЯ",
["TOWER_BARREL_4_SKILL_BARREL_3_DESCRIPTION"] = "Взрыв бочки наносит %$towers.barrel.skill_barrel.explosion.damage_min[3]%$-%$towers.barrel.skill_barrel.explosion.damage_max[3]%$ физического урона. Яд наносит %$towers.barrel.skill_barrel.poison.s_damage%$ чистого урона в секунду в течение %$towers.barrel.skill_barrel.poison.duration%$ секунд.",
["TOWER_BARREL_4_SKILL_BARREL_3_NAME"] = "БРАКОВАННАЯ ПАРТИЯ",
["TOWER_BARREL_4_SKILL_BARREL_NOTE"] = "Только для безбашенных!",
["TOWER_BARREL_4_SKILL_WARRIOR_1_DESCRIPTION"] = "Вызывает на подмогу могучего воина. Он имеет %$towers.barrel.skill_warrior.entity.hp_max[1]%$ здоровья и наносит %$towers.barrel.skill_warrior.entity.damage_min[1]%$-%$towers.barrel.skill_warrior.entity.damage_max[1]%$ физического урона.",
["TOWER_BARREL_4_SKILL_WARRIOR_1_NAME"] = "ЭЛИКСИР МОГУЩЕСТВА",
["TOWER_BARREL_4_SKILL_WARRIOR_2_DESCRIPTION"] = "Воин имеет %$towers.barrel.skill_warrior.entity.hp_max[2]%$ здоровья и наносит %$towers.barrel.skill_warrior.entity.damage_min[2]%$-%$towers.barrel.skill_warrior.entity.damage_max[2]%$ физического урона.",
["TOWER_BARREL_4_SKILL_WARRIOR_2_NAME"] = "ЭЛИКСИР МОГУЩЕСТВА",
["TOWER_BARREL_4_SKILL_WARRIOR_3_DESCRIPTION"] = "Воин имеет %$towers.barrel.skill_warrior.entity.hp_max[3]%$ здоровья и наносит %$towers.barrel.skill_warrior.entity.damage_min[3]%$-%$towers.barrel.skill_warrior.entity.damage_max[3]%$ физического урона.",
["TOWER_BARREL_4_SKILL_WARRIOR_3_NAME"] = "ЭЛИКСИР МОГУЩЕСТВА",
["TOWER_BARREL_4_SKILL_WARRIOR_NOTE"] = "Вкус победы!",
["TOWER_BARREL_DESC"] = "Северяне – эксперты в зельеварении, готовые задействовать свои напитки в бою.",
["TOWER_BARREL_NAME"] = "Боевые Пивовары",
["TOWER_BARREL_WARRIOR_NAME"] = "Хальфдан Тугодум",
["TOWER_BROKEN_DESCRIPTION"] = "Эта башня сломана, потратьте золото, чтобы починить её.",
["TOWER_BROKEN_NAME"] = "Поврежденная Башня",
["TOWER_CROCS_EATEN_DESCRIPTION"] = "Волшебным образом возвращает башне ее первозданный вид.",
["TOWER_CROCS_EATEN_NAME"] = "Съеденная Башня",
["TOWER_DARK_ELF_1_DESCRIPTION"] = "Неважно, как далек или силен враг, все равно слово остается за их меткостью. ",
["TOWER_DARK_ELF_1_NAME"] = "Сумеречные Длиннолучники I",
["TOWER_DARK_ELF_2_DESCRIPTION"] = "Неважно, как далек или силен враг, все равно слово остается за их меткостью. ",
["TOWER_DARK_ELF_2_NAME"] = "Сумеречные Длиннолучники II",
["TOWER_DARK_ELF_3_DESCRIPTION"] = "Неважно, как далек или силен враг, все равно слово остается за их меткостью. ",
["TOWER_DARK_ELF_3_NAME"] = "Сумеречные Длиннолучники III",
["TOWER_DARK_ELF_4_DESCRIPTION"] = "Неважно, как далек или силен враг, все равно слово остается за их меткостью. ",
["TOWER_DARK_ELF_4_NAME"] = "Сумеречные Длиннолучники IV",
["TOWER_DARK_ELF_4_SKILL_BUFF_1_DESCRIPTION"] = "Каждое убийство врага увеличивает урон башни на %$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$.",
["TOWER_DARK_ELF_4_SKILL_BUFF_1_NAME"] = "ОХОТНИЧИЙ АЗАРТ",
["TOWER_DARK_ELF_4_SKILL_BUFF_2_DESCRIPTION"] = "Каждое убийство врага увеличивает урон башни на %$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$.",
["TOWER_DARK_ELF_4_SKILL_BUFF_2_NAME"] = "ОХОТНИЧИЙ АЗАРТ",
["TOWER_DARK_ELF_4_SKILL_BUFF_3_DESCRIPTION"] = "Каждое убийство врага увеличивает урон башни на %$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$.",
["TOWER_DARK_ELF_4_SKILL_BUFF_3_NAME"] = "ОХОТНИЧИЙ АЗАРТ",
["TOWER_DARK_ELF_4_SKILL_BUFF_NOTE"] = "Ату его!",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_1_DESCRIPTION"] = "Призывает двух Сумеречных Истязателей. Они имеют %$towers.dark_elf.soldier.hp[1]%$ здоровья и наносят %$towers.dark_elf.soldier.basic_attack.damage_min[1]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[1]%$ физического урона.",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_1_NAME"] = "КЛИНКИ ПОМОЩИ ",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_2_DESCRIPTION"] = "Истязатели теперь имеют %$towers.dark_elf.soldier.hp[2]%$ здоровья и наносят %$towers.dark_elf.soldier.basic_attack.damage_min[2]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[2]%$ физического урона.",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_2_NAME"] = "КЛИНКИ ПОМОЩИ ",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_3_DESCRIPTION"] = "Истязатели теперь имеют %$towers.dark_elf.soldier.hp[3]%$ здоровья и наносят %$towers.dark_elf.soldier.basic_attack.damage_min[3]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[3]%$ физического урона.",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_3_NAME"] = "КЛИНКИ ПОМОЩИ ",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_NOTE"] = "Они выйдут поиграть.",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_DESCRIPTION"] = "Меняет фокус башни на врага, ближайшего к выходу.",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_NAME"] = "Выбор Цели: Ближайший",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_NOTE"] = "Не дайте им уйти!",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_DESCRIPTION"] = "Меняет фокус башни на врага с наибольшим здоровьем.",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_NAME"] = "Выбор Цели: Макс ОЗ ",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_NOTE"] = "Бери самого крупного! ",
["TOWER_DARK_ELF_DESC"] = "Лучники, искусные в охоте на сильных врагов. Они наполняют свои стрелы темной энергией.",
["TOWER_DARK_ELF_NAME"] = "Сумеречные Длиннолучники",
["TOWER_DEMON_PIT_1_DESCRIPTION"] = "Озорные и опасные, эти демоны всегда ищут неприятностей.",
["TOWER_DEMON_PIT_1_NAME"] = "Яма Демонов I",
["TOWER_DEMON_PIT_2_DESCRIPTION"] = "Озорные и опасные, эти демоны всегда ищут неприятностей.",
["TOWER_DEMON_PIT_2_NAME"] = "Яма Демонов II",
["TOWER_DEMON_PIT_3_DESCRIPTION"] = "Озорные и опасные, эти демоны всегда ищут неприятностей.",
["TOWER_DEMON_PIT_3_NAME"] = "Яма Демонов III",
["TOWER_DEMON_PIT_4_BIG_DEMON_1_DESCRIPTION"] = "Призывает огромного беса с %$towers.demon_pit.big_guy.hp_max[1]%$ здоровья, наносящего %$towers.demon_pit.big_guy.melee_attack.damage_min[1]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[1]%$ физического урона. Его взрыв наносит %$towers.demon_pit.big_guy.explosion_damage[1]%$ урона.",
["TOWER_DEMON_PIT_4_BIG_DEMON_1_NAME"] = "БОЛЬШОЙ БОСС",
["TOWER_DEMON_PIT_4_BIG_DEMON_2_DESCRIPTION"] = "Бес имеет %$towers.demon_pit.big_guy.hp_max[2]%$ здоровья и наносит %$towers.demon_pit.big_guy.melee_attack.damage_min[2]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[2]%$ физического урона. Взрыв наносит %$towers.demon_pit.big_guy.explosion_damage[2]%$ урона.",
["TOWER_DEMON_PIT_4_BIG_DEMON_2_NAME"] = "БОЛЬШОЙ БОСС",
["TOWER_DEMON_PIT_4_BIG_DEMON_3_DESCRIPTION"] = "Бес имеет %$towers.demon_pit.big_guy.hp_max[3]%$ здоровья и наносит %$towers.demon_pit.big_guy.melee_attack.damage_min[3]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[3]%$ физического урона. Взрыв наносит %$towers.demon_pit.big_guy.explosion_damage[3]%$ урона.",
["TOWER_DEMON_PIT_4_BIG_DEMON_3_NAME"] = "БОЛЬШОЙ БОСС",
["TOWER_DEMON_PIT_4_BIG_DEMON_NAME"] = "БОЛЬШОЙ БОСС",
["TOWER_DEMON_PIT_4_BIG_DEMON_NOTE"] = "Просто пытаюсь расслабиться.",
["TOWER_DEMON_PIT_4_DESCRIPTION"] = "Озорные и опасные, эти демоны всегда ищут неприятностей.",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_1_DESCRIPTION"] = "Взрывы чертенят теперь наносят на %$towers.demon_pit.master_exploders.s_damage_increase[1]%$% больше урона и поджигают врагов, нанося %$towers.demon_pit.master_exploders.s_total_burning_damage_min[1]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[1]%$ чистого урона в секунду в течение %$towers.demon_pit.master_exploders.s_burning_duration[1]%$ секунд. ",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_1_NAME"] = "МАСТЕРА ВЗРЫВОВ",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_2_DESCRIPTION"] = "Взрывы чертенят наносят на %$towers.demon_pit.master_exploders.s_damage_increase[2]%$% больше урона. Горение наносит %$towers.demon_pit.master_exploders.s_total_burning_damage_min[2]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[2]%$ чистого урона в секунду в течение %$towers.demon_pit.master_exploders.s_burning_duration[2]%$ секунд.",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_2_NAME"] = "МАСТЕРА ВЗРЫВОВ",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_3_DESCRIPTION"] = "Взрывы чертенят наносят на %$towers.demon_pit.master_exploders.s_damage_increase[3]%$% больше урона. Горение наносит %$towers.demon_pit.master_exploders.s_total_burning_damage_min[3]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[3]%$ чистого урона в секунду в течение %$towers.demon_pit.master_exploders.s_burning_duration[2]%$ секунд.",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_3_NAME"] = "МАСТЕРА ВЗРЫВОВ",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_NAME"] = "МАСТЕРА ВЗРЫВОВ",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_NOTE"] = "Только идиот за такое возьмется.",
["TOWER_DEMON_PIT_4_NAME"] = "Яма Демонов IV",
["TOWER_DEMON_PIT_DESC"] = "Восстав из глубин лавы, эти чертенята без колебаний бросаются наперерез врагам.",
["TOWER_DEMON_PIT_NAME"] = "Яма Демонов",
["TOWER_DEMON_PIT_SOLDIER_BIG_GUY_NAME"] = "Громила",
["TOWER_DEMON_PIT_SOLDIER_NAME"] = "Чертенок",
["TOWER_DWARF_1_DESCRIPTION"] = "Их запал и рост не очень велики, но через их ряды ничего не проходит живьем.",
["TOWER_DWARF_1_NAME"] = "Отряд Пушкарей I",
["TOWER_DWARF_2_DESCRIPTION"] = "Их запал и рост не очень велики, но через их ряды ничего не проходит живьем.",
["TOWER_DWARF_2_NAME"] = "Отряд Пушкарей II",
["TOWER_DWARF_3_DESCRIPTION"] = "Их запал и рост не очень велики, но через их ряды ничего не проходит живьем.",
["TOWER_DWARF_3_NAME"] = "Отряд Пушкарей III",
["TOWER_DWARF_4_DESCRIPTION"] = "Их запал и рост не очень велики, но через их ряды ничего не проходит живьем.",
["TOWER_DWARF_4_FORMATION_1_DESCRIPTION"] = "Добавляет в отряд третьего пушкаря.",
["TOWER_DWARF_4_FORMATION_1_NAME"] = "РАСТУЩИЕ РЯДЫ",
["TOWER_DWARF_4_FORMATION_2_DESCRIPTION"] = "Добавляет в отряд четвертого пушкаря.",
["TOWER_DWARF_4_FORMATION_2_NAME"] = "РАСТУЩИЕ РЯДЫ",
["TOWER_DWARF_4_FORMATION_3_DESCRIPTION"] = "Добавляет в отряд пятого пушкаря.",
["TOWER_DWARF_4_FORMATION_3_NAME"] = "РАСТУЩИЕ РЯДЫ",
["TOWER_DWARF_4_FORMATION_NOTE"] = "Дайте девчонкам пороха.",
["TOWER_DWARF_4_INCENDIARY_AMMO_1_DESCRIPTION"] = "Выстреливает взрывчаткой, которая наносит %$towers.dwarf.incendiary_ammo.damages_min[1]%$-%$towers.dwarf.incendiary_ammo.damages_max[1]%$ урона и поджигает врагов в зоне, нанося дополнительно %$towers.dwarf.incendiary_ammo.burn.s_damage[1]%$ урона в течение %$towers.dwarf.incendiary_ammo.burn.duration%$ секунд.",
["TOWER_DWARF_4_INCENDIARY_AMMO_1_NAME"] = "ОГНЕННЫЙ СНАРЯД",
["TOWER_DWARF_4_INCENDIARY_AMMO_2_DESCRIPTION"] = "Выстреливает взрывчаткой, которая наносит %$towers.dwarf.incendiary_ammo.damages_min[2]%$-%$towers.dwarf.incendiary_ammo.damages_max[2]%$ урона и поджигает врагов в зоне, нанося дополнительно %$towers.dwarf.incendiary_ammo.burn.s_damage[2]%$ урона в течение %$towers.dwarf.incendiary_ammo.burn.duration%$ секунд.",
["TOWER_DWARF_4_INCENDIARY_AMMO_2_NAME"] = "ОГНЕННЫЙ СНАРЯД",
["TOWER_DWARF_4_INCENDIARY_AMMO_3_DESCRIPTION"] = "Выстреливает взрывчаткой, которая наносит %$towers.dwarf.incendiary_ammo.damages_min[3]%$-%$towers.dwarf.incendiary_ammo.damages_max[3]%$ урона и поджигает врагов в зоне, нанося дополнительно %$towers.dwarf.incendiary_ammo.burn.s_damage[3]%$ урона в течение %$towers.dwarf.incendiary_ammo.burn.duration%$ секунд.",
["TOWER_DWARF_4_INCENDIARY_AMMO_3_NAME"] = "ОГНЕННЫЙ СНАРЯД",
["TOWER_DWARF_4_INCENDIARY_AMMO_NOTE"] = "Налетай, пока горячие!",
["TOWER_DWARF_4_NAME"] = "Отряд Пушкарей IV",
["TOWER_DWARF_DESC"] = "Экспертные стрелки с несравненной силой духа, присланные с севера для контроля неадекватного использования технологий.",
["TOWER_DWARF_NAME"] = "Отряд Пушкарей",
["TOWER_ELVEN_STARGAZERS_DESC"] = "Взывая к силе космоса, Эльфийские Астрологи способны сражаться с множеством врагов одновременно.",
["TOWER_ELVEN_STARGAZERS_NAME"] = "Эльфийский Астролог",
["TOWER_FLAMESPITTER_1_DESCRIPTION"] = "Огонь этого оружия способен посоперничать с пламенем дракона, и каждый выстрел сеет панику в рядах злодеев.",
["TOWER_FLAMESPITTER_1_NAME"] = "Огнемет Гномов I",
["TOWER_FLAMESPITTER_2_DESCRIPTION"] = "Огонь этого оружия способен посоперничать с пламенем дракона, и каждый выстрел сеет панику в рядах злодеев.",
["TOWER_FLAMESPITTER_2_NAME"] = "Огнемет Гномов II",
["TOWER_FLAMESPITTER_3_DESCRIPTION"] = "Огонь этого оружия способен посоперничать с пламенем дракона, и каждый выстрел сеет панику в рядах злодеев.",
["TOWER_FLAMESPITTER_3_NAME"] = "Огнемет Гномов III",
["TOWER_FLAMESPITTER_4_DESCRIPTION"] = "Огонь этого оружия способен посоперничать с пламенем дракона, и каждый выстрел сеет панику в рядах злодеев.",
["TOWER_FLAMESPITTER_4_NAME"] = "Огнемет Гномов IV",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_1_DESCRIPTION"] = "Выпускает пылающую бомбу, наносящую %$towers.flamespitter.skill_bomb.s_damage[1]%$ физического урона и поджигающую врагов, причиняя %$towers.flamespitter.skill_bomb.burning.s_damage%$ чистого урона в секунду на протяжении %$towers.flamespitter.skill_bomb.burning.duration%$ секунд. ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_1_NAME"] = "ПЫЛАЮЩИЙ СЛЕД ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_2_DESCRIPTION"] = "Пылающая бомба теперь наносит %$towers.flamespitter.skill_bomb.s_damage[2]%$ физического урона. Горение наносит %$towers.flamespitter.skill_bomb.burning.s_damage%$ чистого урона в секунду на протяжении %$towers.flamespitter.skill_bomb.burning.duration%$ секунд. ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_2_NAME"] = "ПЫЛАЮЩИЙ СЛЕД ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_3_DESCRIPTION"] = "Пылающая бомба теперь наносит %$towers.flamespitter.skill_bomb.s_damage[3]%$ физического урона. Горение наносит %$towers.flamespitter.skill_bomb.burning.s_damage%$ чистого урона в секунду на протяжении %$towers.flamespitter.skill_bomb.burning.duration%$ секунд. ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_3_NAME"] = "ПЫЛАЮЩИЙ СЛЕД ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_NOTE"] = "Огонь? Да я рожден в огне!",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_1_DESCRIPTION"] = "Из дороги вырываются столбы огня, наносящие %$towers.flamespitter.skill_columns.s_damage_out[1]%$-%$towers.flamespitter.skill_columns.s_damage_in[1]%$ физического урона и оглушающие врагов на %$towers.flamespitter.skill_columns.s_stun%$ секунду. ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_1_NAME"] = "ЖГУЧИЕ ОГНИ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_2_DESCRIPTION"] = "Столбы огня теперь наносят %$towers.flamespitter.skill_columns.s_damage_out[2]%$-%$towers.flamespitter.skill_columns.s_damage_in[2]%$ физического урона и оглушают врагов на %$towers.flamespitter.skill_columns.s_stun%$ секунду. ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_2_NAME"] = "ЖГУЧИЕ ОГНИ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_3_DESCRIPTION"] = "Столбы огня теперь наносят %$towers.flamespitter.skill_columns.s_damage_out[3]%$-%$towers.flamespitter.skill_columns.s_damage_in[3]%$ физического урона и оглушают врагов на %$towers.flamespitter.skill_columns.s_stun%$ секунду. ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_3_NAME"] = "ЖГУЧИЕ ОГНИ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_NOTE"] = "Смотри под ноги!",
["TOWER_FLAMESPITTER_DESC"] = "Гномы вселяют в Альянс свою пламенную решимость и с жаром кузницы врываются в битву.",
["TOWER_FLAMESPITTER_NAME"] = "Огнемет Гномов",
["TOWER_GHOST_1_DESCRIPTION"] = "Сейчас ты их видишь. Теперь нет. А теперь ты мертв.",
["TOWER_GHOST_1_NAME"] = "Мрачные Призраки I",
["TOWER_GHOST_2_DESCRIPTION"] = "Сейчас ты их видишь. Теперь нет. А теперь ты мертв.",
["TOWER_GHOST_2_NAME"] = "Мрачные Призраки II",
["TOWER_GHOST_3_DESCRIPTION"] = "Сейчас ты их видишь. Теперь нет. А теперь ты мертв.",
["TOWER_GHOST_3_NAME"] = "Мрачные Призраки III",
["TOWER_GHOST_4_DESCRIPTION"] = "Сейчас ты их видишь. Теперь нет. А теперь ты мертв.",
["TOWER_GHOST_4_EXTRA_DAMAGE_1_DESCRIPTION"] = "Призраки наносят %$towers.ghost.extra_damage.s_damage[1]%$% дополнительного урона, если сражаются дольше %$towers.ghost.extra_damage.cooldown_start%$ секунд.",
["TOWER_GHOST_4_EXTRA_DAMAGE_1_NAME"] = "ПОГЛОЩЕНИЕ ДУШИ",
["TOWER_GHOST_4_EXTRA_DAMAGE_2_DESCRIPTION"] = "Призраки наносят %$towers.ghost.extra_damage.s_damage[2]%$% дополнительного урона, если сражаются дольше %$towers.ghost.extra_damage.cooldown_start%$ секунд.",
["TOWER_GHOST_4_EXTRA_DAMAGE_2_NAME"] = "ПОГЛОЩЕНИЕ ДУШИ",
["TOWER_GHOST_4_EXTRA_DAMAGE_3_DESCRIPTION"] = "Призраки наносят %$towers.ghost.extra_damage.s_damage[3]%$% дополнительного урона, если сражаются дольше %$towers.ghost.extra_damage.cooldown_start%$ секунд.",
["TOWER_GHOST_4_EXTRA_DAMAGE_3_NAME"] = "ПОГЛОЩЕНИЕ ДУШИ",
["TOWER_GHOST_4_EXTRA_DAMAGE_NOTE"] = "Не лезь, убьет!",
["TOWER_GHOST_4_NAME"] = "Мрачные Призраки IV",
["TOWER_GHOST_4_SOUL_ATTACK_1_DESCRIPTION"] = "Поверженные призраки бросаются на ближайшего врага, нанося %$towers.ghost.soul_attack.s_damage[1]%$ чистого урона, снижая его скорость и уменьшая его урон вдвое.",
["TOWER_GHOST_4_SOUL_ATTACK_1_NAME"] = "БЕССМЕРТНЫЙ УЖАС",
["TOWER_GHOST_4_SOUL_ATTACK_2_DESCRIPTION"] = "Поверженные призраки бросаются на ближайшего врага, нанося %$towers.ghost.soul_attack.s_damage[2]%$ чистого урона, снижая его скорость и уменьшая его урон вдвое.",
["TOWER_GHOST_4_SOUL_ATTACK_2_NAME"] = "БЕССМЕРТНЫЙ УЖАС",
["TOWER_GHOST_4_SOUL_ATTACK_3_DESCRIPTION"] = "Поверженные призраки бросаются на ближайшего врага, нанося %$towers.ghost.soul_attack.s_damage[3]%$ чистого урона, снижая его скорость и уменьшая его урон вдвое.",
["TOWER_GHOST_4_SOUL_ATTACK_3_NAME"] = "БЕССМЕРТНЫЙ УЖАС",
["TOWER_GHOST_4_SOUL_ATTACK_NOTE"] = "Ты уйдешь вместе с нами!",
["TOWER_GHOST_DESC"] = "Призраки, сражающиеся даже после смерти. Их сила позволяет им перемещаться среди теней и застигать врагов врасплох.",
["TOWER_GHOST_NAME"] = "Мрачные Призраки",
["TOWER_HERMIT_TOAD_1_DESCRIPTION"] = "Немного магии, немного грубой силы – это все что нужно, чтобы избавиться от надоедливых незваных гостей.",
["TOWER_HERMIT_TOAD_1_NAME"] = "Болотный Отшельник I",
["TOWER_HERMIT_TOAD_2_DESCRIPTION"] = "Немного магии, немного грубой силы – это все что нужно, чтобы избавиться от надоедливых незваных гостей.",
["TOWER_HERMIT_TOAD_2_NAME"] = "Болотный Отшельник II",
["TOWER_HERMIT_TOAD_3_DESCRIPTION"] = "Немного магии, немного грубой силы – это все что нужно, чтобы избавиться от надоедливых незваных гостей.",
["TOWER_HERMIT_TOAD_3_NAME"] = "Болотный Отшельник III",
["TOWER_HERMIT_TOAD_4_DESCRIPTION"] = "Немного магии, немного грубой силы – это все что нужно, чтобы избавиться от надоедливых незваных гостей.",
["TOWER_HERMIT_TOAD_4_INSTAKILL_1_DESCRIPTION"] = "Каждые %$towers.hermit_toad.power_instakill.cooldown[1]%$ секунд отшельник использует свой язык, чтобы поглотить врага.",
["TOWER_HERMIT_TOAD_4_INSTAKILL_1_NAME"] = "Липкий Язык",
["TOWER_HERMIT_TOAD_4_JUMP_1_DESCRIPTION"] = "Каждые %$towers.hermit_toad.power_jump.cooldown[1]%$ секунд отшельник прыгает высоко в небо, падая на врагов, наносит %$towers.hermit_toad.power_jump.damage_min[1]%$ урона и оглушает их на %$towers.hermit_toad.power_jump.stun_duration[1]%$ секунду при приземлении.",
["TOWER_HERMIT_TOAD_4_JUMP_1_NAME"] = "Землетряс",
["TOWER_HERMIT_TOAD_4_NAME"] = "Болотный Отшельник IV",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_1_DESCRIPTION"] = "Каждые %$towers.hermit_toad.power_instakill.cooldown[1]%$ секунд он использует свой язык, чтобы поглотить врага.",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_1_NAME"] = "Липкий Язык I",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_NOTE"] = "Липкое дело.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_1_DESCRIPTION"] = "Каждые %$towers.hermit_toad.power_jump.cooldown[1]%$ секунд отшельник прыгает высоко в небо, падая на врагов, наносит %$towers.hermit_toad.power_jump.damage_min[1]%$ урона и оглушает их на %$towers.hermit_toad.power_jump.stun_duration[1]%$ секунды при приземлении.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_1_NAME"] = "Землетряс I",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_2_DESCRIPTION"] = "Каждые %$towers.hermit_toad.power_jump.cooldown[2]%$ секунды отшельник прыгает высоко в небо, падая на врагов, наносит %$towers.hermit_toad.power_jump.damage_min[2]%$ урона и оглушает их на %$towers.hermit_toad.power_jump.stun_duration[2]%$ секунды при приземлении.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_2_NAME"] = "Землетряс II",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_3_DESCRIPTION"] = "Каждые %$towers.hermit_toad.power_jump.cooldown[3]%$ секунд отшельник прыгает высоко в небо, падая на врагов, наносит %$towers.hermit_toad.power_jump.damage_min[3]%$ урона и оглушает их на %$towers.hermit_toad.power_jump.stun_duration[3]%$ секунды при приземлении.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_3_NAME"] = "Землетряс III",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_NOTE"] = "Готов к болотному волейболу.",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_DESCRIPTION"] = "Отшельник переходит на физическую атаку.",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_NAME"] = "Грязное Болото",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_NOTE"] = "Пачкаемся!",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_DESCRIPTION"] = "Отшельник переходит на магическую атаку.",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_NAME"] = "Волшебный Пруд",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_NOTE"] = "Абсолютная Власть!!",
["TOWER_HERMIT_TOAD_DESC"] = "Огромный жабий маг, умеющий плеваться комками слизи. Всё, что он хочет, – это немного мира и тишины для своих ванн в пруду. НЕ БЕСПОКОЙТЕ ЕГО.",
["TOWER_HERMIT_TOAD_NAME"] = "Болотный Отшельник",
["TOWER_NECROMANCER_1_DESCRIPTION"] = "Используя свою власть над смертью, некроманты жнут хаос, засеянный ими на поле боя.",
["TOWER_NECROMANCER_1_NAME"] = "Некромант I",
["TOWER_NECROMANCER_2_DESCRIPTION"] = "Используя свою власть над смертью, некроманты жнут хаос, засеянный ими на поле боя.",
["TOWER_NECROMANCER_2_NAME"] = "Некромант II",
["TOWER_NECROMANCER_3_DESCRIPTION"] = "Используя свою власть над смертью, некроманты жнут хаос, засеянный ими на поле боя.",
["TOWER_NECROMANCER_3_NAME"] = "Некромант III",
["TOWER_NECROMANCER_4_DESCRIPTION"] = "Используя свою власть над смертью, некроманты жнут хаос, засеянный ими на поле боя.",
["TOWER_NECROMANCER_4_NAME"] = "Некромант IV",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_1_DESCRIPTION"] = "На %$towers.necromancer.skill_debuff.aura_duration[1]%$ секунд размещает тотем, который проклинает врагов и увеличивает урон скелетов на %$towers.necromancer.skill_debuff.s_damage_factor[1]%$%.",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_1_NAME"] = "Гремящий Столп",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_2_DESCRIPTION"] = "Тотем увеличивает урон скелетов на %$towers.necromancer.skill_debuff.s_damage_factor[2]%$%. Время перезарядки сокращается до %$towers.necromancer.skill_debuff.cooldown[2]%$ секунд.",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_2_NAME"] = "Гремящий Столп",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_3_DESCRIPTION"] = "Тотем увеличивает урон скелетов на %$towers.necromancer.skill_debuff.s_damage_factor[3]%$%. Время перезарядки сокращается до %$towers.necromancer.skill_debuff.cooldown[3]%$ секунд.",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_3_NAME"] = "Гремящий Столп",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_NOTE"] = "Армия костоправов!",
["TOWER_NECROMANCER_4_SKILL_RIDER_1_DESCRIPTION"] = "Отправляет по тропе Всадника Смерти, наносящего %$towers.necromancer.skill_rider.s_damage[1]%$ чистого урона любому встречному врагу.",
["TOWER_NECROMANCER_4_SKILL_RIDER_1_NAME"] = "ВСАДНИК СМЕРТИ",
["TOWER_NECROMANCER_4_SKILL_RIDER_2_DESCRIPTION"] = "Всадник Смерти наносит %$towers.necromancer.skill_rider.s_damage[2]%$ чистого урона.",
["TOWER_NECROMANCER_4_SKILL_RIDER_2_NAME"] = "ВСАДНИК СМЕРТИ",
["TOWER_NECROMANCER_4_SKILL_RIDER_3_DESCRIPTION"] = "Всадник Смерти наносит %$towers.necromancer.skill_rider.s_damage[3]%$ чистого урона.",
["TOWER_NECROMANCER_4_SKILL_RIDER_3_NAME"] = "ВСАДНИК СМЕРТИ",
["TOWER_NECROMANCER_4_SKILL_RIDER_NOTE"] = "Билет в один конец...",
["TOWER_NECROMANCER_DESC"] = "Владея самой темной формой магии, Некроманты превращают врагов в солдат своего бесконечного легиона.",
["TOWER_NECROMANCER_NAME"] = "Некромант",
["TOWER_PALADIN_COVENANT_1_DESCRIPTION"] = "Без страха и упрека, паладины неустанно оберегают Королевство от сил зла. ",
["TOWER_PALADIN_COVENANT_1_NAME"] = "Завет Паладинов I",
["TOWER_PALADIN_COVENANT_2_DESCRIPTION"] = "Без страха и упрека, паладины неустанно оберегают Королевство от сил зла. ",
["TOWER_PALADIN_COVENANT_2_NAME"] = "Завет Паладинов II",
["TOWER_PALADIN_COVENANT_3_DESCRIPTION"] = "Без страха и упрека, паладины неустанно оберегают Королевство от сил зла. ",
["TOWER_PALADIN_COVENANT_3_NAME"] = "Завет Паладинов III",
["TOWER_PALADIN_COVENANT_4_DESCRIPTION"] = "Без страха и упрека, паладины неустанно оберегают Королевство от сил зла. ",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_1_DESCRIPTION"] = "Когда здоровье солдат достигает %$towers.paladin_covenant.healing_prayer.health_trigger_factor[1]%$%, они становятся неуязвимыми и восстанавливают %$towers.paladin_covenant.healing_prayer.s_healing[1]%$ здоровья в секунду на протяжении %$towers.paladin_covenant.healing_prayer.duration%$ секунд.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_1_NAME"] = "ИСЦЕЛЯЮЩАЯ МОЛИТВА",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_2_DESCRIPTION"] = "Исцеление увеличивается до %$towers.paladin_covenant.healing_prayer.s_healing[2]%$ здоровья в секунду в течение %$towers.paladin_covenant.healing_prayer.duration%$ секунд.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_2_NAME"] = "ИСЦЕЛЯЮЩАЯ МОЛИТВА",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_3_DESCRIPTION"] = "Исцеление увеличивается до %$towers.paladin_covenant.healing_prayer.s_healing[3]%$ здоровья в секунду в течение %$towers.paladin_covenant.healing_prayer.duration%$ секунд.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_3_NAME"] = "ИСЦЕЛЯЮЩАЯ МОЛИТВА",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_NAME"] = "ИСЦЕЛЯЮЩАЯ МОЛИТВА",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_NOTE"] = "Лишь в смерти кончается долг.",
["TOWER_PALADIN_COVENANT_4_LEAD_1_DESCRIPTION"] = "Заменяет одного из паладинов Ветераном Гвардии, который увеличивает урон союзников поблизости на %$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$%.",
["TOWER_PALADIN_COVENANT_4_LEAD_1_NAME"] = "ПРИМЕР ДЛЯ ПОДРАЖАНИЯ",
["TOWER_PALADIN_COVENANT_4_LEAD_2_DESCRIPTION"] = "Заменяет одного из паладинов Ветераном Гвардии, который увеличивает урон союзников поблизости на %$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$%.",
["TOWER_PALADIN_COVENANT_4_LEAD_2_NAME"] = "ПРИМЕР ДЛЯ ПОДРАЖАНИЯ",
["TOWER_PALADIN_COVENANT_4_LEAD_3_DESCRIPTION"] = "Заменяет одного из паладинов Ветераном Гвардии, который увеличивает урон союзников поблизости на %$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$%.",
["TOWER_PALADIN_COVENANT_4_LEAD_3_NAME"] = "ПРИМЕР ДЛЯ ПОДРАЖАНИЯ",
["TOWER_PALADIN_COVENANT_4_LEAD_NAME"] = "ПРИМЕР ДЛЯ ПОДРАЖАНИЯ",
["TOWER_PALADIN_COVENANT_4_LEAD_NOTE"] = "За короля, за землю, за горы.",
["TOWER_PALADIN_COVENANT_4_NAME"] = "Завет Паладинов IV",
["TOWER_PALADIN_COVENANT_DESC"] = "Паладины – лицо Линерийской военной элиты. Они взывают к божественным силам для защиты и исцеления в пылу битвы. ",
["TOWER_PALADIN_COVENANT_NAME"] = "Завет Паладинов",
["TOWER_PANDAS_1_DESCRIPTION"] = "Вооружённые элементальной мощью и непоколебимой решимостью, Мастера будут неустанно сражаться за сохранение природного баланса в мире.",
["TOWER_PANDAS_1_NAME"] = "Мастера Бамбука I",
["TOWER_PANDAS_2_DESCRIPTION"] = "Вооружённые элементальной мощью и непоколебимой решимостью, Мастера будут неустанно сражаться за сохранение природного баланса в мире.",
["TOWER_PANDAS_2_NAME"] = "Мастера Бамбука II",
["TOWER_PANDAS_3_DESCRIPTION"] = "Вооружённые элементальной мощью и непоколебимой решимостью, Мастера будут неустанно сражаться за сохранение природного баланса в мире.",
["TOWER_PANDAS_3_NAME"] = "Мастера Бамбука III",
["TOWER_PANDAS_4_DESCRIPTION"] = "Вооружённые элементальной мощью и непоколебимой решимостью, Мастера будут неустанно сражаться за сохранение природного баланса в мире.",
["TOWER_PANDAS_4_FIERY"] = "Кавуш",
["TOWER_PANDAS_4_FIERY_1_DESCRIPTION"] = "Выпускает огненный заряд, наносящий %$towers.pandas.soldier.teleport.damage_min[1]%$-%$towers.pandas.soldier.teleport.damage_max[1]%$ истинного урона и телепортирующий поражённых врагов назад по маршруту.",
["TOWER_PANDAS_4_FIERY_1_NAME"] = "Пламя Пустоты",
["TOWER_PANDAS_4_FIERY_2_DESCRIPTION"] = "Выпускает огненный снаряд, наносящий %$towers.pandas.soldier.teleport.damage_min[2]%$-%$towers.pandas.soldier.teleport.damage_max[2]%$ чистого урона и телепортирующий поражённых врагов назад по пути.",
["TOWER_PANDAS_4_FIERY_2_NAME"] = "Пламя Пустоты",
["TOWER_PANDAS_4_HAT"] = "Одна шляпа, чтобы всех ударить",
["TOWER_PANDAS_4_HAT_1_DESCRIPTION"] = "Бросает острую шляпу во врага, рикошетит между целями, нанося по %$towers.pandas.soldier.hat.damage_levels[1].min%$-%$towers.pandas.soldier.hat.damage_levels[1].max%$ урона за попадание.",
["TOWER_PANDAS_4_HAT_1_NAME"] = "Фокус с шляпой",
["TOWER_PANDAS_4_HAT_2_DESCRIPTION"] = "Бросает острую шляпу во врага, рикошетит между целями, нанося по %$towers.pandas.soldier.hat.damage_levels[2].min%$-%$towers.pandas.soldier.hat.damage_levels[2].max%$ урона за попадание.",
["TOWER_PANDAS_4_HAT_2_NAME"] = "Фокус с шляпой",
["TOWER_PANDAS_4_NAME"] = "Мастера Бамбука IV",
["TOWER_PANDAS_4_THUNDER"] = "Панда Комбат",
["TOWER_PANDAS_4_THUNDER_1_DESCRIPTION"] = "Вызывает молнии на небольшую область, каждая из которых наносит %$towers.pandas.soldier.thunder.damage_min[1]%$-%$towers.pandas.soldier.thunder.damage_max[1]%$ урона по области и ненадолго оглушает поражённых врагов.",
["TOWER_PANDAS_4_THUNDER_1_NAME"] = "Молниеносная Перегрузка",
["TOWER_PANDAS_4_THUNDER_2_DESCRIPTION"] = "Вызывает молнии на небольшую область, каждая из которых наносит %$towers.pandas.soldier.thunder.damage_min[2]%$-%$towers.pandas.soldier.thunder.damage_max[2]%$ урона по области и ненадолго оглушает поражённых врагов.",
["TOWER_PANDAS_4_THUNDER_2_NAME"] = "Перегрузка молнией",
["TOWER_PANDAS_DESC"] = "Сочетая боевое мастерство с элементальной связью, эта троица панд разрывает врагов и остаётся угрозой, даже когда кажется поверженной.",
["TOWER_PANDAS_NAME"] = "Мастера Бамбука",
["TOWER_PANDAS_RETREAT_DESCRIPTION"] = "Отступите стоящих панд в убежище на 8 секунд.",
["TOWER_PANDAS_RETREAT_NAME"] = "Тактическое отступление",
["TOWER_PANDAS_RETREAT_NOTE"] = "Осторожность — лучшая часть доблести.",
["TOWER_RAY_1_DESCRIPTION"] = "Опасные, порочные формы магии никогда не останавливали злых магов от преследования своих гнусных целей.",
["TOWER_RAY_1_NAME"] = "Жуткий Чаротворец I",
["TOWER_RAY_2_DESCRIPTION"] = "Опасные, порочные формы магии никогда не останавливали злых магов от преследования своих гнусных целей.",
["TOWER_RAY_2_NAME"] = "Жуткий Чаротворец II",
["TOWER_RAY_3_DESCRIPTION"] = "Опасные, порочные формы магии никогда не останавливали злых магов от преследования своих гнусных целей.",
["TOWER_RAY_3_NAME"] = "Жуткий Чаротворец III",
["TOWER_RAY_4_CHAIN_1_DESCRIPTION"] = "Магический луч теперь распространяется на %$towers.ray.skill_chain.s_max_enemies%$ дополнительных врагов, замедляя их и нанося каждой цели %$towers.ray.skill_chain.damage_mult[1]%$% от общего магического урона.",
["TOWER_RAY_4_CHAIN_1_NAME"] = "ИЗБЫТОК ЭНЕРГИИ",
["TOWER_RAY_4_CHAIN_2_DESCRIPTION"] = "Магический луч теперь распространяется на %$towers.ray.skill_chain.s_max_enemies%$ дополнительных врагов, замедляя их и нанося каждой цели %$towers.ray.skill_chain.damage_mult[2]%$% от общего магического урона.",
["TOWER_RAY_4_CHAIN_2_NAME"] = "ИЗБЫТОК ЭНЕРГИИ",
["TOWER_RAY_4_CHAIN_3_DESCRIPTION"] = "Магический луч теперь распространяется на %$towers.ray.skill_chain.s_max_enemies%$ дополнительных врагов, замедляя их и нанося каждой цели %$towers.ray.skill_chain.damage_mult[3]%$% от общего магического урона.",
["TOWER_RAY_4_CHAIN_3_NAME"] = "ИЗБЫТОК ЭНЕРГИИ",
["TOWER_RAY_4_CHAIN_NOTE"] = "Боли хватит на всех.",
["TOWER_RAY_4_DESCRIPTION"] = "Опасные, порочные формы магии никогда не останавливали злых магов от преследования своих гнусных целей.",
["TOWER_RAY_4_NAME"] = "Жуткий Чаротворец IV",
["TOWER_RAY_4_SHEEP_1_DESCRIPTION"] = "Превращает ближайшего врага в беспомощную овцу. Овца имеет %$towers.ray.skill_sheep.sheep.hp_mult%$% здоровья цели.",
["TOWER_RAY_4_SHEEP_1_NAME"] = "МЕТАМОРФОЗА",
["TOWER_RAY_4_SHEEP_2_DESCRIPTION"] = "Превращает ближайшего врага в беспомощную овцу. Овца имеет %$towers.ray.skill_sheep.sheep.hp_mult%$% здоровья цели.",
["TOWER_RAY_4_SHEEP_2_NAME"] = "МЕТАМОРФОЗА",
["TOWER_RAY_4_SHEEP_3_DESCRIPTION"] = "Превращает ближайшего врага в беспомощную овцу. Овца имеет %$towers.ray.skill_sheep.sheep.hp_mult%$% здоровья цели.",
["TOWER_RAY_4_SHEEP_3_NAME"] = "МЕТАМОРФОЗА",
["TOWER_RAY_4_SHEEP_NOTE"] = "Если честно, так ты выглядишь лучше.",
["TOWER_RAY_DESC"] = "Ученики Вез'нана используют свою порочную силу, направляя на врагов темный луч смерти.",
["TOWER_RAY_NAME"] = "Жуткий Чаротворец",
["TOWER_ROCKET_GUNNERS_1_DESCRIPTION"] = "Вооруженные Армией Тьмы по последнему слову техники, Ракетные Стрелки патрулируют небеса.",
["TOWER_ROCKET_GUNNERS_1_NAME"] = "Ракетные Стрелки I",
["TOWER_ROCKET_GUNNERS_2_DESCRIPTION"] = "Вооруженные Армией Тьмы по последнему слову техники, Ракетные Стрелки патрулируют небеса.",
["TOWER_ROCKET_GUNNERS_2_NAME"] = "Ракетные Стрелки II",
["TOWER_ROCKET_GUNNERS_3_DESCRIPTION"] = "Вооруженные Армией Тьмы по последнему слову техники, Ракетные Стрелки патрулируют небеса.",
["TOWER_ROCKET_GUNNERS_3_NAME"] = "Ракетные Стрелки III",
["TOWER_ROCKET_GUNNERS_4_DESCRIPTION"] = "Вооруженные Армией Тьмы по последнему слову техники, Ракетные Стрелки патрулируют небеса.",
["TOWER_ROCKET_GUNNERS_4_NAME"] = "Ракетные Стрелки IV",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_1_DESCRIPTION"] = "Каждая атака уничтожает %$towers.rocket_gunners.soldier.phosphoric.armor_reduction[1]%$% брони врага и наносит урон по площади.",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_1_NAME"] = "ФОСФОРНОЕ ПОКРЫТИЕ",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_2_DESCRIPTION"] = "Каждая атака уничтожает %$towers.rocket_gunners.soldier.phosphoric.armor_reduction[2]%$% брони врага и наносит %$towers.rocket_gunners.soldier.phosphoric.damage_area_min[2]%$-%$towers.rocket_gunners.soldier.phosphoric.damage_area_max[2]%$ урона по площади.",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_2_NAME"] = "ФОСФОРНОЕ ПОКРЫТИЕ",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_3_DESCRIPTION"] = "Каждая атака уничтожает %$towers.rocket_gunners.soldier.phosphoric.armor_reduction[3]%$% брони врага и наносит %$towers.rocket_gunners.soldier.phosphoric.damage_area_min[3]%$-%$towers.rocket_gunners.soldier.phosphoric.damage_area_max[3]%$ урона по площади.",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_3_NAME"] = "ФОСФОРНОЕ ПОКРЫТИЕ",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_NOTE"] = "Пули, пропитанные злом.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_1_DESCRIPTION"] = "Выстреливает ракету, которая мгновенно убивает цель с максимальным здоровьем до %$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[1]%$ единиц.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_1_NAME"] = "РАКЕТЫ \"ЖАЛО\"",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_2_DESCRIPTION"] = "Сокращает время перезарядки до %$towers.rocket_gunners.sting_missiles.cooldown[2]%$ секунд. Теперь может целиться во врагов с здоровьем до %$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[2]%$ eдиниц.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_2_NAME"] = "РАКЕТЫ \"ЖАЛО\"",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_3_DESCRIPTION"] = "Сокращает время перезарядки до %$towers.rocket_gunners.sting_missiles.cooldown[3]%$ секунд. Теперь может целиться во врагов с здоровьем до %$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[3]%$ единиц.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_3_NAME"] = "РАКЕТЫ \"ЖАЛО\"",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_NOTE"] = "Попробуй увернись!",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_DESCRIPTION"] = "Ракетные стрелки взлетают и не могут блокировать врагов.",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_NAME"] = "На взлет!",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_NOTE"] = "Бесконечность не предел!",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_DESCRIPTION"] = "Ракетные Стрелки опускаются на землю и могут блокировать врагов.",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_NAME"] = "На посадку!",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_NOTE"] = "Орел сел!",
["TOWER_ROCKET_GUNNERS_DESC"] = "Эти специальные войска могут держать оборону как на земле, так и в воздухе, используя свое передовое оружие против ничего не подозревающих врагов.",
["TOWER_ROCKET_GUNNERS_NAME"] = "Ракетные Стрелки",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_1"] = "Эта башня включена в кампанию \"Колоссальная Угроза\"",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_2"] = "Эта башня включена в кампанию «Путешествие Вуконга».",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_1"] = "Кампания \"Колоссальная Угроза\"",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_2"] = "Кампания «Путешествие Укуна»",
["TOWER_ROOM_EQUIPPED_TOWERS_TITLE"] = "Выбранные башни",
["TOWER_ROOM_GET_DLC"] = "ПОЛУЧИТЕ ЭТО",
["TOWER_ROOM_LABEL_ROSTER_THUMB_NEW"] = "Новинка!",
["TOWER_ROOM_SKILLS_TITLE"] = "Навыки",
["TOWER_ROYAL_ARCHERS_1_DESCRIPTION"] = "Верные Линерии до конца, Королевские Лучники защищают ее войска издалека. ",
["TOWER_ROYAL_ARCHERS_1_NAME"] = "Королевские Лучники I ",
["TOWER_ROYAL_ARCHERS_2_DESCRIPTION"] = "Верные Линерии до конца, Королевские Лучники защищают ее войска издалека. ",
["TOWER_ROYAL_ARCHERS_2_NAME"] = "Королевские Лучники II ",
["TOWER_ROYAL_ARCHERS_3_DESCRIPTION"] = "Верные Линерии до конца, Королевские Лучники защищают ее войска издалека. ",
["TOWER_ROYAL_ARCHERS_3_NAME"] = "Королевские Лучники III ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_1_DESCRIPTION"] = "Выпускает три мощные стрелы, игнорирующие %$towers.royal_archers.armor_piercer.armor_penetration[1]%$% брони врага и наносящие %$towers.royal_archers.armor_piercer.damage_min[1]%$-%$towers.royal_archers.armor_piercer.damage_max[1]%$ физического урона каждая.",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_1_NAME"] = "БРОНЕБОЙЩИК ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_2_DESCRIPTION"] = "Выпускает три мощные стрелы, игнорирующие %$towers.royal_archers.armor_piercer.armor_penetration[2]%$% брони врага и наносящие %$towers.royal_archers.armor_piercer.damage_min[2]%$-%$towers.royal_archers.armor_piercer.damage_max[2]%$ физического урона каждая.",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_2_NAME"] = "БРОНЕБОЙЩИК ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_3_DESCRIPTION"] = "Выпускает три мощные стрелы, игнорирующие %$towers.royal_archers.armor_piercer.armor_penetration[3]%$% брони врага и наносящие %$towers.royal_archers.armor_piercer.damage_min[3]%$-%$towers.royal_archers.armor_piercer.damage_max[3]%$ физического урона каждая.",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_3_NAME"] = "БРОНЕБОЙЩИК ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_NAME"] = "БРОНЕБОЙЩИК ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_NOTE"] = "Ты у нас на мушке.",
["TOWER_ROYAL_ARCHERS_4_DESCRIPTION"] = "Верные Линерии до конца, Королевские Лучники защищают ее войска издалека. ",
["TOWER_ROYAL_ARCHERS_4_NAME"] = "Королевские Лучники IV ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_1_DESCRIPTION"] = "Вызывает орла, который атакует врагов на пути, нанося %$towers.royal_archers.rapacious_hunter.damage_min[1]%$-%$towers.royal_archers.rapacious_hunter.damage_max[1]%$ физического урона.",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_1_NAME"] = "ОРЛИНАЯ ОХОТА",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_2_DESCRIPTION"] = "Орел наносит %$towers.royal_archers.rapacious_hunter.damage_min[2]%$-%$towers.royal_archers.rapacious_hunter.damage_max[2]%$ физического урона. ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_2_NAME"] = "ОРЛИНАЯ ОХОТА",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_3_DESCRIPTION"] = "Орел наносит %$towers.royal_archers.rapacious_hunter.damage_min[3]%$-%$towers.royal_archers.rapacious_hunter.damage_max[3]%$ физического урона. ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_3_NAME"] = "ОРЛИНАЯ ОХОТА",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_NAME"] = "ОРЛИНАЯ ОХОТА",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_NOTE"] = "Орлиный глаз печальное скрывает.",
["TOWER_ROYAL_ARCHERS_DESC"] = "Лучшие стрелки королевства, сумевшие заручиться поддержкой боевых орлов.",
["TOWER_ROYAL_ARCHERS_NAME"] = "Королевские Лучники ",
["TOWER_SAND_1_DESCRIPTION"] = "Их мастерства метания лезвий достаточно, чтобы напугать любого слишком самоуверенного наемника.",
["TOWER_SAND_1_NAME"] = "Стражи Дюн I",
["TOWER_SAND_2_DESCRIPTION"] = "Их мастерства метания лезвий достаточно, чтобы напугать любого слишком самоуверенного наемника.",
["TOWER_SAND_2_NAME"] = "Стражи Дюн II",
["TOWER_SAND_3_DESCRIPTION"] = "Их мастерства метания лезвий достаточно, чтобы напугать любого слишком самоуверенного наемника.",
["TOWER_SAND_3_NAME"] = "Стражи Дюн III",
["TOWER_SAND_4_DESCRIPTION"] = "Их мастерства метания лезвий достаточно, чтобы напугать любого слишком самоуверенного наемника.",
["TOWER_SAND_4_NAME"] = "Стражи Дюн IV",
["TOWER_SAND_4_SKILL_BIG_BLADE_1_DESCRIPTION"] = "Бросает на тропу вращающиеся лезвия, наносящие %$towers.sand.skill_big_blade.s_damage_min[1]%$-%$towers.sand.skill_big_blade.s_damage_max[1]%$ физического урона в секунду в течение %$towers.sand.skill_big_blade.duration[1]%$ секунд.",
["TOWER_SAND_4_SKILL_BIG_BLADE_1_NAME"] = "КРУЖАЩИЙСЯ РОК",
["TOWER_SAND_4_SKILL_BIG_BLADE_2_DESCRIPTION"] = "Вращающиеся лезвия наносят %$towers.sand.skill_big_blade.s_damage_min[2]%$-%$towers.sand.skill_big_blade.s_damage_max[2]%$ физического урона в секунду в течение %$towers.sand.skill_big_blade.duration[2]%$ секунд.",
["TOWER_SAND_4_SKILL_BIG_BLADE_2_NAME"] = "КРУЖАЩИЙСЯ РОК",
["TOWER_SAND_4_SKILL_BIG_BLADE_3_DESCRIPTION"] = "Вращающиеся лезвия наносят %$towers.sand.skill_big_blade.s_damage_min[3]%$-%$towers.sand.skill_big_blade.s_damage_max[3]%$ физического урона в секунду в течение %$towers.sand.skill_big_blade.duration[3]%$ секунд.",
["TOWER_SAND_4_SKILL_BIG_BLADE_3_NAME"] = "КРУЖАЩИЙСЯ РОК",
["TOWER_SAND_4_SKILL_BIG_BLADE_NOTE"] = "You spin me round, round, baby.",
["TOWER_SAND_4_SKILL_GOLD_1_DESCRIPTION"] = "Бросает отскакивающее лезвие, наносящее %$towers.sand.skill_gold.s_damage[1]%$ физического урона каждому врагу. Любая убитая им цель приносит %$towers.sand.skill_gold.gold_extra[1]%$ бонусного золота. ",
["TOWER_SAND_4_SKILL_GOLD_1_NAME"] = "ОХОТА ЗА ГОЛОВАМИ",
["TOWER_SAND_4_SKILL_GOLD_2_DESCRIPTION"] = "Лезвие наносит %$towers.sand.skill_gold.s_damage[2]%$ физического урона. Убийство приносит %$towers.sand.skill_gold.gold_extra[2]%$ дополнительного золота. ",
["TOWER_SAND_4_SKILL_GOLD_2_NAME"] = "ОХОТА ЗА ГОЛОВАМИ",
["TOWER_SAND_4_SKILL_GOLD_3_DESCRIPTION"] = "Лезвие наносит %$towers.sand.skill_gold.s_damage[3]%$ физического урона. Убийство приносит %$towers.sand.skill_gold.gold_extra[3]%$ дополнительного золота.",
["TOWER_SAND_4_SKILL_GOLD_3_NAME"] = "ОХОТА ЗА ГОЛОВАМИ",
["TOWER_SAND_4_SKILL_GOLD_NOTE"] = "На листовке написано: живым ИЛИ мертвым.",
["TOWER_SAND_DESC"] = "Родом из Хаммерхолда, Стражи Дюн – быть может, самые смертоносные обитатели пустыни.",
["TOWER_SAND_NAME"] = "Стражи Дюн",
["TOWER_SELL"] = "Продать башню",
["TOWER_SPARKING_GEODE_1_DESCRIPTION"] = "Призыватель бурь и сертифицированный посланник хаоса. Следите за его энергопотреблением.",
["TOWER_SPARKING_GEODE_1_NAME"] = "Грозовой Колосс I",
["TOWER_SPARKING_GEODE_2_DESCRIPTION"] = "Призыватель бурь и сертифицированный посланник хаоса. Следите за его энергопотреблением.",
["TOWER_SPARKING_GEODE_2_NAME"] = "Грозовой Колосс II",
["TOWER_SPARKING_GEODE_3_DESCRIPTION"] = "Призыватель бурь и сертифицированный посланник хаоса. Следите за его энергопотреблением.",
["TOWER_SPARKING_GEODE_3_NAME"] = "Грозовой Колосс III",
["TOWER_SPARKING_GEODE_4_CRISTALIZE"] = "Удар грома!",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_1_DESCRIPTION"] = "Каждые %$towers.sparking_geode.crystalize.cooldown[1]%$ секунды кристаллизует до %$towers.sparking_geode.crystalize.max_targets[1]%$ врагов в радиусе действия, оглушая их и заставляя получать на %$towers.sparking_geode.crystalize.s_received_damage_factor[1]%$% больше урона.",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_1_NAME"] = "Кристаллизация",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_2_DESCRIPTION"] = "Каждые %$towers.sparking_geode.crystalize.cooldown[2]%$ секунды кристаллизует до %$towers.sparking_geode.crystalize.max_targets[2]%$ врагов в радиусе действия, оглушая их и заставляя получать на %$towers.sparking_geode.crystalize.s_received_damage_factor[2]%$% больше урона.",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_2_NAME"] = "Кристаллизация",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_3_DESCRIPTION"] = "Каждые %$towers.sparking_geode.crystalize.cooldown[3]%$ секунд кристаллизует до %$towers.sparking_geode.crystalize.max_targets[3]%$ врагов в радиусе действия, оглушая их и заставляя получать на %$towers.sparking_geode.crystalize.s_received_damage_factor[3]%$% больше урона.",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_3_NAME"] = "Кристаллизация",
["TOWER_SPARKING_GEODE_4_CRYSTALIZE_1_DESCRIPTION"] = "Каждые %$towers.sparking_geode.crystalize.cooldown[1]%$ секунды кристаллизует до %$towers.sparking_geode.crystalize.max_targets[1]%$ врагов в радиусе действия, оглушая их и заставляя получать на %$towers.sparking_geode.crystalize.s_received_damage_factor[1]%$% больше урона.",
["TOWER_SPARKING_GEODE_4_CRYSTALIZE_1_NAME"] = "Кристаллизация",
["TOWER_SPARKING_GEODE_4_DESCRIPTION"] = "Призыватель бурь и сертифицированный посланник хаоса. Следите за его энергопотреблением.",
["TOWER_SPARKING_GEODE_4_NAME"] = "Грозовой Колосс IV",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST"] = "Крепче, лучше, быстрее, сильнее.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_1_DESCRIPTION"] = "Каждые %$towers.sparking_geode.spike_burst.cooldown[1]%$ секунд Колосс создает электрическое поле, наносящее урон врагам и замедляющее их на %$towers.sparking_geode.spike_burst.duration[1]%$ секунд.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_1_NAME"] = "Электрический Разряд",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_2_DESCRIPTION"] = "Каждые %$towers.sparking_geode.spike_burst.cooldown[2]%$ секунды Колосс создает электрическое поле, наносящее урон врагам и замедляющее их на %$towers.sparking_geode.spike_burst.duration[2]%$ секунд.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_2_NAME"] = "Электрический Разряд",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_3_DESCRIPTION"] = "Каждые %$towers.sparking_geode.spike_burst.cooldown[3]%$ секунд Колосс создает электрическое поле, наносящее урон врагам и замедляющее их на %$towers.sparking_geode.spike_burst.duration[3]%$ секунд.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_3_NAME"] = "Электрический Разряд",
["TOWER_SPARKING_GEODE_DESC"] = "Это могущественное существо, происходящее из древней миролюбивой расы, следует своему защитному инстинкту и использует силу молний, чтобы сражаться за Альянс, поражая врагов с яростью бури.",
["TOWER_SPARKING_GEODE_NAME"] = "Грозовой Колосс",
["TOWER_STAGE_13_SUNRAY_NAME"] = "Темный Луч",
["TOWER_STAGE_13_SUNRAY_REPAIR_DESCRIPTION"] = "Отремонтируйте башню, чтобы использовать ее разрушительные силы.",
["TOWER_STAGE_13_SUNRAY_REPAIR_NAME"] = "Отремонтировать",
["TOWER_STAGE_17_WEIRDWOOD_NAME"] = "Стоерос",
["TOWER_STAGE_18_ELVEN_BARRACK_DESCRIPTION"] = "Эльфы, нанятые чтобы биться до конца.",
["TOWER_STAGE_18_ELVEN_BARRACK_NAME"] = "Эльфийские Наемники",
["TOWER_STAGE_20_ARBOREAN_BARRACK_DESCRIPTION"] = "Призовите народ древесников на подмогу.",
["TOWER_STAGE_20_ARBOREAN_BARRACK_NAME"] = "Древесники-горожане",
["TOWER_STAGE_20_ARBOREAN_HONEY_DESCRIPTION"] = "Призывает могучего предводителя пчел. ",
["TOWER_STAGE_20_ARBOREAN_HONEY_NAME"] = "Древесник-пчеловод",
["TOWER_STAGE_20_ARBOREAN_OLDTREE_DESCRIPTION"] = "Попросите мудрое дерево о помощи.",
["TOWER_STAGE_20_ARBOREAN_OLDTREE_NAME"] = "Старый Дуб",
["TOWER_STAGE_22_ARBOREAN_MAGES_NAME"] = "Древесник Маг",
["TOWER_STAGE_28_PRIESTS_BARRACK_DESCRIPTION"] = "Искупленные культисты, превращающиеся в Отродья при смерти.",
["TOWER_STAGE_28_PRIESTS_BARRACK_NAME"] = "Последователи Безглазого",
["TOWER_STARGAZER_1_DESCRIPTION"] = "Астрологи используют могущественную магию из мира, что лежит за пределами земного царства.",
["TOWER_STARGAZER_1_NAME"] = "Эльфийский Астролог I",
["TOWER_STARGAZER_2_DESCRIPTION"] = "Астрологи используют могущественную магию из мира, что лежит за пределами земного царства.",
["TOWER_STARGAZER_2_NAME"] = "Эльфийский Астролог II",
["TOWER_STARGAZER_3_DESCRIPTION"] = "Астрологи используют могущественную магию из мира, что лежит за пределами земного царства.",
["TOWER_STARGAZER_3_NAME"] = "Эльфийский Астролог III",
["TOWER_STARGAZER_4_DESCRIPTION"] = "Астрологи используют могущественную магию из мира, что лежит за пределами земного царства.",
["TOWER_STARGAZER_4_EVENT_HORIZON_1_DESCRIPTION"] = "Телепортирует назад группу до %$towers.elven_stargazers.teleport.max_targets[1]%$ врагов.",
["TOWER_STARGAZER_4_EVENT_HORIZON_1_NAME"] = "ГОРИЗОНТ СОБЫТИЙ",
["TOWER_STARGAZER_4_EVENT_HORIZON_2_DESCRIPTION"] = "Телепортирует на большее расстояние назад группу до %$towers.elven_stargazers.teleport.max_targets[2]%$ врагов.",
["TOWER_STARGAZER_4_EVENT_HORIZON_2_NAME"] = "ГОРИЗОНТ СОБЫТИЙ",
["TOWER_STARGAZER_4_EVENT_HORIZON_3_DESCRIPTION"] = "Телепортирует на еще большее расстояние назад группу до %$towers.elven_stargazers.teleport.max_targets[3]%$ врагов.",
["TOWER_STARGAZER_4_EVENT_HORIZON_3_NAME"] = "ГОРИЗОНТ СОБЫТИЙ",
["TOWER_STARGAZER_4_EVENT_HORIZON_NAME"] = "ГОРИЗОНТ СОБЫТИЙ",
["TOWER_STARGAZER_4_EVENT_HORIZON_NOTE"] = "Вошли и вышли.",
["TOWER_STARGAZER_4_NAME"] = "Эльфийский Астролог IV",
["TOWER_STARGAZER_4_RISING_STAR_1_DESCRIPTION"] = "Враги, убитые башней, взрываются вспышкой из %$towers.elven_stargazers.stars_death.stars[1]%$ звезд, наносящих %$towers.elven_stargazers.stars_death.damage_min[1]%$-%$towers.elven_stargazers.stars_death.damage_max[1]%$ магического урона врагам.",
["TOWER_STARGAZER_4_RISING_STAR_1_NAME"] = "ВОСХОДЯЩАЯ ЗВЕЗДА",
["TOWER_STARGAZER_4_RISING_STAR_2_DESCRIPTION"] = "Количество звезд увеличивается до %$towers.elven_stargazers.stars_death.stars[2]%$. Каждая звезда наносит %$towers.elven_stargazers.stars_death.damage_min[2]%$-%$towers.elven_stargazers.stars_death.damage_max[2]%$ магического урона.",
["TOWER_STARGAZER_4_RISING_STAR_2_NAME"] = "ВОСХОДЯЩАЯ ЗВЕЗДА",
["TOWER_STARGAZER_4_RISING_STAR_3_DESCRIPTION"] = "Количество звезд увеличивается до %$towers.elven_stargazers.stars_death.stars[3]%$. Каждая звезда наносит %$towers.elven_stargazers.stars_death.damage_min[3]%$-%$towers.elven_stargazers.stars_death.damage_max[3]%$ магического урона.",
["TOWER_STARGAZER_4_RISING_STAR_3_NAME"] = "ВОСХОДЯЩАЯ ЗВЕЗДА",
["TOWER_STARGAZER_4_RISING_STAR_NAME"] = "ВОСХОДЯЩАЯ ЗВЕЗДА",
["TOWER_STARGAZER_4_RISING_STAR_NOTE"] = "Это звездная революция!",
["TOWER_TRICANNON_1_DESCRIPTION"] = "Разрушительная ода войне и устрашающее зрелище как для врагов, так и для союзников.",
["TOWER_TRICANNON_1_NAME"] = "Трехпушка I",
["TOWER_TRICANNON_2_DESCRIPTION"] = "Разрушительная ода войне и устрашающее зрелище как для врагов, так и для союзников.",
["TOWER_TRICANNON_2_NAME"] = "Трехпушка II",
["TOWER_TRICANNON_3_DESCRIPTION"] = "Разрушительная ода войне и устрашающее зрелище как для врагов, так и для союзников.",
["TOWER_TRICANNON_3_NAME"] = "Трехпушка III",
["TOWER_TRICANNON_4_BOMBARDMENT_1_DESCRIPTION"] = "Забрасывает бомбами обширную область, каждая бомба наносит %$towers.tricannon.bombardment.damage_min[1]%$-%$towers.tricannon.bombardment.damage_max[1]%$ физического урона.",
["TOWER_TRICANNON_4_BOMBARDMENT_1_NAME"] = "БОМБАРДИРОВКА",
["TOWER_TRICANNON_4_BOMBARDMENT_2_DESCRIPTION"] = "Выпускает больше бомб по более широкой области, каждая наносит %$towers.tricannon.bombardment.damage_min[2]%$-%$towers.tricannon.bombardment.damage_max[2]%$ физического урона.",
["TOWER_TRICANNON_4_BOMBARDMENT_2_NAME"] = "БОМБАРДИРОВКА",
["TOWER_TRICANNON_4_BOMBARDMENT_3_DESCRIPTION"] = "Выпускает еще больше бомб по еще более широкой области, каждая наносит %$towers.tricannon.bombardment.damage_min[3]%$-%$towers.tricannon.bombardment.damage_max[3]%$ физического урона.",
["TOWER_TRICANNON_4_BOMBARDMENT_3_NAME"] = "БОМБАРДИРОВКА",
["TOWER_TRICANNON_4_BOMBARDMENT_NAME"] = "БОМБАРДИРОВКА",
["TOWER_TRICANNON_4_BOMBARDMENT_NOTE"] = "Поговорим о масштабах.",
["TOWER_TRICANNON_4_DESCRIPTION"] = "Разрушительная ода войне и устрашающее зрелище как для врагов, так и для союзников.",
["TOWER_TRICANNON_4_NAME"] = "Трехпушка IV",
["TOWER_TRICANNON_4_OVERHEAT_1_DESCRIPTION"] = "На %$towers.tricannon.overheat.duration[1]%$ секунд стволы Трехпушки раскаляются, заставляя бомбы поджигать землю. Горение наносит врагам %$towers.tricannon.overheat.decal.effect.s_damage[1]%$ чистого урона в секунду.",
["TOWER_TRICANNON_4_OVERHEAT_1_NAME"] = "ПЕРЕГРЕВ",
["TOWER_TRICANNON_4_OVERHEAT_2_DESCRIPTION"] = "Каждая горящая область наносит %$towers.tricannon.overheat.decal.effect.s_damage[2]%$ чистого урона в секунду. Время действия перегрева увеличено до %$towers.tricannon.overheat.duration[2]%$ секунд.",
["TOWER_TRICANNON_4_OVERHEAT_2_NAME"] = "ПЕРЕГРЕВ",
["TOWER_TRICANNON_4_OVERHEAT_3_DESCRIPTION"] = "Каждая горящая область наносит %$towers.tricannon.overheat.decal.effect.s_damage[3]%$ чистого урона в секунду. Время действия перегрева увеличено до %$towers.tricannon.overheat.duration[3]%$ секунд.",
["TOWER_TRICANNON_4_OVERHEAT_3_NAME"] = "ПЕРЕГРЕВ",
["TOWER_TRICANNON_4_OVERHEAT_NAME"] = "ПЕРЕГРЕВ",
["TOWER_TRICANNON_4_OVERHEAT_NOTE"] = "До белого каления.",
["TOWER_TRICANNON_DESC"] = "Армия Тьмы предлагает новейшее определение современной войны, несущее огонь и разрушение из множества орудий.",
["TOWER_TRICANNON_NAME"] = "Трехпушка",
["TUTORIAL_hero_room_hero_points_desc"] = "Зарабатывайте Очки Героя, повышая уровень каждого героя в бою.",
["TUTORIAL_hero_room_hero_points_title"] = "Очки Героя",
["TUTORIAL_hero_room_power_desc"] = "Используйте Очки Героя для открытия и улучшения навыков ваших героев.",
["TUTORIAL_hero_room_power_title"] = "Навыки Героя",
["TUTORIAL_hero_room_tutorial_navigate_desc"] = "Изучайте различных героев.",
["TUTORIAL_hero_room_tutorial_select_desc"] = "Выбирайте героев, которых вы хотите использовать в бою.",
["TUTORIAL_item_room_buy_desc"] = "Используйте ваши Кристаллы для покупки предметов, которые помогут вам на поле боя.",
["TUTORIAL_item_room_buy_title"] = "Покупка Предметов",
["TUTORIAL_item_room_tutorial_equip_desc"] = "Используйте каждый слот для экипировки ваших предметов. Перетащите, чтобы изменить их порядок!",
["TUTORIAL_item_room_tutorial_navigate_desc"] = "Изучайте различные доступные предметы.",
["TUTORIAL_tower_room_power_desc"] = "Эти навыки становятся доступны, как только башня достигает уровня IV.",
["TUTORIAL_tower_room_power_title"] = "Навыки уровня IV",
["TUTORIAL_tower_room_tutorial_equip_desc"] = "Выбирайте новые башни, чтобы пробовать различные комбинации.",
["TUTORIAL_tower_room_tutorial_navigate_desc"] = "Изучайте различные башни.",
["TUTORIAL_tower_room_tutorial_slots_desc"] = "Используйте каждую ячейку для выбора башен. Перетащите, чтобы изменить их порядок!",
["TUTORIAL_upgrade_room_tooltip_buy_desc"] = "Используйте Очки для покупки улучшений для ваших башен, героев и способностей.",
["TUTORIAL_upgrade_room_tooltip_souls_desc"] = "Зарабатывайте Очки Улучшений, проходя этапы кампании.",
["TUTORIAL_upgrade_room_tooltip_souls_title"] = "Очки Улучшений",
["Tap to continue"] = "Нажмите, чтобы продолжить",
["Touch on the path to move the hero."] = "Чтобы переместить героя, коснитесь тропы.",
["Tower construction"] = "Строительство башни",
["Typography"] = "Типография",
["UPDATE_POPUP"] = "ОБНОВИТЬ",
["UPDATING_CLOUDSAVE_MESSAGE"] = "Обновление облачного сохранения ...",
["UPGRADES"] = "УЛУЧШЕНИЯ",
["UPGRADES AND HEROES RESTRICTIONS!"] = "ОГРАНИЧЕНИЯ НА УЛУЧШЕНИЯ И ГЕРОЕВ!",
["Use the earned hero points to train your hero!"] = "Используйте полученные очки героя для его тренировки!",
["Use the earned stars to improve your towers and powers!"] = "Используйте заработанные звезды для улучшения башен и навыков!",
["VICTORY"] = "ПОБЕДА",
["Veteran"] = "Ветеран",
["Victory!"] = "Победа!",
["Voice Talent"] = "Озвучивание",
["WAVE_TOOLTIP_TAP_AGAIN"] = "НАЖМИТЕ СНОВА ДЛЯ ПРИЗЫВА РАНЬШЕ СРОКА",
["WAVE_TOOLTIP_TITLE"] = "НОВАЯ ВОЛНА",
["We would like to thank"] = "Особая благодарность",
["Yes"] = "Да",
["You must log in to Google Play game services to track achievements."] = "Вы должны войти в сервисы Google Play для отслеживания достижений.",
["You must watch the whole video."] = "Посмотрите видео полностью",
["You will no longer be tracking achievements."] = "В этом случае достижения не будут засчитываться.",
["_manually_included_characters"] = "$ ¥ ￥ ƒ ₩ € ™ × $ zł ¢ £ ¤ ¥ ƒ ден дин лв. ؋ ৳ ฿ ლ ₡ ₣ ₤ ₥ ₦ ₨ ₩ ₪ ₫ € ₭ ₮ ₱ ₲ ₴ ₵ ₹ ₺ ₽ ﷼",
["alliance_close_to_home_DESCRIPTION"] = "Дает дополнительное золото в начале этапа.",
["alliance_close_to_home_NAME"] = "СОВМЕСТНЫЕ РЕЗЕРВЫ",
["alliance_corageous_stand_DESCRIPTION"] = "Каждая построенная башня ЛИНЕРИИ увеличивает здоровье Героев.",
["alliance_corageous_stand_NAME"] = "СТОЙКА МУЖЕСТВА",
["alliance_display_of_true_might_dark_DESCRIPTION"] = "Заклинания героев Армии Тьмы теперь также замедляют всех врагов на экране.",
["alliance_display_of_true_might_dark_NAME"] = "ЖУТКОЕ ПРОКЛЯТИЕ",
["alliance_display_of_true_might_linirea_DESCRIPTION"] = "Заклинания героев Линерии теперь также исцеляют и воскрешают всех союзных солдат.",
["alliance_display_of_true_might_linirea_NAME"] = "БЛАГОСЛОВЛЕНИЕ ЖИЗНИ",
["alliance_flux_altering_coils_DESCRIPTION"] = "Заменяет флаги при каждом выходе волшебными колоннами, которые телепортируют ближайших врагов назад.",
["alliance_flux_altering_coils_NAME"] = "ВОЛШЕБНЫЕ КОЛОННЫ",
["alliance_friends_of_the_crown_DESCRIPTION"] = "Каждый выбранный герой ЛИНЕРИИ уменьшает стоимость строительства и улучшения башен.",
["alliance_friends_of_the_crown_NAME"] = "ДРУЗЬЯ КОРОНЫ",
["alliance_merciless_DESCRIPTION"] = "Каждая построенная башня АРМИИ ТЬМЫ увеличивает урон Героев.",
["alliance_merciless_NAME"] = "НЕСОКРУШИМАЯ ОБОРОНА",
["alliance_seal_of_punishment_DESCRIPTION"] = "Заменяет точку защиты рядом с каждым выходом магической печатью, наносящей урон врагам, проходящим через нее.",
["alliance_seal_of_punishment_NAME"] = "ПЕЧАТЬ РАСПЛАТЫ",
["alliance_shady_company_DESCRIPTION"] = "Каждый выбранный герой АРМИИ ТЬМЫ увеличивает урон башен.",
["alliance_shady_company_NAME"] = "ОПАСНАЯ КОМПАНИЯ",
["alliance_shared_reserves_DESCRIPTION"] = "Дает дополнительное золото в начале этапа.",
["alliance_shared_reserves_NAME"] = "СОВМЕСТНЫЕ РЕЗЕРВЫ",
["baloon start battle iphone"] = "Нажмите дважды, чтобы вызвать волну",
["build defensive towers along the road to stop them."] = "Стройте оборонительные башни вдоль дороги, чтобы остановить их.",
["build towers to defend the road."] = "Постройте башни для защиты дороги.",
["check the stage description to see:"] = "Прочитайте описание этапа для ознакомления.",
["deals area damage"] = "Наносит урон по площади",
["don't let enemies past this point."] = "Не дайте врагам пройти за эту точку.",
["earn gold by killing enemies."] = "Зарабатывайте золото, убивая врагов.",
["good rate of fire"] = "Хорошая скорострельность",
["heroes_desperate_effort_DESCRIPTION"] = "Атаки героев игнорируют 10% сопротивления врагов.",
["heroes_desperate_effort_NAME"] = "ЗНАЙ СВОЕГО ВРАГА",
["heroes_lethal_focus_DESCRIPTION"] = "Атаки героев имеют 20% шанс нанести критический урон.",
["heroes_lethal_focus_NAME"] = "УБИЙСТВЕННАЯ ТОЧНОСТЬ",
["heroes_limit_pushing_DESCRIPTION"] = "Заклинания героев мгновенно перезаряжаются после каждого пятого использования.",
["heroes_limit_pushing_NAME"] = "НА ПРЕДЕЛЕ",
["heroes_lone_wolves_DESCRIPTION"] = "Герои получают больше опыта, когда находятся далеко друг от друга.",
["heroes_lone_wolves_NAME"] = "ОДИНОКИЕ ВОЛКИ",
["heroes_nimble_physique_DESCRIPTION"] = "Герои имеют 20% шанс уклониться от вражеской атаки.",
["heroes_nimble_physique_NAME"] = "ПРОВОРНОЕ ТЕЛОСЛОЖЕНИЕ",
["heroes_unlimited_vigor_DESCRIPTION"] = "Уменьшает время перезарядки всех заклинаний героев.",
["heroes_unlimited_vigor_NAME"] = "НЕСКОНЧАЕМАЯ ЭНЕРГИЯ",
["heroes_visual_learning_DESCRIPTION"] = "Герои имеют на 10% больше брони, когда находятся рядом друг с другом. ",
["heroes_visual_learning_NAME"] = "РУКА ПОМОЩИ",
["high damage, armor piercing"] = "Высокий урон, пробивает броню",
["iron and heroic challenges may have restrictions on upgrades!"] = "В железных и героических испытаниях могут быть ограничения на улучшения!",
["max lvl allowed"] = "макс. уровень",
["multi-shot, armor piercing"] = "Залп, пробивает броню",
["no heroes"] = "без героев",
["pause popup"] = "ПАУЗА",
["protect your lands from the enemy attacks."] = "Защищайте свои земли от вражеских атак.",
["rally range"] = "Радиус сбора",
["ready for action!"] = "Готов к бою!",
["reinforcements_intense_workout_DESCRIPTION"] = "Увеличивает здоровье и продолжительность действия подкреплений.",
["reinforcements_intense_workout_NAME"] = "ИНТЕНСИВНАЯ ТРЕНИРОВКА",
["reinforcements_master_blacksmiths_DESCRIPTION"] = "Увеличивает урон и броню подкреплений.",
["reinforcements_master_blacksmiths_NAME"] = "МАСТЕРА-КУЗНЕЦЫ",
["reinforcements_night_veil_DESCRIPTION"] = "Увеличивает скорость и дальность атак Лучников Теней.",
["reinforcements_night_veil_NAME"] = "ЯСЕНЕВЫЕ ЛУКИ",
["reinforcements_power_trio_DESCRIPTION"] = "Вызов подкреплений теперь также призывает Рыцаря-Парагона.",
["reinforcements_power_trio_NAME"] = "ЛИНЕРИЙСКИЙ ПАРАГОН",
["reinforcements_power_trio_dark_DESCRIPTION"] = "Вызов подкреплений также призывает Темного Вороновода.",
["reinforcements_power_trio_dark_NAME"] = "ТЕМНЫЙ ВОРОНОВОД",
["reinforcements_rebel_militia_DESCRIPTION"] = "Подкрепления заменяются Линерийскими Повстанцами, выносливыми бойцами в тяжелых доспехах.",
["reinforcements_rebel_militia_NAME"] = "ЛИНЕРИЙСКОЕ ОПОЛЧЕНИЕ",
["reinforcements_shadow_archer_DESCRIPTION"] = "Подкрепления заменяются Лучниками Теней, которые атакуют издалека и могут поражать летающих врагов.",
["reinforcements_shadow_archer_NAME"] = "ОРДЕН ТЕНЕЙ",
["reinforcements_thorny_armor_DESCRIPTION"] = "Линерийские Повстанцы отражают часть урона от вражеских атак в ближнем бою.",
["reinforcements_thorny_armor_NAME"] = "ШИПАСТЫЕ ДОСПЕХИ",
["resists damage from"] = "Сопротивление урону:",
["select the rally point control"] = "Выберите управление точкой сбора",
["select the tower you want to build!"] = "Выберите башню для постройки!",
["select where you want to move your soldiers"] = "Выберите место для перемещения солдат",
["soldiers block enemies"] = "Солдаты блокируют врагов",
["some enemies enjoy different levels of magic resistance that protects them against magical attacks."] = "Некоторые враги обладают сопротивлением магии различной силы.",
["some enemies wear armor of different strengths that protects them against non-magical attacks."] = "Некоторые враги носят доспехи различной прочности для защиты от физических атак.",
["tap these!"] = "Нажмите на них!",
["tap to continue..."] = "Нажмите, чтобы продолжить...",
["tap twice to call wave"] = "Нажмите дважды, чтобы вызвать волну",
["this is a strategic point."] = "Это стратегическая точка.",
["towers_favorite_customer_DESCRIPTION"] = "При покупке последнего уровня навыка его стоимость снижается на 50%.",
["towers_favorite_customer_NAME"] = "ПОСТОЯННЫЙ КЛИЕНТ",
["towers_golden_time_DESCRIPTION"] = "Увеличивает бонусное золото за ранний вызов волны.",
["towers_golden_time_NAME"] = "ВРЕМЯ – ДЕНЬГИ",
["towers_improved_formulas_DESCRIPTION"] = "Максимизирует урон и увеличивает зону поражения ВСЕХ взрывов от башен.",
["towers_improved_formulas_NAME"] = "ЛУЧШИЙ РЕЦЕПТ",
["towers_keen_accuracy_DESCRIPTION"] = "Уменьшает время перезарядки ВСЕХ умений башен на 20%.",
["towers_keen_accuracy_NAME"] = "БОЕВОЙ ЗАДОР",
["towers_royal_training_DESCRIPTION"] = "Уменьшает время восстановления солдат башен и перезарядку Подкреплений.",
["towers_royal_training_NAME"] = "ТРУБА ЗОВЕТ!",
["towers_scoping_mechanism_DESCRIPTION"] = "Увеличивает дальность атаки ВСЕХ башен на 10%.",
["towers_scoping_mechanism_NAME"] = "МЕХАНИЗМ НАВЕДЕНИЯ",
["towers_war_rations_DESCRIPTION"] = "Увеличивает здоровье ВСЕХ солдат башен на 10%.",
["towers_war_rations_NAME"] = "ВОЕННЫЕ ПАЙКИ",
["towers_wise_investment_DESCRIPTION"] = "Башни теперь возвращают 90% своей стоимости при продаже.",
["towers_wise_investment_NAME"] = "МУДРОЕ ВЛОЖЕНИЕ",
["wOOt!"] = "Ура!",
["you can adjust your soldiers rally point to make them defend a different area."] = "Вы можете изменить расположение точки сбора солдат, чтобы они защищали другую область.",
}
