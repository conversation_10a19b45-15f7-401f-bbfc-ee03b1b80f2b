-- ------------------------------------------------
-- -- WARNING: DO NOT EDIT BY HAND                 
-- -- Generated by kr-i18n/tools/strings-export.lua
-- ------------------------------------------------
return {
["!!!COMMENT_LOCALIZATION_SOURCE"] = "Keywords+CYUGAME",
["%d Life"] = "%d 点生命值",
["%d Lives"] = "%d 条生命",
["%i sec."] = "%i 秒。",
["- if heroes are allowed"] = "- 若允许英雄",
["- max upgrade level allowed"] = "- 允许最大升级等级",
["A good challenge!"] = "不错的挑战！",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_1_NAME"] = "恶煞威利",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_2_NAME"] = "恶煞亨利",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_3_NAME"] = "恶煞杰弗里",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_4_NAME"] = "恶煞尼古拉斯",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_5_NAME"] = "恶煞艾德",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_6_NAME"] = "恶煞霍布",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_7_NAME"] = "恶煞奥多",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_8_NAME"] = "恶煞赛德里克",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_9_NAME"] = "恶煞哈尔",
["ACHIEVEMENT"] = "成就",
["ACHIEVEMENTS"] = "成就",
["ACHIEVEMENTS_TITLE"] = "成就",
["ACHIEVEMENT_AGE_OF_HEROES_DESCRIPTION"] = "赢得所有英雄模式战役的挑战。",
["ACHIEVEMENT_AGE_OF_HEROES_NAME"] = "英雄时代",
["ACHIEVEMENT_ALL_THE_SMALL_THINGS_DESCRIPTION"] = "消灭182个虚空瞥视者。",
["ACHIEVEMENT_ALL_THE_SMALL_THINGS_NAME"] = "无足挂齿",
["ACHIEVEMENT_ARACHNED_DESCRIPTION"] = "击败蜘蛛女王\"狼蛛\"米迦尔。",
["ACHIEVEMENT_ARACHNED_NAME"] = "截肢动物",
["ACHIEVEMENT_A_COON_OF_SURPRISES_DESCRIPTION"] = "帮助弗萝多逃脱。",
["ACHIEVEMENT_A_COON_OF_SURPRISES_NAME"] = "惊喜之茧",
["ACHIEVEMENT_A_TEST_OF_PROWESS_DESCRIPTION"] = "以三星评价赢得一个关卡",
["ACHIEVEMENT_A_TEST_OF_PROWESS_NAME"] = "真金火炼",
["ACHIEVEMENT_BREAKER_OF_CHAINS_DESCRIPTION"] = "在绯红矿坑中拯救4只精灵",
["ACHIEVEMENT_BREAKER_OF_CHAINS_NAME"] = "镣铐破除者",
["ACHIEVEMENT_BUTTERTENTACLES_DESCRIPTION"] = "完成“恶视魔塔”关卡，期间防止魔蒂娅丝诱捕你的单位。",
["ACHIEVEMENT_BUTTERTENTACLES_NAME"] = "圆滑老练",
["ACHIEVEMENT_BYE_BYE_BEAUTIFUL_DESCRIPTION"] = "击败泛视先知魔蒂娅丝",
["ACHIEVEMENT_BYE_BYE_BEAUTIFUL_NAME"] = "再见，美丽",
["ACHIEVEMENT_CIRCLE_OF_LIFE_DESCRIPTION"] = "参加树灵的新生典礼。",
["ACHIEVEMENT_CIRCLE_OF_LIFE_NAME"] = "生生不息",
["ACHIEVEMENT_CLEANSE_THE_KING_DESCRIPTION"] = "解救利尼维亚国王",
["ACHIEVEMENT_CLEANSE_THE_KING_NAME"] = "王者归来",
["ACHIEVEMENT_CLEANUP_IS_OPTIONAL_DESCRIPTION"] = "在不清除塔位上的障碍物的情况下完成荒废郊区。",
["ACHIEVEMENT_CLEANUP_IS_OPTIONAL_NAME"] = "选择性清理",
["ACHIEVEMENT_CONJUNTIVICTORY_DESCRIPTION"] = "击败全视之魔眼。",
["ACHIEVEMENT_CONJUNTIVICTORY_NAME"] = "耀眼胜利",
["ACHIEVEMENT_CONQUEROR_OF_THE_VOID_DESCRIPTION"] = "虚空之境的每个关卡都获得三颗星。",
["ACHIEVEMENT_CONQUEROR_OF_THE_VOID_NAME"] = "虚空征服者",
["ACHIEVEMENT_CRAFTING_IN_THE_MINES_DESCRIPTION"] = "在野兽巢穴中收集所有3块猪排。",
["ACHIEVEMENT_CRAFTING_IN_THE_MINES_NAME"] = "矿坑巧匠",
["ACHIEVEMENT_CROWD_CONTROL_DESCRIPTION"] = "在不从深渊中生成任何血壤巨兽的情况下，完成“腐化山谷”关卡",
["ACHIEVEMENT_CROWD_CONTROL_NAME"] = "群体控制",
["ACHIEVEMENT_CROW_SCARER_DESCRIPTION"] = "把冷峻山谷的乌鸦都吓跑。",
["ACHIEVEMENT_CROW_SCARER_NAME"] = "乌鸦恐吓者",
["ACHIEVEMENT_CRYSTAL_CLEAR_DESCRIPTION"] = "遗忘峡谷的每个关卡都获得三颗星。",
["ACHIEVEMENT_CRYSTAL_CLEAR_NAME"] = "晶莹剔透",
["ACHIEVEMENT_DARK_LIEUTENANT_DESCRIPTION"] = "蕾琳达到10级。",
["ACHIEVEMENT_DARK_LIEUTENANT_NAME"] = "黑暗中尉",
["ACHIEVEMENT_DARK_RUTHLESSNESS_DESCRIPTION"] = "仅使用黑暗大军阵营防御塔和英雄的情况下赢得任一关卡。",
["ACHIEVEMENT_DARK_RUTHLESSNESS_NAME"] = "黑暗无情",
["ACHIEVEMENT_DISTURBING_THE_PEACE_DESCRIPTION"] = "在统治穹顶关卡中打断工人的午休时间。",
["ACHIEVEMENT_DISTURBING_THE_PEACE_NAME"] = "打破宁静",
["ACHIEVEMENT_DLC1_WIN_BOSS_DESCRIPTION"] = "击败诡须并阻止战争机器的建设。",
["ACHIEVEMENT_DLC1_WIN_BOSS_NAME"] = "我裁我自己",
["ACHIEVEMENT_DLC2_GATHER_ENVELOPS_DESCRIPTION"] = "在风暴岛收集8个红包。",
["ACHIEVEMENT_DLC2_GATHER_ENVELOPS_NAME"] = "祝您富贵兴旺。",
["ACHIEVEMENT_DLC2_WIN_BOSS_KING_DESCRIPTION"] = "在他的堡垒中打败牛魔王。",
["ACHIEVEMENT_DLC2_WIN_BOSS_KING_NAME"] = "猴王归来",
["ACHIEVEMENT_DLC2_WIN_BOSS_PRINCESS_DESCRIPTION"] = "击败铁扇公主和她的水军。",
["ACHIEVEMENT_DLC2_WIN_BOSS_PRINCESS_NAME"] = "邪恶的风正在兴起",
["ACHIEVEMENT_DLC2_WIN_BOSS_REDBOY_DESCRIPTION"] = "击败红孩儿和他的火焰军团。",
["ACHIEVEMENT_DLC2_WIN_BOSS_REDBOY_NAME"] = "一切都变了……",
["ACHIEVEMENT_DOMO_ARIGATO_DESCRIPTION"] = "在巨型核心关卡中用大拳头压扁20个敌人。",
["ACHIEVEMENT_DOMO_ARIGATO_NAME"] = "多磨，阿里嘎多",
["ACHIEVEMENT_FACTORY_STRIKE_DESCRIPTION"] = "完成狂热组装厂关卡，不让诡须有机会启动装配线。",
["ACHIEVEMENT_FACTORY_STRIKE_NAME"] = "工厂罢工",
["ACHIEVEMENT_FIELD_TRIP_RUINER_DESCRIPTION"] = "把露营者的篝火扑灭。",
["ACHIEVEMENT_FIELD_TRIP_RUINER_NAME"] = "野炊破坏者",
["ACHIEVEMENT_FOREST_PROTECTOR_DESCRIPTION"] = "尼鲁达到10级。",
["ACHIEVEMENT_FOREST_PROTECTOR_NAME"] = "森林卫士",
["ACHIEVEMENT_GARBAGE_DISPOSAL_DESCRIPTION"] = "消灭10个疯狂修理工，不给他们机会用废铁制造无人机。",
["ACHIEVEMENT_GARBAGE_DISPOSAL_NAME"] = "垃圾处理",
["ACHIEVEMENT_GEM_SPILLER_DESCRIPTION"] = "打破所有宝石篮子。",
["ACHIEVEMENT_GEM_SPILLER_NAME"] = "宝石挥霍专家",
["ACHIEVEMENT_GET_THE_PARTY_STARTED_DESCRIPTION"] = "解开谜题并召唤乐队。",
["ACHIEVEMENT_GET_THE_PARTY_STARTED_NAME"] = "开始派对吧",
["ACHIEVEMENT_GIFT_OF_LIFE_DESCRIPTION"] = "释放克隆密室关卡中的克隆实验体。",
["ACHIEVEMENT_GIFT_OF_LIFE_NAME"] = "生命的礼物",
["ACHIEVEMENT_GREENLIT_ALLIES_DESCRIPTION"] = "召唤10名树灵荆棘矛兵",
["ACHIEVEMENT_GREENLIT_ALLIES_NAME"] = "绿林豪杰",
["ACHIEVEMENT_HAIL_TO_THE_K_BABY_DESCRIPTION"] = "找到鳄鱼国王。",
["ACHIEVEMENT_HAIL_TO_THE_K_BABY_NAME"] = "向鳄鱼国王致敬，宝贝！",
["ACHIEVEMENT_HEARTLESS_VICTORY_DESCRIPTION"] = "在不使用树灵之心力量的情况下完成森林之心关卡。",
["ACHIEVEMENT_HEARTLESS_VICTORY_NAME"] = "无情胜利",
["ACHIEVEMENT_INTO_THE_OGREVERSE_DESCRIPTION"] = "发现神秘蜘蛛人的秘密。",
["ACHIEVEMENT_INTO_THE_OGREVERSE_NAME"] = "不友好邻居",
["ACHIEVEMENT_IRONCLAD_DESCRIPTION"] = "赢得所有铁拳模式战役的挑战。",
["ACHIEVEMENT_IRONCLAD_NAME"] = "铁拳无敌",
["ACHIEVEMENT_ITS_A_SECRET_TO_EVERYONE_DESCRIPTION"] = "帮助琳克钓到5枚卢比。",
["ACHIEVEMENT_ITS_A_SECRET_TO_EVERYONE_NAME"] = "要对大家保密啊",
["ACHIEVEMENT_KEPT_YOU_WAITING_DESCRIPTION"] = "在巨型核心关卡中找到躲躲藏藏的士兵。",
["ACHIEVEMENT_KEPT_YOU_WAITING_NAME"] = "让你久等了？",
["ACHIEVEMENT_LEARNING_THE_ROPES_DESCRIPTION"] = "以三星评价完成教程。",
["ACHIEVEMENT_LEARNING_THE_ROPES_NAME"] = "初学乍练",
["ACHIEVEMENT_LINIREAN_RESISTANCE_DESCRIPTION"] = "仅使用利尼维亚阵营防御塔和英雄的情况下赢得任一关卡。",
["ACHIEVEMENT_LINIREAN_RESISTANCE_NAME"] = "利尼维亚抵抗军",
["ACHIEVEMENT_LUCAS_SPIDER_DESCRIPTION"] = "陪卢咔斯玩到尽兴。",
["ACHIEVEMENT_LUCAS_SPIDER_NAME"] = "小蜘蛛卢咔斯",
["ACHIEVEMENT_MASTER_TACTICIAN_DESCRIPTION"] = "以“不可能”难度完成战役。",
["ACHIEVEMENT_MASTER_TACTICIAN_NAME"] = "战术大师",
["ACHIEVEMENT_MECHANICAL_BURNOUT_DESCRIPTION"] = "在暗钢之门关卡中喂养机器太多次。",
["ACHIEVEMENT_MECHANICAL_BURNOUT_NAME"] = "机械熄火",
["ACHIEVEMENT_MIGHTY_III_DESCRIPTION"] = "击杀10000名敌人。",
["ACHIEVEMENT_MIGHTY_III_NAME"] = "举世无双",
["ACHIEVEMENT_MIGHTY_II_DESCRIPTION"] = "击杀3000名敌人。",
["ACHIEVEMENT_MIGHTY_II_NAME"] = "所向披靡",
["ACHIEVEMENT_MIGHTY_I_DESCRIPTION"] = "击杀500名敌人。",
["ACHIEVEMENT_MIGHTY_I_NAME"] = "锋芒乍现",
["ACHIEVEMENT_MOST_DELICIOUS_DESCRIPTION"] = "喂一些蜂蜜给树灵比吉。",
["ACHIEVEMENT_MOST_DELICIOUS_NAME"] = "美味佳肴",
["ACHIEVEMENT_NATURES_WRATH_DESCRIPTION"] = "使用森林之心杀死30名敌人。",
["ACHIEVEMENT_NATURES_WRATH_NAME"] = "自然之怒",
["ACHIEVEMENT_NONE_SHALL_PASS_DESCRIPTION"] = "在不额外放行任何敌人的情况下完成野兽巢穴。",
["ACHIEVEMENT_NONE_SHALL_PASS_NAME"] = "禁止通行！",
["ACHIEVEMENT_NOT_A_MOMENT_TO_WASTE_DESCRIPTION"] = "提前呼叫15场波次。",
["ACHIEVEMENT_NOT_A_MOMENT_TO_WASTE_NAME"] = "分秒必争",
["ACHIEVEMENT_NO_FLY_ZONE_DESCRIPTION"] = "击杀50只天网蜘蛛。",
["ACHIEVEMENT_NO_FLY_ZONE_NAME"] = "禁飞区",
["ACHIEVEMENT_OBLITERATE_DESCRIPTION"] = "在「巨大的威胁」的每个关卡中都找到被封印的机器人部件。",
["ACHIEVEMENT_OBLITERATE_NAME"] = "愤怒的业火！",
["ACHIEVEMENT_ONE_SHOT_TOWER_DESCRIPTION"] = "用一道暗光高塔光束消灭10个敌人。",
["ACHIEVEMENT_ONE_SHOT_TOWER_NAME"] = "一扫而光",
["ACHIEVEMENT_OUTBACK_BARBEQUICK_DESCRIPTION"] = "在不可能难度下，在血辗跳跃前击败它。",
["ACHIEVEMENT_OUTBACK_BARBEQUICK_NAME"] = "纠缠不清",
["ACHIEVEMENT_OVER_THE_EDGE_DESCRIPTION"] = "把树灵们推下树梢。",
["ACHIEVEMENT_OVER_THE_EDGE_NAME"] = "游戏结束",
["ACHIEVEMENT_OVINE_JOURNALISM_DESCRIPTION"] = "在每个战役地形中找到小绵羊。",
["ACHIEVEMENT_OVINE_JOURNALISM_NAME"] = "羊羊快讯",
["ACHIEVEMENT_PEST_CONTROL_DESCRIPTION"] = "击杀300只眼精。",
["ACHIEVEMENT_PEST_CONTROL_NAME"] = "害虫防治",
["ACHIEVEMENT_PLAYFUL_FRIENDS_DESCRIPTION"] = "在森林之心与所有树灵玩“挖洞”游戏。",
["ACHIEVEMENT_PLAYFUL_FRIENDS_NAME"] = "嬉闹好伙伴",
["ACHIEVEMENT_PORKS_OFF_THE_MENU_DESCRIPTION"] = "打败野兽之王血辗。",
["ACHIEVEMENT_PORKS_OFF_THE_MENU_NAME"] = "清真菜单",
["ACHIEVEMENT_PROMOTION_DENIED_DESCRIPTION"] = "在邪教祭司变身成恶煞之前，击杀30名邪教祭司。",
["ACHIEVEMENT_PROMOTION_DENIED_NAME"] = "晋升被拒",
["ACHIEVEMENT_ROCK_BEATS_ROCK_DESCRIPTION"] = "让雕像自己战胜自己。",
["ACHIEVEMENT_ROCK_BEATS_ROCK_NAME"] = "石头赢…石头？",
["ACHIEVEMENT_ROOM_achievement_claim"] = "获得奖励！",
["ACHIEVEMENT_ROYAL_CAPTAIN_DESCRIPTION"] = "维斯珀达到10级。",
["ACHIEVEMENT_ROYAL_CAPTAIN_NAME"] = "皇家队长",
["ACHIEVEMENT_RUNEQUEST_DESCRIPTION"] = "在永辉森林中激活所有6个符文。",
["ACHIEVEMENT_RUNEQUEST_NAME"] = "符文寻旅",
["ACHIEVEMENT_RUST_IN_PEACE_DESCRIPTION"] = "完成任意关卡，确保没有附身盔甲被再次附身。",
["ACHIEVEMENT_RUST_IN_PEACE_NAME"] = "入锈为安",
["ACHIEVEMENT_SAVIOUR_OF_THE_FOREST_DESCRIPTION"] = "完成战役，不让任何一朵树灵花落入鳄鱼之口。",
["ACHIEVEMENT_SAVIOUR_OF_THE_FOREST_NAME"] = "森林救星",
["ACHIEVEMENT_SAVIOUR_OF_THE_GREEN_DESCRIPTION"] = "永辉森林的每个关卡都获得三颗星。",
["ACHIEVEMENT_SAVIOUR_OF_THE_GREEN_NAME"] = "绿林救星",
["ACHIEVEMENT_SCRAMBLED_EGGS_DESCRIPTION"] = "击杀50只鳄鱼奇趣蛋，不让他们孵化。",
["ACHIEVEMENT_SCRAMBLED_EGGS_NAME"] = "炒蛋大师",
["ACHIEVEMENT_SEASONED_GENERAL_DESCRIPTION"] = "以“老兵”难度完成战役。",
["ACHIEVEMENT_SEASONED_GENERAL_NAME"] = "常胜将军",
["ACHIEVEMENT_SEE_YA_LATER_ALLIGATOR_DESCRIPTION"] = "击败吞世者，噬界灾鳄",
["ACHIEVEMENT_SEE_YA_LATER_ALLIGATOR_NAME"] = "再见了，鳄鱼",
["ACHIEVEMENT_SHUT_YOUR_MOUTH_DESCRIPTION"] = "完成统治穹顶关卡，同时防止诡须点燃你的防御塔。",
["ACHIEVEMENT_SHUT_YOUR_MOUTH_NAME"] = "闭嘴！",
["ACHIEVEMENT_SIGNATURE_TECHNIQUES_DESCRIPTION"] = "使用英雄能力500次。",
["ACHIEVEMENT_SIGNATURE_TECHNIQUES_NAME"] = "招牌技能",
["ACHIEVEMENT_SILVER_FOR_MONSTERS_DESCRIPTION"] = "帮助格哈特杀死树怪。",
["ACHIEVEMENT_SILVER_FOR_MONSTERS_NAME"] = "狩魔之银剑",
["ACHIEVEMENT_SMOOTH_OPER_GATOR_DESCRIPTION"] = "帮助友好鳄鱼发动汽船。",
["ACHIEVEMENT_SMOOTH_OPER_GATOR_NAME"] = "鳄级机师",
["ACHIEVEMENT_SPECTRAL_FURY_DESCRIPTION"] = "击败凡里埃，遏止活尸精灵军团的攻势。",
["ACHIEVEMENT_SPECTRAL_FURY_NAME"] = "亡魂之怒",
["ACHIEVEMENT_STARLIGHT_DESCRIPTION"] = "帮助弗萝多和山拇从巨型蜘蛛爪下逃离。",
["ACHIEVEMENT_STARLIGHT_NAME"] = "明辉星光",
["ACHIEVEMENT_TAKE_ME_HOME_DESCRIPTION"] = "把哥布林里夫送回他的主次元。",
["ACHIEVEMENT_TAKE_ME_HOME_NAME"] = "带我走",
["ACHIEVEMENT_THE_CAVALRY_IS_HERE_DESCRIPTION"] = "召唤1000名援军。",
["ACHIEVEMENT_THE_CAVALRY_IS_HERE_NAME"] = "救星来了！",
["ACHIEVEMENT_TIPPING_THE_SCALES_DESCRIPTION"] = "让罗宾·伍德落水。",
["ACHIEVEMENT_TIPPING_THE_SCALES_NAME"] = "力挽狂澜",
["ACHIEVEMENT_TREE_HUGGER_DESCRIPTION"] = "完成迷雾废墟关卡，确保至少有一棵怪异树木幸存。",
["ACHIEVEMENT_TREE_HUGGER_NAME"] = "树木之友",
["ACHIEVEMENT_TURN_A_BLIND_EYE_DESCRIPTION"] = "击杀100个在[注目]状态下的腐化怪物。",
["ACHIEVEMENT_TURN_A_BLIND_EYE_NAME"] = "视而不见",
["ACHIEVEMENT_UNBOUND_VICTORY_DESCRIPTION"] = "在“邪恶路口”关卡中，确保没有任何梦魇变为武装梦魇。",
["ACHIEVEMENT_UNBOUND_VICTORY_NAME"] = "无拘胜利",
["ACHIEVEMENT_UNENDING_RICHES_DESCRIPTION"] = "收集150000金币",
["ACHIEVEMENT_UNENDING_RICHES_NAME"] = "无尽财富",
["ACHIEVEMENT_UNTAMED_BEAST_DESCRIPTION"] = "格里姆森达到10级。",
["ACHIEVEMENT_UNTAMED_BEAST_NAME"] = "狂野凶兽",
["ACHIEVEMENT_WAR_MASONRY_DESCRIPTION"] = "建造100座防御塔。",
["ACHIEVEMENT_WAR_MASONRY_NAME"] = "大兴土木",
["ACHIEVEMENT_WEIRDER_THINGS_DESCRIPTION"] = "协助艾笛和达斯厅，在枯萎农田击退瞥视者。",
["ACHIEVEMENT_WEIRDER_THINGS_NAME"] = "奇怪物语",
["ACHIEVEMENT_WE_ARE_ALL_MAD_HERE_DESCRIPTION"] = "在不灭狂怒的每一个关卡内都找到幽灵猫。",
["ACHIEVEMENT_WE_ARE_ALL_MAD_HERE_NAME"] = "我们都疯着呢",
["ACHIEVEMENT_WE_RE_NOT_GONNA_TAKE_IT_DESCRIPTION"] = "在梦魇生成前击杀15个扭曲姐妹。",
["ACHIEVEMENT_WE_RE_NOT_GONNA_TAKE_IT_NAME"] = "我们不再忍受",
["ACHIEVEMENT_WOBBA_LUBBA_DUB_DUB_DESCRIPTION"] = "修理睿克和默蒂的传送枪。",
["ACHIEVEMENT_WOBBA_LUBBA_DUB_DUB_NAME"] = "Wobba-Lubba-Dub-Dub!",
["ACHIEVEMENT_YOU_SHALL_NOT_CAST_DESCRIPTION"] = "在不可能难度下解救腐化迪纳斯，且不让泛视先知魔蒂娅丝施放幻影。",
["ACHIEVEMENT_YOU_SHALL_NOT_CAST_NAME"] = "汝不可施法！",
["ADS_MESSAGE_OK"] = "好的",
["ADS_MESSAGE_TITLE"] = "更多宝石",
["ADS_NO_REWARD_VIDEO_AVAILABLE"] = "目前没有奖励视频可看。请稍后再试。",
["ADS_REWARD_EARNED"] = "通过观看视频，您获得了 %i 颗宝石",
["ADVANCED TOWERS"] = "高级防御塔",
["ALERT_VERSION"] = "此游戏已有较新版本。请从商店下载。",
["ALL FOR"] = "一切为了",
["ARCHER TOWER"] = "弓兵",
["ARE YOU SURE YOU WANT TO QUIT?"] = "确定要退出吗？",
["ARMORED ENEMIES!"] = "装甲敌人！",
["ARTILLERY"] = "火炮",
["Achievements"] = "成就",
["BARRACKS"] = "兵营",
["BASIC TOWERS"] = "基本防御塔",
["BEST VALUE"] = "最佳值",
["BOSS_BULL_KING_DESCRIPTION"] = "无情而专横的领袖、战争老兵和务实的战略家。以其巨大的力量、怨恨的性格和高超的武艺而闻名。",
["BOSS_BULL_KING_EXTRA"] = "-高护甲和魔法抗性\n- 对单位和防御塔的大范围眩晕",
["BOSS_BULL_KING_NAME"] = "牛魔王",
["BOSS_CORRUPTED_DENAS_DESCRIPTION"] = "被击败的利尼维亚国王，被全视之魔眼教徒们的黑暗力量变成了高耸的可憎怪物。",
["BOSS_CORRUPTED_DENAS_EXTRA"] = "- 召唤眼精",
["BOSS_CORRUPTED_DENAS_NAME"] = "腐化迪纳斯",
["BOSS_CROCS_DESCRIPTION"] = "饥饿的化身，如果放任这个古老的生物行动，整个世界都会被他吞噬殆尽。",
["BOSS_CROCS_EXTRA"] = "- 吞噬防御塔\n- 吃饱之后进化\n- 召唤鳄鱼奇趣蛋",
["BOSS_CROCS_LVL1_DESCRIPTION"] = "饥饿的化身，如果放任这个古老的生物行动，整个世界都会被他吞噬殆尽。",
["BOSS_CROCS_LVL1_EXTRA"] = "- 吞噬防御塔\n- 吃饱之后进化\n- 召唤鳄鱼奇趣蛋",
["BOSS_CROCS_LVL1_NAME"] = "噬界灾鳄",
["BOSS_CROCS_LVL2_DESCRIPTION"] = "饥饿的化身，如果放任这个古老的生物行动，整个世界都会被他吞噬殆尽。",
["BOSS_CROCS_LVL2_EXTRA"] = "- 吞噬防御塔\n- 吃饱之后进化\n- 召唤鳄鱼奇趣蛋",
["BOSS_CROCS_LVL2_NAME"] = "噬界灾鳄",
["BOSS_CROCS_LVL3_DESCRIPTION"] = "饥饿的化身，如果放任这个古老的生物行动，整个世界都会被他吞噬殆尽。",
["BOSS_CROCS_LVL3_EXTRA"] = "- 吞噬防御塔\n- 吃饱之后进化\n- 召唤鳄鱼奇趣蛋",
["BOSS_CROCS_LVL3_NAME"] = "噬界灾鳄",
["BOSS_CROCS_LVL4_DESCRIPTION"] = "饥饿的化身，如果放任这个古老的生物行动，整个世界都会被他吞噬殆尽。",
["BOSS_CROCS_LVL4_EXTRA"] = "- 吞噬防御塔\n- 吃饱之后进化\n- 召唤鳄鱼奇趣蛋",
["BOSS_CROCS_LVL4_NAME"] = "噬界灾鳄",
["BOSS_CROCS_LVL5_DESCRIPTION"] = "饥饿的化身，如果放任这个古老的生物行动，整个世界都会被他吞噬殆尽。",
["BOSS_CROCS_LVL5_EXTRA"] = "- 吞噬防御塔\n- 吃饱之后进化\n- 召唤鳄鱼奇趣蛋",
["BOSS_CROCS_LVL5_NAME"] = "噬界灾鳄",
["BOSS_CROCS_NAME"] = "噬界灾鳄",
["BOSS_CULT_LEADER_DESCRIPTION"] = "作为邪教现任的领袖，魔蒂娅丝充当全视之魔眼的副手，策划对各个世界的入侵。",
["BOSS_CULT_LEADER_EXTRA"] = "- 未被阻挡时拥有高物理护甲和高魔法抗性\n- 高范围伤害",
["BOSS_CULT_LEADER_NAME"] = "泛视先知魔蒂娅丝",
["BOSS_GRYMBEARD_DESCRIPTION"] = "狂妄自负的矮人，满脑子都是自己，近乎疯狂，危险至极。",
["BOSS_GRYMBEARD_EXTRA"] = "- 向我方单位发射火箭铁拳",
["BOSS_GRYMBEARD_NAME"] = "诡须",
["BOSS_MACHINIST_DESCRIPTION"] = "诡须乘上自己的最新发明追击敌人，降下火焰与金属的倾盆大雨。",
["BOSS_MACHINIST_EXTRA"] = "- 飞行单位\n- 向单位发射废铁",
["BOSS_MACHINIST_NAME"] = "诡须",
["BOSS_NAVIRA_DESCRIPTION"] = "汎里埃沾染了禁忌之力，失去了女神恩典，企图借助死亡魔法重铸精灵一族的荣耀。",
["BOSS_NAVIRA_EXTRA"] = "- 发射火球封锁防御塔\n- 变身成无法阻挡的龙卷风",
["BOSS_NAVIRA_NAME"] = "汎里埃",
["BOSS_PIG_DESCRIPTION"] = "自封为万兽顶点的野兽之王，可使用巨大的连枷碾碎敌人。",
["BOSS_PIG_EXTRA"] = "－在路径上进行超远距离跳跃",
["BOSS_PIG_NAME"] = "野兽之王血辗",
["BOSS_PRINCESS_IRON_FAN_DESCRIPTION"] = "集优雅与致命力量于一身，铁扇公主不仅是牛魔王的妻子，更是一个强大的对手。她是一位冷静而精明的女妖。手持传说中的铁扇，能熄灭火焰，掀起风暴。",
["BOSS_PRINCESS_IRON_FAN_EXTRA"] = "- 克隆自己\n- 将英雄困在瓶中\n- 把防御塔变成敌人生成器",
["BOSS_PRINCESS_IRON_FAN_NAME"] = "铁扇公主",
["BOSS_REDBOY_TEEN_DESCRIPTION"] = "年少轻狂的妖魔王子，性烈如火，气焰嚣张。执掌三昧真火，使得一手好枪。是牛魔王和铁扇公主的儿子。",
["BOSS_REDBOY_TEEN_EXTRA"] = "- 大范围攻击\n - 驱使巨龙封锁防御塔",
["BOSS_REDBOY_TEEN_NAME"] = "红孩儿",
["BOSS_SPIDER_QUEEN_DESCRIPTION"] = "古老的蜘蛛女王，原始力量的化身，从沉睡中苏醒，誓要夺回属于她的一切。",
["BOSS_SPIDER_QUEEN_EXTRA"] = "- 封锁防御塔\n- 吸取周围敌人的生命值\n- 召唤吸血蜘蛛\n- 释放蛛网遮蔽视线",
["BOSS_SPIDER_QUEEN_NAME"] = "米迦尔",
["BRIEFING_LEVEL_WARNING"] = "这个战役的难度等级很高。",
["BUTTON_BUG_CRASH"] = "游戏故障",
["BUTTON_BUG_OTHER"] = "其他",
["BUTTON_BUG_REPORT"] = "错误",
["BUTTON_BUY"] = "购买",
["BUTTON_BUY_UPGRADE"] = "购买升级",
["BUTTON_CLOSE"] = "关闭",
["BUTTON_CONFIRM"] = "确认",
["BUTTON_CONTINUE"] = "继续",
["BUTTON_DISABLE"] = "禁用",
["BUTTON_DONE"] = "完成",
["BUTTON_ENDLESS_QUIT"] = "退出",
["BUTTON_ENDLESS_TRYAGAIN"] = "重试",
["BUTTON_GET_GEMS"] = "获取物品",
["BUTTON_LEVEL_SELECT_FIGHT"] = "战斗！",
["BUTTON_LOST_CONTENT"] = "丢失的内容",
["BUTTON_MAIN_MENU"] = "主菜单",
["BUTTON_NO"] = "否",
["BUTTON_OK"] = "好的！",
["BUTTON_QUIT"] = "退出",
["BUTTON_RESET"] = "重设",
["BUTTON_RESTART"] = "重新开始",
["BUTTON_UNDO"] = "取消",
["BUTTON_YES"] = "是",
["BUY UPGRADES!"] = "购买升级！",
["Basic Tower Types"] = "基本防御塔类型",
["CARD_REWARDS_CAMPAIGN"] = "新战役关卡！",
["CARD_REWARDS_DLC_1"] = "巨大的威胁",
["CARD_REWARDS_DLC_2"] = "大圣游记",
["CARD_REWARDS_HERO"] = "新的英雄！",
["CARD_REWARDS_TOWER"] = "新的防御塔！",
["CARD_REWARDS_TOWER_LEVEL"] = "新的防御塔等级！",
["CARD_REWARDS_TOWER_LEVEL_PREFIX"] = "等级",
["CARD_REWARDS_UPDATE_01"] = "不灭狂怒",
["CARD_REWARDS_UPDATE_02"] = "远古灾饿",
["CARD_REWARDS_UPDATE_03"] = "恐蛛症",
["CARD_REWARDS_UPGRADES"] = "升级点数！",
["CArmor0"] = "无",
["CArmor1"] = "低",
["CArmor2"] = "中",
["CArmor3"] = "高",
["CArmor4"] = "极高",
["CArmor9"] = "免疫",
["CArmorSmall0"] = "无",
["CArmorSmall1"] = "低",
["CArmorSmall2"] = "中",
["CArmorSmall3"] = "高",
["CArmorSmall4"] = "极高",
["CArmorSmall9"] = "免疫",
["CHALLENGE_RULE_DIFFICULTY_CASUAL"] = "休闲",
["CHALLENGE_RULE_DIFFICULTY_IMPOSSIBLE"] = "不可能",
["CHALLENGE_RULE_DIFFICULTY_NORMAL"] = "普通",
["CHALLENGE_RULE_DIFFICULTY_VETERAN"] = "老兵",
["CHANGE_LANGUAGE_QUESTION"] = "确定要更改语言设置吗？",
["CINEMATICS_TAP_TO_CONTINUE"] = "点击以继续……",
["CINEMATICS_TAP_TO_CONTINUE_KR1"] = "点击以继续……",
["CINEMATICS_TAP_TO_CONTINUE_KR2"] = "点击以继续……",
["CINEMATICS_TAP_TO_CONTINUE_KR3"] = "点击以继续……",
["CINEMATICS_TAP_TO_CONTINUE_KR5"] = "点击以继续……",
["CLAIM_GIFT"] = "领取礼物",
["CLOUDSYNC_PLEASE_WAIT"] = "更新保存在云中的游戏……",
["CLOUD_DIALOG_NO"] = "不",
["CLOUD_DIALOG_OK"] = "好的",
["CLOUD_DIALOG_YES"] = "好",
["CLOUD_DOWNLOAD_QUESTION"] = "从 iCloud 下载已保存游戏吗？",
["CLOUD_DOWNLOAD_TITLE"] = "从 iCloud 下载",
["CLOUD_SAVE"] = "云存档",
["CLOUD_SAVE_DISABLE_EXTRA"] = "请注意：卸载游戏后，您的游戏进度可能会丢失。",
["CLOUD_SAVE_DISABLE_GENERIC_DESCRIPTION"] = "确定要禁用此功能，不把您的游戏进度保存在云端？",
["CLOUD_SAVE_OFF"] = "云存储关闭",
["CLOUD_SAVE_ON"] = "云存储已开启",
["CLOUD_UPLOAD_QUESTION"] = "将已保存游戏上传至 iCloud 吗？",
["CLOUD_UPLOAD_TITLE"] = "上传至 iCloud",
["COMIC_10_1_KR5_KR5"] = "放开我！我在拯救我们的王国！",
["COMIC_10_2_KR5_KR5"] = "别再自取其辱了，哥哥。这不是精灵的做派。",
["COMIC_10_3_KR5_KR5"] = "多谢我旧时的好徒儿，这个我们就带走了。",
["COMIC_10_4_KR5_KR5"] = "之后，在营地里。",
["COMIC_10_5_KR5_KR5"] = "那…你确定卫兹南可信？",
["COMIC_10_6_KR5_KR5"] = "我们正盯着他呢…",
["COMIC_10_7_KR5_KR5"] = "…但目前他还乖乖的。",
["COMIC_10_8_KR5_KR5"] = "呵，目前…",
["COMIC_11_1_KR5_KR5"] = "沼泽似乎已经苏醒…",
["COMIC_11_2_KR5_KR5"] = "…像是在凝视着我们…",
["COMIC_11_3_KR5_KR5"] = "…步步逼近，蓄势待发…",
["COMIC_11_4_KR5_KR5"] = "…准备将我们一口吞下。",
["COMIC_11_5_KR5_KR5"] = "小心!",
["COMIC_11_6_KR5_KR5"] = "我们遭到了袭击!",
["COMIC_11_7_KR5_KR5"] = "快去求救，小仙灵!我们的命就靠你了!",
["COMIC_12_1_KR5_KR5"] = "单单把你封印起来看来远远不够。我不会再犯同样的错了。",
["COMIC_12_2_KR5_KR5"] = "不!!!!!",
["COMIC_12_3_KR5_KR5"] = "我要让你灰飞烟灭!!",
["COMIC_12_4_KR5_KR5"] = "咳!",
["COMIC_12_5_KR5_KR5"] = "咳，咳!",
["COMIC_12_6_KR5_KR5"] = "呃，我一定是技艺生疏了。",
["COMIC_13_1_KR5_KR5"] = "有人说，这是疯狂之举。",
["COMIC_13_2_KR5_KR5"] = "说这种武器是不可能完成的。",
["COMIC_13_3_KR5_KR5"] = "但很快他们就会知道自己错得多么离谱……",
["COMIC_13_4_KR5_KR5"] = "……并在诡须的天才面前屈服！",
["COMIC_14_1_KR5_KR5"] = "这些人该如何处置？",
["COMIC_14_2_KR5_KR5"] = "交给我！",
["COMIC_14_3_KR5_KR5"] = "我知道一个好地方。",
["COMIC_14_4_KR5_KR5"] = "这叫好地方？",
["COMIC_14_5_KR5_KR5"] = "让诡须在牢房里腐烂？",
["COMIC_14_6_KR5_KR5"] = "恰恰相反，我的小矮子朋友……",
["COMIC_14_7_KR5_KR5"] = "……你的大脑袋是能干大事情的！",
["COMIC_15_10_KR5_KR5"] = "……但他们状况不太好。",
["COMIC_15_1_KR5_KR5"] = "在山中的某个地方。",
["COMIC_15_2_KR5_KR5"] = "嘿，哥布林！",
["COMIC_15_3_KR5_KR5"] = "干活了！",
["COMIC_15_4_KR5_KR5"] = "你得把消息传回去。",
["COMIC_15_5_KR5_KR5"] = "我们得派遣更多侦察兵。这些邪教徒还在四处游荡，搞得我们心神不宁。",
["COMIC_15_6_KR5_KR5"] = "我们可以派些仙灵去，他们……",
["COMIC_15_7_KR5_KR5"] = "黑暗领主！紧急情报！",
["COMIC_15_8_KR5_KR5"] = "嗯……",
["COMIC_15_9_KR5_KR5"] = "我们找到了我们的侦察兵……",
["COMIC_16_1_KR5_KR5"] = "我的仇必将得报！",
["COMIC_16_2_KR5_KR5"] = "你说我妹妹……什——么？",
["COMIC_17_10_KR5_KR5"] = "若不阻止，天下危在旦夕啊！",
["COMIC_17_11_KR5_KR5"] = "这忙我们得帮！",
["COMIC_17_12_KR5_KR5"] = "哦，那是当然。",
["COMIC_17_13_KR5_KR5"] = "当然啦...",
["COMIC_17_1_KR5_KR5"] = "多么美好的下午啊！",
["COMIC_17_2_KR5_KR5"] = "我快要习惯这种宁静了。",
["COMIC_17_3_KR5_KR5"] = "最好不要。",
["COMIC_17_4_KR5_KR5"] = "太阳，是你吗？！你本可以挥挥手就好了……",
["COMIC_17_5_KR5_KR5"] = "二位，大事不妙哇！",
["COMIC_17_6_KR5_KR5"] = "前些日子老孙在龟背上参禅…",
["COMIC_17_7_KR5_KR5"] = "那魔王一家三口突然现身！",
["COMIC_17_8_KR5_KR5"] = "不用说，老孙定是英勇迎战，怎料…",
["COMIC_17_9_KR5_KR5"] = "他们卑鄙地夺走了我的能量球！",
["COMIC_18_1_KR5_KR5"] = "在牛魔王巢穴的岸边附近……",
["COMIC_18_2_KR5_KR5"] = "目标发现！",
["COMIC_18_3_KR5_KR5"] = "让我们炸掉那座堡垒！",
["COMIC_18_4_KR5_KR5"] = "我的城墙嘲笑你们的石子！",
["COMIC_18_5_KR5_KR5"] = "为了利尼雷亚！",
["COMIC_18_6_KR5_KR5"] = "退后，伙计们！我们需要一个突破口！",
["COMIC_19_1_KR5_KR5"] = "天体球不能由你保管，这太荒谬了！",
["COMIC_19_2_KR5_KR5"] = "嘿，小心点，伙计。",
["COMIC_19_3_KR5_KR5"] = "你真是聪明，高贵的猴子！",
["COMIC_19_4_KR5_KR5"] = "我该拿你们三个怎么办？",
["COMIC_1_1_KR5"] = "我们来到这片土地已经一个月了，来寻找我们失踪的国王……",
["COMIC_1_2B_KR5"] = "…他被黑暗巫师卫兹南放逐。",
["COMIC_1_4_KR5"] = "为了恢复体力，我们找了个地方作为营地……",
["COMIC_1_5_KR5"] = "…休养片刻…",
["COMIC_1_8_KR5"] = "…看来平静的日子结束了。",
["COMIC_2_1_KR5"] = "万岁！！",
["COMIC_2_3_KR5"] = "卫兹南？！",
["COMIC_2_4a_KR5"] = "慢着…我是来提出…",
["COMIC_2_4b_KR5"] = "一项交易的。",
["COMIC_2_5_KR5"] = "在你对王国的所作所为之后？！",
["COMIC_2_6_KR5"] = "迪纳斯国王需要认清现实。",
["COMIC_2_7_KR5"] = "一直以来，他对王国遭遇的危险视而不见。",
["COMIC_2_8_1_KR5"] = "但现在，让我们一起找回你的国王…",
["COMIC_2_8_2_KR5"] = "…然后一起结束这场危机…",
["COMIC_2_8b_KR5"] = "…齐心协力。",
["COMIC_3_1_KR5"] = "天啊！这是什么…？",
["COMIC_3_2_KR5"] = "艾纳尼圣剑！",
["COMIC_3_3_KR5"] = "噢！",
["COMIC_3_4a_KR5"] = "当然了…",
["COMIC_3_4b_KR5"] = "你省省吧！",
["COMIC_3_5a_KR5"] = "啊…但他比你想的要近。",
["COMIC_3_5b_KR5"] = "我们的国王仍然下落不明。",
["COMIC_3_6_KR5"] = "不过，这可能是一场艰苦战斗。",
["COMIC_4_10a_KR5"] = "哈！我向来都是。",
["COMIC_4_10b_KR5"] = "所以…现在怎么办？",
["COMIC_4_11_KR5"] = "我们可能有分歧…",
["COMIC_4_12_KR5"] = "…但我们有一个更大的共同威胁。",
["COMIC_4_1_KR5"] = "艾纳尼…",
["COMIC_4_2_KR5"] = "…赐予他力量吧！",
["COMIC_4_4_KR5"] = "哇啊啊啊！",
["COMIC_4_7a_KR5"] = "看来你“度假”完之后大有长进啊！",
["COMIC_4_7b_KR5"] = "你！！！",
["COMIC_4_8_KR5"] = "你得为你的所作所为付出代价！",
["COMIC_4_9_KR5"] = "但你的忠告是对的。",
["COMIC_5_1_KR2"] = "胜利！",
["COMIC_5_1_KR5_KR5"] = "你们这些蠕虫无法阻止…",
["COMIC_5_2_KR2"] = "胜利！",
["COMIC_5_2_KR5_KR5"] = "…全新全异的世界！",
["COMIC_5_6_KR5_KR5"] = "祂醒了！",
["COMIC_5_7a_KR5_KR5"] = "原来这就是…",
["COMIC_5_7b_KR5_KR5"] = "…最后的决战。",
["COMIC_6_1a_KR5_KR5"] = "胆敢挑战我？你真的很有勇气！",
["COMIC_6_1b_KR5_KR5"] = "但是…我这里可容不下那东西！",
["COMIC_6_4_KR5_KR5"] = "嘿！",
["COMIC_6_5_KR5_KR5"] = "你这宇宙鼻涕虫…",
["COMIC_6_6_KR5_KR5"] = "…不要，低估，我的力量！！！",
["COMIC_6_8_KR5_KR5"] = "准备好，我可撑不了太久！",
["COMIC_7_1_KR5_KR5"] = "不！！！这不…可能！",
["COMIC_7_3_KR5_KR5"] = "所以…现在怎么办？",
["COMIC_7_4a_KR5_KR5"] = "这个嘛，我的任务已经完成了…",
["COMIC_7_4b_KR5_KR5"] = "…他们需要他们的国王",
["COMIC_7_5_2_KR2"] = "没有",
["COMIC_7_6_KR5_KR5"] = "下次再会了，我亲爱的宿敌。",
["COMIC_7_7_KR5_KR5"] = "后来，在永辉森林…",
["COMIC_8_1_KR5_KR5"] = "啊，终于！",
["COMIC_8_2_KR5_KR5"] = "这股力量，再一次…",
["COMIC_8_4_KR5_KR5"] = "...属于我了！",
["COMIC_8_5_KR5_KR5"] = "哇哈哈哈！",
["COMIC_9_1_KR5_KR5"] = "不久之前，我们精灵还因魔力高强，蒙受女神恩典而万人敬仰…",
["COMIC_9_2_KR5_KR5"] = "…直到我们的圣物被腐化，精灵一族也失去了往日的辉煌。",
["COMIC_9_3_KR5_KR5"] = "但有了这支大军，我将重振精灵荣光…",
["COMIC_9_4_KR5_KR5"] = "…主宰一个由精灵统治的新世界！",
["COMIC_BALLOON_0002_KR1"] = "胜利！",
["COMIC_BALLOON_02_KR1"] = "胜利！",
["COMIC_balloon_0002_KR1"] = "胜利！",
["COMMAND YOUR TROOPS!"] = "统领你的部队！",
["CONFIRM_EXIT"] = "是否退出？",
["CONFIRM_RESTART"] = "是否重新开始？",
["CONTROLLER_STAGE_16_OVERSEER_DESCRIPTION"] = "跨越各个次元，入侵其他世界并吸收其能量的饕餮异兽。必须不惜一切代价制止它！",
["CONTROLLER_STAGE_16_OVERSEER_EXTRA"] = "- 交换玩家的防御塔\n- 召唤眼精\n- 摧毁塔位",
["CONTROLLER_STAGE_16_OVERSEER_NAME"] = "全视之魔眼",
["CREDITS_COPYRIGHT"] = "© 2014 Ironhide Game Studio。保留所有权利。",
["CREDITS_POWERED_BY"] = "技术支持",
["CREDITS_SUBTITLE_01"] = "（按字母顺序）",
["CREDITS_SUBTITLE_07"] = "（按字母顺序）",
["CREDITS_SUBTITLE_09"] = "（按字母顺序）",
["CREDITS_SUBTITLE_16"] = "（按字母顺序）",
["CREDITS_TEXT_18"] = "献给我们的家人和朋友。",
["CREDITS_TEXT_18_2"] = "这些年来一直支持我们。",
["CREDITS_TITLE_01"] = "创意总监与执行制作人",
["CREDITS_TITLE_01_CREATIVE_DIRECTORS"] = "程序主管",
["CREDITS_TITLE_01_EXECUTIVE_PRODUCERS"] = "故事编剧",
["CREDITS_TITLE_02"] = "首席游戏设计师",
["CREDITS_TITLE_02_LEAD_GAME_DESIGNERS"] = "项目管理",
["CREDITS_TITLE_03"] = "游戏设计师",
["CREDITS_TITLE_03_GAME_DESIGNER"] = "制作人",
["CREDITS_TITLE_04"] = "故事作者",
["CREDITS_TITLE_04_STORY_WRITERS"] = "市场营销",
["CREDITS_TITLE_05"] = "文本作者",
["CREDITS_TITLE_06"] = "首席程序员",
["CREDITS_TITLE_06_LEAD_PROGRAMMERS"] = "特别合作者",
["CREDITS_TITLE_07"] = "程序员",
["CREDITS_TITLE_08"] = "首席画师",
["CREDITS_TITLE_09"] = "画师",
["CREDITS_TITLE_10"] = "漫画画师",
["CREDITS_TITLE_11"] = "漫画作者",
["CREDITS_TITLE_12"] = "技术美工",
["CREDITS_TITLE_13"] = "音效",
["CREDITS_TITLE_14"] = "原声音乐",
["CREDITS_TITLE_15"] = "配音",
["CREDITS_TITLE_16"] = "质量检验与测试",
["CREDITS_TITLE_17"] = "測試版",
["CREDITS_TITLE_18"] = "特别鸣谢",
["CREDITS_TITLE_19_PMO"] = "项目管理",
["CREDITS_TITLE_20_PRODUCER"] = "制作人",
["CREDITS_TITLE_21_MARKETING"] = "市场营销",
["CREDITS_TITLE_22_SPECIAL_COLLAB"] = "特别合作者",
["CREDITS_TITLE_ANCIENT_HUNGER_UPDATE"] = "远古灾饿 / 恐蛛症 / 大圣游记",
["CREDITS_TITLE_GAME_ENGINE_PROGRAMMER"] = "游戏引擎程序员",
["CREDITS_TITLE_LOCALIZATION"] = "本地化",
["CREDITS_TITLE_LOGO"] = "游戏制作：",
["CRange0"] = "短",
["CRange1"] = "中",
["CRange2"] = "远",
["CRange3"] = "超远",
["CRange4"] = "极远",
["CReload0"] = "非常慢",
["CReload1"] = "慢",
["CReload2"] = "中",
["CReload3"] = "快",
["CReload4"] = "非常快",
["CSpeed0"] = "慢",
["CSpeed1"] = "中",
["CSpeed2"] = "快",
["C_DIFFICULTY_EASY"] = "已完成休闲模式",
["C_DIFFICULTY_HARD"] = "已完成老兵模式",
["C_DIFFICULTY_IMPOSSIBLE"] = "已完成不可能模式",
["C_DIFFICULTY_NORMAL"] = "已完成普通模式",
["C_REWARD"] = "奖励：",
["Campaign"] = "战役",
["Cancel"] = "取消",
["Casual"] = "休闲",
["Challenge Rules"] = "挑战规则",
["Clear_progress"] = "清除进度",
["Community Manager"] = "社区管理员",
["Credits"] = "制作人员",
["DAYS_ABBREVIATION"] = "天",
["DELETE SLOT?"] = "刪除存檔？",
["DIFFICULTY_SELECTION_EASY_DESCRIPTION"] = "策略游戏新手！",
["DIFFICULTY_SELECTION_HARD_DESCRIPTION"] = "硬核级别！有风险！",
["DIFFICULTY_SELECTION_IMPOSSIBLE_DESCRIPTION"] = "只有最强之人 才有生还的希望！",
["DIFFICULTY_SELECTION_IMPOSSIBLE_LOCKED_DESCRIPTION"] = "完成战役 即可解锁此模式",
["DIFFICULTY_SELECTION_NORMAL_DESCRIPTION"] = "不错的挑战！",
["DIFFICULTY_SELECTION_NOTE"] = "你可以在选择关卡的时候更改难度级别。",
["DIFFICULTY_SELECTION_TITLE"] = "选择难度级别！",
["DISCOUNT"] = "折扣",
["DLC_OWNED"] = "买过的",
["Difficulty Level"] = "难度级别",
["ELITE STAGE!"] = "精英关卡！",
["ENEMY_ACOLYTE_DESCRIPTION"] = "身材矮小且弱不禁风的随从，在战场上往往以数量取胜。",
["ENEMY_ACOLYTE_EXTRA"] = "- 死后化身为触手",
["ENEMY_ACOLYTE_NAME"] = "邪教仆从",
["ENEMY_ACOLYTE_SPECIAL"] = "死后化身为触手。",
["ENEMY_ACOLYTE_TENTACLE_DESCRIPTION"] = "作为最后手段，随从们会将生命献给全视之魔眼，从而召唤出致命的触手。",
["ENEMY_ACOLYTE_TENTACLE_EXTRA"] = "- 仆从死亡时产生",
["ENEMY_ACOLYTE_TENTACLE_NAME"] = "仆从触手",
["ENEMY_AMALGAM_DESCRIPTION"] = "由虚空之外的血肉与土壤融合而成的巨兽。行动迟缓，但能够在战场上散布恐惧。",
["ENEMY_AMALGAM_EXTRA"] = "- 小BOSS\n- 死亡时自爆",
["ENEMY_AMALGAM_NAME"] = "血壤巨兽",
["ENEMY_ANIMATED_ARMOR_DESCRIPTION"] = "过往战争留下的残破遗物，如今被亡魂附身，再次加入战斗。",
["ENEMY_ANIMATED_ARMOR_EXTRA"] = "- 击败后可以再次被亡魂附身",
["ENEMY_ANIMATED_ARMOR_NAME"] = "附身盔甲",
["ENEMY_ARMORED_NIGHTMARE_DESCRIPTION"] = "拜邪教魔法所赐，这些梦魇身披盔甲，昂首挺胸地投入战斗。",
["ENEMY_ARMORED_NIGHTMARE_EXTRA"] = "－高护甲\n－击倒时变回普通梦魇",
["ENEMY_ARMORED_NIGHTMARE_NAME"] = "武装梦魇",
["ENEMY_ARMORED_NIGHTMARE_SPECIAL"] = "被击倒时变回普通梦魇。",
["ENEMY_ASH_SPIRIT_DESCRIPTION"] = "沦为可怕巨怪的强大精魄，从岩浆、灰烬和悲伤中孕育而生。",
["ENEMY_ASH_SPIRIT_EXTRA"] = "- 高生命值\n- 高护甲\n- 在燃烧地面上恢复生命值",
["ENEMY_ASH_SPIRIT_NAME"] = "悲烬妖",
["ENEMY_BALLOONING_SPIDER_DESCRIPTION"] = "快速又狡猾的蜘蛛，十分擅长避开麻烦。",
["ENEMY_BALLOONING_SPIDER_EXTRA"] = "- 被拦截时飞行\n- 中等护甲",
["ENEMY_BALLOONING_SPIDER_FLYER_DESCRIPTION"] = "快速又狡猾的蜘蛛，十分擅长避开麻烦。",
["ENEMY_BALLOONING_SPIDER_FLYER_EXTRA"] = "- 被拦截时飞行\n- 中等护甲",
["ENEMY_BALLOONING_SPIDER_FLYER_NAME"] = "天网蜘蛛",
["ENEMY_BALLOONING_SPIDER_NAME"] = "天网蜘蛛",
["ENEMY_BANE_WOLF_DESCRIPTION"] = "要是不能及时发现这群恶狼的身影，便会沦为它们口中的猎物。",
["ENEMY_BANE_WOLF_EXTRA"] = "- 每次受到伤害速度加快",
["ENEMY_BANE_WOLF_NAME"] = "祸狼",
["ENEMY_BEAR_VANGUARD_DESCRIPTION"] = "他们高大、威猛且残暴，能将敌人撕成碎片。",
["ENEMY_BEAR_VANGUARD_EXTRA"] = "- 高护甲\n- 附近有熊死亡时会进入狂暴状态",
["ENEMY_BEAR_VANGUARD_NAME"] = "熊先锋",
["ENEMY_BEAR_VANGUARD_SPECIAL"] = "附近有熊死亡时会进入狂暴状态",
["ENEMY_BEAR_WOODCUTTER_DESCRIPTION"] = "值班时常打瞌睡，但一旦醒来，事情就变得麻烦了。",
["ENEMY_BEAR_WOODCUTTER_EXTRA"] = "- 高护甲\n- 附近有熊死亡时会进入狂暴状态",
["ENEMY_BEAR_WOODCUTTER_NAME"] = "熊樵夫",
["ENEMY_BIG_TERRACOTA_DESCRIPTION"] = "由數個被殺意驅使的靈魂融合而成的人形泥團。",
["ENEMY_BIG_TERRACOTA_EXTRA"] = "- 近战",
["ENEMY_BIG_TERRACOTA_NAME"] = "怪物幻象诱饵",
["ENEMY_BLAZE_RAIDER_DESCRIPTION"] = "骄傲强壮的队长们，亦是火之道的入门者，挥舞着蛇矛以智胜敌。",
["ENEMY_BLAZE_RAIDER_EXTRA"] = "- 低护甲\n- 在燃烧地面上发动特殊攻击",
["ENEMY_BLAZE_RAIDER_NAME"] = "炎教头",
["ENEMY_BLINKER_DESCRIPTION"] = "瞥视者拥有着游隼般的视觉和蝙蝠状的翅膀，会袭击毫无防备的敌人。",
["ENEMY_BLINKER_EXTRA"] = "－晕眩玩家单位",
["ENEMY_BLINKER_NAME"] = "虚空瞥视者",
["ENEMY_BLINKER_SPECIAL"] = "晕眩玩家单位。",
["ENEMY_BOSS_BULL_KING_NAME"] = "牛魔王",
["ENEMY_BOSS_CORRUPTED_DENAS_NAME"] = "腐化迪纳斯",
["ENEMY_BOSS_CROCS_2_NAME"] = "噬界灾鳄·剧毒",
["ENEMY_BOSS_CROCS_3_NAME"] = "噬界灾鳄·烈火",
["ENEMY_BOSS_CROCS_NAME"] = "噬界灾鳄",
["ENEMY_BOSS_CULT_LEADER_NAME"] = "泛视先知魔蒂娅丝",
["ENEMY_BOSS_DEFORMED_GRYMBEARD_NAME"] = "畸变诡须",
["ENEMY_BOSS_GRYMBEARD_NAME"] = "诡须",
["ENEMY_BOSS_MACHINIST_NAME"] = "诡须",
["ENEMY_BOSS_NAVIRA_NAME"] = "汎里埃",
["ENEMY_BOSS_OVERSEER_NAME"] = "全视之魔眼",
["ENEMY_BOSS_PIG_NAME"] = "野兽之王血辗",
["ENEMY_BOSS_PRINCESS_IRON_FAN_CLONE_NAME"] = "铁扇公主克隆体",
["ENEMY_BOSS_PRINCESS_IRON_FAN_NAME"] = "铁扇公主",
["ENEMY_BOSS_REDBOY_TEEN_NAME"] = "红孩儿",
["ENEMY_BOSS_SPIDER_QUEEN_NAME"] = "米迦尔",
["ENEMY_BRUTE_WELDER_DESCRIPTION"] = "这些工人一言不合便抄起焊枪对准敌人。",
["ENEMY_BRUTE_WELDER_EXTRA"] = "- 死亡时封锁一座防御塔",
["ENEMY_BRUTE_WELDER_NAME"] = "野蛮焊工",
["ENEMY_BURNING_TREANT_DESCRIPTION"] = "诞生于燃烧的森林之中，充满恶念的树精。",
["ENEMY_BURNING_TREANT_EXTRA"] = "- 范围伤害\n- 攻击时使地面燃烧",
["ENEMY_BURNING_TREANT_NAME"] = "焚树精",
["ENEMY_CITIZEN_1_DESCRIPTION"] = "为公主效力、偷偷穿行于黑市的阴险渔夫。",
["ENEMY_CITIZEN_1_EXTRA"] = "- 虚弱",
["ENEMY_CITIZEN_1_NAME"] = "老鱼贩",
["ENEMY_CITIZEN_2_DESCRIPTION"] = "为公主效力的邪恶渔夫，在黑市中走私穿行。",
["ENEMY_CITIZEN_2_EXTRA"] = "- 虚弱",
["ENEMY_CITIZEN_2_NAME"] = "黑水漁夫",
["ENEMY_CITIZEN_3_DESCRIPTION"] = "平头百姓，被迫为女王而战。",
["ENEMY_CITIZEN_3_EXTRA"] = "- 虚弱",
["ENEMY_CITIZEN_3_NAME"] = "墨水走私者",
["ENEMY_CITIZEN_4_DESCRIPTION"] = "平头百姓，被迫为女王而战。",
["ENEMY_CITIZEN_4_EXTRA"] = "- 虚弱",
["ENEMY_CITIZEN_4_NAME"] = "潮汐偷猎者",
["ENEMY_CITIZEN_DESCRIPTION"] = "平头百姓，被迫为女王而战。",
["ENEMY_CITIZEN_EXTRA"] = "- 孱弱",
["ENEMY_CITIZEN_NAME"] = "庶民",
["ENEMY_COMMON_CLONE_DESCRIPTION"] = "又平庸又普通，与原版没什么两样。",
["ENEMY_COMMON_CLONE_EXTRA"] = "- 无脑向前推进",
["ENEMY_COMMON_CLONE_NAME"] = "克隆矮人",
["ENEMY_CORRUPTED_ELF_DESCRIPTION"] = "已是行尸走肉的精灵，能猎杀远处的敌人。即便死后也是精英战士。",
["ENEMY_CORRUPTED_ELF_EXTRA"] = "- 死亡后生成一个幽灵",
["ENEMY_CORRUPTED_ELF_NAME"] = "活尸游侠",
["ENEMY_CORRUPTED_STALKER_DESCRIPTION"] = "被邪教随从驯服并成为坐骑的云鹰。",
["ENEMY_CORRUPTED_STALKER_EXTRA"] = "- 飞行单位",
["ENEMY_CORRUPTED_STALKER_NAME"] = "驯化云鹰",
["ENEMY_CORRUPTED_STALKER_SPECIAL"] = "飞行单位。",
["ENEMY_CROCS_BASIC_DESCRIPTION"] = "自傲的鳄鱼族战士，仍是年幼的个体，但仅需几卡路里它便能进化为它所梦寐以求的杀戮机器。",
["ENEMY_CROCS_BASIC_EGG_DESCRIPTION"] = "势不可挡的新生鳄鱼，“孩子长得真快”这句话说的就是这些充满惊喜的小家伙。",
["ENEMY_CROCS_BASIC_EGG_EXTRA"] = "- 不可阻挡\n- 低护甲\n- 几秒钟后孵化成鳄鱼战士",
["ENEMY_CROCS_BASIC_EGG_NAME"] = "鳄鱼奇趣蛋",
["ENEMY_CROCS_BASIC_EXTRA"] = "- 近战",
["ENEMY_CROCS_BASIC_NAME"] = "鳄鱼战士",
["ENEMY_CROCS_EGG_SPAWNER_DESCRIPTION"] = "这只鳄鱼驮着一个麻烦满满的巢！每走几步就会掉下几颗蛋，孵化成狂暴的鳄鱼奇趣蛋。活脱脱一个移动托儿所，里头的孩子可会咬人了！",
["ENEMY_CROCS_EGG_SPAWNER_EXTRA"] = "- 在路径上生成鳄鱼奇趣蛋",
["ENEMY_CROCS_EGG_SPAWNER_NAME"] = "鳄鱼运巢者",
["ENEMY_CROCS_FLIER_DESCRIPTION"] = "这些鳄鱼蔑视自然进化，自行锻造了翅膀，以占据空中优势。",
["ENEMY_CROCS_FLIER_EXTRA"] = "- 飞行",
["ENEMY_CROCS_FLIER_NAME"] = "飞翼鳄",
["ENEMY_CROCS_HYDRA_DESCRIPTION"] = "两颗脑袋总比一颗脑袋强，海德拉的存在证明了这一点。古代神话中也曾记载了一种有三颗脑袋的怪物，但这大概只是谣言吧。",
["ENEMY_CROCS_HYDRA_EXTRA"] = "- 高血量\n- 高伤害\n- 高魔法抗性\n- 死亡时长出第三颗头\n- 向地面喷吐毒液",
["ENEMY_CROCS_HYDRA_NAME"] = "海德拉",
["ENEMY_CROCS_QUICKFEET_GATOR_NAME"] = "鳄鱼跑腿工",
["ENEMY_CROCS_RANGED_DESCRIPTION"] = "敏捷矫健的蜥蜴猎手，使用弹弓对付远处的敌人。",
["ENEMY_CROCS_RANGED_EXTRA"] = "- 快速\n- 远程",
["ENEMY_CROCS_RANGED_NAME"] = "蜥蜴猎手",
["ENEMY_CROCS_SHAMAN_DESCRIPTION"] = "鳄鱼族人中地位显赫的魔法高人。毕竟，对于一个冷血的种族来说，预报天气的变化是生死攸关的大事。",
["ENEMY_CROCS_SHAMAN_EXTRA"] = "- 远程魔法伤害\n- 高魔法抗性\n- 治疗其他鳄鱼\n- 封锁防御塔",
["ENEMY_CROCS_SHAMAN_NAME"] = "鳄鱼智者",
["ENEMY_CROCS_TANK_DESCRIPTION"] = "鳄鱼大军的基石，坚信“防守就是最好的进攻”。他们身披不知从哪儿搞来的钉壳，践行自己的信念。",
["ENEMY_CROCS_TANK_EXTRA"] = "- 高生命值\n- 高护甲\n- 被拦截时向前翻滚",
["ENEMY_CROCS_TANK_NAME"] = "重装鳄鱼",
["ENEMY_CRYSTAL_GOLEM_DESCRIPTION"] = "这群石像的水晶中注入了异界魔法，近乎不可阻挡。",
["ENEMY_CRYSTAL_GOLEM_EXTRA"] = "- 小BOSS\n- 极高护甲值",
["ENEMY_CRYSTAL_GOLEM_NAME"] = "水晶魔像",
["ENEMY_CULTBROOD_DESCRIPTION"] = "半蜘蛛、半邪教徒的怪物，无惧无情地冲入战场。",
["ENEMY_CULTBROOD_EXTRA"] = "- 快速\n- 中毒攻击\n- 如果敌人在中毒期间死亡，会诞生另一只邪教魔蛛",
["ENEMY_CULTBROOD_NAME"] = "邪教魔蛛",
["ENEMY_CUTTHROAT_RAT_DESCRIPTION"] = "天性狡猾诡诈，这些老鼠是敏锐的刺客和潜行者。",
["ENEMY_CUTTHROAT_RAT_EXTRA"] = "- 快速\n- 击中敌人后进入隐形状态",
["ENEMY_CUTTHROAT_RAT_NAME"] = "割喉鼠",
["ENEMY_CUTTHROAT_RAT_SPECIAL"] = "击中敌人后进入隐形状态。",
["ENEMY_DARKSTEEL_ANVIL_DESCRIPTION"] = "铁砧便是矮人的战鼓，看着越沉声音越宏亮。",
["ENEMY_DARKSTEEL_ANVIL_EXTRA"] = "- 为敌方单位提供护甲和速度增益",
["ENEMY_DARKSTEEL_ANVIL_NAME"] = "暗钢锻造师",
["ENEMY_DARKSTEEL_FIST_DESCRIPTION"] = "一对机械手臂本是为了弯折钢铁，拿来痛击对手也绰绰有余。",
["ENEMY_DARKSTEEL_FIST_EXTRA"] = "- 特殊攻击可眩晕玩家单位",
["ENEMY_DARKSTEEL_FIST_NAME"] = "暗钢重拳",
["ENEMY_DARKSTEEL_GUARDIAN_DESCRIPTION"] = "由矮人战士操作、强劲引擎驱动的坚固战斗装甲。绝对是能让人下巴掉下来的打扮。",
["ENEMY_DARKSTEEL_GUARDIAN_EXTRA"] = "- 小Boss\n- 低生命值时进入狂暴状态",
["ENEMY_DARKSTEEL_GUARDIAN_NAME"] = "暗钢守卫",
["ENEMY_DARKSTEEL_HAMMERER_DESCRIPTION"] = "和他们钟爱的武器一样笨重的战士。",
["ENEMY_DARKSTEEL_HAMMERER_EXTRA"] = " ",
["ENEMY_DARKSTEEL_HAMMERER_NAME"] = "暗钢锤兵",
["ENEMY_DARKSTEEL_HULK_DESCRIPTION"] = "脾气暴躁，血管中流淌的是熔融的钢铁，这是矮人能达到的最\"重\"形态。",
["ENEMY_DARKSTEEL_HULK_EXTRA"] = "- 小Boss\n- 低生命值时，沿路径冲刺并造成伤害",
["ENEMY_DARKSTEEL_HULK_NAME"] = "暗钢巨人",
["ENEMY_DARKSTEEL_SHIELDER_DESCRIPTION"] = "手持巨盾抵御攻击，在敌人中破开一条通路前进。",
["ENEMY_DARKSTEEL_SHIELDER_EXTRA"] = "- 被击败后变成锤兵",
["ENEMY_DARKSTEEL_SHIELDER_NAME"] = "暗钢盾卫",
["ENEMY_DEATHWOOD_DESCRIPTION"] = "被黑暗亡魂腐化的怪异树木，在林中游荡，肆意破坏。",
["ENEMY_DEATHWOOD_EXTRA"] = "- 小Boss\n - 投掷诅咒果实造成范围伤害",
["ENEMY_DEATHWOOD_NAME"] = "诡异枯木",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_DESCRIPTION"] = "诡须那目空一切的傲慢所诞下的产物。想必只有这么一颗又大又丑的脑袋才能配得上他的脑力。",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_EXTRA"] = "- 飞行单位\n- 高魔法抗性护盾",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_NAME"] = "畸变克隆",
["ENEMY_DEMON_MINOTAUR_DESCRIPTION"] = "半人半牛的残酷妖魔，以横扫千军之势冲锋陷阵。",
["ENEMY_DEMON_MINOTAUR_EXTRA"] = "- 冲锋攻击\n- 不能被秒杀",
["ENEMY_DEMON_MINOTAUR_NAME"] = "魔牛精",
["ENEMY_DOOM_BRINGER_DESCRIPTION"] = "丧失人性的恐怖杀手，誓效忠于杀戮之道",
["ENEMY_DOOM_BRINGER_EXTRA"] = "- 死亡时生成沉默区域\n- 封锁防御塔",
["ENEMY_DOOM_BRINGER_NAME"] = "灭世魔将",
["ENEMY_DRAINBROOD_DESCRIPTION"] = "咬击足以致命的古老蜘蛛。有些人推测它们是导致其他蜘蛛结晶化的罪魁祸首。",
["ENEMY_DRAINBROOD_EXTRA"] = "- 使敌人结晶化并吸取他们的生命。",
["ENEMY_DRAINBROOD_NAME"] = "吸血蜘蛛",
["ENEMY_DREADEYE_VIPER_DESCRIPTION"] = "用自身毒液涂抹箭矢，并从远处精准夺命。",
["ENEMY_DREADEYE_VIPER_EXTRA"] = "- 低魔法抗性\n- 毒性攻击",
["ENEMY_DREADEYE_VIPER_NAME"] = "骇眼毒蛇",
["ENEMY_DREADEYE_VIPER_SPECIAL"] = "其箭矢会对目标施加中毒效果。",
["ENEMY_DUST_CRYPTID_DESCRIPTION"] = "曾经是深林奇景，如今却是骇人恶虫。",
["ENEMY_DUST_CRYPTID_EXTRA"] = "- 飞行\n- 留下能使敌人免疫伤害的花粉团",
["ENEMY_DUST_CRYPTID_NAME"] = "粉尘飞蛾",
["ENEMY_EVOLVING_SCOURGE_DESCRIPTION"] = "乍看有点可愛，但当它们吞噬掉阵亡的战士时就会变成混世魔王。",
["ENEMY_EVOLVING_SCOURGE_EXTRA"] = "- 吞噬阵亡单位后进化成更强形态\n- 受到[注目]时立即进化成最终形态",
["ENEMY_EVOLVING_SCOURGE_NAME"] = "异变灾星",
["ENEMY_FAN_GUARD_DESCRIPTION"] = "强大且多才多艺的女战士，既擅长造成伤害，也能用魔法扇保护自己。",
["ENEMY_FAN_GUARD_EXTRA"] = "- 未被阻挡时拥有中等护甲和魔法抗性。",
["ENEMY_FAN_GUARD_NAME"] = "铁扇卫",
["ENEMY_FIRE_FOX_DESCRIPTION"] = "诞生于火焰中，长相喜人却难觅踪迹的狐狸。它们跑得飞快且喜怒无常，难以驯服。",
["ENEMY_FIRE_FOX_EXTRA"] = "- 低魔法抗性\n- 在燃烧地面上加速\n- 死亡时使地面燃烧",
["ENEMY_FIRE_FOX_NAME"] = "火狐精",
["ENEMY_FIRE_PHOENIX_DESCRIPTION"] = "神话中的飞鸟，以火焰为食。在炽热的火焰生死轮回。",
["ENEMY_FIRE_PHOENIX_EXTRA"] = "- 飞行\n- 死亡时使地面燃烧",
["ENEMY_FIRE_PHOENIX_NAME"] = "凤雏",
["ENEMY_FLAME_GUARD_DESCRIPTION"] = "渴求师父的认可，擅长使用短刃的低阶弟子",
["ENEMY_FLAME_GUARD_EXTRA"] = "- 近战\n- 在燃烧地面上发动特殊攻击",
["ENEMY_FLAME_GUARD_NAME"] = "火士卒",
["ENEMY_GALE_WARRIOR_DESCRIPTION"] = "举手投足尽显优雅气息的战士，由公主亲自任命，并甘愿为其赴汤蹈火。",
["ENEMY_GALE_WARRIOR_EXTRA"] = "- 中护甲\n- 每3次攻击造成出血",
["ENEMY_GALE_WARRIOR_NAME"] = "风魔将",
["ENEMY_GLAREBROOD_CRYSTAL_NAME"] = "腐败水晶",
["ENEMY_GLARELING_DESCRIPTION"] = "如果不加以遏制，这些看似温顺的生物甚至能击溃最强的军队。",
["ENEMY_GLARELING_EXTRA"] = "- 快速",
["ENEMY_GLARELING_NAME"] = "眼精",
["ENEMY_GLARENWARDEN_DESCRIPTION"] = "这些可憎的蜘蛛是由腐晶幼蛛融合而成的，它们比以往更强大且坚韧。",
["ENEMY_GLARENWARDEN_EXTRA"] = "- 高护甲\n- 攻击时吸取生命",
["ENEMY_GLARENWARDEN_NAME"] = "腐晶巨蛛",
["ENEMY_GOLDEN_EYED_DESCRIPTION"] = "体型硕大的狮头妖兽，震耳欲聋的狮吼放眼天下无可匹敌。",
["ENEMY_GOLDEN_EYED_EXTRA"] = "- 小头目\n- 提高友军的移动速度",
["ENEMY_GOLDEN_EYED_NAME"] = "避水金睛兽",
["ENEMY_HARDENED_HORROR_DESCRIPTION"] = "这种恐怖的怪物手中长有利刃，能劈裂敌人，杀出一条血路。",
["ENEMY_HARDENED_HORROR_EXTRA"] = "- 受到[注目]时，会高速滚动且无法阻挡",
["ENEMY_HARDENED_HORROR_NAME"] = "骇人刃爪",
["ENEMY_HELLFIRE_WARLOCK_DESCRIPTION"] = "极度危险的术士，擅长从地狱深处召唤生物与火焰。",
["ENEMY_HELLFIRE_WARLOCK_EXTRA"] = "- 释放火球\n- 召唤九妖尾狐",
["ENEMY_HELLFIRE_WARLOCK_NAME"] = "业火妖道",
["ENEMY_HOG_INVADER_DESCRIPTION"] = "粗鄙且肮脏的乌合之众。是野兽军团的主力。",
["ENEMY_HOG_INVADER_EXTRA"] = "－低血量",
["ENEMY_HOG_INVADER_NAME"] = "猪头兵",
["ENEMY_HYENA5_DESCRIPTION"] = "嗜血成性的战士，以敌军的尸体为食。",
["ENEMY_HYENA5_EXTRA"] = "－中护甲\n－吃掉倒下的玩家单位恢复血量",
["ENEMY_HYENA5_NAME"] = "腐牙鬣狗",
["ENEMY_HYENA5_SPECIAL"] = "吃掉死亡的玩家单位恢复血量。",
["ENEMY_KILLERTILE_DESCRIPTION"] = "强大的破坏者，多年的战斗经验（或一块鸡肉）使得他们拥有了强大而致命的咬合力。",
["ENEMY_KILLERTILE_EXTRA"] = "- 高血量\n- 高伤害",
["ENEMY_KILLERTILE_NAME"] = "鳄鱼屠夫",
["ENEMY_LESSER_EYE_DESCRIPTION"] = "漂浮在战场上空的邪恶之眼，作为恶毒眼巢的侦察兵而活动。",
["ENEMY_LESSER_EYE_EXTRA"] = "- 飞行单位",
["ENEMY_LESSER_EYE_NAME"] = "小型魔眼",
["ENEMY_LESSER_SISTER_DESCRIPTION"] = "扭曲姐妹施展她们的邪恶魔法，将梦魇带入现实世界。",
["ENEMY_LESSER_SISTER_EXTRA"] = "－高魔法抗性\n－召唤梦魇",
["ENEMY_LESSER_SISTER_NAME"] = "扭曲姐妹",
["ENEMY_LESSER_SISTER_NIGHTMARE_DESCRIPTION"] = "由邪教姐妹们的咒语之书编织而成的虚幻暗影。",
["ENEMY_LESSER_SISTER_NIGHTMARE_EXTRA"] = "- 只能在近战单位拦截时被攻击",
["ENEMY_LESSER_SISTER_NIGHTMARE_NAME"] = "梦魇",
["ENEMY_LESSER_SISTER_SPECIAL"] = "能够召唤梦魇",
["ENEMY_MACHINIST_DESCRIPTION"] = "痴迷于齿轮和引擎，为工业自动化与战争而生的矮人。",
["ENEMY_MACHINIST_EXTRA"] = "- 操作一条生成哨兵的装配线",
["ENEMY_MACHINIST_NAME"] = "诡须",
["ENEMY_MAD_TINKERER_DESCRIPTION"] = "修理工只想着用废品建造东西，除此以外一概不关心。",
["ENEMY_MAD_TINKERER_EXTRA"] = "- 使用其他单位留下的废铁制造无人机",
["ENEMY_MAD_TINKERER_NAME"] = "疯狂修理工",
["ENEMY_MINDLESS_HUSK_DESCRIPTION"] = "看似脆弱不堪的空骸，然而它们每一个都能为战场带来令人头疼的惊喜。",
["ENEMY_MINDLESS_HUSK_EXTRA"] = "- 死亡时召唤眼精。",
["ENEMY_MINDLESS_HUSK_NAME"] = "无主躯壳",
["ENEMY_NINE_TAILED_FOX_DESCRIPTION"] = "神秘且美丽的强大妖精，如同那熊熊烈焰一般冲破敌阵。",
["ENEMY_NINE_TAILED_FOX_EXTRA"] = "- 高魔法抗性\n- 向前传送并在到达时眩晕敌人\n- 范围伤害攻击",
["ENEMY_NINE_TAILED_FOX_NAME"] = "九尾妖狐",
["ENEMY_NOXIOUS_HORROR_DESCRIPTION"] = "外表看似两栖生物的怪物，会向猎物喷射有毒胆汁。近战时也同样危险。",
["ENEMY_NOXIOUS_HORROR_EXTRA"] = "- 受到[注目]时，会获得魔法抗性并释放毒气环",
["ENEMY_NOXIOUS_HORROR_NAME"] = "剧毒喷吐者",
["ENEMY_PALACE_GUARD_DESCRIPTION"] = "资质平庸的学徒，他们唯一的动力就是满足公主的愿望。",
["ENEMY_PALACE_GUARD_EXTRA"] = "- 近战\n- 低护甲",
["ENEMY_PALACE_GUARD_NAME"] = "宫殿守卫",
["ENEMY_PUMPKIN_WITCH_DESCRIPTION"] = "敌人变成了小南瓜，踩几脚就碎。",
["ENEMY_PUMPKIN_WITCH_EXTRA"] = "- 无法阻挡",
["ENEMY_PUMPKIN_WITCH_FLYING_DESCRIPTION"] = "敌人变成了小南瓜，踩几脚就碎。",
["ENEMY_PUMPKIN_WITCH_FLYING_EXTRA"] = "- 无法阻挡",
["ENEMY_PUMPKIN_WITCH_FLYING_NAME"] = "小南瓜",
["ENEMY_PUMPKIN_WITCH_NAME"] = "小南瓜",
["ENEMY_QIONGQI_DESCRIPTION"] = "以雷电之力猛击猎物的飞天狮怪，当之无愧的风暴之王。",
["ENEMY_QIONGQI_EXTRA"] = "- 飞行\n- 极高伤害\n- 中魔法抗性",
["ENEMY_QIONGQI_NAME"] = "穷奇",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_DESCRIPTION"] = "这群鳄鱼多年来负责给兄弟们送鸡肉，腿脚变得飞快，快到有时候连鸡肉都忘了带就出发了。",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_EXTRA"] = "- 快速\n- 远程\n- 当心！他们会给鳄鱼战士送鸡腿，使其进化",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_NAME"] = "鳄鱼跑腿工",
["ENEMY_QUICKFEET_GATOR_DESCRIPTION"] = "这群鳄鱼多年来负责给兄弟们送鸡肉，腿脚变得飞快，快到有时候连鸡肉都忘了带就出发了。",
["ENEMY_QUICKFEET_GATOR_EXTRA"] = "- 快速\n- 远程\n- 当心！他们会给鳄鱼战士送鸡腿，使其进化",
["ENEMY_QUICKFEET_GATOR_NAME"] = "鳄鱼跑腿工",
["ENEMY_REVENANT_HARVESTER_DESCRIPTION"] = "旧时的女祭司变成了游荡林间的怪物，她们支配亡魂为其所用。",
["ENEMY_REVENANT_HARVESTER_EXTRA"] = "- 把附近的亡魂转化为活尸收割者",
["ENEMY_REVENANT_HARVESTER_NAME"] = "活尸收割者",
["ENEMY_REVENANT_SOULCALLER_DESCRIPTION"] = "本该安息的精灵法师被死亡魔法驱使着，能召唤死去精灵的亡魂。",
["ENEMY_REVENANT_SOULCALLER_EXTRA"] = "- 封锁防御塔\n- 召唤亡魂",
["ENEMY_REVENANT_SOULCALLER_NAME"] = "活尸唤魂者",
["ENEMY_RHINO_DESCRIPTION"] = "行走的攻城槌，无视战场上的一切障碍。",
["ENEMY_RHINO_EXTRA"] = "- 小BOSS\n- 向敌群冲锋",
["ENEMY_RHINO_NAME"] = "撼地犀牛",
["ENEMY_RHINO_SPECIAL"] = "向敌群冲锋。",
["ENEMY_ROLLING_SENTRY_DESCRIPTION"] = "即使被击落，它们仍会在地面上追猎敌人。",
["ENEMY_ROLLING_SENTRY_EXTRA"] = "- 死亡时变成废铁\n- 远程",
["ENEMY_ROLLING_SENTRY_NAME"] = "滚动哨兵",
["ENEMY_SCRAP_DRONE_DESCRIPTION"] = "粗制滥造的玩意儿，唯一目标就是骚扰敌军。",
["ENEMY_SCRAP_DRONE_EXTRA"] = "- 飞行单位",
["ENEMY_SCRAP_DRONE_NAME"] = "废铁无人机",
["ENEMY_SCRAP_SPEEDSTER_DESCRIPTION"] = "吵闹且令人讨厌，对速度有着极大的渴望。",
["ENEMY_SCRAP_SPEEDSTER_EXTRA"] = "- 死亡时变成废铁",
["ENEMY_SCRAP_SPEEDSTER_NAME"] = "废铁疾行机",
["ENEMY_SKUNK_BOMBARDIER_DESCRIPTION"] = "臭鼬们将自身的天然毒素发挥到极致，在敌军阵线中制造混乱。",
["ENEMY_SKUNK_BOMBARDIER_EXTRA"] = "－慢速\n－中魔法抗性\n－削弱玩家单位\n－死亡时爆炸",
["ENEMY_SKUNK_BOMBARDIER_NAME"] = "臭鼬投弹手",
["ENEMY_SKUNK_BOMBARDIER_SPECIAL"] = "攻击会削弱玩家单位。死亡时自爆并造成伤害。",
["ENEMY_SMALL_STALKER_DESCRIPTION"] = "被邪教魔法腐化的云鹰会在战场上四处穿梭，散播混乱。",
["ENEMY_SMALL_STALKER_EXTRA"] = "－被攻击时向前传送",
["ENEMY_SMALL_STALKER_NAME"] = "腐化云鹰",
["ENEMY_SMALL_STALKER_SPECIAL"] = "短距离传送，躲避攻击。",
["ENEMY_SPECTER_DESCRIPTION"] = "纵使身体腐朽，灵魂依旧无法解脱，注定要侵扰生者。",
["ENEMY_SPECTER_EXTRA"] = "- 可与其他敌人或物件有联动效果",
["ENEMY_SPECTER_NAME"] = "亡魂",
["ENEMY_SPIDEAD_DESCRIPTION"] = "作为蜘蛛女王米迦尔的直系后裔，这些蜘蛛即使在死后也能不停制造麻烦。",
["ENEMY_SPIDEAD_EXTRA"] = "- 魔法抗性\n- 死亡时生成蜘蛛网",
["ENEMY_SPIDEAD_NAME"] = "米迦尔之女",
["ENEMY_SPIDERLING_DESCRIPTION"] = "邪教魔法强化过后的蜘蛛，敏捷且狂暴，会狠狠咬人。",
["ENEMY_SPIDERLING_EXTRA"] = "－快速\n－低魔法抗性",
["ENEMY_SPIDERLING_NAME"] = "腐晶幼蛛",
["ENEMY_SPIDER_PRIEST_DESCRIPTION"] = "这些祭司被新神层层束缚，在战场上施展黑暗魔法。",
["ENEMY_SPIDER_PRIEST_EXTRA"] = "- 高魔法抗性\n- 濒死时变成腐晶巨蛛",
["ENEMY_SPIDER_PRIEST_NAME"] = "蛛网祭司",
["ENEMY_SPIDER_SISTER_DESCRIPTION"] = "蜘蛛女王的忠实信徒，能施展魔法召唤同族。",
["ENEMY_SPIDER_SISTER_EXTRA"] = "- 魔法抗性\n- 召唤腐晶幼蛛",
["ENEMY_SPIDER_SISTER_NAME"] = "蛛心女巫",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_DESCRIPTION"] = "魔蒂娅丝用于干涉战局的暗影分身。",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_EXTRA"] = "- 保护敌人免受伤害\n- 召唤黑暗触手困锁防御塔",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_NAME"] = "魔蒂娅丝的幻象",
["ENEMY_STORM_ELEMENTAL_DESCRIPTION"] = "由台风、闪电与愤怒孕育的强大元素生物。与悲烬妖同源异脉。",
["ENEMY_STORM_ELEMENTAL_EXTRA"] = "- 高护甲\n- 远程\n- 死亡时击晕附近的防御塔",
["ENEMY_STORM_ELEMENTAL_NAME"] = "怒风怪",
["ENEMY_STORM_SPIRIT_DESCRIPTION"] = "穿梭于风暴中的小龙，灵巧地躲避危险与敌人。",
["ENEMY_STORM_SPIRIT_EXTRA"] = "- 飞行\n- 低魔法抗性\n- 受伤时向前冲刺",
["ENEMY_STORM_SPIRIT_NAME"] = "天雷雏龙",
["ENEMY_SURVEILLANCE_SENTRY_DESCRIPTION"] = "由矮人设计的空中侦察兵。",
["ENEMY_SURVEILLANCE_SENTRY_EXTRA"] = "- 飞行单位",
["ENEMY_SURVEILLANCE_SENTRY_NAME"] = "飞行哨兵",
["ENEMY_SURVEYOR_HARPY_DESCRIPTION"] = "秃鹫一直都跟随野兽军团，伺机寻觅腐肉可餐。",
["ENEMY_SURVEYOR_HARPY_EXTRA"] = "- 飞行单位",
["ENEMY_SURVEYOR_HARPY_NAME"] = "侦察秃鹫",
["ENEMY_SURVEYOR_HARPY_SPECIAL"] = "飞行单位。",
["ENEMY_TERRACOTA_DESCRIPTION"] = "阵亡士兵的幻影，即使身在阴曹地府，依然为公主效力。",
["ENEMY_TERRACOTA_EXTRA"] = "- 近战",
["ENEMY_TERRACOTA_NAME"] = "幻象诱饵",
["ENEMY_TOWER_RAY_SHEEP_DESCRIPTION"] = "咩~~~~~。",
["ENEMY_TOWER_RAY_SHEEP_EXTRA"] = "－无法阻挡",
["ENEMY_TOWER_RAY_SHEEP_FLYING_DESCRIPTION"] = "咩~~~~~。",
["ENEMY_TOWER_RAY_SHEEP_FLYING_EXTRA"] = "- 飞行单位",
["ENEMY_TOWER_RAY_SHEEP_FLYING_NAME"] = "飞行绵羊",
["ENEMY_TOWER_RAY_SHEEP_NAME"] = "绵羊",
["ENEMY_TURTLE_SHAMAN_DESCRIPTION"] = "看似温和，实则邪恶，萨满能够治疗野兽，使其恢复战斗能力。",
["ENEMY_TURTLE_SHAMAN_EXTRA"] = "- 慢速\n- 高血量\n- 高魔法抗性\n- 治疗敌军单位",
["ENEMY_TURTLE_SHAMAN_NAME"] = "乌龟萨满",
["ENEMY_TURTLE_SHAMAN_SPECIAL"] = "能治疗敌军单位。",
["ENEMY_TUSKED_BRAWLER_DESCRIPTION"] = "比一般猪头兵更加难缠，装备着简陋的铠甲。随时准备大打出手。",
["ENEMY_TUSKED_BRAWLER_EXTRA"] = "－低护甲",
["ENEMY_TUSKED_BRAWLER_NAME"] = "獠牙斗士",
["ENEMY_UNBLINDED_ABOMINATION_DESCRIPTION"] = "完全腐化的邪教祭司，以在战斗中的残暴无情而闻名。",
["ENEMY_UNBLINDED_ABOMINATION_EXTRA"] = "－吞噬低血量单位",
["ENEMY_UNBLINDED_ABOMINATION_NAME"] = "邪教恶煞",
["ENEMY_UNBLINDED_ABOMINATION_SPECIAL"] = "偶尔会吞噬低血量单位。",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_DESCRIPTION"] = "在奴役了精灵之后，一些恶煞被指派任命为监工保证矿井的工作顺利进行。",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_EXTRA"] = "- 必须被击败才能解放精灵。",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_NAME"] = "恶煞工头",
["ENEMY_UNBLINDED_PRIEST_DESCRIPTION"] = "游走于宗教与神秘学之间的祭司，操控着黑暗魔法步入战场。",
["ENEMY_UNBLINDED_PRIEST_EXTRA"] = "- 高魔法抗性\n- 濒死时转变为恶煞",
["ENEMY_UNBLINDED_PRIEST_NAME"] = "邪教祭司",
["ENEMY_UNBLINDED_PRIEST_SPECIAL"] = "低血量时，会转变为恶煞。",
["ENEMY_UNBLINDED_SHACKLER_DESCRIPTION"] = "锁链者通过嵌入手臂的水晶引导腐化魔法，在近身战斗中是令人生畏的对手。",
["ENEMY_UNBLINDED_SHACKLER_EXTRA"] = "- 中魔法抗性\n- 低血量时困锁防御塔",
["ENEMY_UNBLINDED_SHACKLER_NAME"] = "锁链者",
["ENEMY_UNBLINDED_SHACKLER_SPECIAL"] = "困锁防御塔，使其无法攻击",
["ENEMY_VILE_SPAWNER_DESCRIPTION"] = "恶毒眼巢将漫天魔眼投向敌人，每时每刻都向四面八方监视着敌人。",
["ENEMY_VILE_SPAWNER_EXTRA"] = "- 生产小型魔眼",
["ENEMY_VILE_SPAWNER_NAME"] = "恶毒眼巢",
["ENEMY_WATER_SORCERESS_DESCRIPTION"] = "执掌水之力的年迈术士，既能治愈盟友，亦能击溃敌军。",
["ENEMY_WATER_SORCERESS_EXTRA"] = "- 远程\n- 中魔法抗性\n- 治疗友军",
["ENEMY_WATER_SORCERESS_NAME"] = "水长老",
["ENEMY_WATER_SPIRIT_DESCRIPTION"] = "海水化形的妖怪，宛如层层怒涛，无情蹂躏海岸。",
["ENEMY_WATER_SPIRIT_EXTRA"] = "- 低魔法抗性\n- 可从水中生成",
["ENEMY_WATER_SPIRIT_NAME"] = "水灵精",
["ENEMY_WATER_SPIRIT_SPAWNLESS_DESCRIPTION"] = "海水化形的妖怪，宛如层层怒涛，无情蹂躏海岸。",
["ENEMY_WATER_SPIRIT_SPAWNLESS_EXTRA"] = "- 低魔法抗性\n- 可从水中生成",
["ENEMY_WATER_SPIRIT_SPAWNLESS_NAME"] = "水灵精",
["ENEMY_WUXIAN_DESCRIPTION"] = "坚韧而强大的法师，能驱使烈焰把一整支军队烧成灰烬。",
["ENEMY_WUXIAN_EXTRA"] = "- 远程\n- 中护甲\n- 在燃烧地面上发动特殊攻击",
["ENEMY_WUXIAN_NAME"] = "巫仙",
["ERROR_MESSAGE_GENERIC"] = "哎呀！出错了。",
["Earn huge bonus points and gold by calling waves earlier!"] = "提前召唤敌人能获得大量额外分数和金币！",
["FIRST_WEEK_PACK"] = "礼物",
["FULLADS_BONUS_REWARDS_TITLE"] = "额外奖励！",
["FULLADS_BUTTON_BUY"] = "购买",
["FULLADS_BUTTON_CLAIM"] = "观看广告后即可获得这些奖励！",
["FULLADS_BUTTON_CLAIM_SHORT"] = "领取！",
["FULLADS_BUTTON_HIRE"] = "雇用",
["FULLADS_BUTTON_INFO"] = "信息",
["FULLADS_BUTTON_PLAY"] = "观看广告后即可获得这些奖励！",
["FULLADS_BUTTON_PLAY_SHORT"] = "开玩！",
["FULLADS_BUTTON_SPIN"] = "观看广告后即可旋转轮盘！",
["FULLADS_BUTTON_SPIN_SHORT"] = "旋转！",
["FULLADS_BUTTON_UNLOCK"] = "解锁",
["FULLADS_DEFEAT_ENDLESS_REWARDS_TITLE"] = "落败奖励！",
["FULLADS_DEFEAT_REWARDS_TITLE"] = "落败奖励！",
["FULLADS_GNOME_REWARDS_TITLE"] = "宝藏精灵奖励！",
["FULLADS_MAP_CROWNS_DESCRIPTION"] = "克朗可以用来雇佣一位英雄一天。",
["FULLADS_MAP_GEMS_DESCRIPTION"] = "宝石可以用来购买道具和永久解锁英雄。",
["FULLADS_MAP_HEROROOM_HELP_CROWNS"] = "雇用一天",
["FULLADS_MAP_HEROROOM_HELP_GEMS"] = "永久购买",
["FULLADS_MAP_STARS_DESCRIPTION"] = "星星可以用来购买升级项目。",
["FULLADS_VICTORY_CLAIM_BONUS"] = "观看广告后即可领取\n%s倍额外奖励。",
["FULLADS_VICTORY_REWARDS_TITLE"] = "获胜奖励！",
["FULLADS_WHEEL_PROBABILITIES_TITLE"] = "奖励出现概率",
["FULLADS_WHEEL_REWARDS_TITLE"] = "幸运大转轮！",
["FULLADS_YOUR_REWARDS_TITLE"] = "你得到的奖励！",
["FULLADS_YOUR_REWARD_TITLE"] = "你得到的奖励！",
["Face an endless unrelenting enemy force and try to defeat as many as possible to comete for the best score!"] = "面对无穷无尽的强大敌人，尽可能多的击败他们，竞得最佳分数！",
["Face an endless unrelenting enemy force and try to defeat as many as possible to compete for the best score!"] = "面对无穷无尽的强大敌人，尽可能多的击败他们，竞得最佳分数！",
["Failed to load Rewarded Video, first session !"] = "有奖视频加载失败。",
["Failed to load Rewarded Video, internal error !"] = "有奖视频加载失败。",
["Failed to load Rewarded Video, missing location parameter !"] = "有奖视频加载失败。",
["Failed to load Rewarded Video, network error !"] = "有奖视频加载失败。",
["Failed to load Rewarded Video, no Internet connection !"] = "有奖视频加载失败。",
["Failed to load Rewarded Video, no ad found !"] = "有奖视频加载失败。",
["Failed to load Rewarded Video, session not started !"] = "有奖视频加载失败。",
["Failed to load Rewarded Video, too many connections !"] = "有奖视频加载失败。",
["Failed to load Rewarded Video, unknown error !"] = "有奖视频加载失败。",
["Failed to load Rewarded Video, wrong orientation !"] = "有奖视频加载失败。",
["GAME PAUSED"] = "游戏暂停",
["GAME_TITLE_KR5"] = "王国保卫战 5：联盟",
["GEMS_BARREL_NAME"] = "一桶宝石",
["GEMS_CHEST_NAME"] = "一箱宝石",
["GEMS_HANDFUL_NAME"] = "一把宝石",
["GEMS_MOUNTAIN_NAME"] = "一山宝石",
["GEMS_POUCH_NAME"] = "一袋宝石",
["GEMS_WAGON_NAME"] = "一车宝石",
["GET_ALL_AWESOME_HEROES"] = "获得所有强大的英雄。",
["GET_THIS_AWESOME"] = "获得这位\n强大的英雄。",
["GET_THIS_AWESOME_2"] = "获得这些\n强大的英雄。",
["GET_THIS_AWESOME_3"] = "获得这些\n强大的英雄。",
["GIFT_CLAIMED"] = "礼物已领取！",
["GOOGLE_PLAY"] = "GOOGLE PLAY商店",
["Got it!"] = "了解了！",
["HERO LEVEL UP!"] = "英雄升级！",
["HERO ROOM"] = "英雄",
["HERO UNLOCKED!"] = "英雄解锁！",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_1"] = "召唤狮鹫飞到区域上空攻击敌人，持续%$heroes.hero_bird.ultimate.bird.duration[2]%$秒，每次攻击造成%$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[2]%$点伤害。",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_2"] = "召唤狮鹫飞到区域上空攻击敌人，持续%$heroes.hero_bird.ultimate.bird.duration[3]%$秒，每次攻击造成%$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[3]%$点伤害。",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_3"] = "召唤狮鹫飞到区域上空攻击敌人，持续%$heroes.hero_bird.ultimate.bird.duration[4]%$秒，每次攻击造成%$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[4]%$点伤害。",
["HERO_BIRD_BIRDS_OF_PREY_MENUBOTTOM_DESCRIPTION"] = "召唤狮鹫飞到区域上空攻击敌人。",
["HERO_BIRD_BIRDS_OF_PREY_MENUBOTTOM_NAME"] = "纷争之翼",
["HERO_BIRD_BIRDS_OF_PREY_TITLE"] = "纷争之翼",
["HERO_BIRD_CLASS"] = "王牌驭手",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_1"] = "投掷一枚分裂炸药，对敌人造成%$heroes.hero_bird.cluster_bomb.explosion_damage_min[1]%$点伤害，并使地面燃烧%$heroes.hero_bird.cluster_bomb.fire_duration[1]%$秒，使敌人在3秒内受到%$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$点燃烧伤害。",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_2"] = "投掷一枚分裂炸药，对敌人造成%$heroes.hero_bird.cluster_bomb.explosion_damage_min[2]%$点伤害，并使地面燃烧%$heroes.hero_bird.cluster_bomb.fire_duration[2]%$秒，使敌人在3秒内受到%$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$点燃烧伤害。",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_3"] = "投掷一枚分裂炸药，对敌人造成%$heroes.hero_bird.cluster_bomb.explosion_damage_min[3]%$点伤害，并使地面燃烧%$heroes.hero_bird.cluster_bomb.fire_duration[3]%$秒，使敌人在3秒内受到%$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$点燃烧伤害。",
["HERO_BIRD_CLUSTER_BOMB_TITLE"] = "扫荡轰炸",
["HERO_BIRD_DESC"] = "英勇的狮鹫骑士挥舞着钢铁与火药的军械飞入战场。布卢登对于黑暗大军曾侵袭他的家园心有芥蒂，但他仍然加入联盟，希望能摧毁邪教，恢复利尼维亚的和平安宁。",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_1"] = "狮鹫俯冲地面，吞噬一名生命值不超过%$heroes.hero_bird.eat_instakill.hp_max[1]%$的敌人。",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_2"] = "狮鹫俯冲地面，吞噬一名生命值不超过%$heroes.hero_bird.eat_instakill.hp_max[2]%$的敌人。",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_3"] = "狮鹫俯冲地面，吞噬一名生命值不超过%$heroes.hero_bird.eat_instakill.hp_max[3]%$的敌人。",
["HERO_BIRD_EAT_INSTAKILL_TITLE"] = "狩猎俯冲",
["HERO_BIRD_GATTLING_DESCRIPTION_1"] = "向一名敌人倾泻子弹，造成%$heroes.hero_bird.gattling.s_damage_min[1]%$-%$heroes.hero_bird.gattling.s_damage_max[1]%$点物理伤害。",
["HERO_BIRD_GATTLING_DESCRIPTION_2"] = "向一名敌人倾泻子弹，造成%$heroes.hero_bird.gattling.s_damage_min[2]%$-%$heroes.hero_bird.gattling.s_damage_max[2]%$点物理伤害。",
["HERO_BIRD_GATTLING_DESCRIPTION_3"] = "向一名敌人倾泻子弹，造成%$heroes.hero_bird.gattling.s_damage_min[3]%$-%$heroes.hero_bird.gattling.s_damage_max[3]%$点物理伤害。",
["HERO_BIRD_GATTLING_TITLE"] = "惩戒子弹",
["HERO_BIRD_NAME"] = "布卢登",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_1"] = "狮鹫发出震耳欲聋的叫声，眩晕敌人%$heroes.hero_bird.shout_stun.stun_duration[1]%$秒，随后使其减速%$heroes.hero_bird.shout_stun.slow_duration[1]%$秒",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_2"] = "狮鹫发出震耳欲聋的叫声，眩晕敌人%$heroes.hero_bird.shout_stun.stun_duration[2]%$秒，随后使其减速%$heroes.hero_bird.shout_stun.slow_duration[2]%$秒",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_3"] = "狮鹫发出震耳欲聋的叫声，眩晕敌人%$heroes.hero_bird.shout_stun.stun_duration[3]%$秒，随后使其减速%$heroes.hero_bird.shout_stun.slow_duration[3]%$秒",
["HERO_BIRD_SHOUT_STUN_TITLE"] = "恐怖尖啸",
["HERO_BUILDER_CLASS"] = "城防总管",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_1"] = "建造一座临时塔楼攻击经过的敌人，持续%$heroes.hero_builder.defensive_turret.duration[1]%$秒，每次攻击造成%$heroes.hero_builder.defensive_turret.attack.damage_min[1]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[1]%$点物理伤害。",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_2"] = "建造一座临时塔楼攻击经过的敌人，持续%$heroes.hero_builder.defensive_turret.duration[2]%$秒，每次攻击造成%$heroes.hero_builder.defensive_turret.attack.damage_min[2]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[2]%$点物理伤害。",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_3"] = "建造一座临时塔楼攻击经过的敌人，持续%$heroes.hero_builder.defensive_turret.duration[3]%$秒，每次攻击造成%$heroes.hero_builder.defensive_turret.attack.damage_min[3]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[3]%$点物理伤害。",
["HERO_BUILDER_DEFENSIVE_TURRET_TITLE"] = "防御塔楼",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_1"] = "快速旋转手中的木梁，对周围的敌人造成%$heroes.hero_builder.demolition_man.s_damage_min[1]%$-%$heroes.hero_builder.demolition_man.s_damage_max[1]%$点物理伤害。",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_2"] = "快速旋转手中的木梁，对周围的敌人造成%$heroes.hero_builder.demolition_man.s_damage_min[2]%$-%$heroes.hero_builder.demolition_man.s_damage_max[2]%$点物理伤害。",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_3"] = "快速旋转手中的木梁，对周围的敌人造成%$heroes.hero_builder.demolition_man.s_damage_min[3]%$-%$heroes.hero_builder.demolition_man.s_damage_max[3]%$点物理伤害。",
["HERO_BUILDER_DEMOLITION_MAN_TITLE"] = "拆迁达人",
["HERO_BUILDER_DESC"] = "托雷斯多年以来负责利尼维亚防御工事的建设，也让他对战斗略知一二。如今整个王国陷入危机之中，早已厌倦旁观的他便将所有工具和知识都投入到前线战斗中。",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_1"] = "托雷斯停下战斗，享用小吃，为自己恢复%$heroes.hero_builder.lunch_break.heal_hp[1]%$点生命。",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_2"] = "托雷斯停下战斗，享用小吃，为自己恢复%$heroes.hero_builder.lunch_break.heal_hp[2]%$点生命。",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_3"] = "托雷斯停下战斗，享用小吃，为自己恢复%$heroes.hero_builder.lunch_break.heal_hp[3]%$点生命。",
["HERO_BUILDER_LUNCH_BREAK_TITLE"] = "午休时间",
["HERO_BUILDER_NAME"] = "托雷斯",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_1"] = "召唤两名工人，在其身旁共同战斗%$heroes.hero_builder.overtime_work.soldier.duration%$秒。",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_2"] = "工人拥有%$heroes.hero_builder.overtime_work.soldier.hp_max[2]%$点生命值，攻击可造成%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_max[2]%$点物理伤害，持续战斗%$heroes.hero_builder.overtime_work.soldier.duration%$秒。",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_3"] = "工人拥有%$heroes.hero_builder.overtime_work.soldier.hp_max[3]%$点生命值，攻击可造成%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_max[3]%$点物理伤害，持续战斗%$heroes.hero_builder.overtime_work.soldier.duration%$秒。",
["HERO_BUILDER_OVERTIME_WORK_TITLE"] = "正在施工",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_1"] = "朝路径上扔出一个巨大钢球，造成%$heroes.hero_builder.ultimate.damage[2]%$点物理伤害，并使敌人眩晕%$heroes.hero_builder.ultimate.stun_duration[2]%$秒。",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_2"] = "朝路径上扔出一个巨大钢球，造成%$heroes.hero_builder.ultimate.damage[3]%$点物理伤害，并使敌人眩晕%$heroes.hero_builder.ultimate.stun_duration[3]%$秒。",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_3"] = "朝路径上扔出一个巨大钢球，造成%$heroes.hero_builder.ultimate.damage[4]%$点物理伤害，并使敌人眩晕%$heroes.hero_builder.ultimate.stun_duration[4]%$秒。",
["HERO_BUILDER_WRECKING_BALL_MENUBOTTOM_DESCRIPTION"] = "朝路径上扔出一个破城球，对敌人造成伤害。",
["HERO_BUILDER_WRECKING_BALL_MENUBOTTOM_NAME"] = "破城钢球",
["HERO_BUILDER_WRECKING_BALL_TITLE"] = "破城钢球",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_1"] = "希尔瓦拉释放她的真实形态，持续%$heroes.hero_dragon_arb.ultimate.duration[2]%$秒，在此期间她获得%$heroes.hero_dragon_arb.ultimate.s_bonuses[2]%$%伤害、速度、抗性提升，并强化一部分技能。",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_2"] = "希尔瓦拉释放她的真实形态，持续%$heroes.hero_dragon_arb.ultimate.duration[3]%$秒，在此期间她获得%$heroes.hero_dragon_arb.ultimate.s_bonuses[3]%$%伤害、速度、抗性提升，并强化一部分技能。",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_3"] = "希尔瓦拉释放她的真实形态，持续%$heroes.hero_dragon_arb.ultimate.duration[4]%$秒，在此期间她获得%$heroes.hero_dragon_arb.ultimate.s_bonuses[4]%$%伤害、速度、抗性提升，并强化一部分技能。",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_MENUBOTTOM_DESCRIPTION"] = "释放希尔瓦拉的真正姿态。",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_MENUBOTTOM_NAME"] = "自然本性",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_TITLE"] = "自然本性",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_1"] = "将敌人死亡留下的绿地变为树灵族人，持续战斗%$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[1]%$秒。释放自然本性期间，会召唤更强大的树灵。",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_2"] = "将敌人死亡留下的绿地变为树灵族人，持续战斗%$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[2]%$秒。释放自然本性期间，会召唤更强大的树灵。",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_3"] = "将敌人死亡留下的绿地变为树灵族人，持续战斗%$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[3]%$秒。释放自然本性期间，会召唤更强大的树灵。",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_TITLE"] = "森林呼唤",
["HERO_DRAGON_ARB_CLASS"] = "自然之力",
["HERO_DRAGON_ARB_DESC"] = "她是自然之龙，亦是树灵族的守护者。她用气息编织森林，扇动双翼与风共舞。就像大自然一样，她一面关怀众生，一面严惩罪人。不要在她眼前乱扔垃圾！",
["HERO_DRAGON_ARB_NAME"] = "希尔瓦拉",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_1"] = "每隔%$heroes.hero_dragon_arb.thorn_bleed.cooldown[1]%$秒，希尔瓦拉强化她的下一次攻击，根据敌人的速度提高伤害。释放自然本性期间，有%$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[1]%$%的几率秒杀敌人。",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_2"] = "每隔%$heroes.hero_dragon_arb.thorn_bleed.cooldown[2]%$秒，希尔瓦拉强化她的下一次攻击，根据敌人的速度提高伤害。释放自然本性期间，有%$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[2]%$%的几率秒杀敌人。",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_3"] = "每隔%$heroes.hero_dragon_arb.thorn_bleed.cooldown[3]%$秒，希尔瓦拉强化她的下一次攻击，根据敌人的速度提高伤害。释放自然本性期间，有%$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[3]%$%的几率秒杀敌人。",
["HERO_DRAGON_ARB_THORN BLEED_TITLE"] = "荆棘吐息",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_1"] = "使附近防御塔的伤害增加%$heroes.hero_dragon_arb.tower_runes.s_damage_factor[1]%$%，持续%$heroes.hero_dragon_arb.tower_runes.duration[1]%$秒。",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_2"] = "使附近防御塔的伤害增加%$heroes.hero_dragon_arb.tower_runes.s_damage_factor[2]%$%，持续%$heroes.hero_dragon_arb.tower_runes.duration[2]%$秒。",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_3"] = "使附近防御塔的伤害增加%$heroes.hero_dragon_arb.tower_runes.s_damage_factor[3]%$%，持续%$heroes.hero_dragon_arb.tower_runes.duration[3]%$秒。",
["HERO_DRAGON_ARB_TOWER RUNES_TITLE"] = "根深蒂固",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_1"] = "在防御塔周围召唤出植物，持续%$heroes.hero_dragon_arb.tower_plants.duration[1]%$秒。若防御塔属于黑暗大军将召唤造成伤害并减速的剧毒植物，若防御塔属于利尼维亚将召唤治疗盟友的治愈植物。",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_2"] = "在防御塔周围召唤出植物，持续%$heroes.hero_dragon_arb.tower_plants.duration[2]%$秒。若防御塔属于黑暗大军将召唤造成伤害并减速的剧毒植物，若防御塔属于利尼维亚将召唤治疗盟友的治愈植物。",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_3"] = "在防御塔周围召唤出植物，持续%$heroes.hero_dragon_arb.tower_plants.duration[3]%$秒。若防御塔属于黑暗大军将召唤造成伤害并减速的剧毒植物，若防御塔属于利尼维亚将召唤治疗盟友的治愈植物。",
["HERO_DRAGON_ARB_TOWER_PLANTS_TITLE"] = "创生之种",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_1"] = "发射%$heroes.hero_dragon_bone.burst.proj_count[1]%$颗魔法弹幕, 每颗造成%$heroes.hero_dragon_bone.burst.damage_min[1]%$-%$heroes.hero_dragon_bone.burst.damage_max[1]%$点真实伤害并施加瘟疫效果。",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_2"] = "发射%$heroes.hero_dragon_bone.burst.proj_count[2]%$颗魔法弹幕, 每颗造成%$heroes.hero_dragon_bone.burst.damage_min[2]%$-%$heroes.hero_dragon_bone.burst.damage_max[2]%$点真实伤害并施加瘟疫效果。",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_3"] = "发射%$heroes.hero_dragon_bone.burst.proj_count[3]%$颗魔法弹幕, 每颗造成%$heroes.hero_dragon_bone.burst.damage_min[3]%$-%$heroes.hero_dragon_bone.burst.damage_max[3]%$点真实伤害并施加瘟疫效果。",
["HERO_DRAGON_BONE_BURST_TITLE"] = "爆发感染",
["HERO_DRAGON_BONE_CLASS"] = "巫妖骨龙",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_1"] = "朝一片区域喷吐毒雾，对敌人施加瘟疫效果，并使其减速%$heroes.hero_dragon_bone.cloud.duration[1]%$秒。",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_2"] = "朝一片区域喷吐毒雾，对敌人施加瘟疫效果，并使其减速%$heroes.hero_dragon_bone.cloud.duration[2]%$秒。",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_3"] = "朝一片区域喷吐毒雾，对敌人施加瘟疫效果，并使其减速%$heroes.hero_dragon_bone.cloud.duration[3]%$秒。",
["HERO_DRAGON_BONE_CLOUD_TITLE"] = "瘟神毒雾",
["HERO_DRAGON_BONE_DESC"] = "卫兹南在征战途中解救了波恩哈特，为了偿还人情，波恩哈特心甘情愿献出自己的力量，飞遍大陆，搜寻可能威胁到黑巫师大计的魔法师。",
["HERO_DRAGON_BONE_NAME"] = "波恩哈特",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_1"] = "朝路径猛冲，对敌人造成%$heroes.hero_dragon_bone.nova.damage_min[1]%$-%$heroes.hero_dragon_bone.nova.damage_max[1]%$点爆炸伤害，并施加瘟疫效果。",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_2"] = "朝路径猛冲，对敌人造成%$heroes.hero_dragon_bone.nova.damage_min[2]%$-%$heroes.hero_dragon_bone.nova.damage_max[2]%$点爆炸伤害，并施加瘟疫效果。",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_3"] = "朝路径猛冲，对敌人造成%$heroes.hero_dragon_bone.nova.damage_min[3]%$-%$heroes.hero_dragon_bone.nova.damage_max[3]%$点爆炸伤害，并施加瘟疫效果。",
["HERO_DRAGON_BONE_NOVA_TITLE"] = "疫病灾星",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_1"] = "朝敌人射出%$heroes.hero_dragon_bone.rain.bones_count[1]%$根脊骨，造成%$heroes.hero_dragon_bone.rain.damage_min[1]%$-%$heroes.hero_dragon_bone.rain.damage_max[1]%$点真实伤害，并使其短暂眩晕。",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_2"] = "朝敌人射出%$heroes.hero_dragon_bone.rain.bones_count[2]%$根脊骨，造成%$heroes.hero_dragon_bone.rain.damage_min[2]%$-%$heroes.hero_dragon_bone.rain.damage_max[2]%$点真实伤害，并使其短暂眩晕。",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_3"] = "朝敌人射出%$heroes.hero_dragon_bone.rain.bones_count[3]%$根脊骨，造成%$heroes.hero_dragon_bone.rain.damage_min[3]%$-%$heroes.hero_dragon_bone.rain.damage_max[3]%$点真实伤害，并使其短暂眩晕。",
["HERO_DRAGON_BONE_RAIN_TITLE"] = "脊骨骤雨",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_1"] = "召唤两只小骨龙，每只拥有%$heroes.hero_dragon_bone.ultimate.dog.hp[2]%$点生命值，攻击造成%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[2]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[2]%$点物理伤害。",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_2"] = "召唤两只小骨龙，每只拥有%$heroes.hero_dragon_bone.ultimate.dog.hp[3]%$点生命值，攻击造成%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[3]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[3]%$点物理伤害。",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_3"] = "召唤两只小骨龙，每只拥有%$heroes.hero_dragon_bone.ultimate.dog.hp[4]%$点生命值，攻击造成%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[4]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[4]%$点物理伤害。",
["HERO_DRAGON_BONE_RAISE_DRAKES_MENUBOTTOM_DESCRIPTION"] = "召唤两只小骨龙。",
["HERO_DRAGON_BONE_RAISE_DRAKES_MENUBOTTOM_NAME"] = "化骨为龙",
["HERO_DRAGON_BONE_RAISE_DRAKES_TITLE"] = "化骨为龙",
["HERO_DRAGON_GEM_CLASS"] = "不灭晶龙",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_1"] = "将一名敌人困在水晶中数秒后爆炸，立即杀死目标并对其周围造成%$heroes.hero_dragon_gem.crystal_instakill.s_damage[1]%$点真实伤害。",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_2"] = "将一名敌人困在水晶中数秒后爆炸，立即杀死目标并对其周围造成%$heroes.hero_dragon_gem.crystal_instakill.s_damage[2]%$点真实伤害。",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_3"] = "将一名敌人困在水晶中数秒后爆炸，立即杀死目标并对其周围造成%$heroes.hero_dragon_gem.crystal_instakill.s_damage[3]%$点真实伤害。",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_TITLE"] = "红晶石冢",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_1"] = "朝路上投掷一颗水晶，使敌人减速%$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$%，并且在周围每1秒造成%$heroes.hero_dragon_gem.crystal_totem.s_damage[1]%$点魔法伤害。持续%$heroes.hero_dragon_gem.crystal_totem.duration[1]%$秒。",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_2"] = "朝路上投掷一颗水晶，使敌人减速%$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$%，并且在周围每1秒造成%$heroes.hero_dragon_gem.crystal_totem.s_damage[2]%$点魔法伤害。持续%$heroes.hero_dragon_gem.crystal_totem.duration[2]%$秒。",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_3"] = "朝路上投掷一颗水晶，使敌人减速%$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$%，并且在周围每1秒造成%$heroes.hero_dragon_gem.crystal_totem.s_damage[3]%$点魔法伤害。持续%$heroes.hero_dragon_gem.crystal_totem.duration[3]%$秒。",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_TITLE"] = "能量输导",
["HERO_DRAGON_GEM_DESC"] = "邪教在遗忘峡谷的活动打断了科斯米尔与世隔绝的生活。为了驱逐入侵者，这条晶龙与卫兹南达成协议，加入联盟对抗共同的敌人。",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_1"] = "召唤%$heroes.hero_dragon_gem.ultimate.max_shards[2]%$根水晶锥，对范围内的敌人造成%$heroes.hero_dragon_gem.ultimate.damage_min[2]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[2]%$点真实伤害。",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_2"] = "召唤%$heroes.hero_dragon_gem.ultimate.max_shards[3]%$根水晶锥，对范围内的敌人造成%$heroes.hero_dragon_gem.ultimate.damage_min[3]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[3]%$点真实伤害。",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_3"] = "召唤%$heroes.hero_dragon_gem.ultimate.max_shards[4]%$根水晶锥，对范围内的敌人造成%$heroes.hero_dragon_gem.ultimate.damage_min[4]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[4]%$点真实伤害。",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_MENUBOTTOM_DESCRIPTION"] = "对敌人投掷各种晶锥弹幕。",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_MENUBOTTOM_NAME"] = "水晶崩落",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_TITLE"] = "水晶崩落",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_1"] = "使他周围的路径上长出水晶尖刺，对每一个被击中的敌人造成%$heroes.hero_dragon_gem.floor_impact.damage_min[1]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[1]%$点物理伤害。",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_2"] = "使他周围的路径上长出水晶尖刺，对每一个被击中的敌人造成%$heroes.hero_dragon_gem.floor_impact.damage_min[2]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[2]%$点物理伤害。",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_3"] = "使他周围的路径上长出水晶尖刺，对每一个被击中的敌人造成%$heroes.hero_dragon_gem.floor_impact.damage_min[3]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[3]%$点物理伤害。",
["HERO_DRAGON_GEM_FLOOR_IMPACT_TITLE"] = "棱晶碎片",
["HERO_DRAGON_GEM_NAME"] = "科斯米尔",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_1"] = "结晶困住一群敌人，使其眩晕%$heroes.hero_dragon_gem.stun.duration[1]%$秒。",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_2"] = "结晶困住一群敌人，使其眩晕%$heroes.hero_dragon_gem.stun.duration[2]%$秒。",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_3"] = "结晶困住一群敌人，使其眩晕%$heroes.hero_dragon_gem.stun.duration[3]%$秒。",
["HERO_DRAGON_GEM_STUN_TITLE"] = "结晶吐息",
["HERO_HUNTER_BEASTS_DESCRIPTION_1"] = "召唤2只蝙蝠攻击附近的敌人，持续%$heroes.hero_hunter.beasts.duration[1]%$秒，造成%$heroes.hero_hunter.beasts.damage_min[1]%$-%$heroes.hero_hunter.beasts.damage_max[1]%$点物理伤害。每只蝙蝠有一定几率从目标处偷取%$heroes.hero_hunter.beasts.gold_to_steal[1]%$金币。",
["HERO_HUNTER_BEASTS_DESCRIPTION_2"] = "召唤2只蝙蝠攻击附近的敌人，持续%$heroes.hero_hunter.beasts.duration[2]%$秒，造成%$heroes.hero_hunter.beasts.damage_min[2]%$-%$heroes.hero_hunter.beasts.damage_max[2]%$点物理伤害。每只蝙蝠有一定几率从目标处偷取%$heroes.hero_hunter.beasts.gold_to_steal[2]%$金币。",
["HERO_HUNTER_BEASTS_DESCRIPTION_3"] = "召唤2只蝙蝠攻击附近的敌人，持续%$heroes.hero_hunter.beasts.duration[3]%$秒，造成%$heroes.hero_hunter.beasts.damage_min[3]%$-%$heroes.hero_hunter.beasts.damage_max[3]%$点物理伤害。每只蝙蝠有一定几率从目标处偷取%$heroes.hero_hunter.beasts.gold_to_steal[3]%$金币。",
["HERO_HUNTER_BEASTS_TITLE"] = "黄昏血妖",
["HERO_HUNTER_CLASS"] = "银月猎手",
["HERO_HUNTER_DESC"] = "安雅是吸血鬼与知名猎人的后代，她循着父亲的足迹，对抗黑暗中的生物。对邪教徒不懈的追猎使她来到了南方大地，并加入到联盟当中。",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_1"] = "每7次近战攻击造成一次%$heroes.hero_hunter.heal_strike.damage_min[1]%$-%$heroes.hero_hunter.heal_strike.damage_max[1]%$点的真实伤害，并为安雅恢复此次攻击目标最大生命值%$heroes.hero_hunter.heal_strike.heal_factor[1]%$%的血量。",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_2"] = "每7次近战攻击造成一次%$heroes.hero_hunter.heal_strike.damage_min[2]%$-%$heroes.hero_hunter.heal_strike.damage_max[2]%$点的真实伤害，并为安雅恢复此次攻击目标最大生命值%$heroes.hero_hunter.heal_strike.heal_factor[2]%$%的血量。",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_3"] = "每7次近战攻击造成一次%$heroes.hero_hunter.heal_strike.damage_min[3]%$-%$heroes.hero_hunter.heal_strike.damage_max[3]%$点的真实伤害，并为安雅恢复此次攻击目标最大生命值%$heroes.hero_hunter.heal_strike.heal_factor[3]%$%的血量。",
["HERO_HUNTER_HEAL_STRIKE_TITLE"] = "吸血爪击",
["HERO_HUNTER_NAME"] = "安雅",
["HERO_HUNTER_RICOCHET_DESCRIPTION_1"] = "安雅变作迷雾，在%$heroes.hero_hunter.ricochet.s_bounces[1]%$个敌人之间跳跃，对每个敌人造成%$heroes.hero_hunter.ricochet.damage_min[1]%$-%$heroes.hero_hunter.ricochet.damage_max[1]%$点物理伤害。",
["HERO_HUNTER_RICOCHET_DESCRIPTION_2"] = "安雅变作迷雾，在%$heroes.hero_hunter.ricochet.s_bounces[2]%$个敌人之间跳跃，对每个敌人造成%$heroes.hero_hunter.ricochet.damage_min[2]%$-%$heroes.hero_hunter.ricochet.damage_max[2]%$点物理伤害。",
["HERO_HUNTER_RICOCHET_DESCRIPTION_3"] = "安雅变作迷雾，在%$heroes.hero_hunter.ricochet.s_bounces[3]%$个敌人之间跳跃，对每个敌人造成%$heroes.hero_hunter.ricochet.damage_min[3]%$-%$heroes.hero_hunter.ricochet.damage_max[3]%$点物理伤害。",
["HERO_HUNTER_RICOCHET_TITLE"] = "迷雾步伐",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_1"] = "对周围所有敌人射击，对每个敌人造成%$heroes.hero_hunter.shoot_around.s_damage_min[1]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[1]%$的真实伤害。",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_2"] = "对周围所有敌人射击，每秒对每个敌人造成%$heroes.hero_hunter.shoot_around.s_damage_min[2]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[2]%$的真实伤害。",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_3"] = "对周围所有敌人射击，每秒对每个敌人造成%$heroes.hero_hunter.shoot_around.s_damage_min[3]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[3]%$的真实伤害。",
["HERO_HUNTER_SHOOT_AROUND_TITLE"] = "银白风暴",
["HERO_HUNTER_SPIRIT_DESCRIPTION_1"] = "召唤但丁的幻影，每秒造成%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[2]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[2]%$点真实伤害，持续%$heroes.hero_hunter.ultimate.duration%$秒。若安雅的遗体在其附近，将会被复活。",
["HERO_HUNTER_SPIRIT_DESCRIPTION_2"] = "召唤但丁的幻影，每秒造成%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[3]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[3]%$点真实伤害，持续%$heroes.hero_hunter.ultimate.duration%$秒。若安雅的遗体在其附近，将会被复活。",
["HERO_HUNTER_SPIRIT_DESCRIPTION_3"] = "召唤但丁的幻影，每秒造成%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[4]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[4]%$点真实伤害，持续%$heroes.hero_hunter.ultimate.duration%$秒。若安雅的遗体在其附近，将会被复活。",
["HERO_HUNTER_SPIRIT_MENUBOTTOM_DESCRIPTION"] = "召唤但丁的幻影，减速并攻击敌人。",
["HERO_HUNTER_SPIRIT_MENUBOTTOM_NAME"] = "猎人援助",
["HERO_HUNTER_SPIRIT_TITLE"] = "猎人援助",
["HERO_HUNTER_ULTIMATE_ENTITY_NAME"] = "但丁的幻影",
["HERO_LAVA_CLASS"] = "熔融狂怒",
["HERO_LAVA_DESC"] = "脾气火爆、下手凶狠的大家伙，因诡须的活动从沉睡中苏醒。因为不善言辞，喀拉托用拳头说话，冲破敌人的防线，直到将其全部击溃才会冷静下来，再次入睡。",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_1"] = "投掷一颗熔岩球，对敌人造成%$heroes.hero_lava.double_trouble.s_damage[1]%$点爆炸伤害，并生成一只熔岩团，其拥有%$heroes.hero_lava.double_trouble.soldier.hp_max[1]%$点生命值，持续战斗%$heroes.hero_lava.double_trouble.soldier.duration%$秒。",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_2"] = "投掷一颗熔岩球，对敌人造成%$heroes.hero_lava.double_trouble.s_damage[2]%$点爆炸伤害，并生成一只熔岩团，其拥有%$heroes.hero_lava.double_trouble.soldier.hp_max[2]%$点生命值，持续战斗%$heroes.hero_lava.double_trouble.soldier.duration%$秒。",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_3"] = "投掷一颗熔岩球，对敌人造成%$heroes.hero_lava.double_trouble.s_damage[3]%$点爆炸伤害，并生成一只熔岩团，其拥有%$heroes.hero_lava.double_trouble.soldier.hp_max[3]%$点生命值，持续战斗%$heroes.hero_lava.double_trouble.soldier.duration%$秒。",
["HERO_LAVA_DOUBLE_TROUBLE_SOLDIER_NAME"] = "熔岩团",
["HERO_LAVA_DOUBLE_TROUBLE_TITLE"] = "双倍麻烦",
["HERO_LAVA_HOTHEADED_DESCRIPTION_1"] = "喀拉托复活时，使附近防御塔的伤害提升%$heroes.hero_lava.hotheaded.s_damage_factors[1]%$%，持续%$heroes.hero_lava.hotheaded.durations[1]%$秒。",
["HERO_LAVA_HOTHEADED_DESCRIPTION_2"] = "喀拉托复活时，使附近防御塔的伤害提升%$heroes.hero_lava.hotheaded.s_damage_factors[2]%$%，持续%$heroes.hero_lava.hotheaded.durations[2]%$秒。",
["HERO_LAVA_HOTHEADED_DESCRIPTION_3"] = "喀拉托复活时，使附近防御塔的伤害提升%$heroes.hero_lava.hotheaded.s_damage_factors[3]%$%，持续%$heroes.hero_lava.hotheaded.durations[3]%$秒。",
["HERO_LAVA_HOTHEADED_TITLE"] = "起床气",
["HERO_LAVA_NAME"] = "喀拉托",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_1"] = "连续猛击一名敌人，造成%$heroes.hero_lava.temper_tantrum.s_damage_min[1]%$-%$heroes.hero_lava.temper_tantrum.s_damage_max[1]%$点物理伤害，并将目标击晕%$heroes.hero_lava.temper_tantrum.duration[1]%$秒。",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_2"] = "连续猛击一名敌人，造成%$heroes.hero_lava.temper_tantrum.s_damage_min[2]%$-%$heroes.hero_lava.temper_tantrum.s_damage_max[2]%$点物理伤害，并将目标击晕%$heroes.hero_lava.temper_tantrum.duration[2]%$秒。",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_3"] = "连续猛击一名敌人，造成%$heroes.hero_lava.temper_tantrum.s_damage_min[3]%$-%$heroes.hero_lava.temper_tantrum.s_damage_max[3]%$点物理伤害，并将目标击晕%$heroes.hero_lava.temper_tantrum.duration[3]%$秒。",
["HERO_LAVA_TEMPER_TANTRUM_TITLE"] = "暴怒猛击",
["HERO_LAVA_ULTIMATE_DESCRIPTION_1"] = "向路径喷射%$heroes.hero_lava.ultimate.fireball_count[2]%$发熔岩弹，每发对敌人造成%$heroes.hero_lava.ultimate.bullet.s_damage[2]%$点真实伤害，并使其灼烧%$heroes.hero_lava.ultimate.bullet.scorch.duration%$秒。",
["HERO_LAVA_ULTIMATE_DESCRIPTION_2"] = "向路径喷射%$heroes.hero_lava.ultimate.fireball_count[3]%$发熔岩弹，每发对敌人造成%$heroes.hero_lava.ultimate.bullet.s_damage[3]%$点真实伤害，并使其灼烧%$heroes.hero_lava.ultimate.bullet.scorch.duration%$秒。",
["HERO_LAVA_ULTIMATE_DESCRIPTION_3"] = "向路径喷射%$heroes.hero_lava.ultimate.fireball_count[4]%$发熔岩弹，每发对敌人造成%$heroes.hero_lava.ultimate.bullet.s_damage[4]%$点真实伤害，并使其灼烧%$heroes.hero_lava.ultimate.bullet.scorch.duration%$秒。",
["HERO_LAVA_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "朝路径上喷射数发熔岩弹，持续灼烧地面。",
["HERO_LAVA_ULTIMATE_MENUBOTTOM_NAME"] = "愤怒爆发",
["HERO_LAVA_ULTIMATE_TITLE"] = "愤怒爆发",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_1"] = "甩出飞溅的熔岩攻击敌人，每秒造成%$heroes.hero_lava.wild_eruption.s_damage[1]%$点真实伤害，并使其灼烧%$heroes.hero_lava.wild_eruption.duration[1]%$秒。",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_2"] = "甩出飞溅的熔岩攻击敌人，每秒造成%$heroes.hero_lava.wild_eruption.s_damage[2]%$点真实伤害，并使其灼烧%$heroes.hero_lava.wild_eruption.duration[2]%$秒。",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_3"] = "甩出飞溅的熔岩攻击敌人，每秒造成%$heroes.hero_lava.wild_eruption.s_damage[3]%$点真实伤害，并使其灼烧%$heroes.hero_lava.wild_eruption.duration[3]%$秒。",
["HERO_LAVA_WILD_ERUPTION_TITLE"] = "狂野喷发",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_1"] = "召唤%$heroes.hero_lumenir.ultimate.soldier_count[1]%$名光之战士，短暂击晕附近的敌人，并造成%$heroes.hero_lumenir.ultimate.damage_min[1]%$-%$heroes.hero_lumenir.ultimate.damage_max[1]%$点真实伤害。",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_2"] = "召唤%$heroes.hero_lumenir.ultimate.soldier_count[2]%$名光之战士，短暂击晕附近的敌人，并造成%$heroes.hero_lumenir.ultimate.damage_min[2]%$-%$heroes.hero_lumenir.ultimate.damage_max[2]%$点真实伤害。",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_3"] = "召唤%$heroes.hero_lumenir.ultimate.soldier_count[3]%$名光之战士，短暂击晕附近的敌人，并造成%$heroes.hero_lumenir.ultimate.damage_min[3]%$-%$heroes.hero_lumenir.ultimate.damage_max[3]%$点真实伤害。",
["HERO_LUMENIR_ARROW_STORM_MENUBOTTOM_DESCRIPTION"] = "召唤神圣战士与敌人战斗。",
["HERO_LUMENIR_ARROW_STORM_MENUBOTTOM_NAME"] = "光耀凯歌",
["HERO_LUMENIR_ARROW_STORM_TITLE"] = "光耀凯歌",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_1"] = "向附近最强的敌人施放圣光之剑，造成%$heroes.hero_lumenir.celestial_judgement.damage[1]%$点真实伤害，并使其眩晕%$heroes.lumenin.celestial_judgement.stun_duration[1]%$秒。",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_2"] = "向附近最强的敌人施放圣光之剑，造成%$heroes.hero_lumenir.celestial_judgement.damage[2]%$点真实伤害，并使其眩晕%$heroes.lumenin.celestial_judgement.stun_duration[2]%$秒。",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_3"] = "向附近最强的敌人施放圣光之剑，造成%$heroes.hero_lumenir.celestial_judgement.damage[3]%$点真实伤害，并使其眩晕%$heroes.lumenin.celestial_judgement.stun_duration[3]%$秒。",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_TITLE"] = "天国裁决",
["HERO_LUMENIR_CLASS"] = "圣光使者",
["HERO_LUMENIR_DESC"] = "卢米妮尔翱翔于诸界之间，作为正义与决心的化身屹立不倒。她是传说中的光明使者，受到利尼维亚圣骑士们的崇敬，她向他们赐予祝福，赋予他们强大的力量，以助他们在对抗邪恶的战斗中无往不利。",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_1"] = "吐息%$heroes.hero_lumenir.fire_balls.flames_count[1]%$个神圣光球，在路径上移动并对敌人造成伤害。每个光球对每名经过的敌人造成%$heroes.hero_lumenir.fire_balls.flame_damage_min[1]%$-%$heroes.hero_lumenir.fire_balls.flame_damage_max[1]%$点真实伤害。",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_2"] = "吐息%$heroes.hero_lumenir.fire_balls.flames_count[2]%$个神圣光球，在路径上移动并对敌人造成伤害。每个光球对每名经过的敌人造成%$heroes.hero_lumenir.fire_balls.flame_damage_min[2]%$-%$heroes.hero_lumenir.fire_balls.flame_damage_max[2]%$点真实伤害。",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_3"] = "吐息%$heroes.hero_lumenir.fire_balls.flames_count[3]%$个神圣光球，在路径上移动并对敌人造成伤害。每个光球对每名经过的敌人造成%$heroes.hero_lumenir.fire_balls.flame_damage_min[3]%$-%$heroes.hero_lumenir.fire_balls.flame_damage_max[3]%$点真实伤害。",
["HERO_LUMENIR_FIRE_BALLS_TITLE"] = "光辉波动",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_1"] = "召唤一条光明小龙跟随另一位英雄，持续%$heroes.hero_lumenir.mini_dragon.dragon.duration[1]%$秒。每条龙每次攻击造成%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[1]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[1]%$点物理伤害。",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_2"] = "召唤一条光明小龙跟随另一位英雄，持续%$heroes.hero_lumenir.mini_dragon.dragon.duration[2]%$秒。每条龙每次攻击造成%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[2]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[2]%$点物理伤害。",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_3"] = "召唤一条光明小龙跟随另一位英雄，持续%$heroes.hero_lumenir.mini_dragon.dragon.duration[3]%$秒。每条龙每次攻击造成%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[3]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[3]%$点物理伤害。",
["HERO_LUMENIR_MINI_DRAGON_TITLE"] = "光明伙伴",
["HERO_LUMENIR_NAME"] = "卢米妮尔",
["HERO_LUMENIR_SHIELD_DESCRIPTION_1"] = "赋予友方单位一道提供%$heroes.hero_lumenir.shield.armor[1]%$%护甲的护盾，将%$heroes.hero_lumenir.shield.spiked_armor[1]%$%的伤害反射回敌人身上。",
["HERO_LUMENIR_SHIELD_DESCRIPTION_2"] = "赋予友方单位一道提供%$heroes.hero_lumenir.shield.armor[2]%$%护甲的护盾，将%$heroes.hero_lumenir.shield.spiked_armor[2]%$%的伤害反射回敌人身上。",
["HERO_LUMENIR_SHIELD_DESCRIPTION_3"] = "赋予友方单位一道提供%$heroes.hero_lumenir.shield.armor[3]%$%护甲的护盾，将%$heroes.hero_lumenir.shield.spiked_armor[3]%$%的伤害反射回敌人身上。",
["HERO_LUMENIR_SHIELD_TITLE"] = "反伤赐福",
["HERO_MECHA_CLASS"] = "巨械危机",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_1"] = "呼叫一架哥布林飞艇，对目标范围附近的敌人进行轰炸，每次攻击造成%$heroes.hero_mecha.ultimate.ranged_attack.damage_min[2]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[2]%$点真实范围伤害。",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_2"] = "呼叫一架哥布林飞艇，对目标范围附近的敌人进行轰炸，每次攻击造成%$heroes.hero_mecha.ultimate.ranged_attack.damage_min[3]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[3]%$点真实范围伤害。",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_3"] = "呼叫一架哥布林飞艇，对目标范围附近的敌人进行轰炸，每次攻击造成%$heroes.hero_mecha.ultimate.ranged_attack.damage_min[4]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[4]%$点真实范围伤害。",
["HERO_MECHA_DEATH_FROM_ABOVE_MENUBOTTOM_DESCRIPTION"] = "召唤一架哥布林飞艇对范围内敌人进行轰炸。",
["HERO_MECHA_DEATH_FROM_ABOVE_MENUBOTTOM_NAME"] = "死神天降",
["HERO_MECHA_DEATH_FROM_ABOVE_TITLE"] = "死神天降",
["HERO_MECHA_DESC"] = "奥纳格罗源自两位疯狂哥布林发明家的奇思妙想，并以窃取的矮人科技作为基础打造而成，是哥布林战争机器的终极形态，对黑暗军的敌人来说，这是令人畏惧的存在。",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_1"] = "召唤%$heroes.hero_mecha.goblidrones.units%$架无人机攻击敌人，持续%$heroes.hero_mecha.goblidrones.drone.duration[1]%$秒，每次攻击造成%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[1]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[1]%$点物理伤害。",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_2"] = "召唤%$heroes.hero_mecha.goblidrones.units%$架无人机攻击敌人，持续%$heroes.hero_mecha.goblidrones.drone.duration[2]%$秒，每次攻击造成%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[2]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[2]%$点物理伤害。",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_3"] = "召唤%$heroes.hero_mecha.goblidrones.units%$架无人机攻击敌人，持续%$heroes.hero_mecha.goblidrones.drone.duration[3]%$秒，每次攻击造成%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[3]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[3]%$点物理伤害。",
["HERO_MECHA_GOBLIDRONES_TITLE"] = "哥布林无人机",
["HERO_MECHA_MINE_DROP_DESCRIPTION_1"] = "静止时，机甲会定期在路径上安放至多%$heroes.hero_mecha.mine_drop.max_mines[1]%$枚爆炸地雷。每枚地雷的爆炸会造成%$heroes.hero_mecha.mine_drop.damage_min[1]%$-%$heroes.hero_mecha.mine_drop.damage_max[1]%$点爆炸伤害。",
["HERO_MECHA_MINE_DROP_DESCRIPTION_2"] = "静止时，机甲会定期在路径上安放至多%$heroes.hero_mecha.mine_drop.max_mines[2]%$枚爆炸地雷。每枚地雷的爆炸会造成%$heroes.hero_mecha.mine_drop.damage_min[2]%$-%$heroes.hero_mecha.mine_drop.damage_max[2]%$点爆炸伤害。",
["HERO_MECHA_MINE_DROP_DESCRIPTION_3"] = "静止时，机甲会定期在路径上安放至多%$heroes.hero_mecha.mine_drop.max_mines[3]%$枚爆炸地雷。每枚地雷的爆炸会造成%$heroes.hero_mecha.mine_drop.damage_min[3]%$-%$heroes.hero_mecha.mine_drop.damage_max[3]%$点爆炸伤害。",
["HERO_MECHA_MINE_DROP_TITLE"] = "地雷部署",
["HERO_MECHA_NAME"] = "奥纳格罗",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_1"] = "机甲撞击地面，短暂眩晕附近所有敌人，并造成%$heroes.hero_mecha.power_slam.s_damage[1]%$点物理伤害。",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_2"] = "机甲撞击地面，短暂眩晕附近所有敌人，并造成%$heroes.hero_mecha.power_slam.s_damage[2]%$点物理伤害。",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_3"] = "机甲撞击地面，短暂眩晕附近所有敌人，并造成%$heroes.hero_mecha.power_slam.s_damage[3]%$点物理伤害。",
["HERO_MECHA_POWER_SLAM_TITLE"] = "冲力猛击",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_1"] = "朝路上投掷一颗沥青弹，使敌人减速%$heroes.hero_mecha.tar_bomb.slow_factor%$%, 持续%$heroes.hero_mecha.tar_bomb.duration[1]%$秒。",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_2"] = "朝路上投掷一颗沥青弹，使敌人减速%$heroes.hero_mecha.tar_bomb.slow_factor%$%, 持续%$heroes.hero_mecha.tar_bomb.duration[2]%$秒。",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_3"] = "朝路上投掷一颗沥青弹，使敌人减速%$heroes.hero_mecha.tar_bomb.slow_factor%$%, 持续%$heroes.hero_mecha.tar_bomb.duration[3]%$秒。",
["HERO_MECHA_TAR_BOMB_TITLE"] = "焦油炸弹",
["HERO_MUYRN_CLASS"] = "森林守卫",
["HERO_MUYRN_DESC"] = "尽管外表稚嫩，古灵精怪的尼鲁却已凭借自然之力守护森林数百年之久。为了早日终结日益激烈的入侵，保卫自己的家园，尼鲁加入了联盟。",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_1"] = "对区域内所有敌人施咒，降低其攻击伤害%$heroes.hero_muyrn.faery_dust.s_damage_factor[1]%$%，持续%$heroes.hero_muyrn.faery_dust.duration[1]%$秒。",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_2"] = "对区域内所有敌人施咒，降低其攻击伤害%$heroes.hero_muyrn.faery_dust.s_damage_factor[2]%$%，持续%$heroes.hero_muyrn.faery_dust.duration[2]%$秒。",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_3"] = "对区域内所有敌人施咒，降低其攻击伤害%$heroes.hero_muyrn.faery_dust.s_damage_factor[3]%$%，持续%$heroes.hero_muyrn.faery_dust.duration[3]%$秒。",
["HERO_MUYRN_FAERY_DUST_TITLE"] = "衰弱咒语",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_1"] = "战斗时，尼鲁在自己周围创造一个树叶护盾。护盾每秒造成%$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[1]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[1]%$点魔法伤害，并治疗尼鲁，持续%$heroes.hero_muyrn.leaf_whirlwind.duration[1]%$秒。",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_2"] = "战斗时，尼鲁在自己周围创造一个树叶护盾。护盾每秒造成%$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[2]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[2]%$点魔法伤害，并治疗尼鲁，持续%$heroes.hero_muyrn.leaf_whirlwind.duration[2]%$秒。",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_3"] = "战斗时，尼鲁在自己周围创造一个树叶护盾。护盾每秒造成%$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[3]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[3]%$点魔法伤害，并治疗尼鲁，持续%$heroes.hero_muyrn.leaf_whirlwind.duration[3]%$秒。",
["HERO_MUYRN_LEAF_WHIRLWIND_TITLE"] = "叶片旋风",
["HERO_MUYRN_NAME"] = "尼鲁",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_1"] = "在一定范围内生成树根，持续%$heroes.hero_muyrn.ultimate.duration[2]%$秒，减速敌人，且每秒造成%$heroes.hero_muyrn.ultimate.s_damage_min[2]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[2]%$点真实伤害。",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_2"] = "在一定范围内生成树根，持续%$heroes.hero_muyrn.ultimate.duration[3]%$秒，减速敌人，且每秒造成%$heroes.hero_muyrn.ultimate.s_damage_min[3]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[3]%$点真实伤害。",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_3"] = "在一定范围内生成树根，持续%$heroes.hero_muyrn.ultimate.duration[4]%$秒，减速敌人，且每秒造成%$heroes.hero_muyrn.ultimate.s_damage_min[4]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[4]%$点真实伤害。",
["HERO_MUYRN_ROOT_DEFENDER_MENUBOTTOM_DESCRIPTION"] = "生长出能减速敌人并造成伤害的树根。",
["HERO_MUYRN_ROOT_DEFENDER_MENUBOTTOM_NAME"] = "根系守卫",
["HERO_MUYRN_ROOT_DEFENDER_TITLE"] = "根系守卫",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_1"] = "召唤%$heroes.hero_muyrn.sentinel_wisps.max_summons[1]%$只友善的仙灵跟随尼鲁%$heroes.hero_muyrn.sentinel_wisps.wisp.duration[1]%$秒。仙灵会造成%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[1]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[1]%$点魔法伤害。",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_2"] = "召唤%$heroes.hero_muyrn.sentinel_wisps.max_summons[2]%$只友善的仙灵跟随尼鲁%$heroes.hero_muyrn.sentinel_wisps.wisp.duration[2]%$秒。仙灵会造成%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[2]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[2]%$点魔法伤害。",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_3"] = "召唤%$heroes.hero_muyrn.sentinel_wisps.max_summons[3]%$只友善的仙灵跟随尼鲁%$heroes.hero_muyrn.sentinel_wisps.wisp.duration[3]%$秒。仙灵会造成%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[3]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[3]%$点魔法伤害。",
["HERO_MUYRN_SENTINEL_WISPS_TITLE"] = "哨兵仙灵",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_1"] = "向敌人发射一束绿色能量，造成%$heroes.hero_muyrn.verdant_blast.s_damage[1]%$点魔法伤害。",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_2"] = "向敌人发射一束绿色能量，造成%$heroes.hero_muyrn.verdant_blast.s_damage[2]%$点魔法伤害。",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_3"] = "向敌人发射一束绿色能量，造成%$heroes.hero_muyrn.verdant_blast.s_damage[3]%$点魔法伤害。",
["HERO_MUYRN_VERDANT_BLAST_TITLE"] = "翠绿迸发",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_1"] = "用她的剑残忍地攻击敌人，造成%$heroes.hero_raelyn.brutal_slash.s_damage[1]%$点真实伤害。",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_2"] = "用她的剑残忍地攻击敌人，造成%$heroes.hero_raelyn.brutal_slash.s_damage[2]%$点真实伤害。",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_3"] = "用她的剑残忍地攻击敌人，造成%$heroes.hero_raelyn.brutal_slash.s_damage[3]%$点真实伤害。",
["HERO_RAELYN_BRUTAL_SLASH_TITLE"] = "残酷斩击",
["HERO_RAELYN_CLASS"] = "黑暗中尉",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_1"] = "召唤一名黑暗骑士，其拥有%$heroes.hero_raelyn.ultimate.entity.hp_max[2]%$点生命值，攻击可造成%$heroes.hero_raelyn.ultimate.entity.damage_min[2]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[2]%$点真实伤害。",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_2"] = "召唤一名黑暗骑士，其拥有%$heroes.hero_raelyn.ultimate.entity.hp_max[3]%$点生命值，攻击可造成%$heroes.hero_raelyn.ultimate.entity.damage_min[3]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[3]%$点真实伤害。",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_3"] = "召唤一名黑暗骑士，其拥有%$heroes.hero_raelyn.ultimate.entity.hp_max[4]%$点生命值，攻击可造成%$heroes.hero_raelyn.ultimate.entity.damage_min[4]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[4]%$点真实伤害。",
["HERO_RAELYN_COMMAND_ORDERS_MENUBOTTOM_DESCRIPTION"] = "召唤一名黑暗骑士到战场上。",
["HERO_RAELYN_COMMAND_ORDERS_MENUBOTTOM_NAME"] = "指挥号令",
["HERO_RAELYN_COMMAND_ORDERS_TITLE"] = "指挥号令",
["HERO_RAELYN_DESC"] = "威风凛凛的蕾琳生来就是为了率领黑暗骑士冲锋陷阵。她的残酷与不屈赢得了卫兹南的认可，也令利尼维亚人闻风丧胆。好勇斗狠的她成为了第一位自愿加入黑暗巫师麾下的人。",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_1"] = "使附近敌人眩晕%$heroes.hero_raelyn.inspire_fear.stun_duration[1]%$秒，并降低其攻击伤害%$heroes.hero_raelyn.inspire_fear .s_inflicted_damage_factor[1]%$%，持续%$heroes.hero_raelyn.inspire_fear.damage_duration[1]%$秒。",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_2"] = "使附近敌人眩晕%$heroes.hero_raelyn.inspire_fear.stun_duration[2]%$秒，并降低其攻击伤害%$heroes.hero_raelyn.inspire_fear .s_inflicted_damage_factor[2]%$%，持续%$heroes.hero_raelyn.inspire_fear.damage_duration[2]%$秒。",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_3"] = "使附近敌人眩晕%$heroes.hero_raelyn.inspire_fear.stun_duration[3]%$秒，并降低其攻击伤害%$heroes.hero_raelyn.inspire_fear .s_inflicted_damage_factor[3]%$%，持续%$heroes.hero_raelyn.inspire_fear.damage_duration[3]%$秒。",
["HERO_RAELYN_INSPIRE_FEAR_TITLE"] = "闻风丧胆",
["HERO_RAELYN_NAME"] = "蕾琳",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_1"] = "在%$heroes.hero_raelyn.onslaught.duration[1]%$秒内，蕾琳的攻击速度加快，并对攻击目标周围小范围内的敌人造成她攻击力%$heroes.hero_raelyn.onslaught.damage_factor[1]%$%的伤害。",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_2"] = "在%$heroes.hero_raelyn.onslaught.duration[2]%$秒内，蕾琳的攻击速度加快，并对攻击目标周围小范围内的敌人造成她攻击力%$heroes.hero_raelyn.onslaught.damage_factor[2]%$%的伤害。",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_3"] = "在%$heroes.hero_raelyn.onslaught.duration[3]%$秒内，蕾琳的攻击速度加快，并对攻击目标周围小范围内的敌人造成她攻击力%$heroes.hero_raelyn.onslaught.damage_factor[3]%$%的伤害。",
["HERO_RAELYN_ONSLAUGHT_TITLE"] = "全力猛攻",
["HERO_RAELYN_ULTIMATE_ENTITY_NAME"] = "黑暗骑士",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_1"] = "蕾琳会在战斗中根据附近敌人的数量生成生命护盾（每有一名敌人，将生成她总生命值%$heroes.hero_raelyn.unbreakable.shield_per_enemy[1]%$%的护盾，上限%$heroes.hero_raelyn.unbreakable .max_targets%$名敌人）",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_2"] = "蕾琳会在战斗中根据附近敌人的数量生成生命护盾（每有一名敌人，将生成她总生命值%$heroes.hero_raelyn.unbreakable.shield_per_enemy[2]%$%的护盾，上限%$heroes.hero_raelyn.unbreakable .max_targets%$名敌人）",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_3"] = "蕾琳会在战斗中根据附近敌人的数量生成生命护盾（每有一名敌人，将生成她总生命值%$heroes.hero_raelyn.unbreakable.shield_per_enemy[3]%$%的护盾，上限%$heroes.hero_raelyn.unbreakable .max_targets%$名敌人）",
["HERO_RAELYN_UNBREAKABLE_TITLE"] = "坚不可摧",
["HERO_ROBOT_CLASS"] = "攻城魔像",
["HERO_ROBOT_DESC"] = "黑暗大军的锻造大师们突破极限，创造出一台名为“战争巨头”的战争机械。这台由炽热引擎驱动的无情机器在战场上横扫一切，无论友军还是敌人都不会放在眼里。",
["HERO_ROBOT_EXPLODE_DESCRIPTION_1"] = "制造一阵爆炸，对敌人造成%$heroes.hero_robot.explode.damage_min[1]%$-%$heroes.hero_robot.explode.damage_max[1]%$点爆炸伤害，并使他们燃烧%$heroes.hero_robot.explode.burning_duration%$秒。燃烧每秒造成%$heroes.hero_robot.explode.burning_duration%$点伤害。",
["HERO_ROBOT_EXPLODE_DESCRIPTION_2"] = "制造一阵爆炸，对敌人造成%$heroes.hero_robot.explode.damage_min[2]%$-%$heroes.hero_robot.explode.damage_max[2]%$点爆炸伤害，并使他们燃烧%$heroes.hero_robot.explode.burning_duration%$秒。燃烧每秒造成%$heroes.hero_robot.explode.burning_duration%$点伤害。",
["HERO_ROBOT_EXPLODE_DESCRIPTION_3"] = "制造一阵爆炸，对敌人造成%$heroes.hero_robot.explode.damage_min[3]%$-%$heroes.hero_robot.explode.damage_max[3]%$点爆炸伤害，并使他们燃烧%$heroes.hero_robot.explode.burning_duration%$秒。燃烧每秒造成%$heroes.hero_robot.explode.burning_duration%$点伤害。",
["HERO_ROBOT_EXPLODE_TITLE"] = "战争献祭",
["HERO_ROBOT_FIRE_DESCRIPTION_1"] = "发射装满炽热余烬的炮弹，对敌人造成%$heroes.hero_robot.fire.damage_min[1]%$-%$heroes.hero_robot.fire.damage_max[1]%$点物理伤害，并晕眩敌人%$heroes.hero_robot.fire.s_slow_duration[1]%$秒。",
["HERO_ROBOT_FIRE_DESCRIPTION_2"] = "发射装满炽热余烬的炮弹，对敌人造成%$heroes.hero_robot.fire.damage_min[2]%$-%$heroes.hero_robot.fire.damage_max[2]%$点物理伤害，并晕眩敌人%$heroes.hero_robot.fire.s_slow_duration[1]%$秒。",
["HERO_ROBOT_FIRE_DESCRIPTION_3"] = "发射装满炽热余烬的炮弹，对敌人造成%$heroes.hero_robot.fire.damage_min[3]%$-%$heroes.hero_robot.fire.damage_max[3]%$点物理伤害，并晕眩敌人%$heroes.hero_robot.fire.s_slow_duration[1]%$秒。",
["HERO_ROBOT_FIRE_TITLE"] = "瓦斯烟幕",
["HERO_ROBOT_JUMP_DESCRIPTION_1"] = "跳到空中，砸向一名敌人，使其眩晕%$heroes.hero_robot.jump.stun_duration[1]%$秒，并对范围内造成%$heroes.hero_robot.jump.s_damage[1]%$点物理伤害。",
["HERO_ROBOT_JUMP_DESCRIPTION_2"] = "跳到空中，砸向一名敌人，使其眩晕%$heroes.hero_robot.jump.stun_duration[2]%$秒，并对范围内造成%$heroes.hero_robot.jump.s_damage[2]%$点物理伤害。",
["HERO_ROBOT_JUMP_DESCRIPTION_3"] = "跳到空中，砸向一名敌人，使其眩晕%$heroes.hero_robot.jump.stun_duration[3]%$秒，并对范围内造成%$heroes.hero_robot.jump.s_damage[3]%$点物理伤害。",
["HERO_ROBOT_JUMP_TITLE"] = "震撼冲击",
["HERO_ROBOT_NAME"] = "战争巨头",
["HERO_ROBOT_TRAIN_DESCRIPTION_1"] = "召唤一辆战车沿路径行进，对敌人造成%$heroes.hero_robot.ultimate.s_damage[2]%$点伤害并使其燃烧，持续%$heroes.hero_robot.ultimate.burning_duration%$秒。燃烧每秒造成%$heroes.hero_robot.ultimate.s_burning_damage%$点伤害。",
["HERO_ROBOT_TRAIN_DESCRIPTION_2"] = "召唤一辆战车沿路径行进，对敌人造成%$heroes.hero_robot.ultimate.s_damage[3]%$点伤害并使其燃烧，持续%$heroes.hero_robot.ultimate.burning_duration%$秒。燃烧每秒造成%$heroes.hero_robot.ultimate.s_burning_damage%$点伤害。",
["HERO_ROBOT_TRAIN_DESCRIPTION_3"] = "召唤一辆战车沿路径行进，对敌人造成%$heroes.hero_robot.ultimate.s_damage[4]%$点伤害并使其燃烧，持续%$heroes.hero_robot.ultimate.burning_duration%$秒。燃烧每秒造成%$heroes.hero_robot.ultimate.s_burning_damage%$点伤害。",
["HERO_ROBOT_TRAIN_MENUBOTTOM_DESCRIPTION"] = "召唤一辆战车，辗压敌人。",
["HERO_ROBOT_TRAIN_MENUBOTTOM_NAME"] = "轰鸣火车",
["HERO_ROBOT_TRAIN_TITLE"] = "轰鸣火车",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_1"] = "痛殴一名生命值低于%$heroes.hero_robot.uppercut.s_life_threshold[1]%$%的敌人，一拳毙命。",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_2"] = "痛殴一名生命值低于%$heroes.hero_robot.uppercut.s_life_threshold[2]%$%的敌人，一拳毙命。",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_3"] = "痛殴一名生命值低于%$heroes.hero_robot.uppercut.s_life_threshold[3]%$%的敌人，一拳毙命。",
["HERO_ROBOT_UPPERCUT_TITLE"] = "钢铁勾拳",
["HERO_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_1"] = "此英雄为「巨大的威胁」解锁内容",
["HERO_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_2"] = "此英雄包含在“悟空之旅”战役中。",
["HERO_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_1"] = "巨大的威胁",
["HERO_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_2"] = "悟空之旅战役",
["HERO_ROOM_EQUIPPED_HEROES"] = "已配置英雄",
["HERO_ROOM_GET_DLC"] = "得到它",
["HERO_ROOM_LABEL_ROSTER_THUMB_NEW"] = "新英雄！",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_1"] = "召唤一名赛莉恩的魔法分身，攻击造成%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[1]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[1]%$点魔法伤害。",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_2"] = "召唤一名赛莉恩的魔法分身，攻击造成%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[2]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[2]%$点魔法伤害。",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_3"] = "召唤一名赛莉恩的魔法分身，攻击造成%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[3]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[3]%$点魔法伤害。",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_ENTITY_NAME"] = "星界镜像",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_TITLE"] = "星界镜像",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_1"] = "为一名友军单位施加护盾，最多可承受%$heroes.hero_space_elf.black_aegis.shield_base[1]%$点伤害。护盾破碎或效果结束时爆炸，在一定范围内造成%$heroes.hero_space_elf.black_aegis.explosion_damage[1]%$点魔法伤害。",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_2"] = "为一名友军单位施加护盾，最多可承受%$heroes.hero_space_elf.black_aegis.shield_base[2]%$点伤害。护盾破碎或效果结束时爆炸，在一定范围内造成%$heroes.hero_space_elf.black_aegis.explosion_damage[2]%$点魔法伤害。",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_3"] = "为一名友军单位施加护盾，最多可承受%$heroes.hero_space_elf.black_aegis.shield_base[3]%$点伤害。护盾破碎或效果结束时爆炸，在一定范围内造成%$heroes.hero_space_elf.black_aegis.explosion_damage[3]%$点魔法伤害。",
["HERO_SPACE_ELF_BLACK_AEGIS_TITLE"] = "黑曜庇护",
["HERO_SPACE_ELF_CLASS"] = "虚空法师",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_1"] = "将一群敌人困在虚空中%$heroes.hero_space_elf.ultimate.duration[2]%$秒，并造成%$heroes.hero_space_elf.ultimate.damage[2]%$点伤害。",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_2"] = "将一群敌人困在虚空中%$heroes.hero_space_elf.ultimate.duration[3]%$秒，并造成%$heroes.hero_space_elf.ultimate.damage[3]%$点伤害。",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_3"] = "将一群敌人困在虚空中%$heroes.hero_space_elf.ultimate.duration[4]%$秒，并造成%$heroes.hero_space_elf.ultimate.damage[4]%$点伤害。",
["HERO_SPACE_ELF_COSMIC_PRISON_MENUBOTTOM_DESCRIPTION"] = "困住一定范围内的敌人，造成伤害。",
["HERO_SPACE_ELF_COSMIC_PRISON_MENUBOTTOM_NAME"] = "异域囚笼",
["HERO_SPACE_ELF_COSMIC_PRISON_TITLE"] = "异域囚笼",
["HERO_SPACE_ELF_DESC"] = "多年来，虚空法师赛莉恩因涉足未知的异世界力量而被同伴们排斥。如今，她成为了联盟了解全视之魔眼和其他来自这个世界之外的势力的重要渠道。",
["HERO_SPACE_ELF_NAME"] = "赛莉恩",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_1"] = "扭曲所有防御塔周围的空间%$heroes.hero_space_elf.spatial_distortion.duration[1]%$秒，增加防御塔%$heroes.hero_space_elf.spatial_distortion.s_range_factor[1]%$%攻击范围。",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_2"] = "扭曲所有防御塔周围的空间%$heroes.hero_space_elf.spatial_distortion.duration[2]%$秒，增加防御塔%$heroes.hero_space_elf.spatial_distortion.s_range_factor[2]%$%攻击范围。",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_3"] = "扭曲所有防御塔周围的空间%$heroes.hero_space_elf.spatial_distortion.duration[3]%$秒，增加防御塔%$heroes.hero_space_elf.spatial_distortion.s_range_factor[3]%$%攻击范围。",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_TITLE"] = "空间畸变",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_1"] = "在路径上打开%$heroes.hero_space_elf.void_rift.cracks_amount[1]%$道裂口，持续%$heroes.hero_space_elf.void_rift.duration[1]%$秒，对每一名经过的敌人每秒造成%$heroes.hero_space_elf.void_rift.damage_min[1]%$-%$heroes.hero_space_elf.void_rift.damage_max[1]%$点伤害。",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_2"] = "在路径上打开%$heroes.hero_space_elf.void_rift.cracks_amount[2]%$道裂口，持续%$heroes.hero_space_elf.void_rift.duration[2]%$秒，对每一名经过的敌人每秒造成%$heroes.hero_space_elf.void_rift.damage_min[2]%$-%$heroes.hero_space_elf.void_rift.damage_max[2]%$点伤害。",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_3"] = "在路径上打开%$heroes.hero_space_elf.void_rift.cracks_amount[3]%$道裂口，持续%$heroes.hero_space_elf.void_rift.duration[3]%$秒，对每一名经过的敌人每秒造成%$heroes.hero_space_elf.void_rift.damage_min[3]%$-%$heroes.hero_space_elf.void_rift.damage_max[3]%$点伤害。",
["HERO_SPACE_ELF_VOID_RIFT_TITLE"] = "虚空裂缝",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_1"] = "召唤%$heroes.hero_spider.ultimate.spawn_amount[2]%$只蜘蛛，持续战斗%$heroes.hero_spider.ultimate.spider.duration[2]%$秒，蜘蛛攻击敌人时使其眩晕。",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_2"] = "召唤%$heroes.hero_spider.ultimate.spawn_amount[3]%$只蜘蛛，持续战斗%$heroes.hero_spider.ultimate.spider.duration[3]%$秒，蜘蛛攻击敌人时使其眩晕。",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_3"] = "召唤%$heroes.hero_spider.ultimate.spawn_amount[4]%$只蜘蛛，持续战斗%$heroes.hero_spider.ultimate.spider.duration[4]%$秒，蜘蛛攻击敌人时使其眩晕。",
["HERO_SPIDER_ARACNID_SPAWNER_MENUBOTTOM_DESCRIPTION"] = "召唤一群能使敌人眩晕的蜘蛛。",
["HERO_SPIDER_ARACNID_SPAWNER_MENUBOTTOM_NAME"] = "猎手呼唤",
["HERO_SPIDER_ARACNID_SPAWNER_TITLE"] = "猎手呼唤",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_1"] = "每%$heroes.hero_spider.area_attack.cooldown[1]%$秒，丝派蒂尔释放威压，使周围的敌人眩晕%$heroes.hero_spider.area_attack.s_stun_time[1]%$秒。",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_2"] = "每%$heroes.hero_spider.area_attack.cooldown[2]%$秒，丝派蒂尔释放威压，使周围的敌人眩晕%$heroes.hero_spider.area_attack.s_stun_time[2]%$秒。",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_3"] = "每%$heroes.hero_spider.area_attack.cooldown[3]%$秒，丝派蒂尔释放威压，使周围的敌人眩晕%$heroes.hero_spider.area_attack.s_stun_time[3]%$秒。",
["HERO_SPIDER_AREA_ATTACK_TITLE"] = "驭蛛威影",
["HERO_SPIDER_DESC"] = "丝派蒂尔隶属暮光精灵的一支猎蛛部队，任务是歼灭蜘蛛女王和其手下的邪教，如今她是最后一名队员。她将暗影魔法与独一无二狩猎技巧结合，成为令整片大陆都闻风丧胆致命刺客。",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_1"] = "每%$heroes.hero_spider.instakill_melee.cooldown[1]%$秒，丝派蒂尔会处决一名眩晕状态且生命值低于%$heroes.hero_spider.instakill_melee.life_threshold[1]%$的敌人。",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_2"] = "每%$heroes.hero_spider.instakill_melee.cooldown[2]%$秒，丝派蒂尔会处决一名眩晕状态且生命值低于%$heroes.hero_spider.instakill_melee.life_threshold[2]%$的敌人。",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_3"] = "每%$heroes.hero_spider.instakill_melee.cooldown[3]%$秒，丝派蒂尔会处决一名眩晕状态且生命值低于%$heroes.hero_spider.instakill_melee.life_threshold[3]%$的敌人。",
["HERO_SPIDER_INSTAKILL_MELEE_TITLE"] = "死亡之握",
["HERO_SPIDER_NAME"] = "丝派蒂尔",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_1"] = "眨眼之间，丝派蒂尔瞬移至生命值最高的敌人，对其造成%$heroes.hero_spider.supreme_hunter.damage_min[1]%$-%$heroes.hero_spider.supreme_hunter.damage_max[1]%$点伤害。",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_2"] = "眨眼之间，丝派蒂尔瞬移至生命值最高的敌人，对其造成%$heroes.hero_spider.supreme_hunter.damage_min[2]%$-%$heroes.hero_spider.supreme_hunter.damage_max[2]%$点伤害。",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_3"] = "眨眼之间，丝派蒂尔瞬移至生命值最高的敌人，对其造成%$heroes.hero_spider.supreme_hunter.damage_min[3]%$-%$heroes.hero_spider.supreme_hunter.damage_max[3]%$点伤害。",
["HERO_SPIDER_SUPREME_HUNTER_TITLE"] = "暗影瞬步",
["HERO_SPIDER_TUNNELING_DESCRIPTION_1"] = "丝派蒂尔破土而出时，造成%$heroes.hero_spider.tunneling.damage_min[1]%$-%$heroes.hero_spider.tunneling.damage_max[1]%$点伤害。",
["HERO_SPIDER_TUNNELING_DESCRIPTION_2"] = "丝派蒂尔破土而出时，造成%$heroes.hero_spider.tunneling.damage_min[2]%$-%$heroes.hero_spider.tunneling.damage_max[2]%$点伤害。",
["HERO_SPIDER_TUNNELING_DESCRIPTION_3"] = "丝派蒂尔破土而出时，造成%$heroes.hero_spider.tunneling.damage_min[3]%$-%$heroes.hero_spider.tunneling.damage_max[3]%$点伤害。",
["HERO_SPIDER_TUNNELING_TITLE"] = "破土奇袭",
["HERO_VENOM_CLASS"] = "污堕杀手",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_1"] = "在一定范围散布粘稠物质，使敌人减速，片刻之后转化为穿刺尖刺，对敌人造成%$heroes.hero_venom.ultimate.s_damage[2]%$点真实伤害。",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_2"] = "在一定范围散布粘稠物质，使敌人减速，片刻之后转化为穿刺尖刺，对敌人造成%$heroes.hero_venom.ultimate.s_damage[3]%$点真实伤害。",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_3"] = "在一定范围散布粘稠物质，使敌人减速，片刻之后转化为穿刺尖刺，对敌人造成%$heroes.hero_venom.ultimate.s_damage[4]%$点真实伤害。",
["HERO_VENOM_CREEPING_DEATH_MENUBOTTOM_DESCRIPTION"] = "在路径上召唤粘稠物质，使敌人减速并受到伤害。",
["HERO_VENOM_CREEPING_DEATH_MENUBOTTOM_NAME"] = "死亡蔓延",
["HERO_VENOM_CREEPING_DEATH_TITLE"] = "死亡蔓延",
["HERO_VENOM_DESC"] = "邪教徒没能把佣兵格里姆森变成恶煞，便将他囚禁起来，任其腐烂。这段痛苦的经历意外赐予了格里姆森变形的能力，他利用这份力量逃出了邪教的控制，并发誓有朝一日必将归来，完成复仇。",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_1"] = "格里姆森吞噬一个生命值低于 %$heroes.hero_venom.eat_enemy.hp_trigger%$% 的敌人，同时恢复自身总生命值的 %$heroes.hero_venom.eat_enemy.regen[1]%$%。",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_2"] = "格里姆森吞噬一个生命值低于 %$heroes.hero_venom.eat_enemy.hp_trigger%$% 的敌人，同时恢复自身总生命值的 %$heroes.hero_venom.eat_enemy.regen[2]%$%。",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_3"] = "格里姆森吞噬一个生命值低于 %$heroes.hero_venom.eat_enemy.hp_trigger%$% 的敌人，同时恢复自身总生命值的 %$heroes.hero_venom.eat_enemy.regen[3]%$%。",
["HERO_VENOM_EAT_ENEMY_TITLE"] = "重塑血肉",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_1"] = "在路径上散布尖刺触须，每根尖刺对附近敌人造成%$heroes.hero_venom.floor_spikes.s_damage[1]%$点真实伤害。",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_2"] = "在路径上散布尖刺触须，每根尖刺对附近敌人造成%$heroes.hero_venom.floor_spikes.s_damage[2]%$点真实伤害。",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_3"] = "在路径上散布尖刺触须，每根尖刺对附近敌人造成%$heroes.hero_venom.floor_spikes.s_damage[3]%$点真实伤害。",
["HERO_VENOM_FLOOR_SPIKES_TITLE"] = "致命尖刺",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_1"] = "当格里姆森的生命值低于%$heroes.hero_venom.inner_beast.trigger_hp%$% 时将完全变身，攻击伤害提升%$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[1]%$%，并且每次攻击命中时恢复自身总生命值的%$heroes.hero_venom.inner_beast.basic_melee.regen_health%$%，持续%$heroes.hero_venom.inner_beast.duration%$秒。",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_2"] = "当格里姆森的生命值低于%$heroes.hero_venom.inner_beast.trigger_hp%$% 时将完全变身，攻击伤害提升%$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[2]%$%，并且每次攻击命中时恢复自身总生命值的%$heroes.hero_venom.inner_beast.basic_melee.regen_health%$%，持续%$heroes.hero_venom.inner_beast.duration%$秒。",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_3"] = "当格里姆森的生命值低于%$heroes.hero_venom.inner_beast.trigger_hp%$% 时将完全变身，攻击伤害提升%$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[3]%$%，并且每次攻击命中时恢复自身总生命值的%$heroes.hero_venom.inner_beast.basic_melee.regen_health%$%，持续%$heroes.hero_venom.inner_beast.duration%$秒。",
["HERO_VENOM_INNER_BEAST_TITLE"] = "原始野性",
["HERO_VENOM_NAME"] = "格里姆森",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_1"] = "远程攻击一名敌人，造成%$heroes.hero_venom.ranged_tentacle.s_damage[1]%$点物理伤害，并有%$heroes.hero_venom.ranged_tentacle.bleed_chance[1]%$%机率施加出血效果。出血效果每秒造成%$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$点伤害，持续%$heroes.hero_venom.ranged_tentacle.bleed_duration[1]%$秒。",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_2"] = "远程攻击一名敌人，造成%$heroes.hero_venom.ranged_tentacle.s_damage[2]%$点物理伤害，并有%$heroes.hero_venom.ranged_tentacle.bleed_chance[2]%$%机率施加出血效果。出血效果每秒造成%$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$点伤害，持续%$heroes.hero_venom.ranged_tentacle.bleed_duration[2]%$秒。",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_3"] = "远程攻击一名敌人，造成%$heroes.hero_venom.ranged_tentacle.s_damage[3]%$点物理伤害，并有%$heroes.hero_venom.ranged_tentacle.bleed_chance[3]%$%机率施加出血效果。出血效果每秒造成%$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$点伤害，持续%$heroes.hero_venom.ranged_tentacle.bleed_duration[3]%$秒。",
["HERO_VENOM_RANGED_TENTACLE_TITLE"] = "贯心追猎",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_1"] = "朝一片区域倾泻%$heroes.hero_vesper.ultimate.s_spread[2]%$支箭矢，每支对敌人造成%$heroes.hero_vesper.ultimate.damage[2]%$点物理伤害。",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_2"] = "朝一片区域倾泻%$heroes.hero_vesper.ultimate.s_spread[3]%$支箭矢，每支对敌人造成%$heroes.hero_vesper.ultimate.damage[3]%$点物理伤害。",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_3"] = "朝一片区域倾泻%$heroes.hero_vesper.ultimate.s_spread[4]%$支箭矢，每支对敌人造成%$heroes.hero_vesper.ultimate.damage[4]%$点物理伤害。",
["HERO_VESPER_ARROW_STORM_MENUBOTTOM_DESCRIPTION"] = "朝一片区域内倾泻大量箭矢，对敌人造成伤害。",
["HERO_VESPER_ARROW_STORM_MENUBOTTOM_NAME"] = "箭矢风暴",
["HERO_VESPER_ARROW_STORM_TITLE"] = "箭矢风暴",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_1"] = "射出一支利箭，眩晕敌人%$heroes.hero_vesper.arrow_to_the_knee.stun_duration[1]%$秒，造成%$heroes.hero_vesper.arrow_to_the_knee.s_damage[1]%$点物理伤害。",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_2"] = "射出一支利箭，眩晕敌人%$heroes.hero_vesper.arrow_to_the_knee.stun_duration[2]%$秒，造成%$heroes.hero_vesper.arrow_to_the_knee.s_damage[2]%$点物理伤害。",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_3"] = "射出一支利箭，眩晕敌人%$heroes.hero_vesper.arrow_to_the_knee.stun_duration[3]%$秒，造成%$heroes.hero_vesper.arrow_to_the_knee.s_damage[3]%$点物理伤害。",
["HERO_VESPER_ARROW_TO_THE_KNEE_TITLE"] = "穿膝利箭",
["HERO_VESPER_CLASS"] = "皇家队长",
["HERO_VESPER_DESC"] = "维斯珀精通剑术与箭法，凭此胜任利尼维亚军队指挥官的职位。在利尼维亚沦陷、迪纳斯国王失踪之后，他集结了所有能够召集的部队，开始了一场旨在迎回前任统治者的征途。",
["HERO_VESPER_DISENGAGE_DESCRIPTION_1"] = "当维斯珀的生命值低于%$heroes.hero_vesper.disengage.hp_to_trigger%$%时，会向后跳跃避开下一次近战攻击，接着射出三支箭矢，每支对附近敌人造成%$heroes.hero_vesper.disengage.s_damage[1]%$点物理伤害。",
["HERO_VESPER_DISENGAGE_DESCRIPTION_2"] = "当维斯珀的生命值低于%$heroes.hero_vesper.disengage.hp_to_trigger%$%时，会向后跳跃避开下一次近战攻击，接着射出三支箭矢，每支对附近敌人造成%$heroes.hero_vesper.disengage.s_damage[2]%$点物理伤害。",
["HERO_VESPER_DISENGAGE_DESCRIPTION_3"] = "当维斯珀的生命值低于%$heroes.hero_vesper.disengage.hp_to_trigger%$%时，会向后跳跃避开下一次近战攻击，接着射出三支箭矢，每支对附近敌人造成%$heroes.hero_vesper.disengage.s_damage[3]%$点物理伤害。",
["HERO_VESPER_DISENGAGE_TITLE"] = "金蝉脱壳",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_1"] = "攻击一名敌人三次，造成%$heroes.hero_vesper.martial_flourish.s_damage[1]%$点物理伤害。",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_2"] = "攻击一名敌人三次，造成%$heroes.hero_vesper.martial_flourish.s_damage[2]%$点物理伤害。",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_3"] = "攻击一名敌人三次，造成%$heroes.hero_vesper.martial_flourish.s_damage[3]%$点物理伤害。",
["HERO_VESPER_MARTIAL_FLOURISH_TITLE"] = "武艺绽放",
["HERO_VESPER_NAME"] = "维斯珀",
["HERO_VESPER_RICOCHET_DESCRIPTION_1"] = "射出一支利箭，在%$heroes.hero_vesper.ricochet.s_bounces[1]%$名敌人间弹射，每次造成%$heroes.hero_vesper.ricochet.s_damage[1]%$点物理伤害。",
["HERO_VESPER_RICOCHET_DESCRIPTION_2"] = "射出一支利箭，在%$heroes.hero_vesper.ricochet.s_bounces[2]%$名敌人间弹射，每次造成%$heroes.hero_vesper.ricochet.s_damage[2]%$点物理伤害。",
["HERO_VESPER_RICOCHET_DESCRIPTION_3"] = "射出一支利箭，在%$heroes.hero_vesper.ricochet.s_bounces[3]%$名敌人间弹射，每次造成%$heroes.hero_vesper.ricochet.s_damage[3]%$点物理伤害。",
["HERO_VESPER_RICOCHET_TITLE"] = "弹射箭矢",
["HERO_WITCH_CLASS"] = "捣蛋女巫",
["HERO_WITCH_DESC"] = "斯特蕾吉总喜欢变些不伤人的有趣戏法，给路过仙境森林的人吓一跳。但谁若是威胁到了森林和她的侏儒同胞，立马就会明白平日她顽皮的笑脸只是表象，这位女巫一旦认真起来绝不善罢甘休。",
["HERO_WITCH_DISENGAGE_DESCRIPTION_1"] = "当斯特蕾吉生命值低于%$heroes.hero_witch.disengage.hp_to_trigger%$%时，会向后传送并在原先位置留下一个诱饵。其拥有%$heroes.hero_witch.disengage.decoy.hp_max[1]%$点生命值，被摧毁时爆炸，眩晕敌人%$heroes.hero_witch.disengage.decoy.explotion.stun_duration[1]%$秒。",
["HERO_WITCH_DISENGAGE_DESCRIPTION_2"] = "当斯特蕾吉生命值低于%$heroes.hero_witch.disengage.hp_to_trigger%$%时，会向后传送并在原先位置留下一个诱饵。其拥有%$heroes.hero_witch.disengage.decoy.hp_max[2]%$点生命值，被摧毁时爆炸，眩晕敌人%$heroes.hero_witch.disengage.decoy.explotion.stun_duration[2]%$秒。",
["HERO_WITCH_DISENGAGE_DESCRIPTION_3"] = "当斯特蕾吉生命值低于%$heroes.hero_witch.disengage.hp_to_trigger%$%时，会向后传送并在原先位置留下一个诱饵。其拥有%$heroes.hero_witch.disengage.decoy.hp_max[3]%$点生命值，被摧毁时爆炸，眩晕敌人%$heroes.hero_witch.disengage.decoy.explotion.stun_duration[3]%$秒。",
["HERO_WITCH_DISENGAGE_TITLE"] = "闪光诱饵",
["HERO_WITCH_NAME"] = "斯特蕾吉",
["HERO_WITCH_PATH_AOE_DESCRIPTION_1"] = "朝路径上投掷一大瓶药水，对范围内敌人造成%$heroes.hero_witch.skill_path_aoe.s_damage[1]%$点魔法伤害，并使其减速%$heroes.hero_witch.skill_path_aoe.duration[1]%$秒。",
["HERO_WITCH_PATH_AOE_DESCRIPTION_2"] = "朝路径上投掷一大瓶药水，对范围内敌人造成%$heroes.hero_witch.skill_path_aoe.s_damage[2]%$点魔法伤害，并使其减速%$heroes.hero_witch.skill_path_aoe.duration[2]%$秒。",
["HERO_WITCH_PATH_AOE_DESCRIPTION_3"] = "朝路径上投掷一大瓶药水，对范围内敌人造成%$heroes.hero_witch.skill_path_aoe.s_damage[3]%$点魔法伤害，并使其减速%$heroes.hero_witch.skill_path_aoe.duration[3]%$秒。",
["HERO_WITCH_PATH_AOE_TITLE"] = "咻~啪啦！",
["HERO_WITCH_POLYMORPH_DESCRIPTION_1"] = "将一名敌人变成南瓜%$heroes.hero_witch.skill_polymorph.duration[1]%$秒。",
["HERO_WITCH_POLYMORPH_DESCRIPTION_2"] = "将一名敌人变成南瓜%$heroes.hero_witch.skill_polymorph.duration[2]%$秒。",
["HERO_WITCH_POLYMORPH_DESCRIPTION_3"] = "将一名敌人变成南瓜%$heroes.hero_witch.skill_polymorph.duration[3]%$秒。",
["HERO_WITCH_POLYMORPH_TITLE"] = "变成蔬菜！",
["HERO_WITCH_SOLDIERS_DESCRIPTION_1"] = "召唤%$heroes.hero_witch.skill_soldiers.soldiers_amount[1]%$只黑猫与敌人作战，每只拥有%$heroes.hero_witch.skill_soldiers.soldier.hp_max[1]%$点生命值，其攻击可造成%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[1]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[1]%$点物理伤害。",
["HERO_WITCH_SOLDIERS_DESCRIPTION_2"] = "召唤%$heroes.hero_witch.skill_soldiers.soldiers_amount[2]%$只黑猫与敌人作战，每只拥有%$heroes.hero_witch.skill_soldiers.soldier.hp_max[2]%$点生命值，其攻击可造成%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[2]%$点物理伤害。",
["HERO_WITCH_SOLDIERS_DESCRIPTION_3"] = "召唤%$heroes.hero_witch.skill_soldiers.soldiers_amount[3]%$只黑猫与敌人作战，每只拥有%$heroes.hero_witch.skill_soldiers.soldier.hp_max[3]%$点生命值，其攻击可造成%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[3]%$点物理伤害。",
["HERO_WITCH_SOLDIERS_TITLE"] = "黑夜煞星",
["HERO_WITCH_ULTIMATE_DESCRIPTION_1"] = "将%$heroes.hero_witch.ultimate.max_targets[2]%$名敌人往回传送，传送后使其昏睡%$heroes.hero_witch.ultimate.duration[2]%$秒。",
["HERO_WITCH_ULTIMATE_DESCRIPTION_2"] = "将%$heroes.hero_witch.ultimate.max_targets[3]%$名敌人往回传送，传送后使其昏睡%$heroes.hero_witch.ultimate.duration[3]%$秒。",
["HERO_WITCH_ULTIMATE_DESCRIPTION_3"] = "将%$heroes.hero_witch.ultimate.max_targets[4]%$名敌人往回传送，传送后使其昏睡%$heroes.hero_witch.ultimate.duration[4]%$秒。",
["HERO_WITCH_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "将敌人沿路径往回传送，并昏睡一段时间。",
["HERO_WITCH_ULTIMATE_MENUBOTTOM_NAME"] = "昏昏欲退",
["HERO_WITCH_ULTIMATE_TITLE"] = "昏昏欲退",
["HERO_WUKONG_CLASS"] = "美猴王",
["HERO_WUKONG_DESC"] = "话说那孙悟空本是混沌仙石吸收天地灵气、日月精华而生，有举世无双之力、飞天遁地之技、长生不老之身。如今灵珠失窃，大圣重出江湖，须是夺将回来，以免为时晚矣！",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_1"] = "腾空而起，让金箍棒变为顶天立地般大小，重压一名敌人，将其秒杀并对目标周围造成%$heroes.hero_wukong.giant_staff.area_damage.damage_min[1]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[1]%$点范围伤害。",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_2"] = "腾空而起，让金箍棒变为顶天立地般大小，重压一名敌人，将其秒杀并对目标周围造成%$heroes.hero_wukong.giant_staff.area_damage.damage_min[2]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[2]%$点范围伤害。",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_3"] = "腾空而起，让金箍棒变为顶天立地般大小，重压一名敌人，将其秒杀并对目标周围造成%$heroes.hero_wukong.giant_staff.area_damage.damage_min[3]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[3]%$点范围伤害。",
["HERO_WUKONG_GIANT_STAFF_TITLE"] = "神珍定海",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_1"] = "召唤2只毛猴与孙悟空并肩作战。毛猴攻击造成%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[1]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[1]%$点伤害，持续战斗%$heroes.hero_wukong.hair_clones.soldier.melee_attack.duration[1]%$。",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_2"] = "召唤2只毛猴与孙悟空并肩作战。毛猴攻击造成%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[2]%$点伤害，持续战斗%$heroes.hero_wukong.hair_clones.soldier.melee_attack.duration[2]%$。",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_3"] = "召唤2只毛猴与孙悟空并肩作战。毛猴攻击造成%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[3]%$点伤害，持续战斗%$heroes.hero_wukong.hair_clones.soldier.melee_attack.duration[3]%$。",
["HERO_WUKONG_HAIR_CLONES_TITLE"] = "身外身法",
["HERO_WUKONG_NAME"] = "孙悟空",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_1"] = "将金箍棒掷向空中，变出%$heroes.hero_wukong.pole_ranged.pole_amounts[1]%$根落向敌人，每根造成%$heroes.hero_wukong.pole_ranged.damage_min[1]%$点小范围伤害。",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_2"] = "将金箍棒掷向空中，变出%$heroes.hero_wukong.pole_ranged.pole_amounts[2]%$根落向敌人，每根造成%$heroes.hero_wukong.pole_ranged.damage_min[2]%$点小范围伤害。",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_3"] = "将金箍棒掷向空中，变出%$heroes.hero_wukong.pole_ranged.pole_amounts[3]%$根落向敌人，每根造成%$heroes.hero_wukong.pole_ranged.damage_min[3]%$点小范围伤害。",
["HERO_WUKONG_POLE_RANGED_TITLE"] = "雨落千钧",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_1"] = "小白龙从天而降，猛扎进地面，造成%$heroes.hero_wukong.ultimate.damage_total[2]%$点真实伤害，并留下减速区域。",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_2"] = "小白龙从天而降，猛扎进地面，造成%$heroes.hero_wukong.ultimate.damage_total[3]%$点真实伤害，并留下减速区域。",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_3"] = "小白龙从天而降，猛扎进地面，造成%$heroes.hero_wukong.ultimate.damage_total[4]%$点真实伤害，并留下减速区域。",
["HERO_WUKONG_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "召唤小白龙",
["HERO_WUKONG_ULTIMATE_MENUBOTTOM_NAME"] = "白龙腾渊",
["HERO_WUKONG_ULTIMATE_TITLE"] = "白龙腾渊",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_1"] = "孙悟空的好师弟猪八戒可是走到哪跟到哪！八戒攻击造成%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[1]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[1]%$点伤害，有小概率使出大范围攻击。",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_2"] = "孙悟空的好师弟猪八戒可是走到哪跟到哪！八戒攻击造成%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[2]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[2]%$点伤害，有小概率使出大范围攻击。",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_3"] = "孙悟空的好师弟猪八戒可是走到哪跟到哪！八戒攻击造成%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[3]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[3]%$点伤害，有小概率使出大范围攻击。",
["HERO_WUKONG_ZHU_APPRENTICE_TITLE"] = "八戒师弟",
["HOURS_ABBREVIATION"] = "小时",
["Hero at your command!"] = "英雄听候你的调遣！",
["Heroes are elite units that can face strong enemies and support your forces."] = "英雄是精英单位，能对抗强大的敌人，支援你的军队。",
["Heroes gain experience every time they damage an enemy or use an ability."] = "英雄通过伤害敌人或使用技能获得经验。",
["IAP_CONNECTION_ERROR"] = "无法连接到%@",
["INCOMING NEXT WAVE!"] = "下一波敌人即将到来！",
["INCOMING WAVE"] = "一波敌人即将到来",
["INGAME_BALLOON_BUILD_HERE"] = "造在这！",
["INGAME_BALLOON_GOAL"] = "不要让敌人越过这一地点",
["INGAME_BALLOON_GOLD"] = "通过杀死敌人获得金币",
["INGAME_BALLOON_INCOMING"] = "下一波攻势即将来袭！",
["INGAME_BALLOON_NEW_HERO"] = "新的英雄！",
["INGAME_BALLOON_NEW_POWER"] = "新的能力！",
["INGAME_BALLOON_NOTIFICATION_TAP_HERE"] = "点击这里！",
["INGAME_BALLOON_SELECT_HERO"] = "点击选择！",
["INGAME_BALLOON_START_BATTLE"] = "开始战斗！",
["INGAME_BALLOON_TAP_HERE"] = "点击路径",
["INGAME_BALLOON_TAP_TO_CALL"] = "点击以提前波次",
["INGAME_BALLOON_TAP_TWICE_BUILD"] = "点击两次以建造防御塔",
["INGAME_BALLOON_TAP_TWICE_START"] = "点击两次以开始战斗",
["INGAME_BALLOON_TAP_TWICE_WAVE"] = "点击两次召唤波次",
["INGAME_TUTORIAL1_HELP1"] = "不要让敌人越过这一地点。",
["INGAME_TUTORIAL1_HELP2"] = "建造防御塔守卫路径。",
["INGAME_TUTORIAL1_HELP3"] = "通过杀死敌人获得金币。",
["INGAME_TUTORIAL1_SUBTITLE1"] = "保护你的领地不受敌人的攻击。",
["INGAME_TUTORIAL1_SUBTITLE2"] = "沿着道路建造防御塔来阻止他们。",
["INGAME_TUTORIAL1_TITLE"] = "目标",
["INGAME_TUTORIAL_GOTCHA_1"] = "明白了！",
["INGAME_TUTORIAL_GOTCHA_2"] = "我准备好了，来吧！",
["INGAME_TUTORIAL_HINT"] = "暗示",
["INGAME_TUTORIAL_INSTRUCTIONS"] = "说明",
["INGAME_TUTORIAL_NEW_TIP"] = "新提示",
["INGAME_TUTORIAL_NEXT"] = "下一个！",
["INGAME_TUTORIAL_OK"] = "明白！",
["INGAME_TUTORIAL_SKIP"] = "跳过这个！",
["INGAME_TUTORIAL_TIP_CHALLENGE"] = "警告",
["ITEM_CLUSTER_BOMB_BOTTOM_DESC"] = "就像爆米花一样，没那么好吃，但好玩。",
["ITEM_CLUSTER_BOMB_BOTTOM_INFO"] = "一种能分裂出更小炸弹的炸弹。",
["ITEM_CLUSTER_BOMB_DESC"] = "投掷一枚炸弹，对范围内的敌人造成伤害，并会再分裂出更多小型炸弹。",
["ITEM_CLUSTER_BOMB_NAME"] = "集束炸弹",
["ITEM_DEATHS_TOUCH_BOTTOM_DESC"] = "成为神的感觉真是太棒啦……噢，我是说死神！",
["ITEM_DEATHS_TOUCH_BOTTOM_INFO"] = "选择，点击，杀死。",
["ITEM_DEATHS_TOUCH_DESC"] = "在您的指尖灌注死亡之力，点击任意敌人即可瞬间消灭它们。对Boss或小Boss无效。",
["ITEM_DEATHS_TOUCH_NAME"] = "死亡之触",
["ITEM_LOOT_BOX_BOTTOM_DESC"] = "要多几个这样的宝箱，就可以躺平了。",
["ITEM_LOOT_BOX_BOTTOM_INFO"] = "扔一个箱子到路径上，伤害敌人并立即获得金币。",
["ITEM_LOOT_BOX_DESC"] = "扔一个箱子到路径上，伤害敌人并立即获得300金币。",
["ITEM_LOOT_BOX_NAME"] = "富矿宝箱",
["ITEM_MEDICAL_KIT_BOTTOM_DESC"] = "你只需要包扎一下，将军。",
["ITEM_MEDICAL_KIT_BOTTOM_INFO"] = "为玩家恢复最多3颗心。",
["ITEM_MEDICAL_KIT_DESC"] = "一个特殊装备，可以为玩家恢复最多3颗心。",
["ITEM_MEDICAL_KIT_NAME"] = "医疗包",
["ITEM_PORTABLE_COIL_BOTTOM_DESC"] = "嗖！啪！像烤老鼠一样焦脆！",
["ITEM_PORTABLE_COIL_BOTTOM_INFO"] = "设置一个范围陷阱，伤害并眩晕将其触发的敌人。",
["ITEM_PORTABLE_COIL_DESC"] = "设置一个范围陷阱，伤害并眩晕将其触发的敌人。效果可连锁到周围敌人。",
["ITEM_PORTABLE_COIL_NAME"] = "便携式线圈",
["ITEM_ROOM_EQUIP"] = "装备",
["ITEM_ROOM_EQUIPPED"] = "已装备",
["ITEM_ROOM_EQUIPPED_ITEMS"] = "已装备道具",
["ITEM_SCROLL_OF_SPACESHIFT_BOTTOM_DESC"] = "是否觉得与敌人战斗的时间不够？别担心了！",
["ITEM_SCROLL_OF_SPACESHIFT_BOTTOM_INFO"] = "将一群敌人传送回来时的路径上。",
["ITEM_SCROLL_OF_SPACESHIFT_DESC"] = "将最多10名敌人传送回来时的路径上。",
["ITEM_SCROLL_OF_SPACESHIFT_NAME"] = "空间移动卷轴",
["ITEM_SECOND_BREATH_BOTTOM_DESC"] = "起死回生，但不会像不死族一样。",
["ITEM_SECOND_BREATH_BOTTOM_INFO"] = "复活倒下的英雄，治疗伤员，重置英雄能力的冷却时间。",
["ITEM_SECOND_BREATH_DESC"] = "一个神圣的祝福，可以复活死去的英雄，治疗伤员，重置英雄能力的冷却时间。",
["ITEM_SECOND_BREATH_NAME"] = "重获新生",
["ITEM_SUMMON_BLACKBURN_BOTTOM_DESC"] = "独一无二，无可比拟。",
["ITEM_SUMMON_BLACKBURN_BOTTOM_INFO"] = "召唤强大的布莱克本与你并肩作战。",
["ITEM_SUMMON_BLACKBURN_DESC"] = "召唤强大的战士亡魂来击败你的敌人。",
["ITEM_SUMMON_BLACKBURN_NAME"] = "布莱克本之盔",
["ITEM_VEZNAN_WRATH_BOTTOM_DESC"] = "让他们尝尝黑暗巫师的无限力量吧！",
["ITEM_VEZNAN_WRATH_BOTTOM_INFO"] = "消灭每一个在战场上敌人。",
["ITEM_VEZNAN_WRATH_DESC"] = "卫兹南施放一个强大魔法，消灭每一个在战场上的敌人",
["ITEM_VEZNAN_WRATH_NAME"] = "卫兹南之怒",
["ITEM_WINTER_AGE_BOTTOM_DESC"] = "如果你真的讨厌夏天的话，也可以使用噢。",
["ITEM_WINTER_AGE_BOTTOM_INFO"] = "冻结屏幕上的所有敌人",
["ITEM_WINTER_AGE_DESC"] = "强力法术，能召唤刺骨寒风，将所有敌人冻结数秒。",
["ITEM_WINTER_AGE_NAME"] = "凛冬已至",
["If you enjoy using %@, would you mind taking a moment to rate it? It won't take more than a minute. Thanks for your support!"] = "如果你喜欢使用%@，你介意花一点时间给它评分吗？不会超过一分钟。感谢您的支持！",
["Impossible"] = "不可能",
["Iron Challenge"] = "钢铁挑战",
["KR5_NO_GEMS"] = "你没有足够的宝石。\n你想要买更多吗？",
["KR5_PURCHASE_ERROR"] = "处理您的购买时发生错误。",
["KR5_RATE_US"] = "你喜欢这款游戏吗？请在商店给我们评分！",
["LEVEL_10_HEROIC"] = "英雄模式10",
["LEVEL_10_HISTORY"] = "原来复明教会利用开采的水晶，在峡谷外建造了一个外观诡异的法器。它嗡嗡作响，充满怪异的能量，连周围的空气都显得格外沉重。在我们继续深入之前绝不能留此祸害。",
["LEVEL_10_IRON"] = "铁拳模式10",
["LEVEL_10_IRON_UNLOCK"] = "待定",
["LEVEL_10_MODES_UPGRADES"] = "最高等级5",
["LEVEL_10_TITLE"] = "10.神庙庭院",
["LEVEL_11_HEROIC"] = "英雄模式11",
["LEVEL_11_HISTORY"] = "我们终于走出了峡谷，但前路依旧漫长。现在，我们站在一个嵌满水晶的巨型传送门前，泛视先知魔蒂娅丝已经完成了她的仪式。接下来要面临什么，我们无从知晓，但我们依旧严阵以待。全体戒备！",
["LEVEL_11_IRON"] = "铁拳模式11",
["LEVEL_11_IRON_UNLOCK"] = "待定",
["LEVEL_11_MODES_UPGRADES"] = "最高等级5",
["LEVEL_11_TITLE"] = "11.峡谷高原",
["LEVEL_12_HEROIC"] = "英雄模式12",
["LEVEL_12_HISTORY"] = "我们迎回了迪纳斯国王，并一起穿过传送门踏入未知世界。这个奇异的世界看起来像是利尼维亚的扭曲倒影，但却是满目疮痍。小心脚下，潜伏在黑暗中的东西可比复明教会还要可怕。",
["LEVEL_12_IRON"] = "铁拳模式12",
["LEVEL_12_IRON_UNLOCK"] = "待定",
["LEVEL_12_MODES_UPGRADES"] = "最高等级5",
["LEVEL_12_TITLE"] = "12.枯萎农田",
["LEVEL_13_HEROIC"] = "英雄模式13",
["LEVEL_13_HISTORY"] = "熟悉的暴云寺院的轮廓隐约出现在地平线上。该去的方向非常明确，跟随那股愈发浓重的恶臭与异变前行，我们将找到这一切的源头。我们唯一要做的是在这片不断涌现骇人怪物的土地上幸存下来。",
["LEVEL_13_IRON"] = "铁拳模式13",
["LEVEL_13_IRON_UNLOCK"] = "待定",
["LEVEL_13_MODES_UPGRADES"] = "最高等级5",
["LEVEL_13_TITLE"] = "13.亵渎神庙",
["LEVEL_14_HEROIC"] = "英雄模式14",
["LEVEL_14_HISTORY"] = "这些该死的生物不知是从哪冒出来的！大部队已经身心俱疲，我们触碰到的一切都仿佛有生命，随时准备向我们发动攻击，似乎这整片土地都在尽全力与我们为敌。泛视先知魔蒂娅丝及其爪牙必定就在附近。",
["LEVEL_14_IRON"] = "铁拳模式14",
["LEVEL_14_IRON_UNLOCK"] = "待定",
["LEVEL_14_MODES_UPGRADES"] = "最高等级5",
["LEVEL_14_TITLE"] = "14.腐化山谷",
["LEVEL_15_HEROIC"] = "英雄模式15",
["LEVEL_15_HISTORY"] = "我们成功走出了山谷，现在唯一阻挡在我们与全视之魔眼之间的，就只剩下魔蒂娅丝了。早在山谷中我们就见识过她的能力，但在这里，她拥有自己主人的注视与庇护，势必占据更大的优势。尽管如此，我们仍不会被逆境所阻。振作起来！",
["LEVEL_15_IRON"] = "铁拳模式15",
["LEVEL_15_IRON_UNLOCK"] = "待定",
["LEVEL_15_MODES_UPGRADES"] = "最高等级5",
["LEVEL_15_TITLE"] = "15.恶视魔塔",
["LEVEL_16_HEROIC"] = "英雄模式16",
["LEVEL_16_HISTORY"] = "魔蒂娅丝已不复存在，但更强大的敌人全视之魔眼还在等着我们。这是彻底终结邪教与侵略的最后机会。如果我们不能团结到最后一刻，接下来不管事态如何发展都将毫无意义。开始决战吧！",
["LEVEL_16_IRON"] = "铁拳模式16",
["LEVEL_16_IRON_UNLOCK"] = "待定",
["LEVEL_16_MODES_UPGRADES"] = "最高等级5",
["LEVEL_16_TITLE"] = "16.欲念之巅",
["LEVEL_17_HISTORY"] = "曾经古灵精怪的仙境森林如今充满了不怀好意又毛骨悚然的气息。有消息称成群结队的活尸精灵战士和亡魂正在这片土地游荡，攻击旅人，腐化森林。将军，我们必须深入调查。",
["LEVEL_17_TITLE"] = "17. 迷雾废墟",
["LEVEL_18_HISTORY"] = "深叶哨站传来消息，精灵们快要无法抵御活尸大军的攻势了。我们必须赶快前去支援精灵部队和他们队长——艾利丹，以免为时已晚。等到保住了哨站之后，我们就可以继续前进，直至这场入侵的根源所在。",
["LEVEL_18_TITLE"] = "18. 深叶哨所",
["LEVEL_19_HISTORY"] = "艾利丹已经战至精疲力竭，他指引我们前往堕落神庙，一位号称灵魂掌控者·汎里埃的法师唤醒了活尸大军，企图指挥他们荡平大陆。我们必须不惜一切代价阻止他！",
["LEVEL_19_TITLE"] = "19. 堕落神庙",
["LEVEL_1_HEROIC"] = "英雄模式1",
["LEVEL_1_HISTORY"] = "数月以来，我们在南方森林中搜寻，却始终没能成功找到迪纳斯国王的踪迹。这期间，我们与自然之灵——树灵结为了朋友，并遇到了他们不太友善的邻居——野兽族，它们一见到我们就不断袭击。\n我们必须尽快结束这场战斗，好继续寻找国王。",
["LEVEL_1_IRON"] = "铁拳模式1",
["LEVEL_1_IRON_UNLOCK"] = "皇家弓箭手\n圣骑士殿堂",
["LEVEL_1_MODES_UPGRADES"] = "最高等级1",
["LEVEL_1_TITLE"] = "1.树之海洋",
["LEVEL_20_HISTORY"] = "我们收到了森林边界树灵族的紧急求救，他们正在遭受鳄鱼族的无情袭击，撑不了多久了。请务必要小心，将军。鳄鱼族肚子里可有很多的阴谋诡计。",
["LEVEL_20_TITLE"] = "20. 树灵村落",
["LEVEL_21_HISTORY"] = "村落已经安全了，树灵族告诉我们说，在袭击发生之前，他们就感应到远古的封印已摇摇欲坠，这大概就是鳄鱼族人突然袭击的原因。带着这条线索，我们深入到了沼泽的中心，偶然发现了一个古老的树灵族石阵，它看起来像一个巢穴…某种巨大生物的巢穴。",
["LEVEL_21_TITLE"] = "21. 沉没遗迹",
["LEVEL_22_HISTORY"] = "到达古老的神庙后，我们最担心的事情还是发生了。长久以来保护世界不受侵害，封印着“吞世者，噬界灾鳄”的法阵几乎已经瓦解，仅剩几名绝望的树灵族萨满在勉强维持。将军，阻止噬界灾鳄，否则整个王国都将落入他永不满足的大口之中。",
["LEVEL_22_TITLE"] = "22. 极饿凶谷",
["LEVEL_23_HISTORY"] = "侦察兵报告称，邻近的山脉发生了异常的山体滑坡。进一步调查发现，罪魁祸首是一群陌生的矮人，他们正在山的南面组装一座巨型机器人。将军，请务必前去调查。",
["LEVEL_23_TITLE"] = "23. 暗钢之门",
["LEVEL_24_HISTORY"] = "虽说矮人一直以发明而著称，但这个自称\"暗钢\"的部族对金属的执着已经走火入魔，甚至让博古尔手下的矮人都相形见绌，一炉接着一炉不停地炼造钢铁武装自己。这背后是谁搞的鬼？我们必须找出真相！",
["LEVEL_24_TITLE"] = "24. 狂热组装厂",
["LEVEL_25_HISTORY"] = "正如我们所担心的那样，只有这座大山的内部才能容纳足以打造这等巨大机器人的锻炉。这里到底有多少矮人啊？那么多人在阻挡我们前进，却还有人在不停地锻造和焊接。更诡异的是，他们全都长得一模一样？这其中一定还隐藏着什么不可告人的秘密。",
["LEVEL_25_TITLE"] = "25. 巨型核心",
["LEVEL_26_HISTORY"] = "我们在大山内外不断穿行，来到一个满是大缸的房间，这些缸里的东西可不得了。难怪这群矮人兵力充沛，技艺和样貌也像一个模子刻出来的。他们全都是同一个矮人——诡须！他利用邪恶的科学伎俩源源不断地制造自己的复制体。将军，我们必须阻止这一切！",
["LEVEL_26_TITLE"] = "26. 克隆密室",
["LEVEL_27_HISTORY"] = "暗钢势力在这座山中的阴谋几乎已被我们成功遏止，但如果诡须仍然逍遥法外，一切都将付诸东流。他肯定正在完成最后工作，准备启动机器人的头部。将军，率领部队登上山顶，但愿这次我们能面对真正的诡须本人吧。",
["LEVEL_27_TITLE"] = "27. 统治穹顶",
["LEVEL_28_HISTORY"] = "顺着侦察兵留下的线索，我们发现了那些阴魂不散的邪教徒的踪迹。看来他们又找了个新的神明来拜——一个邪恶的、织网的怪物……邪教徒加蜘蛛？这俩凑一块准没好事！",
["LEVEL_28_TITLE"] = "28. 玷污神庙",
["LEVEL_29_HISTORY"] = "我们越往深处走，就越能清晰地感受到那股恐怖早已在暗处盘踞许久，只待时机成熟发动袭击。这周围蛛网密布，诡异的黑暗直逼脊背，我敢说我们离巢穴的核心不远了。",
["LEVEL_29_TITLE"] = "29. 繁殖室",
["LEVEL_2_HEROIC"] = "英雄模式2",
["LEVEL_2_HISTORY"] = "保持警惕，消息通过仙灵传到了我们这里！森林之心正遭受攻击！我们必须回去帮助树灵。一些黑暗大军的部队会与我们一起加入战场，要时刻保持警惕。目前我们可能同舟共济，但情况随时可能发生改变。",
["LEVEL_2_IRON"] = "铁拳模式2",
["LEVEL_2_IRON_UNLOCK"] = "奥术法师\n三管加农炮",
["LEVEL_2_MODES_UPGRADES"] = "最高等级2",
["LEVEL_2_TITLE"] = "2.守卫之门",
["LEVEL_30_HISTORY"] = "终于，我们到达了他们口中女神的巢穴——一座破败不堪、遗弃已久的神庙，在尘封的历史的重压之下已经摇摇欲坠。世人抛弃的神明确实配得上这般王座。必须彻底消灭这些害虫，一只不留。",
["LEVEL_30_TITLE"] = "30. 遗忘王座",
["LEVEL_31_HISTORY"] = "经历了所有战斗与挣扎，王国终于重归和平。如今，唯一要做的事，就是听着海浪拍打的声音，在等待一位老朋友时玩些棋盘游戏。然而，即使一切看起来如此平静，我仍然想知道这份和平能持续多久……",
["LEVEL_31_TITLE"] = "31. 仙界猴林",
["LEVEL_32_HISTORY"] = "我军追至火山深处，见一荒废古庙，此地曾是供奉烈焰之所。\n然而镇守熔岩深渊的赤炎神龙，本与世无争，如今却躁动不安，戾气冲天。此番异状，定是红孩儿施术惑其心志。与龙相斗，虽凶险万分，然退无可退——诸君，拔剑！",
["LEVEL_32_TITLE"] = "32. 炎龙洞穴",
["LEVEL_33_HISTORY"] = "在与红孩儿一番激战后，我们继续深入暴风岛。刚踏上这片土地，雷电交加的乌云和猛烈的狂风便以奇怪扭曲的方式呼啸而来。但我们别无选择，因为这座岛屿藏有通往公主宫殿的唯一入口。做好准备……风暴来了。",
["LEVEL_33_TITLE"] = "33. 怒风岛",
["LEVEL_34_HISTORY"] = "我们能挺过这些磨难，要感谢公主和她的铁扇。跨过大桥，穿越最猛烈的风暴后，我们如今站在这一切的中心。这个地方依然完好无损—表面宁静而美丽却暗藏危机。我们不能掉以轻心。哪怕是恶魔皇族，也无法阻挡我们。",
["LEVEL_34_TITLE"] = "34. 风暴之眼",
["LEVEL_35_HISTORY"] = "就是现在。牛魔王高踞在他那座固若金汤的堡垒中。带着剩余的部队，我们以力量与智慧正面冲锋。必须在他完全释放宝珠之力前发动攻击。\n为了这片美好土地上你所珍视的一切……我命令你们坚守，联盟！",
["LEVEL_35_TITLE"] = "35. 魔王堡垒",
["LEVEL_3_HEROIC"] = "英雄模式3",
["LEVEL_3_HISTORY"] = "我们及时赶回了森林之心，但野兽族已经突破防线。打起精神，加固阵地！不惜一切代价保护森林之心，否则森林和树灵族必将遭遇灭顶之灾。",
["LEVEL_3_IRON"] = "铁拳模式3",
["LEVEL_3_IRON_UNLOCK"] = "皇家弓箭手\n圣骑士殿堂",
["LEVEL_3_MODES_UPGRADES"] = "最高等级3",
["LEVEL_3_TITLE"] = "3.森林之心",
["LEVEL_4_HEROIC"] = "英雄模式4",
["LEVEL_4_HISTORY"] = "目前森林之心已经安全了，我们必须重新集结，乘胜追击。是时候将战线推向野兽族的领地了。带领部队登上森林的树梢，从制高点寻找它们的营地。",
["LEVEL_4_IRON"] = "铁拳模式4",
["LEVEL_4_IRON_UNLOCK"] = "三管加农炮\n树灵使者",
["LEVEL_4_MODES_UPGRADES"] = "最高等级4",
["LEVEL_4_TITLE"] = "4.翡翠树梢",
["LEVEL_5_HEROIC"] = "英雄模式5",
["LEVEL_5_HISTORY"] = "多亏你占领了制高点，我们才在森林之外的某个古老遗迹中找到了野兽族的营地。请率领部队朝它们的领地进发，同时要警惕它们的埋伏。虽然我们又赢得了一场战斗，但这还远远没有结束。",
["LEVEL_5_IRON"] = "铁拳模式5",
["LEVEL_5_IRON_UNLOCK"] = "奥术法师\n圣骑士殿堂",
["LEVEL_5_MODES_UPGRADES"] = "最高等级5",
["LEVEL_5_TITLE"] = "5.荒废郊区",
["LEVEL_6_HEROIC"] = "英雄模式6",
["LEVEL_6_HISTORY"] = "我们或许在对抗野兽族时占了上风，但仍需面对它们的领袖——血辗。这位自封的野兽之王是个强大的对手，所以不要被他的蠢样所迷惑，否则你将命丧在它的獠牙之下。",
["LEVEL_6_IRON"] = "铁拳模式6",
["LEVEL_6_IRON_UNLOCK"] = "皇家弓箭手\n恶魔熔坑",
["LEVEL_6_MODES_UPGRADES"] = "最高等级5",
["LEVEL_6_TITLE"] = "6.野兽巢穴",
["LEVEL_7_HEROIC"] = "英雄模式7",
["LEVEL_7_HISTORY"] = "阻止野兽族夷平永辉森林后，我们追踪在背后推波助澜的复明教徒，来到一片荒凉之地。我们怀疑这就是他们实施邪恶计划的据点。敌在暗，我在明，必须小心为上……他们似乎有一些不为人知的本事。",
["LEVEL_7_IRON"] = "铁拳模式7",
["LEVEL_7_IRON_UNLOCK"] = "禁用皇家弓箭手",
["LEVEL_7_MODES_UPGRADES"] = "最高等级5",
["LEVEL_7_TITLE"] = "7.冷峻山谷",
["LEVEL_8_HEROIC"] = "英雄模式8",
["LEVEL_8_HISTORY"] = "我们在深入邪教领地时，发现了一系列巨大洞穴，里面充满了与诡异魔法产生共鸣的水晶。复明教会正在开采这些水晶，这无疑是为了充当能量来源。至于目的何在，我们尚不清楚，但干扰开采活动一定能有效地打乱他们的阵脚。",
["LEVEL_8_IRON"] = "铁拳模式8",
["LEVEL_8_IRON_UNLOCK"] = "三管加农炮\n圣骑士殿堂",
["LEVEL_8_MODES_UPGRADES"] = "最高等级5",
["LEVEL_8_TITLE"] = "8.绯红矿坑",
["LEVEL_9_HEROIC"] = "英雄模式9",
["LEVEL_9_HISTORY"] = "这些坑道曲折蜿蜒，令人晕头转向，但我们确信走对了路，因为复明教会的活动愈发频繁。随着我们逐渐深入，各种恐怖景象映入眼帘，这不禁让人怀疑邪教内部的癫狂程度究竟有多深。",
["LEVEL_9_IRON"] = "铁拳模式9",
["LEVEL_9_IRON_UNLOCK"] = "恶魔熔坑\n奥术法师",
["LEVEL_9_MODES_UPGRADES"] = "最高等级5",
["LEVEL_9_TITLE"] = "9.邪恶路口",
["LEVEL_DEFEAT_ADVICE"] = "用宝石购买特殊物品，轰炸敌人！",
["LEVEL_DEFEAT_GEMS_COLLECTED"] = "你收集了",
["LEVEL_DEFEAT_GEMS_COUNT"] = "%i宝石",
["LEVEL_DEFEAT_TITLE"] = "失败！",
["LEVEL_MODE_CAMPAIGN"] = "战役",
["LEVEL_MODE_HEROIC"] = "英雄挑战",
["LEVEL_MODE_HEROIC_DESCRIPTION"] = "这个挑战只为最具英雄气概的防御者准备，面对敌人的精英部队，测验一下你的战术技巧吧！",
["LEVEL_MODE_IRON"] = "钢铁挑战",
["LEVEL_MODE_IRON_DESCRIPTION"] = "钢铁挑战是为终极防御者提供的考验，它将把你的战术技巧提升至极限。",
["LEVEL_SELECT_AVAILABLE_TOWERS"] = "可用防御塔",
["LEVEL_SELECT_CHALLENGE_ONE_ELITE_WAVE"] = "1波精英部队",
["LEVEL_SELECT_CHALLENGE_ONE_LIFE"] = "共1点生命",
["LEVEL_SELECT_CHALLENGE_ONE_WAVE"] = "1波超级部队",
["LEVEL_SELECT_CHALLENGE_RULES"] = "挑战规则",
["LEVEL_SELECT_CHALLENGE_SIX_ELITE_WAVE"] = "6波精英部队",
["LEVEL_SELECT_DIFFICULTY_CASUAL"] = "休闲",
["LEVEL_SELECT_DIFFICULTY_IMPOSSIBLE"] = "不可能",
["LEVEL_SELECT_DIFFICULTY_NORMAL"] = "普通",
["LEVEL_SELECT_DIFFICULTY_VETERAN"] = "老兵",
["LEVEL_SELECT_GAME_MODE"] = "游戏模式",
["LEVEL_SELECT_GET_DLC"] = "得到它",
["LEVEL_SELECT_HELP1"] = "在此选择游戏模式！",
["LEVEL_SELECT_HELP2"] = "选择难度！",
["LEVEL_SELECT_HELP3"] = "开始战斗！",
["LEVEL_SELECT_MODE_LOCKED1"] = "模式已锁定",
["LEVEL_SELECT_MODE_LOCKED2"] = "完成此阶段以解锁此模式。",
["LEVEL_SELECT_TO_BATTLE"] = "进入\n战斗",
["LOADING"] = "正在载入",
["LV22_BOSS_BEFORE_FIGHT_EAT_01"] = "美味!哈哈哈",
["LV22_BOSS_BEFORE_FIGHT_EAT_02"] = "我最讨厌植物了",
["LV22_BOSS_BEFORE_FIGHT_EAT_03"] = "人如其食",
["LV22_BOSS_BEFORE_FIGHT_EAT_04"] = "这一口真清爽",
["LV22_BOSS_BEFORE_FIGHT_EAT_05"] = "撑不住了？",
["LV22_BOSS_BEFORE_FIGHT_EAT_06"] = "我再也饿不着肚子了！",
["LV22_BOSS_BEFORE_FIGHT_EAT_07"] = "多好的一座防御塔，哈哈哈",
["LV22_BOSS_BEFORE_FIGHT_EAT_08"] = "自由的味道",
["LV22_BOSS_INTRO_01"] = "第一餐就拿你们开胃吧。",
["LV22_BOSS_INTRO_02"] = "看着就…生脆",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_01"] = "你只能吃到这些藤蔓！",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_02"] = "植物是我们的伙伴，不是你的口粮！",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_03"] = "你没东西吃了！",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_04"] = "滚回牢里去，怪物！",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_05"] = "汝不可进食！",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_06"] = "我将保护这片森林！",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_07"] = "你不会笑到最后的",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_08"] = "他的力量变强了！快来人！！",
["LV22_MAGE_INTRO_01"] = "闭上你的嘴!",
["LV22_MAGE_INTRO_02"] = "赶紧！我们没法限制他太久！",
["Localization Manager"] = "本地化管理器",
["Log in"] = "登录",
["Log out?"] = "注销？",
["Login Required"] = "登录",
["MAGES’ GUILD"] = "法师公会",
["MAGIC RESISTANT ENEMIES!"] = "魔法抗性敌人！",
["MAP_BALLON_BUY_UPGRADES_DESCRIPTION"] = "用获得的星星改良防御塔和能力！",
["MAP_BALLON_BUY_UPGRADES_TITLE"] = "购买升级！",
["MAP_BALLON_HERO_LEVELUP_DESCRIPTION"] = "用获得的英雄点数来训练英雄！",
["MAP_BALLON_HERO_LEVELUP_TITLE"] = "英雄升级！",
["MAP_BALLON_HERO_UNLOCKED"] = "英雄解锁！",
["MAP_BALLON_START_HERE"] = "从此处开始！",
["MAP_BUTTON_ACHIEVEMENTS"] = "成就",
["MAP_BUTTON_CHALLENGES"] = "挑战",
["MAP_BUTTON_ENCYCLOPEDIA"] = "百科全书",
["MAP_BUTTON_HERO_ROOM"] = "英雄",
["MAP_BUTTON_ITEMS"] = "道具",
["MAP_BUTTON_SHOP"] = "商店",
["MAP_BUTTON_TOWER_ROOM"] = "防御塔",
["MAP_BUTTON_UPGRADES"] = "升级",
["MAP_ENCYCLOPEDIA_STRATEGY_GUIDE"] = "战术指导",
["MAP_ENCYCLOPEDIA_TIPS"] = "提示",
["MAP_HEROROOM_HELP1"] = "选择并训练能力！",
["MAP_HEROROOM_HELP2"] = "点击以继续",
["MAP_HEROROOM_HELP3"] = "提升英雄能力！",
["MAP_HERO_ROOM_GET_IT_NOW"] = "立刻获得！",
["MAP_HERO_ROOM_SELECT"] = "装备",
["MAP_HERO_ROOM_SELECTED"] = "已装备",
["MAP_HERO_ROOM_TRAIN"] = "训练",
["MAP_HERO_ROOM_UNLOCK"] = "第%d關解鎖",
["MAP_HERO_ROOM_UNLOCK_10"] = "第10關解鎖",
["MAP_HERO_ROOM_UNLOCK_14"] = "第14關解鎖",
["MAP_HERO_ROOM_UNLOCK_15"] = "第15關解鎖",
["MAP_HERO_ROOM_UNLOCK_4"] = "第4關解鎖",
["MAP_HERO_ROOM_UNLOCK_7"] = "第7關解鎖",
["MAP_HERO_ROOM_UNLOCK_9"] = "第9關解鎖",
["MAP_HERO_ROOM_UNLOCK_AFTER_CAMPAIGN"] = "游戏结束后解锁。",
["MAP_INAPPS_BUBBLE_INFO_1"] = "进行游戏就能收集宝石。",
["MAP_INAPPS_BUBBLE_INFO_2"] = "使用宝石可以购买特殊物品！",
["MAP_INAPPS_BUBBLE_MORE_GEMS"] = "你需要更多宝石！",
["MAP_INAPPS_BUBBLE_SUCCESSFUL"] = "购买\n成功！",
["MAP_INAPP_GEMS_GEM_SHOP_TITLE"] = "宝石商店",
["MAP_INAPP_GEM_PACK_1"] = "一把宝石",
["MAP_INAPP_GEM_PACK_2"] = "一袋宝石",
["MAP_INAPP_GEM_PACK_3"] = "一桶宝石",
["MAP_INAPP_GEM_PACK_4"] = "一箱宝石",
["MAP_INAPP_GEM_PACK_5"] = "一车宝石",
["MAP_INAPP_GEM_PACK_6"] = "一山宝石",
["MAP_INAPP_GEM_PACK_BAG"] = "一袋宝石",
["MAP_INAPP_GEM_PACK_BARREL"] = "一桶宝石",
["MAP_INAPP_GEM_PACK_CHEST"] = "一箱宝石",
["MAP_INAPP_GEM_PACK_FREE"] = "免费宝石",
["MAP_INAPP_GEM_PACK_HANDFUL"] = "一把宝石",
["MAP_INAPP_GEM_PACK_VAULT"] = "一仓库宝石",
["MAP_INAPP_GEM_PACK_WAGON"] = "一车宝石",
["MAP_INAPP_MORE_GEMS"] = "更多宝石",
["MAP_INAPP_TEXT_1"] = "一把宝石",
["MAP_INAPP_TEXT_2"] = "一袋宝石",
["MAP_INAPP_TEXT_3"] = "一箱宝石",
["MAP_INAPP_TEXT_4"] = "免费宝石",
["MAP_INAPP_TEXT_GEMS"] = "宝石",
["MAP_NEW_GAMEMODE_UNLOCKED_DESCRIPTION"] = "面对无尽的敌人，争夺最高分！",
["MAP_NEW_GAMEMODE_UNLOCKED_TITLE"] = "新的挑战！",
["MAP_NEW_HERO_ALERT"] = "新的英雄！",
["MAP_NEW_TOWER_ALERT"] = "新的防御塔！",
["MAP_TOWER_ROOM_SELECT"] = "装备",
["MAP_TOWER_ROOM_SELECTED"] = "已装备",
["MEDIUM"] = "中等",
["MENU_HUD_WAVES"] = "%i/%i",
["MINUTES_ABBREVIATION"] = "分",
["MORE_GAMES"] = "更多游戏",
["Magic resistant enemies take less damage from mages."] = "身着装甲的敌人受到来自射手、士兵和火炮的伤害较少。",
["NEW"] = "新",
["NEW SPECIAL POWER!"] = "新特殊能力！",
["NEW TOWER UNLOCKED"] = "新防御塔解锁",
["NEW TOWER UPGRADES"] = "新防御塔升级",
["NEWS"] = "消息",
["NEWS_ERROR"] = "无法连接。请查看你的互联网连接，或稍后重试",
["NOTIFICATION_NEW_ENEMY_TITLE"] = "新敌人",
["NOTIFICATION_NEW_SPECIAL_TITLE"] = "新的特殊能力！",
["NOTIFICATION_NEW_TOWERS_SUB_DESCRIPTION"] = "现在你可以把防御塔升级至%d级。",
["NOTIFICATION_NEW_TOWERS_SUB_TITLE"] = "%d级防御塔可用",
["NOTIFICATION_NEW_TOWERS_TITLE"] = "新的防御塔升级",
["NOTIFICATION_NEW_TOWER_TITLE"] = "新的防御塔解锁",
["NOTIFICATION_armored_enemies_desc_body_1"] = "一些敌人会穿着不同强度的护甲，可以帮助他们抵挡非魔法攻击。",
["NOTIFICATION_armored_enemies_desc_body_2"] = "抵抗伤害类型：",
["NOTIFICATION_armored_enemies_desc_body_3"] = "有护甲的敌人受到箭塔、兵营和炮塔的伤害减少。",
["NOTIFICATION_armored_enemies_desc_title"] = "有护甲的敌人！",
["NOTIFICATION_armored_enemies_enemy_name"] = "獠牙斗士",
["NOTIFICATION_bottom_info_desc_body"] = "可随时通过点击一个单位和头像来查看敌人的信息。",
["NOTIFICATION_bottom_info_desc_title"] = "敌人信息",
["NOTIFICATION_bottom_info_tap_portrait_desc"] = "点击此处重新开启。",
["NOTIFICATION_button_ok"] = "好的",
["NOTIFICATION_glare_desc_body"] = "全视之魔眼凝视着战场，用腐化[注目]赋予周围敌人力量",
["NOTIFICATION_glare_desc_bullets"] = "－治疗范围内的敌人\n－触发敌人的特殊技能",
["NOTIFICATION_glare_desc_title"] = "全视之魔眼的注目",
["NOTIFICATION_hero_desc"] = "显示等级、血量和经验。",
["NOTIFICATION_hero_desc_baloon_1"] = "通过点击英雄或其肖像来选择。",
["NOTIFICATION_hero_desc_baloon_2"] = "在路径上点击或拖动即可移动。",
["NOTIFICATION_hero_desc_body_1"] = "英雄是精英单位，可以直面强大的敌人，并支援你的部队。",
["NOTIFICATION_hero_desc_body_2"] = "英雄每次攻击敌人或使用技能都会获得经验值。",
["NOTIFICATION_hero_desc_title"] = "英雄听从你的指挥！",
["NOTIFICATION_magic_resistant_enemies_desc_body_1"] = "一些敌人有着不同等级的魔法抗性，可以帮助他们抵挡魔法攻击。",
["NOTIFICATION_magic_resistant_enemies_desc_body_2"] = "抵抗伤害类型：",
["NOTIFICATION_magic_resistant_enemies_desc_body_3"] = "魔抗敌人受到法师塔的伤害减少。",
["NOTIFICATION_magic_resistant_enemies_desc_title"] = "有魔抗的敌人！",
["NOTIFICATION_magic_resistant_enemies_enemy_name"] = "乌龟萨满",
["NOTIFICATION_rally_point_desc_body_1"] = "你可以调整兵营的集结点，让单位防守不同的区域。",
["NOTIFICATION_rally_point_desc_body_2"] = "选择集结点控制",
["NOTIFICATION_rally_point_desc_body_3"] = "选择你想让士兵前往的位置",
["NOTIFICATION_rally_point_desc_subtitle"] = "集结范围",
["NOTIFICATION_rally_point_desc_title"] = "指挥你的部队！",
["NOTIFICATION_special_desc_body"] = "你可以召唤额外部队支援战场。",
["NOTIFICATION_special_desc_bullets"] = "援军对延缓敌人很有帮助。",
["NOTIFICATION_special_desc_title"] = "召唤援军",
["NOTIFICATION_title_enemy"] = "敌人信息",
["NOTIFICATION_title_glare"] = "新提示！",
["NOTIFICATION_title_hint"] = "英雄解锁",
["NOTIFICATION_title_new_tip"] = "新提示",
["NOTIFICATION_title_special"] = "已解锁特殊能力",
["No"] = "不",
["No, Thanks"] = "不，谢谢",
["None"] = "无",
["Nope"] = "没有",
["Normal"] = "普通",
["OFF!"] = "优惠！",
["OFFERS_END"] = "距特惠结束还有：",
["OFFER_GET_IT_NOW"] = "立刻获得",
["OFFER_GET_THEM_NOW"] = "立刻获得它们",
["OFFER_ICON_BANNER"] = "特惠",
["OFFER_OFF"] = "关闭",
["OFFER_PACK_DESCRIPTION_ALL_HEROES"] = "立刻获得所有英雄！",
["OFFER_PACK_DESCRIPTION_ALL_TOWERS"] = "立刻获得所有防御塔！",
["OFFER_PACK_DESCRIPTION_TEXT_01"] = "独一无二的限时特惠，让你的军队锐不可当！",
["OFFER_PACK_DESCRIPTION_TEXT_02"] = "购买特惠！",
["OFFER_PACK_TIMELEFT_TEXT"] = "距特惠结束还有：",
["OFFER_PACK_TITLE_01"] = "万圣节特惠",
["OFFER_PACK_TITLE_02"] = "黑色星期五特惠",
["OFFER_PACK_TITLE_03"] = "圣诞节特惠",
["OFFER_PACK_TITLE_04"] = "新年特惠",
["OFFER_PACK_TITLE_05"] = "春节特惠",
["OFFER_PACK_TITLE_06"] = "夏日促销特惠",
["OFFER_PACK_TITLE_07"] = "Ironhide 日特惠",
["OFFER_PACK_TITLE_08"] = "新手包特惠",
["OFFER_PACK_TITLE_09"] = "限时特惠",
["OFFER_PACK_TITLE_ALL_HEROES"] = "超大英雄组合包",
["OFFER_PACK_TITLE_ALL_TOWERS"] = "超大防御塔组合包",
["OFFER_PACK_TITLE_STARTER_PACK"] = "新手包特惠",
["OFFER_REGULAR"] = "原价",
["ONE_TIME_OFFER"] = "一次性优惠！",
["ON_SALE"] = "出售",
["OPTIONS"] = "选项",
["OPTIONS_PAGE_CONTROLS"] = "操作设定",
["OPTIONS_PAGE_HELP"] = "操作说明",
["OPTIONS_PAGE_SHORTCUTS"] = "键盘操作说明",
["OPTIONS_PAGE_VIDEO"] = "视频",
["Objective"] = "目标",
["Over 50 stars are recommended to face this stage."] = "推荐拥有 50 颗星星以上再挑战本关。",
["POPUP_CLEAR_PROGRESS_CONFIRM"] = "确定要清除已有进度吗？",
["POPUP_LABEL_MAIN_MENU"] = "主菜单",
["POPUP_SETTINGS_LANGUAGE"] = "语言",
["POPUP_SETTINGS_MUSIC"] = "音乐",
["POPUP_SETTINGS_SFX"] = "音效",
["POPUP_label_error_msg"] = "哎呀！出错了。",
["POPUP_label_error_msg2"] = "哎呀！出错了。",
["POPUP_label_purchasing"] = "正在处理你的请求",
["POPUP_label_title_options"] = "选项",
["POPUP_label_version"] = "0.0.9版本",
["POWER_REINFORCEMENTS_NAME"] = "援军",
["POWER_SUMMON_DESCRIPTION"] = "召唤援军加入战场。",
["POWER_SUMMON_LARGE_DESCRIPTION"] = "你可以在战场上召唤部队前来助阵。\n\n援军是免费的，每隔15秒就能召唤一次。",
["POWER_SUMMON_NAME"] = "援军",
["PRICE_FREE"] = "免费",
["PRIVACY_POLICY_ASK_AGE"] = "你什么时候出生？",
["PRIVACY_POLICY_BUTTON_LINK"] = "隐私政策",
["PRIVACY_POLICY_CONSENT_SHORT"] = "在开始游玩我们的游戏之前，请先确认您（和您的家长，如果您是青少年的话）已经阅读过我们的隐私政策，并知悉其中的内容。",
["PRIVACY_POLICY_LINK"] = "隐私政策",
["PRIVACY_POLICY_WELCOME"] = "欢迎！",
["PROCESSING ITEMS TO RESTORE"] = "正在恢复",
["PROCESSING YOUR REQUEST"] = "正在处理你的请求",
["PURCHASE_PENDING_MESSAGE"] = "购买待处理，将在付款或处理完成后交付。",
["PUSH_NOTIFICATIONS_PERMISSION_RATIONALE"] = "您想收到有关 Ironhide 的产品销售和新游戏的信息吗？",
["Produced by %s"] = "%s 出品",
["QUIT"] = "退出",
["Quit"] = "退出",
["RESTORE"] = "恢复",
["RESTORE_PURCHASES"] = "恢复内购",
["RESTORE_SLOT_ADD_GEMS_TITLE"] = "选择添加宝石的槽位",
["RESTORE_SLOT_PROGRESS_MSG"] = "正在从服务器获取恢复数据…",
["RESTORE_SLOT_STATS_TITLE"] = "统计",
["RESTORE_SLOT_TITLE"] = "选择要替换的插槽",
["Rate %@"] = "给%@评分",
["Remind me later"] = "稍后提醒我",
["SALE_SCREEN_MAP_ROOMS"] = "特卖",
["SECONDS_ABBREVIATION"] = "秒",
["SETTINGS_LANGUAGE"] = "语言",
["SETTINGS_SUPPORT"] = "帮助",
["SHOP_DESKTOP_GET_DLC_BUTTON"] = "得到它",
["SHOP_DESKTOP_TITLE"] = "商店",
["SHOP_ROOM_BEST_VALUE_TITLE"] = "超值",
["SHOP_ROOM_DLC_1_DESCRIPTION"] = "开启新的宏大征程",
["SHOP_ROOM_DLC_1_TITLE"] = "巨大的威胁",
["SHOP_ROOM_DLC_1_TOOLTIP_DESCRIPTION"] = "五个新关卡\n新防御塔\n新英雄\n十余种新敌人\n两场小Boss战\n一场史诗级Boss战\n还有更多...",
["SHOP_ROOM_DLC_1_TOOLTIP_TITLE"] = "巨大的威胁",
["SHOP_ROOM_DLC_2_DESCRIPTION"] = "开启新的宏大征程",
["SHOP_ROOM_DLC_2_TITLE"] = "大圣游记",
["SHOP_ROOM_MOST_POPULAR_TITLE"] = "热门",
["SLOT_CLOUD_DOWNLOADING"] = "正在下载……",
["SLOT_CLOUD_DOWNLOAD_FAILED"] = "从 iCloud 下载已保存游戏失败，请稍后再试。",
["SLOT_CLOUD_DOWNLOAD_SUCCESSFUL"] = "下载成功。",
["SLOT_CLOUD_UPLOADING"] = "正在上传……",
["SLOT_CLOUD_UPLOAD_FAILED"] = "将已保存游戏上传至 iCloud 失败，请稍后再试。",
["SLOT_CLOUD_UPLOAD_ICLOUD_NOT_CONFIGURED"] = "您的设备未设置 iCloud。",
["SLOT_CLOUD_UPLOAD_SUCCESSFUL"] = "上传成功。",
["SLOT_DELETE_SLOT"] = "刪除存檔？",
["SLOT_NAME"] = "存档",
["SLOT_NEW_GAME"] = "新游戏",
["SOLDIER_ARBOREAN_BARRACK_NAME"] = "树灵卫兵",
["SOLDIER_ARBOREAN_SENTINELS_1_NAME"] = "巴鲁",
["SOLDIER_ARBOREAN_SENTINELS_2_NAME"] = "维拉",
["SOLDIER_ARBOREAN_SENTINELS_3_NAME"] = "伊康",
["SOLDIER_ARBOREAN_SENTINELS_4_NAME"] = "哈维",
["SOLDIER_ARBOREAN_SENTINELS_5_NAME"] = "普洛克",
["SOLDIER_ARBOREAN_SENTINELS_6_NAME"] = "古尔德",
["SOLDIER_ARBOREAN_SENTINELS_7_NAME"] = "蒂娜",
["SOLDIER_ARBOREAN_SENTINELS_8_NAME"] = "乌兹基",
["SOLDIER_ARBOREAN_SENTINELS_9_NAME"] = "德鲁",
["SOLDIER_DRAGON_BONE_ULTIMATE_DOG_NAME"] = "小骨龙",
["SOLDIER_EARTH_HOLDER_NAME"] = "石头战士",
["SOLDIER_GHOST_TOWER_NAME"] = "幽灵战士",
["SOLDIER_HERO_BUILDER_WORKER_1_NAME"] = "赫马尔",
["SOLDIER_HERO_BUILDER_WORKER_2_NAME"] = "奥图尔",
["SOLDIER_HERO_BUILDER_WORKER_3_NAME"] = "克鲁斯",
["SOLDIER_HERO_BUILDER_WORKER_4_NAME"] = "比尔柯",
["SOLDIER_HERO_BUILDER_WORKER_5_NAME"] = "劳克",
["SOLDIER_HERO_BUILDER_WORKER_6_NAME"] = "奥尼尔",
["SOLDIER_HERO_BUILDER_WORKER_7_NAME"] = "霍夫斯",
["SOLDIER_HERO_BUILDER_WORKER_8_NAME"] = "伍迪",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL1_NAME"] = "树灵守卫",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL2_NAME"] = "树灵守卫",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL3_NAME"] = "树灵守卫",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL1_NAME"] = "树灵楷模",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL2_NAME"] = "树灵楷模",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL3_NAME"] = "树灵楷模",
["SOLDIER_HERO_SPIDER_ULTIMATE_NAME"] = "小蜘蛛",
["SOLDIER_HERO_WITCH_CAT_1_NAME"] = "柯南",
["SOLDIER_HERO_WITCH_CAT_2_NAME"] = "派派",
["SOLDIER_HERO_WITCH_CAT_3_NAME"] = "笨笨",
["SOLDIER_HERO_WITCH_CAT_4_NAME"] = "绒绒",
["SOLDIER_HERO_WITCH_CAT_5_NAME"] = "枇杷",
["SOLDIER_HERO_WITCH_CAT_6_NAME"] = "华生",
["SOLDIER_HERO_WITCH_CAT_7_NAME"] = "奇米",
["SOLDIER_HERO_WITCH_CAT_8_NAME"] = "小拖",
["SOLDIER_HERO_WITCH_DECOY_NAME"] = "布娃娃",
["SOLDIER_HERO_WUKONG_HAIR_CLONES_1_NAME"] = "孙捂空",
["SOLDIER_HERO_WUKONG_HAIR_CLONES_2_NAME"] = "孙唔空",
["SOLDIER_ITEM_SUMMON_BLACKBURN_NAME"] = "领主布莱克本",
["SOLDIER_PALADINS_10_NAME"] = "乔亚奇姆爵士",
["SOLDIER_PALADINS_11_NAME"] = "安德烈爵士",
["SOLDIER_PALADINS_12_NAME"] = "萨米特爵士",
["SOLDIER_PALADINS_13_NAME"] = "乌多爵士",
["SOLDIER_PALADINS_14_NAME"] = "埃里克爵士",
["SOLDIER_PALADINS_15_NAME"] = "布鲁斯爵士",
["SOLDIER_PALADINS_16_NAME"] = "罗伯爵士",
["SOLDIER_PALADINS_17_NAME"] = "比夫爵士",
["SOLDIER_PALADINS_18_NAME"] = "鲍斯爵士",
["SOLDIER_PALADINS_1_NAME"] = "凯爵士",
["SOLDIER_PALADINS_2_NAME"] = "汉西爵士",
["SOLDIER_PALADINS_3_NAME"] = "卢卡爵士",
["SOLDIER_PALADINS_4_NAME"] = "迪莫爵士",
["SOLDIER_PALADINS_5_NAME"] = "拉尔夫爵士",
["SOLDIER_PALADINS_6_NAME"] = "托拜厄斯爵士",
["SOLDIER_PALADINS_7_NAME"] = "德里斯爵士",
["SOLDIER_PALADINS_8_NAME"] = "基斯科爵士",
["SOLDIER_PALADINS_9_NAME"] = "佩施爵士",
["SOLDIER_PRIESTS_BARRACK_1_NAME"] = "威利",
["SOLDIER_PRIESTS_BARRACK_2_NAME"] = "亨利",
["SOLDIER_PRIESTS_BARRACK_3_NAME"] = "杰弗里",
["SOLDIER_PRIESTS_BARRACK_4_NAME"] = "尼古拉斯",
["SOLDIER_PRIESTS_BARRACK_5_NAME"] = "艾德",
["SOLDIER_PRIESTS_BARRACK_6_NAME"] = "霍布",
["SOLDIER_PRIESTS_BARRACK_7_NAME"] = "奥多",
["SOLDIER_PRIESTS_BARRACK_8_NAME"] = "赛德里克",
["SOLDIER_PRIESTS_BARRACK_9_NAME"] = "哈尔",
["SOLDIER_RANDOM_10_NAME"] = "阿尔维斯",
["SOLDIER_RANDOM_11_NAME"] = "博林",
["SOLDIER_RANDOM_12_NAME"] = "哈德良",
["SOLDIER_RANDOM_13_NAME"] = "托马斯",
["SOLDIER_RANDOM_14_NAME"] = "亨利",
["SOLDIER_RANDOM_15_NAME"] = "布莱斯",
["SOLDIER_RANDOM_16_NAME"] = "洛夫",
["SOLDIER_RANDOM_17_NAME"] = "阿利斯特",
["SOLDIER_RANDOM_18_NAME"] = "阿泰尔",
["SOLDIER_RANDOM_19_NAME"] = "西蒙",
["SOLDIER_RANDOM_1_NAME"] = "道格拉斯",
["SOLDIER_RANDOM_20_NAME"] = "埃格伯特",
["SOLDIER_RANDOM_21_NAME"] = "艾尔登",
["SOLDIER_RANDOM_22_NAME"] = "加勒特",
["SOLDIER_RANDOM_23_NAME"] = "戈德温",
["SOLDIER_RANDOM_24_NAME"] = "戈登",
["SOLDIER_RANDOM_25_NAME"] = "杰拉尔德",
["SOLDIER_RANDOM_26_NAME"] = "开尔文",
["SOLDIER_RANDOM_27_NAME"] = "兰多",
["SOLDIER_RANDOM_28_NAME"] = "马多克斯",
["SOLDIER_RANDOM_29_NAME"] = "佩顿",
["SOLDIER_RANDOM_2_NAME"] = "丹·迈基尔",
["SOLDIER_RANDOM_30_NAME"] = "拉姆齐",
["SOLDIER_RANDOM_31_NAME"] = "雷蒙",
["SOLDIER_RANDOM_32_NAME"] = "罗伯特",
["SOLDIER_RANDOM_33_NAME"] = "索耶",
["SOLDIER_RANDOM_34_NAME"] = "塞拉斯",
["SOLDIER_RANDOM_35_NAME"] = "斯图亚特",
["SOLDIER_RANDOM_36_NAME"] = "坦纳",
["SOLDIER_RANDOM_37_NAME"] = "厄舍",
["SOLDIER_RANDOM_38_NAME"] = "华莱士",
["SOLDIER_RANDOM_39_NAME"] = "韦斯利",
["SOLDIER_RANDOM_3_NAME"] = "詹姆斯·李",
["SOLDIER_RANDOM_40_NAME"] = "威拉德",
["SOLDIER_RANDOM_4_NAME"] = "贾·约翰逊",
["SOLDIER_RANDOM_5_NAME"] = "菲尔",
["SOLDIER_RANDOM_6_NAME"] = "罗宾",
["SOLDIER_RANDOM_7_NAME"] = "威廉",
["SOLDIER_RANDOM_8_NAME"] = "马丁",
["SOLDIER_RANDOM_9_NAME"] = "亚瑟",
["SOLDIER_REINFORCEMENTS_F_1_NAME"] = "阿塔娜",
["SOLDIER_REINFORCEMENTS_F_2_NAME"] = "毛希尔",
["SOLDIER_REINFORCEMENTS_F_3_NAME"] = "古丽卡",
["SOLDIER_REINFORCEMENTS_F_4_NAME"] = "洛伽斯",
["SOLDIER_REINFORCEMENTS_M_10_NAME"] = "伯吉",
["SOLDIER_REINFORCEMENTS_M_1_NAME"] = "加比尼",
["SOLDIER_REINFORCEMENTS_M_2_NAME"] = "欧贝尔",
["SOLDIER_REINFORCEMENTS_M_3_NAME"] = "肯特",
["SOLDIER_REINFORCEMENTS_M_4_NAME"] = "珍达斯",
["SOLDIER_REINFORCEMENTS_M_5_NAME"] = "贾洛斯克",
["SOLDIER_REINFORCEMENTS_M_6_NAME"] = "阿斯顿",
["SOLDIER_REINFORCEMENTS_M_7_NAME"] = "布吉尔",
["SOLDIER_REINFORCEMENTS_M_8_NAME"] = "克莱",
["SOLDIER_REINFORCEMENTS_M_9_NAME"] = "玛古斯",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_1_NAME"] = "丹奇",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_2_NAME"] = "史密斯",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_3_NAME"] = "安德鲁",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_4_NAME"] = "汤普森",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_5_NAME"] = "泰勒",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_1_NAME"] = "麦卡尼",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_2_NAME"] = "麦克莱恩",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_3_NAME"] = "霍普金斯",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_4_NAME"] = "凯恩",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_5_NAME"] = "金斯利",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_10_NAME"] = "毒蛇",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_1_NAME"] = "尖牙",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_2_NAME"] = "利刃",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_3_NAME"] = "利爪",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_4_NAME"] = "魔爪",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_5_NAME"] = "刀锋",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_6_NAME"] = "剃刀",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_7_NAME"] = "镰刀",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_8_NAME"] = "匕首",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_9_NAME"] = "毒刺",
["SOLDIER_REINFORCEMENTS_SPECIAL_DARK_ARMY_1_NAME"] = "暗影唤鸦者",
["SOLDIER_REINFORCEMENTS_SPECIAL_LINIREA_1_NAME"] = "骑士楷模",
["SOLDIER_STAGE_10_YMCA_BIKER_NAME"] = "格伦",
["SOLDIER_STAGE_10_YMCA_CONSTRUCTOR_NAME"] = "大卫",
["SOLDIER_STAGE_10_YMCA_INDIO_NAME"] = "菲利普",
["SOLDIER_STAGE_10_YMCA_POLICIA_NAME"] = "维克多",
["SOLDIER_STAGE_15_DENAS_NAME"] = "迪纳斯国王",
["SOLDIER_TOWER_DARK_ELF_1_NAME"] = "费勒润",
["SOLDIER_TOWER_DARK_ELF_2_NAME"] = "菲尔瑞",
["SOLDIER_TOWER_DARK_ELF_3_NAME"] = "古丽娜",
["SOLDIER_TOWER_DARK_ELF_4_NAME"] = "贾拉丝",
["SOLDIER_TOWER_DARK_ELF_5_NAME"] = "索伦扎",
["SOLDIER_TOWER_DARK_ELF_6_NAME"] = "泰布润",
["SOLDIER_TOWER_DARK_ELF_7_NAME"] = "维尔娜",
["SOLDIER_TOWER_DARK_ELF_8_NAME"] = "泽恩",
["SOLDIER_TOWER_DARK_ELF_9_NAME"] = "伊莱拉",
["SOLDIER_TOWER_DWARF_10_NAME"] = "芭比",
["SOLDIER_TOWER_DWARF_1_NAME"] = "皮皮",
["SOLDIER_TOWER_DWARF_2_NAME"] = "金妮",
["SOLDIER_TOWER_DWARF_3_NAME"] = "梅莉",
["SOLDIER_TOWER_DWARF_4_NAME"] = "洛莉",
["SOLDIER_TOWER_DWARF_5_NAME"] = "塔莉",
["SOLDIER_TOWER_DWARF_6_NAME"] = "丹妮",
["SOLDIER_TOWER_DWARF_7_NAME"] = "盖蒂",
["SOLDIER_TOWER_DWARF_8_NAME"] = "达菲",
["SOLDIER_TOWER_DWARF_9_NAME"] = "比比",
["SOLDIER_TOWER_ELVEN_BARRACK_1_NAME"] = "伊兰迪尔",
["SOLDIER_TOWER_ELVEN_BARRACK_2_NAME"] = "迫克",
["SOLDIER_TOWER_ELVEN_BARRACK_3_NAME"] = "萨斯",
["SOLDIER_TOWER_ELVEN_BARRACK_4_NAME"] = "卡斯托",
["SOLDIER_TOWER_ELVEN_BARRACK_5_NAME"] = "艾尔瑞克",
["SOLDIER_TOWER_ELVEN_BARRACK_6_NAME"] = "伊莱斯",
["SOLDIER_TOWER_NECROMANCER_SKELETON_GOLEM_NAME"] = "碎骨魔像",
["SOLDIER_TOWER_NECROMANCER_SKELETON_NAME"] = "骷髅",
["SOLDIER_TOWER_PANDAS_FEMALE_1_NAME"] = "小燕",
["SOLDIER_TOWER_PANDAS_FEMALE_2_NAME"] = "清照",
["SOLDIER_TOWER_PANDAS_FEMALE_3_NAME"] = "小惠",
["SOLDIER_TOWER_PANDAS_FEMALE_4_NAME"] = "爱玲",
["SOLDIER_TOWER_PANDAS_MALE_1_NAME"] = "阿祖",
["SOLDIER_TOWER_PANDAS_MALE_2_NAME"] = "阿乾",
["SOLDIER_TOWER_PANDAS_MALE_3_NAME"] = "雪芹",
["SOLDIER_TOWER_PANDAS_MALE_4_NAME"] = "耐庵",
["SOLDIER_TOWER_PANDAS_MALE_5_NAME"] = "阿迅",
["SOLDIER_TOWER_PANDAS_MALE_6_NAME"] = "行健",
["SOLDIER_TOWER_PANDAS_MALE_7_NAME"] = "阿伟",
["SOLDIER_TOWER_PANDAS_MALE_8_NAME"] = "阿陈",
["SOLDIER_TOWER_ROCKET_GUNNERS_10_NAME"] = "福尔图斯",
["SOLDIER_TOWER_ROCKET_GUNNERS_1_NAME"] = "埃克索",
["SOLDIER_TOWER_ROCKET_GUNNERS_2_NAME"] = "罗斯",
["SOLDIER_TOWER_ROCKET_GUNNERS_3_NAME"] = "斯拉什",
["SOLDIER_TOWER_ROCKET_GUNNERS_4_NAME"] = "哈德森",
["SOLDIER_TOWER_ROCKET_GUNNERS_5_NAME"] = "伊兹",
["SOLDIER_TOWER_ROCKET_GUNNERS_6_NAME"] = "达夫",
["SOLDIER_TOWER_ROCKET_GUNNERS_7_NAME"] = "阿德勒",
["SOLDIER_TOWER_ROCKET_GUNNERS_8_NAME"] = "迪兹",
["SOLDIER_TOWER_ROCKET_GUNNERS_9_NAME"] = "费勒",
["SOLDIER_ZHU_APPRENTICE_NAME"] = "猪八戒",
["SPECIAL_ARBOREAN_BARRACK_DESCRIPTION"] = "召唤3名树灵卫兵与路上的敌人战斗。",
["SPECIAL_ARBOREAN_BARRACK_NAME"] = "树灵卫队",
["SPECIAL_ARBOREAN_HONEY_DESCRIPTION"] = "养蜂人拿出看家本领，驱使蜂群痛蜇敌人，用黏糊糊的蜂蜜减缓敌人的脚步。",
["SPECIAL_ARBOREAN_HONEY_NAME"] = "树灵养蜂人",
["SPECIAL_ARBOREAN_OLDTREE_DESCRIPTION"] = "生性暴躁的老家伙。丢出巨大滚木碾碎途经的敌人。",
["SPECIAL_ARBOREAN_OLDTREE_NAME"] = "古树",
["SPECIAL_ARBOREAN_SENTINELS_SPEARMEN_DESCRIPTION"] = "敏捷的森林保卫者。",
["SPECIAL_ARBOREAN_SENTINELS_SPEARMEN_NAME"] = "树灵荆棘矛兵",
["SPECIAL_PRIESTS_SOLDIERS_DESCRIPTION"] = "痛改前非的教徒，在死亡时化作怪物。",
["SPECIAL_PRIESTS_SOLDIERS_NAME"] = "盲目教徒",
["SPECIAL_REPAIR_HOLDER_DRAGON_DESCRIPTION"] = "熄灭火焰以立即解封防御塔。",
["SPECIAL_REPAIR_HOLDER_DRAGON_NAME"] = "火焰吞没",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_EARTH_DESCRIPTION"] = "增加防御塔单位的生命值。\n召唤最多3个石头战士。",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_EARTH_NAME"] = "五行龙魂：土",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_FIRE_DESCRIPTION"] = "增加所建防御塔的伤害。\n每隔一段时间秒杀一名敌人。",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_FIRE_NAME"] = "五行龙魂：火",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_METAL_DESCRIPTION"] = "减少建造费用。\n对敌人造成伤害可获得金币。",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_METAL_NAME"] = "五行龙魂：金",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WATER_DESCRIPTION"] = "定期治疗附近的友方单位。\n将敌人沿路径往回传送。",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WATER_NAME"] = "五行龙魂：水",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WOOD_DESCRIPTION"] = "增加所建防御塔的射程。\n每隔一段时间短暂生成树根使敌人减速。",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WOOD_NAME"] = "五行龙魂：木",
["SPECIAL_REPAIR_HOLDER_SEA_OF_TREES_DESCRIPTION"] = "清理障碍物，恢复这个塔位。",
["SPECIAL_REPAIR_HOLDER_SEA_OF_TREES_NAME"] = "障碍物",
["SPECIAL_REPAIR_HOLDER_SPIDERS_DESCRIPTION"] = "清除蛛网，恢复这个塔位。",
["SPECIAL_REPAIR_HOLDER_SPIDERS_NAME"] = "蛛网覆盖的塔位",
["SPECIAL_REPAIR_OVERSEER_DESCRIPTION"] = "击退触手以解锁这个塔位。",
["SPECIAL_REPAIR_OVERSEER_NAME"] = "触手",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_1_DESCRIPTION"] = "招募1名精灵雇佣军并肩作战。战败后过10秒可重生。",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_1_NAME"] = "精灵雇佣兵 I",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_2_DESCRIPTION"] = "招募2名精灵雇佣军并肩作战。战败后过10秒可重生。",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_2_NAME"] = "精灵雇佣兵 II",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_3_DESCRIPTION"] = "招募3名精灵雇佣军并肩作战。战败后过10秒可重生。",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_3_NAME"] = "精灵雇佣兵 III",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_1"] = "释放魔法能量，摧毁魔蒂娅丝的幻象，阻止她在接下来数秒内创造更多幻象。",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_2"] = "召唤2名恶魔守卫沿路径行走并与敌人战斗。",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_3"] = "困住迪纳斯，使其无法移动和攻击。",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_1"] = "灵魂冲击",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_2"] = "地狱孽种",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_3"] = "魔法锁链",
["START BATTLE!"] = "开始战斗！",
["START HERE!"] = "从此处开始！",
["STRATEGY BASICS!"] = "战略的基本！",
["Select by tapping on the portrait or hero unit."] = "轻按肖像或英雄进行选择。",
["Sell Tower"] = "出售防御塔",
["Sell this tower and get a %s GP refund."] = "出售该防御塔并获得%s金币。",
["Shows level, health and experience."] = "显示等级、生命值和经验值。",
["Special abilities"] = "特殊技能",
["Support your soldiers with ranged towers!"] = "用远程防御塔支援你的士兵！",
["Survival mode!"] = "生存模式！",
["TAP_TO_START"] = "点击开始",
["TAUNT_BOSS_PIG_FROM_POOL_0001"] = "我要让你惊声尖叫！",
["TAUNT_BOSS_PIG_FROM_POOL_0002"] = "有种再叫声“培根”试试，谅你也不敢！",
["TAUNT_BOSS_PIG_FROM_POOL_0003"] = "小的们，现在我们又有人肉吃了！",
["TAUNT_BOSS_PIG_FROM_POOL_0004"] = "快点！我超饿的。",
["TAUNT_BOSS_PIG_FROM_POOL_0005"] = "我很乐意见证你的死亡。",
["TAUNT_BOSS_PIG_FROM_POOL_0006"] = "我知道，我是个“坏心肠”。",
["TAUNT_LVL30_BOSS_ABILITY_01"] = "开宴吧，我的孩子们！",
["TAUNT_LVL30_BOSS_ABILITY_02"] = "粘稳了！哇哈哈哈！",
["TAUNT_LVL30_BOSS_ABILITY_03"] = "为了教会！",
["TAUNT_LVL30_BOSS_ABILITY_04"] = "美味的盛宴人人有份！",
["TAUNT_LVL30_BOSS_ABILITY_05"] = "我的蜘蛛感应在颤动！",
["TAUNT_LVL30_BOSS_ABILITY_06"] = "在我面前跪下，联盟！",
["TAUNT_LVL30_BOSS_ABILITY_07"] = "我的地盘，我来主宰！",
["TAUNT_LVL30_BOSS_ABILITY_08"] = "没有人能逃出我的网！",
["TAUNT_LVL30_BOSS_ABILITY_09"] = "死吧，人形害虫！",
["TAUNT_LVL30_BOSS_ABILITY_10"] = "命运的丝线在我手里！",
["TAUNT_LVL30_BOSS_ABILITY_11"] = "杀光他们！",
["TAUNT_LVL30_BOSS_INTRO_01"] = "终于，害死我妹妹的凶手现身了……",
["TAUNT_LVL30_BOSS_INTRO_02"] = "萨雷格兹，黑寡妇……姐姐替你们报仇……",
["TAUNT_LVL30_BOSS_INTRO_03"] = "你们终将俯首称臣，成为蜘蛛女王的信徒！",
["TAUNT_LVL30_BOSS_PREFIGHT_01"] = "够了……",
["TAUNT_LVL30_BOSS_PREFIGHT_02"] = "不过是一群微不足道的小虫子……",
["TAUNT_LVL30_BOSS_PREFIGHT_03"] = "岂能逃出女王的蛛网！",
["TAUNT_LVL32_BOSS_ABILITY_01"] = "蠢货！我掌控的是神火——三昧真火！",
["TAUNT_LVL32_BOSS_ABILITY_02"] = "看我烧了那灵霄宝殿！",
["TAUNT_LVL32_BOSS_ABILITY_03"] = "畏惧吧！这是最纯粹的真火！",
["TAUNT_LVL32_BOSS_ABILITY_04"] = "肉身魂灵，统统烧尽！",
["TAUNT_LVL32_BOSS_FIGHT_01"] = "熊熊心火，生生不熄！",
["TAUNT_LVL32_BOSS_FINAL_01"] = "真火式微...\n但我还有炎龙助我……",
["TAUNT_LVL32_BOSS_INTRO_01"] = "听说你有大军在手？",
["TAUNT_LVL32_BOSS_INTRO_02"] = " 本大王手上可是一头巨龙！哈哈哈哈！",
["TAUNT_LVL32_BOSS_PREFIGHT_01"] = "够了！轮到小爷我大显神通了！",
["TAUNT_LVL32_BOSS_PREFIGHT_02"] = "见识一下我的真正形态吧！",
["TAUNT_LVL34_BOSS_BOSSFIGHT_01"] = "好吧，我知道我们需要什么。更多的我。我，我，我……",
["TAUNT_LVL34_BOSS_DEATH_01"] = "这不可能……没关系，我丈夫会让你们付出代价……",
["TAUNT_LVL34_BOSS_INTRO_01"] = "你们这些猴子！竟敢在对我儿子做出那种事之后来这里？",
["TAUNT_LVL34_BOSS_WAVES_01"] = "尝尝我的力量吧，无耻的蠢货们！",
["TAUNT_LVL34_BOSS_WAVES_02"] = "终结将至！",
["TAUNT_LVL35_BOSS_DEATH_01"] = "于是我的统治……在鲜血中终结。",
["TAUNT_LVL35_BOSS_DESTROY_HOUSE_01"] = "嗯，那可真贵。该动用点火力了！",
["TAUNT_LVL35_BOSS_DESTROY_HOUSE_02"] = "啊，坚持的声音。夫人，该喝水了！",
["TAUNT_LVL35_BOSS_DESTROY_HOUSE_03"] = "格尔！屈服于我粗鄙的力量展示！",
["TAUNT_LVL35_BOSS_INTRO_01"] = "渺小的人类啊，在你们还活着的时候尽情欢喜吧。",
["TAUNT_LVL35_BOSS_INTRO_02"] = "是时候建立新秩序了。",
["TAUNT_LVL35_BOSS_INTRO_03"] = "啊啊，我在为复仇而呐喊！",
["TAUNT_LVL35_BOSS_PREFIGHT_01"] = "好吧，那我就让你看看为什么杀戮是我的生意！",
["TAUNT_STAGE02_RAELYN_0001"] = "放手一搏吧。",
["TAUNT_STAGE02_VEZNAN_0001"] = "他们来了。我会帮助你那弱小的部队……",
["TAUNT_STAGE02_VEZNAN_0002"] = "…我的意思是，我最好的战士会帮助你们。哈！",
["TAUNT_STAGE02_VEZNAN_0003"] = "哈－哈－哈！",
["TAUNT_STAGE06_BOSS_PIG_PREBATTLE_0001"] = "好吧…我自己来吧。",
["TAUNT_STAGE06_BOSS_PIG_RESPONSE_0001"] = "放轻松，一切都在掌握中。",
["TAUNT_STAGE06_CULTIST_GREETING_0001"] = "我看你在那儿很惬意…",
["TAUNT_STAGE06_CULTIST_GREETING_0002"] = "……你最好遵守承诺。",
["TAUNT_STAGE11_CULTIST_LEADER_0001"] = "你能走到这里很了不起……",
["TAUNT_STAGE11_CULTIST_LEADER_0002"] = "…但你无法阻止“天命”!",
["TAUNT_STAGE11_CULTIST_LEADER_0003"] = "够了！！！",
["TAUNT_STAGE11_CULTIST_LEADER_0004"] = "是时候让你向我们鞠躬了！",
["TAUNT_STAGE11_CULTIST_LEADER_0005"] = "呃啊…这还没完！",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0001"] = "一个全新世界正等待我们。",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0002"] = "你低估了我的力量。",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0003"] = "Oculus Poculus!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0004"] = "聆听这“天命”之声吧！",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0005"] = "我很邪恶吗？当然了！",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0006"] = "全视之主保佑我们！",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0001"] = "你的末日就在眼前！",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0002"] = "真是大开眼界啊！",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0003"] = "向我的虚空朋友们说声好！",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0004"] = "Oculus Poculus!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0005"] = "卑微弱小的渣滓！",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0006"] = "全视之主保佑我们！",
["TAUNT_STAGE11_VEZNAN_0001"] = "迪纳斯，老朋友，好久不见了！",
["TAUNT_STAGE15_CULTIST_0001"] = "就快了…我能感受到祂在苏醒！",
["TAUNT_STAGE15_CULTIST_0002"] = "新时代即将到来，你们将前功尽弃！",
["TAUNT_STAGE15_CULTIST_0003"] = "呃啊……你的联盟确实强大。",
["TAUNT_STAGE15_CULTIST_0004"] = "但我会让你知道什么是真正的力量！",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0001"] = "愚蠢的白痴！你这是来送死。",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0002"] = "在祂的凝视下投降吧！",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0003"] = "你将成为一名真正的信徒。",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0004"] = "无论是否结盟，你的命运都是注定的！",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0005"] = "虛空中沒有生命。只有死亡。",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0006"] = "别浪费我的时间！",
["TAUNT_STAGE15_DENAS_0001"] = "我还有一笔账要算。这场战斗我绝不缺席！",
["TAUNT_STAGE16_DENAS_AFTER_BOSSFIGHT_0001"] = "没料到这手吧？",
["TAUNT_STAGE18_ERIDAN_FIGHT_0001"] = "今夜必有杀戮。",
["TAUNT_STAGE18_ERIDAN_FIGHT_0002"] = "我们信仰艾纳妮。",
["TAUNT_STAGE18_ERIDAN_FIGHT_0003"] = "伊帝远永皮铁！",
["TAUNT_STAGE18_ERIDAN_FIGHT_0004"] = "我就是射不偏。",
["TAUNT_STAGE18_ERIDAN_FIGHT_0005"] = "艾瑞戴尔将再次强大！",
["TAUNT_STAGE18_ERIDAN_FIGHT_0006"] = "游侠们个个都不简单！",
["TAUNT_STAGE18_ERIDAN_FIGHT_0007"] = "你还在记录比分吧？",
["TAUNT_STAGE18_ERIDAN_FIGHT_0008"] = "放他们过来！",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0001"] = "我的弓箭为你效力!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0002"] = "加快动作！",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0003"] = "全体就位！",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0004"] = "注意观察！",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0001"] = "热身到此为止！",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0002"] = "你还真是纠缠不休……",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0003"] = "让真正的战斗开始吧！",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0001"] = "我掌控一切灵魂！",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0002"] = "精灵一族将东山再起。",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0003"] = "我当然有好身体…那些尸体！",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0004"] = "古老邪力助我！",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0005"] = "颤抖吧，我的子嗣已从坟墓中醒来！",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0006"] = "我将复兴族人的荣耀。",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0001"] = "啊，无坚不摧的联盟大驾光临。",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0002"] = "这斗篷也该掀掉了！",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0003"] = "见识一下死亡魔法的威力吧！",
["TAUNT_STAGE22_BOSS_CROCS_BEFORE_BOSSFIGHT_0001"] = "我自由了！终于可以吞噬…",
["TAUNT_STAGE22_BOSS_CROCS_BEFORE_BOSSFIGHT_0002"] = "一切了！！！",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0001"] = "够了，别再坏我好事了！",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0002"] = "还得让诡须教你们一些规矩。",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0003"] = "全员登船，哈哈哈哈！",
["TAUNT_STAGE25_BOSS_MACHINIST_END_0001"] = "你们这些无礼的蠢货！",
["TAUNT_STAGE25_BOSS_MACHINIST_END_0002"] = "你们永远抓不到我，哈哈哈！",
["TAUNT_STAGE26_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0001"] = "不！还没结束……",
["TAUNT_STAGE26_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0002"] = "糟糕！！！",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0001"] = "你们不是这支军队的对手！",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0002"] = "诡须绝非身处危险。",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0003"] = "诡须正是危险本身！",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0004"] = "疯子能做到这些吗？",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0005"] = "全世界都会向诡须俯首称臣！",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0001"] = "诡须的耐心已经耗尽。",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0002"] = "待会儿就有厉害的东西瞧了！",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0003"] = "诡须只需要\"他自己\"就够了！",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0004"] = "动作快点行不行？！",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0001"] = "你和你那该死的联盟，净会多管闲事！",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0002"] = "我奉劝你们别轻易招惹……",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0003"] = "……诡须本尊！",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0001"] = "随你消灭多少克隆，我都会造出更多来。",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0002"] = "想把事情做好，就得自己动手。",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0003"] = "哦，诡须，你真是个天才！",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0004"] = "惹了麻烦就别想脱身！",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0005"] = "你们出力了没有啊？",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0006"] = "区区凡人能超越我的造物？",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0001"] = "你们还没见识够\"我\"的厉害……",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0002"] = "……现在想来挑战最强矮人本人了吗？",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0003"] = "尽管来试试吧。",
["TAUNT_TUTORIAL_ARBOREAN_ALL_0001"] = "继续！你树我们的希望！",
["TAUNT_TUTORIAL_ARBOREAN_BARRACK_0001"] = "在这建造兵营！",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_1_NAME"] = "触手威利",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_2_NAME"] = "触手亨利",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_3_NAME"] = "触手杰弗里",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_4_NAME"] = "触手尼古拉斯",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_5_NAME"] = "触手艾德",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_6_NAME"] = "触手霍布",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_7_NAME"] = "触手奥多",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_8_NAME"] = "触手赛德里克",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_9_NAME"] = "触手哈尔",
["TERMS_OF_SERVICE_LINK"] = "服务条款",
["TIP_TITLE"] = "提示：",
["TOWER_ARBOREAN_EMISSARY_1_DESCRIPTION"] = "树灵使用强大的自然魔法使他们的敌人脆弱无力。",
["TOWER_ARBOREAN_EMISSARY_1_NAME"] = "树灵使者I",
["TOWER_ARBOREAN_EMISSARY_2_DESCRIPTION"] = "树灵使用强大的自然魔法使他们的敌人脆弱无力。",
["TOWER_ARBOREAN_EMISSARY_2_NAME"] = "树灵使者II",
["TOWER_ARBOREAN_EMISSARY_3_DESCRIPTION"] = "树灵使用强大的自然魔法使他们的敌人脆弱无力。",
["TOWER_ARBOREAN_EMISSARY_3_NAME"] = "树灵使者III",
["TOWER_ARBOREAN_EMISSARY_4_DESCRIPTION"] = "树灵使用强大的自然魔法使他们的敌人脆弱无力。",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_1_DESCRIPTION"] = "召唤仙灵，使范围内的友军单位每秒恢复%$towers.arborean_emissary.gift_of_nature.s_heal[1]%$点生命值，持续%$towers.arborean_emissary.gift_of_nature.duration[1]%$秒。",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_1_NAME"] = "自然馈赠",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_2_DESCRIPTION"] = "召唤仙灵，使范围内的友军单位每秒恢复%$towers.arborean_emissary.gift_of_nature.s_heal[2]%$点生命值，持续%$towers.arborean_emissary.gift_of_nature.duration[2]%$秒。",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_2_NAME"] = "自然馈赠",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_3_DESCRIPTION"] = "召唤仙灵，使范围内的友军单位每秒恢复%$towers.arborean_emissary.gift_of_nature.s_heal[3]%$点生命值，持续%$towers.arborean_emissary.gift_of_nature.duration[3]%$秒。",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_3_NAME"] = "自然馈赠",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_NAME"] = "自然馈赠",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_NOTE"] = "切莫与自然为敌。",
["TOWER_ARBOREAN_EMISSARY_4_NAME"] = "树灵使者IV",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_1_DESCRIPTION"] = "生长出%$towers.arborean_emissary.wave_of_roots.max_targets[1]%$道树根缠绕敌人，使其眩晕%$towers.arborean_emissary.wave_of_roots.mod_duration[1]%$秒，并造成%$towers.arborean_emissary.wave_of_roots.s_damage[1]%$点真实伤害。",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_1_NAME"] = "荆棘缠绕",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_2_DESCRIPTION"] = "生长出%$towers.arborean_emissary.wave_of_roots.max_targets[2]%$道根须缠绕敌人，使其眩晕%$towers.arborean_emissary.wave_of_roots.mod_duration[2]%$秒，并造成%$towers.arborean_emissary.wave_of_roots.s_damage[2]%$点真实伤害。",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_2_NAME"] = "荆棘缠绕",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_3_DESCRIPTION"] = "生长出%$towers.arborean_emissary.wave_of_roots.max_targets[3]%$道根须缠绕敌人，使其眩晕%$towers.arborean_emissary.wave_of_roots.mod_duration[3]%$秒，并造成%$towers.arborean_emissary.wave_of_roots.s_damage[3]%$点真实伤害。",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_3_NAME"] = "荆棘缠绕",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_NAME"] = "荆棘缠绕",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_NOTE"] = "当心脚下。",
["TOWER_ARBOREAN_EMISSARY_DESC"] = "树灵素来平和，但若遭激怒，便会施展魔法标记敌人，使其虚弱无力。",
["TOWER_ARBOREAN_EMISSARY_NAME"] = "树灵使者",
["TOWER_ARBOREAN_SENTINELS_DESCRIPTION"] = "敏捷的森林保卫者。",
["TOWER_ARBOREAN_SENTINELS_NAME"] = "树灵荆棘矛兵",
["TOWER_ARCANE_WIZARD_1_DESCRIPTION"] = "精通奥术的法师，随时随地都能从容应战。",
["TOWER_ARCANE_WIZARD_1_NAME"] = "奥术法师I",
["TOWER_ARCANE_WIZARD_2_DESCRIPTION"] = "精通奥术的法师，随时随地都能从容应战。",
["TOWER_ARCANE_WIZARD_2_NAME"] = "奥术法师II",
["TOWER_ARCANE_WIZARD_3_DESCRIPTION"] = "精通奥术的法师，随时随地都能从容应战。",
["TOWER_ARCANE_WIZARD_3_NAME"] = "奥术法师III",
["TOWER_ARCANE_WIZARD_4_DESCRIPTION"] = "精通奥术的法师，随时随地都能从容应战。",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_1_DESCRIPTION"] = "释放一束能够瞬间杀死目标的光线。如目标为Boss或小Boss则改为造成%$towers.arcane_wizard.disintegrate.boss_damage[1]%$点魔法伤害。",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_1_NAME"] = "分解射线",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_2_DESCRIPTION"] = "技能冷却时间减少至%$towers.arcane_wizard.disintegrate.cooldown[2]%$秒。对Boss或小Boss的伤害增加至%$towers.arcane_wizard.disintegrate.boss_damage[2]%$点。",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_2_NAME"] = "分解射线",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_3_DESCRIPTION"] = "技能冷却时间减少至%$towers.arcane_wizard.disintegrate.cooldown[3]%$秒。对Boss或小Boss的伤害增加至%$towers.arcane_wizard.disintegrate.boss_damage[3]%$点。",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_3_NAME"] = "分解射线",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_NAME"] = "分解射线",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_NOTE"] = "尘归尘，土归土。",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_1_DESCRIPTION"] = "使附近防御塔的伤害提高%$towers.arcane_wizard.empowerment.s_damage_factor[1]%$%。",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_1_NAME"] = "强化光环",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_2_DESCRIPTION"] = "使附近防御塔的伤害提高%$towers.arcane_wizard.empowerment.s_damage_factor[2]%$%。",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_2_NAME"] = "强化光环",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_3_DESCRIPTION"] = "使附近防御塔的伤害提高%$towers.arcane_wizard.empowerment.s_damage_factor[3]%$%",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_3_NAME"] = "强化光环",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_NAME"] = "强化光环",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_NOTE"] = "无穷的力量！",
["TOWER_ARCANE_WIZARD_4_NAME"] = "奥术法师IV",
["TOWER_ARCANE_WIZARD_DESC"] = "利尼维亚的法师能够运用纯粹的魔法，彻底摧毁他们的敌人。",
["TOWER_ARCANE_WIZARD_NAME"] = "奥术法师",
["TOWER_BALLISTA_1_DESCRIPTION"] = "作为哥布林一族强大的新设备，它至今都没塌掉简直是个奇迹。",
["TOWER_BALLISTA_1_NAME"] = "巨弩哨站I",
["TOWER_BALLISTA_2_DESCRIPTION"] = "作为哥布林一族强大的新设备，它至今都没塌掉简直是个奇迹。",
["TOWER_BALLISTA_2_NAME"] = "巨弩哨站II",
["TOWER_BALLISTA_3_DESCRIPTION"] = "作为哥布林一族强大的新设备，它至今都没塌掉简直是个奇迹。",
["TOWER_BALLISTA_3_NAME"] = "巨弩哨站III",
["TOWER_BALLISTA_4_DESCRIPTION"] = "作为哥布林一族强大的新设备，它至今都没塌掉简直是个奇迹。",
["TOWER_BALLISTA_4_NAME"] = "巨弩哨站IV",
["TOWER_BALLISTA_4_SKILL_BOMB_1_DESCRIPTION"] = "长距离发射一枚废品制成的炸弹，造成%$towers.ballista.skill_bomb.damage_min[1]%$-%$towers.ballista.skill_bomb.damage_max[1]%$点物理伤害。使敌人减速%$towers.ballista.skill_bomb.duration[1]%$秒。",
["TOWER_BALLISTA_4_SKILL_BOMB_1_NAME"] = "废品炸弹",
["TOWER_BALLISTA_4_SKILL_BOMB_2_DESCRIPTION"] = "废品炸弹造成%$towers.ballista.skill_bomb.damage_min[2]%$-%$towers.ballista.skill_bomb.damage_max[2]%$点物理伤害，使敌人减速%$towers.ballista.skill_bomb.duration[1]%$秒",
["TOWER_BALLISTA_4_SKILL_BOMB_2_NAME"] = "废品炸弹",
["TOWER_BALLISTA_4_SKILL_BOMB_3_DESCRIPTION"] = "废品炸弹造成%$towers.ballista.skill_bomb.damage_min[3]%$-%$towers.ballista.skill_bomb.damage_max[3]%$点物理伤害，使敌人减速%$towers.ballista.skill_bomb.duration[1]%$秒",
["TOWER_BALLISTA_4_SKILL_BOMB_3_NAME"] = "废品炸弹",
["TOWER_BALLISTA_4_SKILL_BOMB_NOTE"] = "注意，准备发射！",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_1_DESCRIPTION"] = "最后一击造成的伤害提升%$towers.ballista.skill_final_shot.s_damage_factor[1]%$%，并使目标眩晕%$towers.ballista.skill_final_shot.s_stun%$秒。",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_1_NAME"] = "终结一钉",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_2_DESCRIPTION"] = "最后一击造成的伤害提升%$towers.ballista.skill_final_shot.s_damage_factor[2]%$%，并使目标眩晕%$towers.ballista.skill_final_shot.s_stun%$秒。",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_2_NAME"] = "终结一钉",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_3_DESCRIPTION"] = "最后一击造成的伤害提升%$towers.ballista.skill_final_shot.s_damage_factor[3]%$%，并使目标眩晕%$towers.ballista.skill_final_shot.s_stun%$秒。",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_3_NAME"] = "终结一钉",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_NOTE"] = "小子，你这是中头奖了！",
["TOWER_BALLISTA_DESC"] = "哥布林一族对战争极为狂热，为了不用再拉弓射箭而研发了这座巨弩。",
["TOWER_BALLISTA_NAME"] = "巨弩哨站",
["TOWER_BARREL_1_DESCRIPTION"] = "北国人的酒桶是对抗大批敌人的强力武器。",
["TOWER_BARREL_1_NAME"] = "酿酒战匠I",
["TOWER_BARREL_2_DESCRIPTION"] = "北国人的酒桶是对抗大批敌人的强力武器。",
["TOWER_BARREL_2_NAME"] = "酿酒战匠II",
["TOWER_BARREL_3_DESCRIPTION"] = "北国人的酒桶是对抗大批敌人的强力武器。",
["TOWER_BARREL_3_NAME"] = "酿酒战匠III",
["TOWER_BARREL_4_DESCRIPTION"] = "北国人的酒桶是对抗大批敌人的强力武器。",
["TOWER_BARREL_4_NAME"] = "酿酒战匠IV",
["TOWER_BARREL_4_SKILL_BARREL_1_DESCRIPTION"] = "投掷一个毒酒桶，造成%$towers.barrel.skill_barrel.explosion.damage_min[1]%$-%$towers.barrel.skill_barrel.explosion.damage_max[1]%$点物理伤害，桶留下的毒酒每秒造成%$towers.barrel.skill_barrel.poison.s_damage%$点真实伤害，持续%$towers.barrel.skill_barrel.poison.duration%$秒。",
["TOWER_BARREL_4_SKILL_BARREL_1_NAME"] = "不良批次",
["TOWER_BARREL_4_SKILL_BARREL_2_DESCRIPTION"] = "毒酒桶爆炸造成%$towers.barrel.skill_barrel.explosion.damage_min[2]%$-%$towers.barrel.skill_barrel.explosion.damage_max[2]%$点物理伤害，毒酒每秒造成%$towers.barrel.skill_barrel.poison.s_damage%$点真实伤害，持续%$towers.barrel.skill_barrel.poison.duration%$秒。",
["TOWER_BARREL_4_SKILL_BARREL_2_NAME"] = "不良批次",
["TOWER_BARREL_4_SKILL_BARREL_3_DESCRIPTION"] = "毒酒桶的爆炸造成%$towers.barrel.skill_barrel.explosion.damage_min[3]%$-%$towers.barrel.skill_barrel.explosion.damage_max[3]%$点物理伤害，毒酒每秒造成%$towers.barrel.skill_barrel.poison.s_damage%$点真实伤害，持续%$towers.barrel.skill_barrel.poison.duration%$秒。",
["TOWER_BARREL_4_SKILL_BARREL_3_NAME"] = "不良批次",
["TOWER_BARREL_4_SKILL_BARREL_NOTE"] = "敢喝你就来！",
["TOWER_BARREL_4_SKILL_WARRIOR_1_DESCRIPTION"] = "召唤一名喝下力量药酒的战士，其拥有%$towers.barrel.skill_warrior.entity.hp_max[1]%$点生命值，攻击可造成%$towers.barrel.skill_warrior.entity.damage_min[1]%$-%$towers.barrel.skill_warrior.entity.damage_max[1]%$点物理伤害。",
["TOWER_BARREL_4_SKILL_WARRIOR_1_NAME"] = "神奇药酒",
["TOWER_BARREL_4_SKILL_WARRIOR_2_DESCRIPTION"] = "战士拥有%$towers.barrel.skill_warrior.entity.hp_max[2]%$点生命值，攻击可造成%$towers.barrel.skill_warrior.entity.damage_min[2]%$-%$towers.barrel.skill_warrior.entity.damage_max[2]%$点物理伤害。",
["TOWER_BARREL_4_SKILL_WARRIOR_2_NAME"] = "神奇药酒",
["TOWER_BARREL_4_SKILL_WARRIOR_3_DESCRIPTION"] = "战士拥有%$towers.barrel.skill_warrior.entity.hp_max[3]%$点生命值，攻击可造成%$towers.barrel.skill_warrior.entity.damage_min[3]%$-%$towers.barrel.skill_warrior.entity.damage_max[3]%$点物理伤害。",
["TOWER_BARREL_4_SKILL_WARRIOR_3_NAME"] = "神奇药酒",
["TOWER_BARREL_4_SKILL_WARRIOR_NOTE"] = "胜利的滋味！",
["TOWER_BARREL_DESC"] = "北国人是精通药剂制作的大师，他们能在战斗中使用自制的药酒来对抗敌人。",
["TOWER_BARREL_NAME"] = "酿酒战匠",
["TOWER_BARREL_WARRIOR_NAME"] = "勇猛者哈尔丹",
["TOWER_BROKEN_DESCRIPTION"] = "这座防御塔受损，请花费金币修复。",
["TOWER_BROKEN_NAME"] = "受损的防御塔",
["TOWER_CROCS_EATEN_DESCRIPTION"] = "用\"魔法\"将防御塔修复如初。",
["TOWER_CROCS_EATEN_NAME"] = "防御塔残骸",
["TOWER_DARK_ELF_1_DESCRIPTION"] = "无论远近强弱，皆是箭无虚发。",
["TOWER_DARK_ELF_1_NAME"] = "暮光长弓I",
["TOWER_DARK_ELF_2_DESCRIPTION"] = "无论远近强弱，皆是箭无虚发。",
["TOWER_DARK_ELF_2_NAME"] = "暮光长弓II",
["TOWER_DARK_ELF_3_DESCRIPTION"] = "无论远近强弱，皆是箭无虚发。",
["TOWER_DARK_ELF_3_NAME"] = "暮光长弓III",
["TOWER_DARK_ELF_4_DESCRIPTION"] = "无论远近强弱，皆是箭无虚发。",
["TOWER_DARK_ELF_4_NAME"] = "暮光长弓IV",
["TOWER_DARK_ELF_4_SKILL_BUFF_1_DESCRIPTION"] = "暮光长弓手每杀死一名敌人，伤害提升%$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$。",
["TOWER_DARK_ELF_4_SKILL_BUFF_1_NAME"] = "猎杀戾气",
["TOWER_DARK_ELF_4_SKILL_BUFF_2_DESCRIPTION"] = "暮光长弓手每杀死一名敌人，伤害提升%$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$.",
["TOWER_DARK_ELF_4_SKILL_BUFF_2_NAME"] = "猎杀戾气",
["TOWER_DARK_ELF_4_SKILL_BUFF_3_DESCRIPTION"] = "暮光长弓手每杀死一名敌人，伤害提升%$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$.",
["TOWER_DARK_ELF_4_SKILL_BUFF_3_NAME"] = "猎杀戾气",
["TOWER_DARK_ELF_4_SKILL_BUFF_NOTE"] = "逮到咯！",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_1_DESCRIPTION"] = "召唤两名暮光骚扰者，拥有%$towers.dark_elf.soldier.hp[1]%$点生命值，攻击可造成%$towers.dark_elf.soldier.basic_attack.damage_min[1]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[1]%$点物理伤害。",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_1_NAME"] = "利刃援军",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_2_DESCRIPTION"] = "暮光骚扰者拥有%$towers.dark_elf.soldier.hp[2]%$点生命值，攻击可造成%$towers.dark_elf.soldier.basic_attack.damage_min[2]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[2]%$点物理伤害。",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_2_NAME"] = "利刃援军",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_3_DESCRIPTION"] = "暮光骚扰者拥有%$towers.dark_elf.soldier.hp[3]%$点生命值，攻击可造成%$towers.dark_elf.soldier.basic_attack.damage_min[3]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[3]%$点物理伤害。",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_3_NAME"] = "利刃援军",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_NOTE"] = "该下来耍耍了。",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_DESCRIPTION"] = "改变模式，优先攻击离出口最近的敌人",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_NAME"] = "锁定：离出口最近",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_NOTE"] = "别放他们过去！",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_DESCRIPTION"] = "改变模式，优先攻击生命值上限最高的敌人",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_NAME"] = "锁定：生命值上限最高",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_NOTE"] = "拿下大家伙！",
["TOWER_DARK_ELF_DESC"] = "暮光弓箭手拥有黑暗能量加持，善于狩猎远处的强敌。",
["TOWER_DARK_ELF_NAME"] = "暮光长弓",
["TOWER_DEMON_PIT_1_DESCRIPTION"] = "这些恶魔狡猾且危险，总想惹是生非。",
["TOWER_DEMON_PIT_1_NAME"] = "恶魔熔坑I",
["TOWER_DEMON_PIT_2_DESCRIPTION"] = "这些恶魔狡猾且危险，总想惹是生非。",
["TOWER_DEMON_PIT_2_NAME"] = "恶魔熔坑II",
["TOWER_DEMON_PIT_3_DESCRIPTION"] = "这些恶魔狡猾且危险，总想惹是生非。",
["TOWER_DEMON_PIT_3_NAME"] = "恶魔熔坑III",
["TOWER_DEMON_PIT_4_BIG_DEMON_1_DESCRIPTION"] = "召唤一个拥有%$towers.demon_pit.big_guy.hp_max[1]%$点生命值的大恶魔，其攻击可造成%$towers.demon_pit.big_guy.melee_attack.damage_min[1]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[1]%$点物理伤害。爆炸造成%$towers.demon_pit.big_guy.explosion_damage[1]%$点伤害。",
["TOWER_DEMON_PIT_4_BIG_DEMON_1_NAME"] = "天降魔头",
["TOWER_DEMON_PIT_4_BIG_DEMON_2_DESCRIPTION"] = "大恶魔拥有%$towers.demon_pit.big_guy.hp_max[2]%$点生命值，攻击造成%$towers.demon_pit.big_guy.melee_attack.damage_min[2]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[2]%$点物理伤害。爆炸造成%$towers.demon_pit.big_guy.explosion_damage[2]%$点伤害。",
["TOWER_DEMON_PIT_4_BIG_DEMON_2_NAME"] = "天降魔头",
["TOWER_DEMON_PIT_4_BIG_DEMON_3_DESCRIPTION"] = "大恶魔拥有%$towers.demon_pit.big_guy.hp_max[3]%$点生命值，攻击造成%$towers.demon_pit.big_guy.melee_attack.damage_min[3]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[3]%$点物理伤害。爆炸造成%$towers.demon_pit.big_guy.explosion_damage[3]%$点伤害。",
["TOWER_DEMON_PIT_4_BIG_DEMON_3_NAME"] = "天降魔头",
["TOWER_DEMON_PIT_4_BIG_DEMON_NAME"] = "天降魔头",
["TOWER_DEMON_PIT_4_BIG_DEMON_NOTE"] = "我只是想放松一下。",
["TOWER_DEMON_PIT_4_DESCRIPTION"] = "这些恶魔狡猾且危险，总想惹是生非。",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_1_DESCRIPTION"] = "小恶魔的爆炸伤害提升%$towers.demon_pit.master_exploders.s_damage_increase[1]%$%，并使敌人燃烧，每秒造成%$towers.demon_pit.master_exploders.s_total_burning_damage_min[1]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[1]%$点真实伤害，持续%$towers.demon_pit.master_exploders.s_burning_duration[1]%$秒。",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_1_NAME"] = "爆炸大师",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_2_DESCRIPTION"] = "小恶魔的爆炸伤害提升%$towers.demon_pit.master_exploders.s_damage_increase[2]%$%，燃烧效果每秒造成%$towers.demon_pit.master_exploders.s_total_burning_damage_min[2]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[2]%$点真实伤害，持续%$towers.demon_pit.master_exploders.s_burning_duration[2]%$秒。",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_2_NAME"] = "爆炸大师",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_3_DESCRIPTION"] = "小恶魔的爆炸伤害提升%$towers.demon_pit.master_exploders.s_damage_increase[3]%$%，燃烧效果每秒造成%$towers.demon_pit.master_exploders.s_total_burning_damage_min[3]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[3]%$点真实伤害，持续%$towers.demon_pit.master_exploders.s_burning_duration[3]%$秒。",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_3_NAME"] = "爆炸大师",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_NAME"] = "爆炸大师",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_NOTE"] = "只有白痴才会干这活儿。",
["TOWER_DEMON_PIT_4_NAME"] = "恶魔熔坑IV",
["TOWER_DEMON_PIT_DESC"] = "这些来自熔岩深处的小恶魔会毫不犹豫地“投入”战场。",
["TOWER_DEMON_PIT_NAME"] = "恶魔熔坑",
["TOWER_DEMON_PIT_SOLDIER_BIG_GUY_NAME"] = "大恶魔",
["TOWER_DEMON_PIT_SOLDIER_NAME"] = "小恶魔",
["TOWER_DWARF_1_DESCRIPTION"] = "身材娇小，一点就着，没有任何敌人能活着穿过她们的防线。",
["TOWER_DWARF_1_NAME"] = "炮兵小队 I",
["TOWER_DWARF_2_DESCRIPTION"] = "身材娇小，一点就着，没有任何敌人能活着穿过她们的防线。",
["TOWER_DWARF_2_NAME"] = "炮兵小队 II",
["TOWER_DWARF_3_DESCRIPTION"] = "身材娇小，一点就着，没有任何敌人能活着穿过她们的防线。",
["TOWER_DWARF_3_NAME"] = "炮兵小队 III",
["TOWER_DWARF_4_DESCRIPTION"] = "身材娇小，一点就着，没有任何敌人能活着穿过她们的防线。",
["TOWER_DWARF_4_FORMATION_1_DESCRIPTION"] = "为小队增加第三名炮兵。",
["TOWER_DWARF_4_FORMATION_1_NAME"] = "队伍扩充",
["TOWER_DWARF_4_FORMATION_2_DESCRIPTION"] = "为小队增加第四名炮兵。",
["TOWER_DWARF_4_FORMATION_2_NAME"] = "队伍扩充",
["TOWER_DWARF_4_FORMATION_3_DESCRIPTION"] = "为小队增加第五名炮兵。",
["TOWER_DWARF_4_FORMATION_3_NAME"] = "队伍扩充",
["TOWER_DWARF_4_FORMATION_NOTE"] = "不爱红装爱武装。",
["TOWER_DWARF_4_INCENDIARY_AMMO_1_DESCRIPTION"] = "发射一枚爆弹，造成%$towers.dwarf.incendiary_ammo.damages_min[1]%$-%$towers.dwarf.incendiary_ammo.damages_max[1]%$点伤害，并使范围内的敌人在%$towers.dwarf.incendiary_ammo.burn.duration%$秒内受到%$towers.dwarf.incendiary_ammo.burn.s_damage[1]%$点燃烧伤害。",
["TOWER_DWARF_4_INCENDIARY_AMMO_1_NAME"] = "燃烧弹药",
["TOWER_DWARF_4_INCENDIARY_AMMO_2_DESCRIPTION"] = "发射一枚爆弹，造成%$towers.dwarf.incendiary_ammo.damages_min[2]%$-%$towers.dwarf.incendiary_ammo.damages_max[2]%$点伤害，并使范围内的敌人在%$towers.dwarf.incendiary_ammo.burn.duration%$秒内受到%$towers.dwarf.incendiary_ammo.burn.s_damage[2]%$点燃烧伤害。",
["TOWER_DWARF_4_INCENDIARY_AMMO_2_NAME"] = "燃烧弹药",
["TOWER_DWARF_4_INCENDIARY_AMMO_3_DESCRIPTION"] = "发射一枚爆弹，造成%$towers.dwarf.incendiary_ammo.damages_min[3]%$-%$towers.dwarf.incendiary_ammo.damages_max[3]%$点伤害，并使范围内的敌人在%$towers.dwarf.incendiary_ammo.burn.duration%$秒内受到%$towers.dwarf.incendiary_ammo.burn.s_damage[3]%$点燃烧伤害。",
["TOWER_DWARF_4_INCENDIARY_AMMO_3_NAME"] = "燃烧弹药",
["TOWER_DWARF_4_INCENDIARY_AMMO_NOTE"] = "热力全开！",
["TOWER_DWARF_4_NAME"] = "炮兵小队 IV",
["TOWER_DWARF_DESC"] = "技术精湛的射手，拥有无与伦比的团队精神。她们从北方赶来支援联盟，控制技术的不当使用。",
["TOWER_DWARF_NAME"] = "炮兵小队",
["TOWER_ELVEN_STARGAZERS_DESC"] = "精灵观星者能够运用宇宙的力量，同时对抗多名敌人。",
["TOWER_ELVEN_STARGAZERS_NAME"] = "精灵观星者",
["TOWER_FLAMESPITTER_1_DESCRIPTION"] = "喷火器的火焰可与龙焰媲美，令敌人闻风丧胆。",
["TOWER_FLAMESPITTER_1_NAME"] = "矮人喷火器I",
["TOWER_FLAMESPITTER_2_DESCRIPTION"] = "喷火器的火焰可与龙焰媲美，令敌人闻风丧胆。",
["TOWER_FLAMESPITTER_2_NAME"] = "矮人喷火器II",
["TOWER_FLAMESPITTER_3_DESCRIPTION"] = "喷火器的火焰可与龙焰媲美，令敌人闻风丧胆。",
["TOWER_FLAMESPITTER_3_NAME"] = "矮人喷火器III",
["TOWER_FLAMESPITTER_4_DESCRIPTION"] = "喷火器的火焰可与龙焰媲美，令敌人闻风丧胆。",
["TOWER_FLAMESPITTER_4_NAME"] = "矮人喷火器IV",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_1_DESCRIPTION"] = "发射一枚燃烧弹，造成%$towers.flamespitter.skill_bomb.s_damage[1]%$点物理伤害，并灼烧敌人，每秒造成%$towers.flamespitter.skill_bomb.burning.s_damage%$点真实伤害，持续%$towers.flamespitter.skill_bomb.burning.duration%$秒。",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_1_NAME"] = "炽热轨迹",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_2_DESCRIPTION"] = "燃烧弹造成%$towers.flamespitter.skill_bomb.s_damage[2]%$点物理伤害，燃烧每秒造成%$towers.flamespitter.skill_bomb.burning.s_damage%$点真实伤害，持续%$towers.flamespitter.skill_bomb.burning.duration%$秒。",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_2_NAME"] = "炽热轨迹",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_3_DESCRIPTION"] = "燃烧弹造成%$towers.flamespitter.skill_bomb.s_damage[3]%$点物理伤害，燃烧每秒造成%$towers.flamespitter.skill_bomb.burning.s_damage%$点真实伤害，持续%$towers.flamespitter.skill_bomb.burning.duration%$秒。",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_3_NAME"] = "炽热轨迹",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_NOTE"] = "生为野火。",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_1_DESCRIPTION"] = "地面喷发出火柱，对敌人造成%$towers.flamespitter.skill_columns.s_damage_out[1]%$-%$towers.flamespitter.skill_columns.s_damage_in[1]%$点物理伤害，并使敌人眩晕%$towers.flamespitter.skill_columns.s_stun%$秒。",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_1_NAME"] = "烈焰火柱",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_2_DESCRIPTION"] = "火柱造成%$towers.flamespitter.skill_columns.s_damage_out[2]%$-%$towers.flamespitter.skill_columns.s_damage_in[2]%$点物理伤害，并使敌人眩晕%$towers.flamespitter.skill_columns.s_stun%$秒。",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_2_NAME"] = "烈焰火柱",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_3_DESCRIPTION"] = "火柱造成%$towers.flamespitter.skill_columns.s_damage_out[3]%$-%$towers.flamespitter.skill_columns.s_damage_in[3]%$点物理伤害，并使敌人眩晕%$towers.flamespitter.skill_columns.s_stun%$秒。",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_3_NAME"] = "烈焰火柱",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_NOTE"] = "注意你的脚下！",
["TOWER_FLAMESPITTER_DESC"] = "矮人们将熔炉的热量化为武器，把自己熊熊燃烧的决心奉献给联盟。",
["TOWER_FLAMESPITTER_NAME"] = "矮人喷火器",
["TOWER_GHOST_1_DESCRIPTION"] = "神出鬼没的幽灵，敌人来不及反应就已一命呜呼。",
["TOWER_GHOST_1_NAME"] = "幽冥战魂I",
["TOWER_GHOST_2_DESCRIPTION"] = "神出鬼没的幽灵，敌人来不及反应就已一命呜呼。",
["TOWER_GHOST_2_NAME"] = "幽冥战魂II",
["TOWER_GHOST_3_DESCRIPTION"] = "神出鬼没的幽灵，敌人来不及反应就已一命呜呼。",
["TOWER_GHOST_3_NAME"] = "幽冥战魂III",
["TOWER_GHOST_4_DESCRIPTION"] = "神出鬼没的幽灵，敌人来不及反应就已一命呜呼。",
["TOWER_GHOST_4_EXTRA_DAMAGE_1_DESCRIPTION"] = "幽灵战士战斗%$towers.ghost.extra_damage.cooldown_start%$秒后，伤害提升%$towers.ghost.extra_damage.s_damage[1]%$%。",
["TOWER_GHOST_4_EXTRA_DAMAGE_1_NAME"] = "灵魂虹吸",
["TOWER_GHOST_4_EXTRA_DAMAGE_2_DESCRIPTION"] = "幽灵战士战斗%$towers.ghost.extra_damage.cooldown_start%$秒后，伤害提升%$towers.ghost.extra_damage.s_damage[2]%$%。",
["TOWER_GHOST_4_EXTRA_DAMAGE_2_NAME"] = "灵魂虹吸",
["TOWER_GHOST_4_EXTRA_DAMAGE_3_DESCRIPTION"] = "幽灵战士战斗%$towers.ghost.extra_damage.cooldown_start%$秒后，伤害提升%$towers.ghost.extra_damage.s_damage[3]%$%。",
["TOWER_GHOST_4_EXTRA_DAMAGE_3_NAME"] = "灵魂虹吸",
["TOWER_GHOST_4_EXTRA_DAMAGE_NOTE"] = "生人勿近。",
["TOWER_GHOST_4_NAME"] = "幽冥战魂IV",
["TOWER_GHOST_4_SOUL_ATTACK_1_DESCRIPTION"] = "幽灵战士被击败后扑向一名敌人，造成%$towers.ghost.soul_attack.s_damage[1]%$点真实伤害，降低其速度并使其攻击伤害减半。",
["TOWER_GHOST_4_SOUL_ATTACK_1_NAME"] = "永恒恐惧",
["TOWER_GHOST_4_SOUL_ATTACK_2_DESCRIPTION"] = "幽灵战士被击败后扑向一名敌人，造成%$towers.ghost.soul_attack.s_damage[2]%$点真实伤害，降低其速度并使其攻击伤害减半。",
["TOWER_GHOST_4_SOUL_ATTACK_2_NAME"] = "永恒恐惧",
["TOWER_GHOST_4_SOUL_ATTACK_3_DESCRIPTION"] = "幽灵战士被击败后扑向一名敌人，造成%$towers.ghost.soul_attack.s_damage[3]%$点真实伤害，降低其速度并使其攻击伤害减半。",
["TOWER_GHOST_4_SOUL_ATTACK_3_NAME"] = "永恒恐惧",
["TOWER_GHOST_4_SOUL_ATTACK_NOTE"] = "死了也要带上你！",
["TOWER_GHOST_DESC"] = "即便死后也不停战斗的幽灵战士，他们能在阴影中来去自如，出其不意地袭击敌人。",
["TOWER_GHOST_NAME"] = "幽冥战魂",
["TOWER_HERMIT_TOAD_1_DESCRIPTION"] = "无论用魔法，还是蛮力，这只蟾蜍会采取一切手段驱逐讨厌的入侵者。",
["TOWER_HERMIT_TOAD_1_NAME"] = "沼泽隐士 I",
["TOWER_HERMIT_TOAD_2_DESCRIPTION"] = "无论用魔法，还是蛮力，这只蟾蜍会采取一切手段驱逐讨厌的入侵者。",
["TOWER_HERMIT_TOAD_2_NAME"] = "沼泽隐士 II",
["TOWER_HERMIT_TOAD_3_DESCRIPTION"] = "无论用魔法，还是蛮力，这只蟾蜍会采取一切手段驱逐讨厌的入侵者。",
["TOWER_HERMIT_TOAD_3_NAME"] = "沼泽隐士 III",
["TOWER_HERMIT_TOAD_4_DESCRIPTION"] = "无论用魔法，还是蛮力，这只蟾蜍会采取一切手段驱逐讨厌的入侵者。",
["TOWER_HERMIT_TOAD_4_INSTAKILL_1_DESCRIPTION"] = "每隔%$towers.hermit_toad.power_instakill.cooldown[1]%$秒，隐士吐出舌头吞噬一名敌人。",
["TOWER_HERMIT_TOAD_4_INSTAKILL_1_NAME"] = "黏性长舌",
["TOWER_HERMIT_TOAD_4_JUMP_1_DESCRIPTION"] = "每隔%$towers.hermit_toad.power_jump.cooldown[1]%$秒，隐士会高高跃起，砸在敌人身上，造成%$towers.hermit_toad.power_jump.damage_min[1]%$点伤害，并震晕敌人%$towers.hermit_toad.power_jump.stun_duration[1]%$秒。",
["TOWER_HERMIT_TOAD_4_JUMP_1_NAME"] = "泰山压顶",
["TOWER_HERMIT_TOAD_4_NAME"] = "沼泽隐士 IV",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_1_DESCRIPTION"] = "每隔%$towers.hermit_toad.power_instakill.cooldown[1]%$秒，隐士吐出舌头吞噬一名敌人。",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_1_NAME"] = "黏性长舌 I",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_NOTE"] = "真是让人左右为黏。",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_1_DESCRIPTION"] = "每隔%$towers.hermit_toad.power_jump.cooldown[1]%$秒，隐士会高高跃起，砸在敌人身上，造成%$towers.hermit_toad.power_jump.damage_min[1]%$点伤害，并震晕敌人%$towers.hermit_toad.power_jump.stun_duration[1]%$秒。",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_1_NAME"] = "泰山压顶 I",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_2_DESCRIPTION"] = "每隔%$towers.hermit_toad.power_jump.cooldown[2]%$秒，隐士会高高跃起，砸在敌人身上，造成%$towers.hermit_toad.power_jump.damage_min[2]%$点伤害，并震晕敌人%$towers.hermit_toad.power_jump.stun_duration[2]%$秒。",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_2_NAME"] = "泰山压顶 II",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_3_DESCRIPTION"] = "每隔%$towers.hermit_toad.power_jump.cooldown[3]%$秒，隐士会高高跃起，砸在敌人身上，造成%$towers.hermit_toad.power_jump.damage_min[3]%$伤害，并震晕敌人%$towers.hermit_toad.power_jump.stun_duration[3]%$秒。",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_3_NAME"] = "泰山压顶 III",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_NOTE"] = "沼泽排球队登场。",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_DESCRIPTION"] = "隐士变为物理形态。",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_NAME"] = "肉搏泥沼",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_NOTE"] = "顾不得干净了！",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_DESCRIPTION"] = "隐士变为魔法形态。",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_NAME"] = "魔法池塘",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_NOTE"] = "无尽的力量!!",
["TOWER_HERMIT_TOAD_DESC"] = "巨大的蟾蜍法师，拿手好戏是吐粘液球，一心只想和平安静得享受池塘浴。切勿打扰！",
["TOWER_HERMIT_TOAD_NAME"] = "沼泽隐士",
["TOWER_NECROMANCER_1_DESCRIPTION"] = "死灵法师化死亡为己用，在战场上播下混乱的种子，再收割丰硕的果实。",
["TOWER_NECROMANCER_1_NAME"] = "死灵法师I",
["TOWER_NECROMANCER_2_DESCRIPTION"] = "死灵法师化死亡为己用，在战场上播下混乱的种子，再收割丰硕的果实。",
["TOWER_NECROMANCER_2_NAME"] = "死灵法师II",
["TOWER_NECROMANCER_3_DESCRIPTION"] = "死灵法师化死亡为己用，在战场上播下混乱的种子，再收割丰硕的果实。",
["TOWER_NECROMANCER_3_NAME"] = "死灵法师III",
["TOWER_NECROMANCER_4_DESCRIPTION"] = "死灵法师化死亡为己用，在战场上播下混乱的种子，再收割丰硕的果实。",
["TOWER_NECROMANCER_4_NAME"] = "死灵法师IV",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_1_DESCRIPTION"] = "放置一个图腾，持续%$towers.necromancer.skill_debuff.aura_duration[1]%$秒，诅咒敌人并使骷髅的攻击伤害提升%$towers.necromancer.skill_debuff.s_damage_factor[1]%$%。",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_1_NAME"] = "颤骨图腾",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_2_DESCRIPTION"] = "图腾使骷髅的攻击伤害提升%$towers.necromancer.skill_debuff.s_damage_factor[2]%$%。冷却时间减少至%$towers.necromancer.skill_debuff.cooldown[2]%$秒。",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_2_NAME"] = "颤骨图腾",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_3_DESCRIPTION"] = "图腾使骷髅的攻击伤害提升%$towers.necromancer.skill_debuff.s_damage_factor[3]%$%。冷却时间减少至%$towers.necromancer.skill_debuff.cooldown[3]%$秒。",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_3_NAME"] = "颤骨图腾",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_NOTE"] = "强骨军队！",
["TOWER_NECROMANCER_4_SKILL_RIDER_1_DESCRIPTION"] = "召唤一名亡灵骑士向前冲锋，对经过的敌人造成%$towers.necromancer.skill_rider.s_damage[1]%$点真实伤害。",
["TOWER_NECROMANCER_4_SKILL_RIDER_1_NAME"] = "亡灵骑士",
["TOWER_NECROMANCER_4_SKILL_RIDER_2_DESCRIPTION"] = "亡灵骑士造成%$towers.necromancer.skill_rider.s_damage[2]%$点真实伤害。",
["TOWER_NECROMANCER_4_SKILL_RIDER_2_NAME"] = "亡灵骑士",
["TOWER_NECROMANCER_4_SKILL_RIDER_3_DESCRIPTION"] = "亡灵骑士造成%$towers.necromancer.skill_rider.s_damage[3]%$点真实伤害。",
["TOWER_NECROMANCER_4_SKILL_RIDER_3_NAME"] = "亡灵骑士",
["TOWER_NECROMANCER_4_SKILL_RIDER_NOTE"] = "这可没有回头路……",
["TOWER_NECROMANCER_DESC"] = "死灵法师掌握着最黑暗的魔法，能将敌人转化为不死军团的一员。",
["TOWER_NECROMANCER_NAME"] = "死灵法师",
["TOWER_PALADIN_COVENANT_1_DESCRIPTION"] = "圣骑士们忠勇义烈，不遗余力地保卫着王国。",
["TOWER_PALADIN_COVENANT_1_NAME"] = "圣骑士殿堂I",
["TOWER_PALADIN_COVENANT_2_DESCRIPTION"] = "圣骑士们忠勇义烈，不遗余力地保卫着王国。",
["TOWER_PALADIN_COVENANT_2_NAME"] = "圣骑士殿堂II",
["TOWER_PALADIN_COVENANT_3_DESCRIPTION"] = "圣骑士们忠勇义烈，不遗余力地保卫着王国。",
["TOWER_PALADIN_COVENANT_3_NAME"] = "圣骑士殿堂III",
["TOWER_PALADIN_COVENANT_4_DESCRIPTION"] = "圣骑士们忠勇义烈，不遗余力地保卫着王国。",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_1_DESCRIPTION"] = "当圣骑士的生命值低于%$towers.paladin_covenant.healing_prayer.health_trigger_factor[1]%$%时，他将获得无敌，并每秒恢复%$towers.paladin_covenant.healing_prayer.s_healing[1]%$点生命值，持续%$towers.paladin_covenant.healing_prayer.duration%$秒。",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_1_NAME"] = "治愈祷告",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_2_DESCRIPTION"] = "治疗效果提升至每秒恢复%$towers.paladin_covenant.healing_prayer.s_healing[2]%$点生命值，持续%$towers.paladin_covenant.healing_prayer.duration%$秒。",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_2_NAME"] = "治愈祷告",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_3_DESCRIPTION"] = "治疗效果提升至每秒恢复%$towers.paladin_covenant.healing_prayer.s_healing[3]%$点生命值，持续%$towers.paladin_covenant.healing_prayer.duration%$秒。",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_3_NAME"] = "治愈祷告",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_NAME"] = "治愈祷告",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_NOTE"] = "恪尽职守，至死方休。",
["TOWER_PALADIN_COVENANT_4_LEAD_1_DESCRIPTION"] = "将一名圣骑士替换为卫队老将，为附近的友方单位提供%$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$%的攻击伤害增益。",
["TOWER_PALADIN_COVENANT_4_LEAD_1_NAME"] = "身先士卒",
["TOWER_PALADIN_COVENANT_4_LEAD_2_DESCRIPTION"] = "将一名骑士替换为卫队老将，为附近的友方单位提供%$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$%的攻击伤害增益。",
["TOWER_PALADIN_COVENANT_4_LEAD_2_NAME"] = "身先士卒",
["TOWER_PALADIN_COVENANT_4_LEAD_3_DESCRIPTION"] = "将一名骑士替换为卫队老将，为附近的友方单位提供%$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$%的攻击伤害增益。",
["TOWER_PALADIN_COVENANT_4_LEAD_3_NAME"] = "身先士卒",
["TOWER_PALADIN_COVENANT_4_LEAD_NAME"] = "身先士卒",
["TOWER_PALADIN_COVENANT_4_LEAD_NOTE"] = "为了国王，为了家园，为了江山！",
["TOWER_PALADIN_COVENANT_4_NAME"] = "圣骑士殿堂IV",
["TOWER_PALADIN_COVENANT_DESC"] = "圣骑士是利尼维亚精锐部队的中坚力量，他们能在战斗中运用神圣的力量保护和治疗自己。",
["TOWER_PALADIN_COVENANT_NAME"] = "圣骑士殿堂",
["TOWER_PANDAS_1_DESCRIPTION"] = "通晓元素技艺，胸怀不渝决心，三侠为守护世界的自然平衡而不懈奋战。",
["TOWER_PANDAS_1_NAME"] = "竹宗三侠 I",
["TOWER_PANDAS_2_DESCRIPTION"] = "通晓元素技艺，胸怀不渝决心，三侠为守护世界的自然平衡而不懈奋战。",
["TOWER_PANDAS_2_NAME"] = "竹宗三侠 II",
["TOWER_PANDAS_3_DESCRIPTION"] = "通晓元素技艺，胸怀不渝决心，三侠为守护世界的自然平衡而不懈奋战。",
["TOWER_PANDAS_3_NAME"] = "竹宗三侠 III",
["TOWER_PANDAS_4_DESCRIPTION"] = "通晓元素技艺，胸怀不渝决心，三侠为守护世界的自然平衡而不懈奋战。",
["TOWER_PANDAS_4_FIERY"] = "星门启动",
["TOWER_PANDAS_4_FIERY_1_DESCRIPTION"] = "发射火焰弹，造成%$towers.pandas.soldier.teleport.damage_min[1]%$-%$towers.pandas.soldier.teleport.damage_max[1]%$点真实伤害，并将命中的敌人沿路径往回传送。",
["TOWER_PANDAS_4_FIERY_1_NAME"] = "狱火劫",
["TOWER_PANDAS_4_FIERY_2_DESCRIPTION"] = "发射火焰弹，造成%$towers.pandas.soldier.teleport.damage_min[2]%$-%$towers.pandas.soldier.teleport.damage_max[2]%$点真实伤害，并将命中的敌人沿路径往回传送。",
["TOWER_PANDAS_4_FIERY_2_NAME"] = "狱火劫",
["TOWER_PANDAS_4_HAT"] = "一帽打天下",
["TOWER_PANDAS_4_HAT_1_DESCRIPTION"] = "将斗笠如飞刃掷出，在敌人之间反弹，每次命中造成%$towers.pandas.soldier.hat.damage_min[1]%$-%$towers.pandas.soldier.hat.damage_max[1]%$点伤害。",
["TOWER_PANDAS_4_HAT_1_NAME"] = "飞笠斩",
["TOWER_PANDAS_4_HAT_2_DESCRIPTION"] = "将斗笠如飞刃掷出，在敌人之间反弹，每次命中造成%$towers.pandas.soldier.hat.damage_min[2]%$-%$towers.pandas.soldier.hat.damage_max[2]%$点伤害。",
["TOWER_PANDAS_4_HAT_2_NAME"] = "飞笠斩",
["TOWER_PANDAS_4_NAME"] = "竹宗三侠 IV",
["TOWER_PANDAS_4_THUNDER"] = "熊猫快打",
["TOWER_PANDAS_4_THUNDER_1_DESCRIPTION"] = "召唤雷电进行小范围攻击，每道雷电造成%$towers.pandas.soldier.thunder.damage_min[1]%$-%$towers.pandas.soldier.thunder.damage_max[1]%$点范围伤害并短暂眩晕命中的敌人。",
["TOWER_PANDAS_4_THUNDER_1_NAME"] = "天雷破",
["TOWER_PANDAS_4_THUNDER_2_DESCRIPTION"] = "召唤雷电进行小范围攻击，每道雷电造成%$towers.pandas.soldier.thunder.damage_min[2]%$-%$towers.pandas.soldier.thunder.damage_max[2]%$点范围伤害并短暂眩晕命中的敌人。",
["TOWER_PANDAS_4_THUNDER_2_NAME"] = "天雷破",
["TOWER_PANDAS_DESC"] = "熊猫三人组兼备高强武艺与元素法力，巧用二者横扫敌人，即便一时落败亦不容小觑。",
["TOWER_PANDAS_NAME"] = "竹宗三侠",
["TOWER_PANDAS_RETREAT_DESCRIPTION"] = "让站立的熊猫撤退至避难所，持续8秒。",
["TOWER_PANDAS_RETREAT_NAME"] = "战术撤退",
["TOWER_PANDAS_RETREAT_NOTE"] = "谨慎是勇气的最高体现。",
["TOWER_RAY_1_DESCRIPTION"] = "邪恶法师为了追寻自己的邪恶目标，甘愿涉足危险而污秽的魔法。",
["TOWER_RAY_1_NAME"] = "诡术魔导师I",
["TOWER_RAY_2_DESCRIPTION"] = "邪恶法师为了追寻自己的邪恶目标，甘愿涉足危险而污秽的魔法。",
["TOWER_RAY_2_NAME"] = "诡术魔导师II",
["TOWER_RAY_3_DESCRIPTION"] = "邪恶法师为了追寻自己的邪恶目标，甘愿涉足危险而污秽的魔法。",
["TOWER_RAY_3_NAME"] = "诡术魔导师III",
["TOWER_RAY_4_CHAIN_1_DESCRIPTION"] = "魔法射线额外连锁%$towers.ray.skill_chain.s_max_enemies%$名敌人，使其减速，并造成总魔法伤害的%$towers.ray.skill_chain.damage_mult[1]%$%。",
["TOWER_RAY_4_CHAIN_1_NAME"] = "魔力溢流",
["TOWER_RAY_4_CHAIN_2_DESCRIPTION"] = "魔法射线额外连锁%$towers.ray.skill_chain.s_max_enemies%$名敌人，使其减速，并造成总魔法伤害的%$towers.ray.skill_chain.damage_mult[2]%$%。",
["TOWER_RAY_4_CHAIN_2_NAME"] = "魔力溢流",
["TOWER_RAY_4_CHAIN_3_DESCRIPTION"] = "魔法射线额外连锁%$towers.ray.skill_chain.s_max_enemies%$名敌人，使其减速，并造成总魔法伤害的%$towers.ray.skill_chain.damage_mult[3]%$%。",
["TOWER_RAY_4_CHAIN_3_NAME"] = "魔力溢流",
["TOWER_RAY_4_CHAIN_NOTE"] = "苦难足够分给每一个人。",
["TOWER_RAY_4_DESCRIPTION"] = "邪恶法师为了追寻自己的邪恶目标，甘愿涉足危险而污秽的魔法。",
["TOWER_RAY_4_NAME"] = "诡术魔导师IV",
["TOWER_RAY_4_SHEEP_1_DESCRIPTION"] = "将附近的一名敌人变成无力的绵羊。绵羊拥有其本体%$towers.ray.skill_sheep.sheep.hp_mult%$%的生命值。",
["TOWER_RAY_4_SHEEP_1_NAME"] = "变形魔咒",
["TOWER_RAY_4_SHEEP_2_DESCRIPTION"] = "将附近的一名敌人变成无力的绵羊。绵羊拥有其本体%$towers.ray.skill_sheep.sheep.hp_mult%$%的生命值。",
["TOWER_RAY_4_SHEEP_2_NAME"] = "变形魔咒",
["TOWER_RAY_4_SHEEP_3_DESCRIPTION"] = "将附近的一名敌人变成无力的绵羊。绵羊拥有其本体%$towers.ray.skill_sheep.sheep.hp_mult%$%的生命值。",
["TOWER_RAY_4_SHEEP_3_NAME"] = "变形魔咒",
["TOWER_RAY_4_SHEEP_NOTE"] = "说实话，你现在看着更顺眼了。",
["TOWER_RAY_DESC"] = "卫兹南的学徒们利用邪恶之力，向敌人施放带来痛苦的黑暗射线。",
["TOWER_RAY_NAME"] = "诡术魔导师",
["TOWER_ROCKET_GUNNERS_1_DESCRIPTION"] = "火箭枪手装备了黑暗军的最新科技，负责在空中巡逻。",
["TOWER_ROCKET_GUNNERS_1_NAME"] = "火箭枪手I",
["TOWER_ROCKET_GUNNERS_2_DESCRIPTION"] = "火箭枪手装备了黑暗军的最新科技，负责在空中巡逻。",
["TOWER_ROCKET_GUNNERS_2_NAME"] = "火箭枪手II",
["TOWER_ROCKET_GUNNERS_3_DESCRIPTION"] = "火箭枪手装备了黑暗军的最新科技，负责在空中巡逻。",
["TOWER_ROCKET_GUNNERS_3_NAME"] = "火箭枪手III",
["TOWER_ROCKET_GUNNERS_4_DESCRIPTION"] = "火箭枪手装备了黑暗军的最新科技，负责在空中巡逻。",
["TOWER_ROCKET_GUNNERS_4_NAME"] = "火箭枪手IV",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_1_DESCRIPTION"] = "每次攻击都会破坏敌人%$towers.rocket_gunners.soldier.phosphoric.armor_reduction[1]%$%护甲，并造成范围伤害。",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_1_NAME"] = "磷化子弹",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_2_DESCRIPTION"] = "每次攻击都会破坏敌人%$towers.rocket_gunners.soldier.phosphoric.armor_reduction[2]%$%护甲，并造成%$towers.rocket_gunners.soldier.phosphoric.damage_area_min[2]%$-%$towers.rocket_gunners.soldier.phosphoric.damage_area_max[2]%$点范围伤害。",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_2_NAME"] = "磷化子弹",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_3_DESCRIPTION"] = "每次攻击都会破坏敌人%$towers.rocket_gunners.soldier.phosphoric.armor_reduction[3]%$%护甲，并造成%$towers.rocket_gunners.soldier.phosphoric.damage_area_min[3]%$-%$towers.rocket_gunners.soldier.phosphoric.damage_area_max[3]%$点范围伤害。",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_3_NAME"] = "磷化子弹",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_NOTE"] = "邪恶淬炼过的子弹。",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_1_DESCRIPTION"] = "发射一枚导弹， 秒杀一名生命值不多于%$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[1]%$的敌人。",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_1_NAME"] = "蜂刺导弹",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_2_DESCRIPTION"] = "导弹冷却时间减至%$towers.rocket_gunners.sting_missiles.cooldown[2]%$秒，可以秒杀生命值不多于%$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[2]%$的敌人。",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_2_NAME"] = "蜂刺导弹",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_3_DESCRIPTION"] = "导弹冷却时间减至%$towers.rocket_gunners.sting_missiles.cooldown[3]%$秒，可以秒杀生命值不多于%$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[3]%$的敌人。",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_3_NAME"] = "蜂刺导弹",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_NOTE"] = "试试躲开这个？",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_DESCRIPTION"] = "令火箭枪手起飞，无法拦截敌人。",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_NAME"] = "升空",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_NOTE"] = "飞向宇宙，浩瀚无垠！",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_DESCRIPTION"] = "令火箭枪手着陆，可以拦截敌人。",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_NAME"] = "着陆",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_NOTE"] = "猎鹰已着陆！",
["TOWER_ROCKET_GUNNERS_DESC"] = "这支特种部队在地面和空中都能独立作战，用先进的武器对敌人发起奇袭。",
["TOWER_ROCKET_GUNNERS_NAME"] = "火箭枪手",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_1"] = "此防御塔为「巨大的威胁」解锁内容",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_2"] = "此塔包含在「悟空之旅」战役中。",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_1"] = "巨大的威胁",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_2"] = "悟空之旅战役",
["TOWER_ROOM_EQUIPPED_TOWERS_TITLE"] = "已装备的防御塔",
["TOWER_ROOM_GET_DLC"] = "得到它",
["TOWER_ROOM_LABEL_ROSTER_THUMB_NEW"] = "新防御塔！",
["TOWER_ROOM_SKILLS_TITLE"] = "技能",
["TOWER_ROYAL_ARCHERS_1_DESCRIPTION"] = "誓死效忠王国的皇家弓箭手，能在远处保护利尼维亚的军队。",
["TOWER_ROYAL_ARCHERS_1_NAME"] = "皇家弓箭手I",
["TOWER_ROYAL_ARCHERS_2_DESCRIPTION"] = "誓死效忠王国的皇家弓箭手，能在远处保护利尼维亚的军队。",
["TOWER_ROYAL_ARCHERS_2_NAME"] = "皇家弓箭手II",
["TOWER_ROYAL_ARCHERS_3_DESCRIPTION"] = "誓死效忠王国的皇家弓箭手，能在远处保护利尼维亚的军队。",
["TOWER_ROYAL_ARCHERS_3_NAME"] = "皇家弓箭手III",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_1_DESCRIPTION"] = "射出三支强化箭矢，造成%$towers.royal_archers.armor_piercer.damage_min[1]%$-%$towers.royal_archers.armor_piercer.damage_max[1]%$点物理伤害，并无视敌人%$towers.royal_archers.armor_piercer.armor_penetration[1]%$%的护甲。",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_1_NAME"] = "穿甲射击",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_2_DESCRIPTION"] = "射出三支强化箭矢，造成%$towers.royal_archers.armor_piercer.damage_min[2]%$-%$towers.royal_archers.armor_piercer.damage_max[2]%$点物理伤害，并无视敌人%$towers.royal_archers.armor_piercer.armor_penetration[2]%$%的护甲。",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_2_NAME"] = "穿甲射击",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_3_DESCRIPTION"] = "射出三支强化箭矢，造成%$towers.royal_archers.armor_piercer.damage_min[3]%$-%$towers.royal_archers.armor_piercer.damage_max[3]%$点物理伤害，并无视敌人%$towers.royal_archers.armor_piercer.armor_penetration[3]%$%的护甲。",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_3_NAME"] = "穿甲射击",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_NAME"] = "穿甲射击",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_NOTE"] = "我们看到你了。",
["TOWER_ROYAL_ARCHERS_4_DESCRIPTION"] = "誓死效忠王国的皇家弓箭手，能在远处保护利尼维亚的军队。",
["TOWER_ROYAL_ARCHERS_4_NAME"] = "皇家弓箭手IV",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_1_DESCRIPTION"] = "召唤一只战鹰攻击路径上的敌人，造成%$towers.royal_archers.rapacious_hunter.damage_min[1]%$-%$towers.royal_archers.rapacious_hunter.damage_max[1]%$点物理伤害。",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_1_NAME"] = "贪婪猎手",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_2_DESCRIPTION"] = "战鹰造成%$towers.royal_archers.rapacious_hunter.damage_min[2]%$-%$towers.royal_archers.rapacious_hunter.damage_max[2]%$点物理伤害。",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_2_NAME"] = "贪婪猎手",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_3_DESCRIPTION"] = "战鹰造成%$towers.royal_archers.rapacious_hunter.damage_min[3]%$-%$towers.royal_archers.rapacious_hunter.damage_max[3]%$点物理伤害。",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_3_NAME"] = "贪婪猎手",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_NAME"] = "贪婪猎手",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_NOTE"] = "鹰眼背后暗藏杀机。",
["TOWER_ROYAL_ARCHERS_DESC"] = "王国中最强大的神射手，他们还因与战鹰并肩作战而闻名遐迩。",
["TOWER_ROYAL_ARCHERS_NAME"] = "皇家弓箭手",
["TOWER_SAND_1_DESCRIPTION"] = "哨兵炉火纯青的回旋镖功夫足以震慑任何自视甚高的佣兵。",
["TOWER_SAND_1_NAME"] = "沙丘哨兵I",
["TOWER_SAND_2_DESCRIPTION"] = "哨兵炉火纯青的回旋镖功夫足以震慑任何自视甚高的佣兵。",
["TOWER_SAND_2_NAME"] = "沙丘哨兵II",
["TOWER_SAND_3_DESCRIPTION"] = "哨兵炉火纯青的回旋镖功夫足以震慑任何自视甚高的佣兵。",
["TOWER_SAND_3_NAME"] = "沙丘哨兵III",
["TOWER_SAND_4_DESCRIPTION"] = "哨兵炉火纯青的回旋镖功夫足以震慑任何自视甚高的佣兵。",
["TOWER_SAND_4_NAME"] = "沙丘哨兵IV",
["TOWER_SAND_4_SKILL_BIG_BLADE_1_DESCRIPTION"] = "发射旋转刀刃，每秒造成 %$towers.sand.skill_big_blade.s_damage_min[1]%$-%$towers.sand.skill_big_blade.s_damage_max[1]%$点物理伤害，持续%$towers.sand.skill_big_blade.duration[1]%$秒。",
["TOWER_SAND_4_SKILL_BIG_BLADE_1_NAME"] = "厄运回旋",
["TOWER_SAND_4_SKILL_BIG_BLADE_2_DESCRIPTION"] = "旋转刀刃每秒造成%$towers.sand.skill_big_blade.s_damage_min[2]%$-%$towers.sand.skill_big_blade.s_damage_max[2]%$点物理伤害，持续%$towers.sand.skill_big_blade.duration[2]%$秒。",
["TOWER_SAND_4_SKILL_BIG_BLADE_2_NAME"] = "厄运回旋",
["TOWER_SAND_4_SKILL_BIG_BLADE_3_DESCRIPTION"] = "旋转刀刃每秒造成%$towers.sand.skill_big_blade.s_damage_min[3]%$到%$towers.sand.skill_big_blade.s_damage_max[3]%$点物理伤害，持续%$towers.sand.skill_big_blade.duration[3]%$秒。",
["TOWER_SAND_4_SKILL_BIG_BLADE_3_NAME"] = "厄运回旋",
["TOWER_SAND_4_SKILL_BIG_BLADE_NOTE"] = "你迷得我团团转，宝贝。",
["TOWER_SAND_4_SKILL_GOLD_1_DESCRIPTION"] = "投掷一枚弹射镖刃，对敌人造成%$towers.sand.skill_gold.s_damage[1]%$点物理伤害，被镖刃击杀的敌人会额外产生%$towers.sand.skill_gold.gold_extra[1]%$枚金币。",
["TOWER_SAND_4_SKILL_GOLD_1_NAME"] = "赏金狩猎",
["TOWER_SAND_4_SKILL_GOLD_2_DESCRIPTION"] = "镖刃造成%$towers.sand.skill_gold.s_damage[2]%$点物理伤害，击杀敌人会额外产生%$towers.sand.skill_gold.gold_extra[2]%$枚金币。",
["TOWER_SAND_4_SKILL_GOLD_2_NAME"] = "赏金狩猎",
["TOWER_SAND_4_SKILL_GOLD_3_DESCRIPTION"] = "镖刃造成%$towers.sand.skill_gold.s_damage[3]%$点物理伤害，击杀敌人会额外产生%$towers.sand.skill_gold.gold_extra[3]%$枚金币。",
["TOWER_SAND_4_SKILL_GOLD_3_NAME"] = "赏金狩猎",
["TOWER_SAND_4_SKILL_GOLD_NOTE"] = "悬赏写的是“不论死活”。",
["TOWER_SAND_DESC"] = "来自战锤要塞的沙丘哨兵可谓是沙漠中最致命的杀手。",
["TOWER_SAND_NAME"] = "沙丘哨兵",
["TOWER_SELL"] = "出售防御塔",
["TOWER_SPARKING_GEODE_1_DESCRIPTION"] = "风暴召唤者，混沌制造者。注意其能量消耗。",
["TOWER_SPARKING_GEODE_1_NAME"] = "电涌巨像 I",
["TOWER_SPARKING_GEODE_2_DESCRIPTION"] = "风暴召唤者，混沌制造者。注意其能量消耗。",
["TOWER_SPARKING_GEODE_2_NAME"] = "电涌巨像 II",
["TOWER_SPARKING_GEODE_3_DESCRIPTION"] = "风暴召唤者，混沌制造者。注意其能量消耗。",
["TOWER_SPARKING_GEODE_3_NAME"] = "电涌巨像 III",
["TOWER_SPARKING_GEODE_4_CRISTALIZE"] = "十万伏特！",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_1_DESCRIPTION"] = "每%$towers.sparking_geode.crystalize.cooldown[1]%$秒，巨像会使范围内的%$towers.sparking_geode.crystalize.max_targets[1]%$名敌人结晶化，使其眩晕，受到的伤害增加%$towers.sparking_geode.crystalize.s_received_damage_factor[1]%$%。",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_1_NAME"] = "结晶化",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_2_DESCRIPTION"] = "每%$towers.sparking_geode.crystalize.cooldown[2]%$秒，巨像会使范围内的%$towers.sparking_geode.crystalize.max_targets[2]%$名敌人结晶化，使其眩晕，受到的伤害增加%$towers.sparking_geode.crystalize.s_received_damage_factor[2]%$%。",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_2_NAME"] = "结晶化",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_3_DESCRIPTION"] = "每%$towers.sparking_geode.crystalize.cooldown[3]%$秒，巨像会使范围内的%$towers.sparking_geode.crystalize.max_targets[3]%$名敌人结晶化，使其眩晕，受到的伤害增加%$towers.sparking_geode.crystalize.s_received_damage_factor[3]%$%。",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_3_NAME"] = "结晶化",
["TOWER_SPARKING_GEODE_4_CRYSTALIZE_1_DESCRIPTION"] = "每%$towers.sparking_geode.crystalize.cooldown[1]%$秒，巨像会使范围内的%$towers.sparking_geode.crystalize.max_targets[1]%$名敌人结晶化，使其眩晕，受到的伤害增加%$towers.sparking_geode.crystalize.s_received_damage_factor[1]%$%。",
["TOWER_SPARKING_GEODE_4_CRYSTALIZE_1_NAME"] = "结晶化",
["TOWER_SPARKING_GEODE_4_DESCRIPTION"] = "风暴召唤者，混沌制造者。注意其能量消耗。",
["TOWER_SPARKING_GEODE_4_NAME"] = "电涌巨像 IV",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST"] = "更努力、更出色、更迅速、更强大。",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_1_DESCRIPTION"] = "每%$towers.sparking_geode.spike_burst.cooldown[1]%$秒，巨像会创造一个电场，对周围的敌人造成伤害并使其减速，持续%$towers.sparking_geode.spike_burst.duration[1]%$秒。",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_1_NAME"] = "电气涌流",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_2_DESCRIPTION"] = "每%$towers.sparking_geode.spike_burst.cooldown[2]%$秒，巨像会创造一个电场，对周围的敌人造成伤害并使其减速，持续%$towers.sparking_geode.spike_burst.duration[2]%$秒。",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_2_NAME"] = "电气涌流",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_3_DESCRIPTION"] = "每%$towers.sparking_geode.spike_burst.cooldown[3]%$秒，巨像会创造一个电场，对周围的敌人造成伤害并使其减速，持续%$towers.sparking_geode.spike_burst.duration[3]%$秒。",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_3_NAME"] = "电气涌流",
["TOWER_SPARKING_GEODE_DESC"] = "这尊强大的巨像源自一个和平的古老种族，他遵循着保护的本能，驱使闪电力量为联盟而战，如暴风骤雨般横扫敌人。",
["TOWER_SPARKING_GEODE_NAME"] = "电涌巨像",
["TOWER_STAGE_13_SUNRAY_NAME"] = "暗光高塔",
["TOWER_STAGE_13_SUNRAY_REPAIR_DESCRIPTION"] = "修复这座塔以使用其毁灭性的力量。",
["TOWER_STAGE_13_SUNRAY_REPAIR_NAME"] = "修理",
["TOWER_STAGE_17_WEIRDWOOD_NAME"] = "怪异树木",
["TOWER_STAGE_18_ELVEN_BARRACK_DESCRIPTION"] = "雇佣精灵奋战到底。",
["TOWER_STAGE_18_ELVEN_BARRACK_NAME"] = "精灵雇佣兵",
["TOWER_STAGE_20_ARBOREAN_BARRACK_DESCRIPTION"] = "召集树灵族人参战。",
["TOWER_STAGE_20_ARBOREAN_BARRACK_NAME"] = "树灵卫队",
["TOWER_STAGE_20_ARBOREAN_HONEY_DESCRIPTION"] = "召唤强大的养蜂人。",
["TOWER_STAGE_20_ARBOREAN_HONEY_NAME"] = "树灵养蜂人",
["TOWER_STAGE_20_ARBOREAN_OLDTREE_DESCRIPTION"] = "唤醒古树支援战场。",
["TOWER_STAGE_20_ARBOREAN_OLDTREE_NAME"] = "古树",
["TOWER_STAGE_22_ARBOREAN_MAGES_NAME"] = "树灵法师",
["TOWER_STAGE_28_PRIESTS_BARRACK_DESCRIPTION"] = "痛改前非的教徒。他们会施展巫术与敌人作战，在死亡时化作怪物。",
["TOWER_STAGE_28_PRIESTS_BARRACK_NAME"] = "无明教团",
["TOWER_STARGAZER_1_DESCRIPTION"] = "观星者试图掌控来自凡尘之外的强大魔法。",
["TOWER_STARGAZER_1_NAME"] = "精灵观星者I",
["TOWER_STARGAZER_2_DESCRIPTION"] = "观星者试图掌控来自凡尘之外的强大魔法。",
["TOWER_STARGAZER_2_NAME"] = "精灵观星者II",
["TOWER_STARGAZER_3_DESCRIPTION"] = "观星者试图掌控来自凡尘之外的强大魔法。",
["TOWER_STARGAZER_3_NAME"] = "精灵观星者III",
["TOWER_STARGAZER_4_DESCRIPTION"] = "观星者试图掌控来自凡尘之外的强大魔法。",
["TOWER_STARGAZER_4_EVENT_HORIZON_1_DESCRIPTION"] = "传送最多%$towers.elven_stargazers.teleport.max_targets[1]%$名敌人，使他们倒退一段距离。",
["TOWER_STARGAZER_4_EVENT_HORIZON_1_NAME"] = "事件视界",
["TOWER_STARGAZER_4_EVENT_HORIZON_2_DESCRIPTION"] = "传送最多%$towers.elven_stargazers.teleport.max_targets[2]%$名敌人，使他们倒退更远距离。",
["TOWER_STARGAZER_4_EVENT_HORIZON_2_NAME"] = "事件视界",
["TOWER_STARGAZER_4_EVENT_HORIZON_3_DESCRIPTION"] = "传送最多%$towers.elven_stargazers.teleport.max_targets[3]%$名敌人，使他们倒退更更远距离。",
["TOWER_STARGAZER_4_EVENT_HORIZON_3_NAME"] = "事件视界",
["TOWER_STARGAZER_4_EVENT_HORIZON_NAME"] = "事件视界",
["TOWER_STARGAZER_4_EVENT_HORIZON_NOTE"] = "消失，又重现。",
["TOWER_STARGAZER_4_NAME"] = "精灵观星者IV",
["TOWER_STARGAZER_4_RISING_STAR_1_DESCRIPTION"] = "精灵观星者消灭的目标会爆出%$towers.elven_stargazers.stars_death.stars[1]%$颗星星，对敌人造成%$towers.elven_stargazers.stars_death.damage_min[1]%$-%$towers.elven_stargazers.stars_death.damage_max[1]%$点伤害。",
["TOWER_STARGAZER_4_RISING_STAR_1_NAME"] = "星光涌现",
["TOWER_STARGAZER_4_RISING_STAR_2_DESCRIPTION"] = "星星数量增加至%$towers.elven_stargazers.stars_death.stars[2]%$，对敌人造成%$towers.elven_stargazers.stars_death.damage_min[2]%$-%$towers.elven_stargazers.stars_death.damage_max[2]%$点魔法伤害。",
["TOWER_STARGAZER_4_RISING_STAR_2_NAME"] = "星光涌现",
["TOWER_STARGAZER_4_RISING_STAR_3_DESCRIPTION"] = "星星数量增加至%$towers.elven_stargazers.stars_death.stars[3]%$，对敌人造成%$towers.elven_stargazers.stars_death.damage_min[3]%$-%$towers.elven_stargazers.stars_death.damage_max[3]%$魔法伤害。",
["TOWER_STARGAZER_4_RISING_STAR_3_NAME"] = "星光涌现",
["TOWER_STARGAZER_4_RISING_STAR_NAME"] = "星光涌现",
["TOWER_STARGAZER_4_RISING_STAR_NOTE"] = "星屑旋转功！",
["TOWER_TRICANNON_1_DESCRIPTION"] = "一首献给战争的毁灭情歌，描述的景象对敌我而言皆是人间炼狱。",
["TOWER_TRICANNON_1_NAME"] = "三管加农炮I",
["TOWER_TRICANNON_2_DESCRIPTION"] = "一首献给战争的毁灭情歌，描述的景象对敌我而言皆是人间炼狱。",
["TOWER_TRICANNON_2_NAME"] = "三管加农炮II",
["TOWER_TRICANNON_3_DESCRIPTION"] = "一首献给战争的毁灭情歌，描述的景象对敌我而言皆是人间炼狱。",
["TOWER_TRICANNON_3_NAME"] = "三管加农炮III",
["TOWER_TRICANNON_4_BOMBARDMENT_1_DESCRIPTION"] = "在大范围内迅速发射多枚炸弹，每枚造成%$towers.tricannon.bombardment.damage_min[1]%$-%$towers.tricannon.bombardment.damage_max[1]%$点物理伤害。",
["TOWER_TRICANNON_4_BOMBARDMENT_1_NAME"] = "猛烈轰炸",
["TOWER_TRICANNON_4_BOMBARDMENT_2_DESCRIPTION"] = "扩大范围，发射更多炸弹，每枚造成%$towers.tricannon.bombardment.damage_min[2]%$-%$towers.tricannon.bombardment.damage_max[2]%$点物理伤害。",
["TOWER_TRICANNON_4_BOMBARDMENT_2_NAME"] = "猛烈轰炸",
["TOWER_TRICANNON_4_BOMBARDMENT_3_DESCRIPTION"] = "扩大范围，发射超多炸弹，每枚造成%$towers.tricannon.bombardment.damage_min[3]%$-%$towers.tricannon.bombardment.damage_max[3]%$点物理伤害。",
["TOWER_TRICANNON_4_BOMBARDMENT_3_NAME"] = "猛烈轰炸",
["TOWER_TRICANNON_4_BOMBARDMENT_NAME"] = "猛烈轰炸",
["TOWER_TRICANNON_4_BOMBARDMENT_NOTE"] = "这才叫火力全开。",
["TOWER_TRICANNON_4_DESCRIPTION"] = "一首献给战争的毁灭情歌，描述的景象对敌我而言皆是人间炼狱。",
["TOWER_TRICANNON_4_NAME"] = "三管加农炮IV",
["TOWER_TRICANNON_4_OVERHEAT_1_DESCRIPTION"] = "加农炮的炮管变得红热，使炸弹灼烧地面，对敌人每秒造成%$towers.tricannon.overheat.decal.effect.s_damage[1]%$点真实伤害，持续%$towers.tricannon.overheat.duration[1]%$秒。",
["TOWER_TRICANNON_4_OVERHEAT_1_NAME"] = "过热模式",
["TOWER_TRICANNON_4_OVERHEAT_2_DESCRIPTION"] = "每片灼烧区域每秒造成%$towers.tricannon.overheat.decal.effect.s_damage[2]%$点真实伤害，持续时间延长至%$towers.tricannon.overheat.duration[2]%$秒。",
["TOWER_TRICANNON_4_OVERHEAT_2_NAME"] = "过热模式",
["TOWER_TRICANNON_4_OVERHEAT_3_DESCRIPTION"] = "每片灼烧区域每秒造成%$towers.tricannon.overheat.decal.effect.s_damage[3]%$点真实伤害，持续时间延长至%$towers.tricannon.overheat.duration[3]%$秒。",
["TOWER_TRICANNON_4_OVERHEAT_3_NAME"] = "过热模式",
["TOWER_TRICANNON_4_OVERHEAT_NAME"] = "过热模式",
["TOWER_TRICANNON_4_OVERHEAT_NOTE"] = "我们热得发烫。",
["TOWER_TRICANNON_DESC"] = "黑暗军研制的多管加农炮，降下火焰与毁灭，重新定义了现代战争。",
["TOWER_TRICANNON_NAME"] = "三管加农炮",
["TUTORIAL_hero_room_hero_points_desc"] = "通过在战斗中升级各个英雄来获得英雄点数。",
["TUTORIAL_hero_room_hero_points_title"] = "英雄点数",
["TUTORIAL_hero_room_power_desc"] = "使用英雄点数购买并提高英雄的能力。",
["TUTORIAL_hero_room_power_title"] = "英雄能力",
["TUTORIAL_hero_room_tutorial_navigate_desc"] = "浏览不同的英雄",
["TUTORIAL_hero_room_tutorial_select_desc"] = "配置你想在战场上使用的英雄。",
["TUTORIAL_item_room_buy_desc"] = "使用你的宝石购买战场上的辅助道具。",
["TUTORIAL_item_room_buy_title"] = "购买道具",
["TUTORIAL_item_room_tutorial_equip_desc"] = "将道具装备至栏位中，拖动以调整顺序！",
["TUTORIAL_item_room_tutorial_navigate_desc"] = "浏览各种可用的道具。",
["TUTORIAL_tower_room_power_desc"] = "这些技能可以在防御塔四级时使用。",
["TUTORIAL_tower_room_power_title"] = "四级技能",
["TUTORIAL_tower_room_tutorial_equip_desc"] = "装备新的防御塔来尝试不同的组合。",
["TUTORIAL_tower_room_tutorial_navigate_desc"] = "浏览不同的防御塔",
["TUTORIAL_tower_room_tutorial_slots_desc"] = "将防御塔装备至栏位中，拖动以调整顺序！",
["TUTORIAL_upgrade_room_tooltip_buy_desc"] = "使用点数购买并升级你的能力、防御塔和英雄。",
["TUTORIAL_upgrade_room_tooltip_souls_desc"] = "完成战役关卡可获得升级点数。",
["TUTORIAL_upgrade_room_tooltip_souls_title"] = "升级点数",
["Tap to continue"] = "点击以继续",
["Touch on the path to move the hero."] = "路径上轻按可移动英雄。",
["Tower construction"] = "防御塔建造",
["Typography"] = "排版",
["UPDATE_POPUP"] = "更新",
["UPDATING_CLOUDSAVE_MESSAGE"] = "更新保存在云中的游戏……",
["UPGRADES"] = "升级",
["UPGRADES AND HEROES RESTRICTIONS!"] = "升级与英雄限制！",
["Use the earned hero points to train your hero!"] = "用获得的英雄点数来训练英雄！",
["Use the earned stars to improve your towers and powers!"] = "用获得的星星改良防御塔和能力！",
["VICTORY"] = "胜利",
["Veteran"] = "老兵",
["Victory!"] = "胜利！",
["Voice Talent"] = "配音",
["WAVE_TOOLTIP_TAP_AGAIN"] = "再次点击来提前召唤下一波",
["WAVE_TOOLTIP_TITLE"] = "一波敌人即将到来",
["We would like to thank"] = "特别鸣谢",
["YODO1_OPTIONS_FOOTER"] = "客服邮箱：<EMAIL>",
["YODO1_OTHER_TEXTS_FOR_FONT_SUBSETTING"] = "根据《国家新闻出版署通知》，非周五、周六、周日和法定节假日20时至21时的时段，未成年人账号，不得进行游戏提示隐私协议用户条款退出游戏非游戏时间已经到认证失败实名广告播放失败",
["YODO1_SDK_CADPA_12_TIPS"] = "（1）本游戏是一款卡通风格塔防类的休闲移动游戏，适用于年满 12周岁及以上的用户，建议未成年人在家长监护下使用游戏产品。\n（2）本游戏基于架空的故事背景，没有基于真实历史的改编内容。游戏玩法基于肢体操作，锻炼玩家手眼协调能力。鼓励玩家提升和挑战自我。\n（3）本游戏中有用户实名认证系统，认证为未成年人的用户将接受以下管理：未成年人仅限周五、周六、周日和法定节假日每日20时至21时向未成年人提供1小时网络游戏服务，其他时间均不以任何形式向未成年人提供网络游戏服务。\n（4）本游戏是一款卡通风格塔防类的休闲移动游戏，游戏操作上手简单，有助于锻炼玩家的大脑反应速度和手眼协调能力，能够带给玩家积极愉悦的情绪体验，增强玩家的自信心。\n",
["YODO1_SDK_CONSENT_MSG"] = "继续游戏代表同意我们的用户协议和隐私政策",
["YODO1_SDK_MORE_GAMES"] = "更多精彩",
["YODO1_SDK_SPLASH_CUSTOM_TEXT"] = "抵制不良游戏，拒绝盗版游戏。  注意自我保护，谨防受骗上当。\n适度游戏益脑，沉迷游戏伤身。  合理安排时间，享受健康生活。",
["YODO1_SDK_TOS_BUTTON"] = "用户协议",
["Yes"] = "好",
["You must log in to Google Play game services to track achievements."] = "您必须登录Google Play游戏服务才能跟踪成就。",
["You must watch the whole video."] = "您必须看完完整视频",
["You will no longer be tracking achievements."] = "您的成就将不会继续跟踪。",
["_manually_included_characters"] = "$ ¥ ￥ ƒ ₩ € ™ × $ zł ¢ £ ¤ ¥ ƒ ден дин лв. ؋ ৳ ฿ ლ ₡ ₣ ₤ ₥ ₦ ₨ ₩ ₪ ₫ € ₭ ₮ ₱ ₲ ₴ ₵ ₹ ₺ ₽ ﷼",
["alliance_close_to_home_DESCRIPTION"] = "在关卡开始时提供额外金币。",
["alliance_close_to_home_NAME"] = "共享储备",
["alliance_corageous_stand_DESCRIPTION"] = "每建立一座利尼维亚防御塔就会提高英雄的血量。",
["alliance_corageous_stand_NAME"] = "英勇无畏",
["alliance_display_of_true_might_dark_DESCRIPTION"] = "黑暗大军的英雄奥义现在能够使屏幕上的所有敌人减速。",
["alliance_display_of_true_might_dark_NAME"] = "灾厄诅咒",
["alliance_display_of_true_might_linirea_DESCRIPTION"] = "利尼维亚的英雄奥义现在能够治疗和复活所有友军单位。",
["alliance_display_of_true_might_linirea_NAME"] = "生命赐福",
["alliance_flux_altering_coils_DESCRIPTION"] = "将所有出口旗帜替换为奥术石柱，可将附近的敌人往回传送。",
["alliance_flux_altering_coils_NAME"] = "奥术石柱",
["alliance_friends_of_the_crown_DESCRIPTION"] = "每带上一位利尼维亚的英雄就会减少建造和升级防御塔的费用。",
["alliance_friends_of_the_crown_NAME"] = "王室忠魂",
["alliance_merciless_DESCRIPTION"] = "每建立一座黑暗大军防御塔就会提高英雄的攻击伤害。",
["alliance_merciless_NAME"] = "无情阵线",
["alliance_seal_of_punishment_DESCRIPTION"] = "将防御点替换为魔法封印，对经过的敌人造成伤害。",
["alliance_seal_of_punishment_NAME"] = "惩戒之印",
["alliance_shady_company_DESCRIPTION"] = "每带上一名黑暗大军的英雄就会提高防御塔的攻击伤害。",
["alliance_shady_company_NAME"] = "暗影随行",
["alliance_shared_reserves_DESCRIPTION"] = "在关卡开始时提供额外金币。",
["alliance_shared_reserves_NAME"] = "共享储备",
["baloon start battle iphone"] = "轻按两次召唤敌军",
["build defensive towers along the road to stop them."] = "沿着道路建造防御塔来阻止他们。",
["build towers to defend the road."] = "建造防御塔来守卫道路。",
["check the stage description to see:"] = "确认关卡描述来查看：",
["deals area damage"] = "造成范围伤害",
["don't let enemies past this point."] = "不要让敌人通过这个点。",
["earn gold by killing enemies."] = "消灭敌人来获取金币。",
["good rate of fire"] = "良好的射击速率",
["heroes_desperate_effort_DESCRIPTION"] = "英雄的攻击将无视敌人10％的抗性",
["heroes_desperate_effort_NAME"] = "知己知彼",
["heroes_lethal_focus_DESCRIPTION"] = "英雄的攻击有20％几率造成暴击伤害。",
["heroes_lethal_focus_NAME"] = "弱点打击",
["heroes_limit_pushing_DESCRIPTION"] = "每个英雄奥义使用五次后，其冷却时间将立即重置。",
["heroes_limit_pushing_NAME"] = "突破极限",
["heroes_lone_wolves_DESCRIPTION"] = "英雄们远离彼此时将获得更多经验。",
["heroes_lone_wolves_NAME"] = "孤胆英雄",
["heroes_nimble_physique_DESCRIPTION"] = "英雄有20％几率闪避敌人攻击。",
["heroes_nimble_physique_NAME"] = "矫健身手",
["heroes_unlimited_vigor_DESCRIPTION"] = "减少所有英雄奥义10%的冷却时间。",
["heroes_unlimited_vigor_NAME"] = "活力无限",
["heroes_visual_learning_DESCRIPTION"] = "当英雄们彼此靠近时会获得额外10%的护甲。",
["heroes_visual_learning_NAME"] = "并肩作战",
["high damage, armor piercing"] = "伤害高，护甲穿透",
["iron and heroic challenges may have restrictions on upgrades!"] = "钢铁和英雄挑战中升级会有所限制！",
["max lvl allowed"] = "允许最大等级",
["multi-shot, armor piercing"] = "多重射击，护甲穿刺",
["no heroes"] = "无英雄",
["pause popup"] = "游戏暂停",
["protect your lands from the enemy attacks."] = "保护土地免受敌人攻击。",
["rally range"] = "集结范围",
["ready for action!"] = "准备好行动！",
["reinforcements_intense_workout_DESCRIPTION"] = "提高援军血量和持续时间。",
["reinforcements_intense_workout_NAME"] = "强化训练",
["reinforcements_master_blacksmiths_DESCRIPTION"] = "增强援军的伤害和护甲。",
["reinforcements_master_blacksmiths_NAME"] = "铁匠大师",
["reinforcements_night_veil_DESCRIPTION"] = "暗影射手的射程和攻击速度增加。",
["reinforcements_night_veil_NAME"] = "灰烬之弓",
["reinforcements_power_trio_DESCRIPTION"] = "召唤援军时也会同时召唤一名骑士楷模。",
["reinforcements_power_trio_NAME"] = "王国楷模",
["reinforcements_power_trio_dark_DESCRIPTION"] = "召唤援军时也会同时召唤一名暗影唤鸦者。",
["reinforcements_power_trio_dark_NAME"] = "暗影唤鸦者",
["reinforcements_rebel_militia_DESCRIPTION"] = "利尼维亚抵抗军将取代援军，他们是身穿厚重铠甲的坚韧战士。",
["reinforcements_rebel_militia_NAME"] = "王国义勇",
["reinforcements_shadow_archer_DESCRIPTION"] = "暗影射手将取代援军，进行远程攻击并可以锁定飞行单位。",
["reinforcements_shadow_archer_NAME"] = "暗影号令",
["reinforcements_thorny_armor_DESCRIPTION"] = "利尼维亚抵抗军将反弹敌人的一部分近战伤害。",
["reinforcements_thorny_armor_NAME"] = "反伤护甲",
["resists damage from"] = "抵抗 伤害，免于",
["select the rally point control"] = "选择集结点控制",
["select the tower you want to build!"] = "选择你想建造的防御塔",
["select where you want to move your soldiers"] = "选择你想让士兵前往的位置",
["soldiers block enemies"] = "士兵能阻截敌人",
["some enemies enjoy different levels of magic resistance that protects them against magical attacks."] = "有些敌人具有等级不一的魔法抗性，保护他们免受魔法攻击伤害。",
["some enemies wear armor of different strengths that protects them against non-magical attacks."] = "有些敌人穿着强度不一的护甲，保护他们免受 非魔法攻击伤害。",
["tap these!"] = "点击这些！",
["tap to continue..."] = "点击以继续……",
["tap twice to call wave"] = "轻按两次召唤敌军",
["this is a strategic point."] = "这是一个战略点。",
["towers_favorite_customer_DESCRIPTION"] = "购买技能的最终等级时，将其费用降低50%。",
["towers_favorite_customer_NAME"] = "贵宾待遇",
["towers_golden_time_DESCRIPTION"] = "增加提前召唤波次获得的额外金币奖励。",
["towers_golden_time_NAME"] = "黄金时间",
["towers_improved_formulas_DESCRIPTION"] = "使所有来自防御塔的爆炸伤害最大化，并增加其伤害范围。",
["towers_improved_formulas_NAME"] = "改进公式",
["towers_keen_accuracy_DESCRIPTION"] = "减少所有防御塔技能20%的冷却时间。",
["towers_keen_accuracy_NAME"] = "战斗狂热",
["towers_royal_training_DESCRIPTION"] = "减少防御塔单位的刷新时间和援军的冷却时间。",
["towers_royal_training_NAME"] = "行动纲领",
["towers_scoping_mechanism_DESCRIPTION"] = "所有塔的攻击范围增加10%。",
["towers_scoping_mechanism_NAME"] = "瞄准装置",
["towers_war_rations_DESCRIPTION"] = "所有塔单位的生命值提高10%。",
["towers_war_rations_NAME"] = "作战口粮",
["towers_wise_investment_DESCRIPTION"] = "塔现在出售时返还其成本的90%。",
["towers_wise_investment_NAME"] = "明智投资",
["wOOt!"] = "噢吼！",
["you can adjust your soldiers rally point to make them defend a different area."] = "你可以更改士兵的集结点来使他们防御不同的区域。",
}
