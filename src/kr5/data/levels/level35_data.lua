return {
    custom_spawn_pos = {
        {
            pos = {
                x = 543,
                y = 89
            }
        },
        {
            pos = {
                x = 1085,
                y = 190
            }
        }
    },
    custom_start_pos = {
        pos = {
            x = 512,
            y = 384
        },
        zoom = 1.2
    },
    entities_list = {
        {
            template = "controller_stage_35",
            pos = {
                x = 512,
                y = 384
            }
        },
        {
            template = "controller_stage_35_bull_king",
            pos = {
                x = 517,
                y = 533
            }
        },
        {
            template = "controller_stage_35_lava_splash",
            pos = {
                x = 512,
                y = 384
            }
        },
        {
            template = "controller_stage_35_portal_left",
            pos = {
                x = -23,
                y = 470
            }
        },
        {
            template = "controller_stage_35_portal_right",
            pos = {
                x = 1042,
                y = 473
            }
        },
        {
            template = "controller_stage_35_princess_powers",
            pos = {
                x = 512,
                y = 404
            }
        },
        {
            template = "controller_stage_35_redboy_powers",
            pos = {
                x = 512,
                y = 404
            }
        },
        {
            template = "controller_stage_35_small_spawner",
            pos = {
                x = 412,
                y = 416
            },
            spawner_nmbr = 2
        },
        {
            template = "controller_stage_35_small_spawner",
            pos = {
                x = 199,
                y = 456
            },
            spawner_nmbr = 3
        },
        {
            template = "controller_stage_35_small_spawner",
            pos = {
                x = 819,
                y = 494
            },
            spawner_nmbr = 1
        },
        {
            template = "controller_stage_35_water_splash",
            pos = {
                x = 512,
                y = 384
            }
        },
        {
            template = "controller_stage_35_golden_eyed_left",
            pos = {
                x = 422,
                y = 505
            }
        },
        {
            template = "controller_stage_35_golden_eyed_right",
            pos = {
                x = 611,
                y = 505
            }
        },
        {
            template = "debug_path_renderer",
            ["path_debug.background_color"] = {
                46,
                193,
                142,
                0
            },
            ["path_debug.path_color"] = {
                168,
                199,
                169,
                0
            },
            pos = {
                x = -300,
                y = 868
            }
        },
        {
            template = "decal_background",
            pos = {
                x = 512,
                y = 384
            },
            ["render.sprites[1].name"] = "Stage35_0001",
            ["render.sprites[1].z"] = 1000
        },
        {
            template = "decal_defend_point5",
            ["editor.alpha"] = 10,
            ["editor.exit_id"] = 1,
            ["editor.flip"] = 0,
            ["editor.orientation"] = 1,
            pos = {
                x = 543,
                y = 89
            }
        },
        {
            template = "decal_defend_point5",
            ["editor.alpha"] = 10,
            ["editor.exit_id"] = 1,
            ["editor.flip"] = 1,
            ["editor.orientation"] = 1,
            pos = {
                x = 1085,
                y = 190
            }
        },
        {
            template = "decal_defense_flag5",
            ["editor.flip"] = 0,
            pos = {
                x = 461,
                y = 88
            }
        },
        {
            template = "decal_defense_flag5",
            ["editor.flip"] = 0,
            pos = {
                x = 624,
                y = 88
            }
        },
        {
            template = "decal_defense_flag5",
            ["editor.flip"] = -1,
            pos = {
                x = 1116,
                y = 112
            }
        },
        {
            template = "decal_defense_flag5",
            ["editor.flip"] = 0,
            pos = {
                x = 1071,
                y = 261
            }
        },
        {
            template = "decal_generic_kill_area_rect",
            kill_area_id = 1,
            kill_size = {
                x = 489.28571428571001,
                y = 382.14285714286001
            },
            pos = {
                x = 358,
                y = 145
            }
        },
        {
            template = "decal_generic_kill_area_rect",
            kill_area_id = 2,
            kill_size = {
                x = 411.42857142857002,
                y = 800
            },
            pos = {
                x = 402,
                y = 380
            }
        },
        {
            template = "decal_generic_kill_area_rect",
            kill_area_id = 1,
            kill_size = {
                x = 317.85714285713999,
                y = 617.85714285714005
            },
            pos = {
                x = 2,
                y = 439
            }
        },
        {
            template = "decal_generic_kill_area_rect",
            kill_area_id = 1,
            kill_size = {
                x = 446.42857142857002,
                y = 296.42857142857002
            },
            pos = {
                x = 125,
                y = 728
            }
        },
        {
            template = "decal_stage_35_mask_path_closed",
            pos = {
                x = 517,
                y = 384
            }
        },
        {
            template = "decal_stage_35_mask_path_open",
            pos = {
                x = 512,
                y = 384
            }
        },
        {
            template = "decal_stage_35_mask_princess_bottom",
            pos = {
                x = 507,
                y = 387
            }
        },
        {
            template = "decal_stage_35_mask_princess_top",
            pos = {
                x = 507,
                y = 387
            }
        },
        {
            template = "decal_stage_35_mask_redboy_bottom",
            pos = {
                x = 512,
                y = 384
            }
        },
        {
            template = "decal_stage_35_mask_redboy_top",
            pos = {
                x = 512,
                y = 384
            }
        },
        {
            template = "editor_wave_flag",
            ["editor.len"] = 130,
            ["editor.path_id"] = 7,
            ["editor.r"] = -3.1415926535899001,
            pos = {
                x = 28,
                y = 420
            }
        },
        {
            template = "editor_wave_flag",
            ["editor.len"] = 130,
            ["editor.path_id"] = 8,
            ["editor.r"] = -6.2831853071796004,
            pos = {
                x = 975,
                y = 427
            }
        },
        {
            template = "editor_wave_flag",
            ["editor.len"] = 130,
            ["editor.path_id"] = 6,
            ["editor.r"] = -4.7123889803847003,
            pos = {
                x = 637,
                y = 501
            }
        },
        {
            template = "editor_wave_flag",
            ["editor.len"] = 130,
            ["editor.path_id"] = 5,
            ["editor.r"] = -4.7123889803847003,
            pos = {
                x = 384,
                y = 506
            }
        },
        {
            template = "editor_wave_flag",
            ["editor.len"] = 130,
            ["editor.path_id"] = 4,
            ["editor.r"] = -4.7123889803847003,
            pos = {
                x = 822,
                y = 591
            }
        },
        {
            template = "editor_wave_flag",
            ["editor.len"] = 130,
            ["editor.path_id"] = 3,
            ["editor.r"] = -4.7123889803847003,
            pos = {
                x = 775,
                y = 592
            }
        },
        {
            template = "editor_wave_flag",
            ["editor.len"] = 130,
            ["editor.path_id"] = 1,
            ["editor.r"] = -4.7123889803847003,
            pos = {
                x = 205,
                y = 608
            }
        },
        {
            template = "editor_wave_flag",
            ["editor.len"] = 130,
            ["editor.path_id"] = 2,
            ["editor.r"] = -4.7123889803847003,
            pos = {
                x = 251,
                y = 608
            }
        },
        {
            template = "tower_holder_blocked_elemental_earth",
            ["editor.game_mode"] = 2,
            pos = {
                x = 364,
                y = 209
            },
            ["tower.default_rally_pos"] = {
                x = 305,
                y = 292
            },
            ["tower.holder_id"] = "21",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "21"
        },
        {
            template = "tower_holder_blocked_elemental_fire",
            ["editor.game_mode"] = 2,
            pos = {
                x = 180,
                y = 309
            },
            ["tower.default_rally_pos"] = {
                x = 48,
                y = 305
            },
            ["tower.holder_id"] = "23",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "23"
        },
        {
            template = "tower_holder_blocked_elemental_metal",
            ["editor.game_mode"] = 1,
            pos = {
                x = 180,
                y = 309
            },
            ["tower.default_rally_pos"] = {
                x = 48,
                y = 305
            },
            ["tower.holder_id"] = "3",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "3",
            unlock_holder_type = "tower_holder_sea_of_trees_16"
        },
        {
            template = "tower_holder_blocked_elemental_metal",
            ["editor.game_mode"] = 2,
            pos = {
                x = 831,
                y = 313
            },
            ["tower.default_rally_pos"] = {
                x = 740,
                y = 270
            },
            ["tower.holder_id"] = "32",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "32"
        },
        {
            template = "tower_holder_blocked_elemental_water",
            ["editor.game_mode"] = 2,
            pos = {
                x = 660,
                y = 184
            },
            ["tower.default_rally_pos"] = {
                x = 545,
                y = 197
            },
            ["tower.holder_id"] = "33",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "33"
        },
        {
            template = "tower_holder_blocked_elemental_wood",
            ["editor.game_mode"] = 2,
            pos = {
                x = 818,
                y = 448
            },
            ["tower.default_rally_pos"] = {
                x = 825,
                y = 395
            },
            ["tower.holder_id"] = "29",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "29"
        },
        {
            template = "tower_holder_blocked_stage_35_house_1",
            ["editor.game_mode"] = 1,
            pos = {
                x = 21,
                y = 189
            },
            ["tower.default_rally_pos"] = {
                x = 130,
                y = 236
            },
            ["tower.holder_id"] = "2",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "2",
            unlock_holder_type = "tower_holder_sea_of_trees_16"
        },
        {
            template = "tower_holder_blocked_stage_35_house_2",
            ["editor.game_mode"] = 1,
            pos = {
                x = 913,
                y = 585
            },
            ["tower.default_rally_pos"] = {
                x = 814,
                y = 558
            },
            ["tower.holder_id"] = "10",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "10",
            unlock_holder_type = "tower_holder_sea_of_trees_16"
        },
        {
            template = "tower_holder_blocked_stage_35_house_3",
            ["editor.game_mode"] = 1,
            pos = {
                x = 184,
                y = 525
            },
            ["tower.default_rally_pos"] = {
                x = 269,
                y = 575
            },
            ["tower.holder_id"] = "5",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "5",
            unlock_holder_type = "tower_holder_sea_of_trees_16"
        },
        {
            template = "tower_holder_blocked_stage_35_house_4",
            ["editor.game_mode"] = 1,
            pos = {
                x = 933,
                y = 283
            },
            ["tower.default_rally_pos"] = {
                x = 925,
                y = 210
            },
            ["tower.holder_id"] = "11",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "11",
            unlock_holder_type = "tower_holder_sea_of_trees_16"
        },
        {
            template = "tower_holder_blocked_stage_35_house_5",
            ["editor.game_mode"] = 1,
            pos = {
                x = 818,
                y = 448
            },
            ["tower.default_rally_pos"] = {
                x = 825,
                y = 395
            },
            ["tower.holder_id"] = "9",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "9",
            unlock_holder_type = "tower_holder_sea_of_trees_16"
        },
        {
            template = "tower_holder_blocked_stage_35_house_6",
            ["editor.game_mode"] = 1,
            pos = {
                x = 201,
                y = 420
            },
            ["tower.default_rally_pos"] = {
                x = 300,
                y = 425
            },
            ["tower.holder_id"] = "4",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "4",
            unlock_holder_type = "tower_holder_sea_of_trees_16"
        },
        {
            template = "tower_holder_blocked_stage_35_house_7",
            ["editor.game_mode"] = 1,
            pos = {
                x = 401,
                y = 367
            },
            ["tower.default_rally_pos"] = {
                x = 310,
                y = 312
            },
            ["tower.holder_id"] = "6",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "6",
            unlock_holder_type = "tower_holder_sea_of_trees_16"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 1,
            pos = {
                x = 660,
                y = 184
            },
            ["tower.default_rally_pos"] = {
                x = 545,
                y = 197
            },
            ["tower.holder_id"] = "13",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "13"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 3,
            pos = {
                x = 660,
                y = 184
            },
            ["tower.default_rally_pos"] = {
                x = 545,
                y = 197
            },
            ["tower.holder_id"] = "53",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "53"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 2,
            pos = {
                x = 21,
                y = 189
            },
            ["tower.default_rally_pos"] = {
                x = 130,
                y = 236
            },
            ["tower.holder_id"] = "22",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "22"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 3,
            pos = {
                x = 21,
                y = 189
            },
            ["tower.default_rally_pos"] = {
                x = 130,
                y = 236
            },
            ["tower.holder_id"] = "42",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "42"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 1,
            pos = {
                x = 364,
                y = 209
            },
            ["tower.default_rally_pos"] = {
                x = 305,
                y = 292
            },
            ["tower.holder_id"] = "1",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "1"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 3,
            pos = {
                x = 364,
                y = 209
            },
            ["tower.default_rally_pos"] = {
                x = 305,
                y = 292
            },
            ["tower.holder_id"] = "41",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "41"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 2,
            pos = {
                x = 933,
                y = 283
            },
            ["tower.default_rally_pos"] = {
                x = 925,
                y = 210
            },
            ["tower.holder_id"] = "31",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "31"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 3,
            pos = {
                x = 933,
                y = 283
            },
            ["tower.default_rally_pos"] = {
                x = 925,
                y = 210
            },
            ["tower.holder_id"] = "51",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "51"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 3,
            pos = {
                x = 180,
                y = 309
            },
            ["tower.default_rally_pos"] = {
                x = 48,
                y = 305
            },
            ["tower.holder_id"] = "43",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "43"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 1,
            pos = {
                x = 831,
                y = 313
            },
            ["tower.default_rally_pos"] = {
                x = 740,
                y = 270
            },
            ["tower.holder_id"] = "12",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "12"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 3,
            pos = {
                x = 831,
                y = 313
            },
            ["tower.default_rally_pos"] = {
                x = 740,
                y = 270
            },
            ["tower.holder_id"] = "52",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "52"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 1,
            pos = {
                x = 614,
                y = 322
            },
            ["tower.default_rally_pos"] = {
                x = 719,
                y = 369
            },
            ["tower.holder_id"] = "8",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "8"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 2,
            pos = {
                x = 614,
                y = 322
            },
            ["tower.default_rally_pos"] = {
                x = 719,
                y = 369
            },
            ["tower.holder_id"] = "28",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "28"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 3,
            pos = {
                x = 614,
                y = 322
            },
            ["tower.default_rally_pos"] = {
                x = 719,
                y = 369
            },
            ["tower.holder_id"] = "48",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "48"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 1,
            pos = {
                x = 524,
                y = 354
            },
            ["tower.default_rally_pos"] = {
                x = 477,
                y = 290
            },
            ["tower.holder_id"] = "7",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "7"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 2,
            pos = {
                x = 524,
                y = 354
            },
            ["tower.default_rally_pos"] = {
                x = 477,
                y = 290
            },
            ["tower.holder_id"] = "27",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "27"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 3,
            pos = {
                x = 524,
                y = 354
            },
            ["tower.default_rally_pos"] = {
                x = 477,
                y = 290
            },
            ["tower.holder_id"] = "47",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "47"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 2,
            pos = {
                x = 401,
                y = 367
            },
            ["tower.default_rally_pos"] = {
                x = 310,
                y = 312
            },
            ["tower.holder_id"] = "26",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "26"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 3,
            pos = {
                x = 401,
                y = 367
            },
            ["tower.default_rally_pos"] = {
                x = 310,
                y = 312
            },
            ["tower.holder_id"] = "46",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "46"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 2,
            pos = {
                x = 201,
                y = 420
            },
            ["tower.default_rally_pos"] = {
                x = 300,
                y = 425
            },
            ["tower.holder_id"] = "24",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "24"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 3,
            pos = {
                x = 201,
                y = 420
            },
            ["tower.default_rally_pos"] = {
                x = 300,
                y = 425
            },
            ["tower.holder_id"] = "44",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "44"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 3,
            pos = {
                x = 818,
                y = 448
            },
            ["tower.default_rally_pos"] = {
                x = 825,
                y = 395
            },
            ["tower.holder_id"] = "49",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "49"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 2,
            pos = {
                x = 184,
                y = 525
            },
            ["tower.default_rally_pos"] = {
                x = 269,
                y = 575
            },
            ["tower.holder_id"] = "25",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "25"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 3,
            pos = {
                x = 184,
                y = 525
            },
            ["tower.default_rally_pos"] = {
                x = 269,
                y = 575
            },
            ["tower.holder_id"] = "45",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "45"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 2,
            pos = {
                x = 913,
                y = 585
            },
            ["tower.default_rally_pos"] = {
                x = 814,
                y = 558
            },
            ["tower.holder_id"] = "30",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "30"
        },
        {
            template = "tower_holder_sea_of_trees_16",
            ["editor.game_mode"] = 3,
            pos = {
                x = 913,
                y = 585
            },
            ["tower.default_rally_pos"] = {
                x = 814,
                y = 558
            },
            ["tower.holder_id"] = "50",
            ["tower.terrain_style"] = 16,
            ["ui.nav_mesh_id"] = "50"
        }
    },
    ignore_walk_backwards_paths = {},
    invalid_path_ranges = {},
    level_mode_overrides = {
        {
            locked_powers = {},
            locked_towers = {},
            max_upgrade_level = 5
        },
        {
            nav_mesh = {
                nil,
                nil,
                nil,
                nil,
                nil,
                nil,
                nil,
                nil,
                nil,
                nil,
                nil,
                nil,
                nil,
                nil,
                nil,
                nil,
                nil,
                nil,
                nil,
                nil,
                {
                    33,
                    26,
                    22
                },
                {
                    21,
                    23
                },
                {
                    26,
                    24,
                    22
                },
                {
                    26,
                    25,
                    nil,
                    23
                },
                {
                    26,
                    nil,
                    nil,
                    24
                },
                {
                    27,
                    nil,
                    23,
                    21
                },
                {
                    28,
                    nil,
                    26,
                    21
                },
                {
                    32,
                    29,
                    27,
                    33
                },
                {
                    nil,
                    30,
                    28,
                    32
                },
                {
                    [4] = 29
                },
                {
                    nil,
                    30,
                    32
                },
                {
                    31,
                    29,
                    28,
                    33
                },
                {
                    32,
                    28,
                    21
                }
            }
        },
        {
            available_towers = {
                "tower_build_arcane_wizard",
                "tower_build_tricannon"
            },
            locked_towers = {
                "tower_build_arborean_emissary",
                "tower_build_ballista",
                "tower_build_paladin_covenant",
                "tower_build_sand",
                "tower_build_demon_pit",
                "tower_build_elven_stargazers",
                "tower_build_rocket_gunners",
                "tower_build_barrel",
                "tower_build_royal_archers",
                "tower_build_ray",
                "tower_build_ghost",
                "tower_build_necromancer",
                "tower_build_hermit_toad"
            },
            nav_mesh = {
                [41] = {
                    53,
                    46,
                    42
                },
                [42] = {
                    41,
                    43
                },
                [43] = {
                    46,
                    44,
                    42
                },
                [44] = {
                    46,
                    45,
                    nil,
                    43
                },
                [45] = {
                    46,
                    nil,
                    nil,
                    44
                },
                [46] = {
                    47,
                    45,
                    43,
                    41
                },
                [47] = {
                    48,
                    nil,
                    46,
                    41
                },
                [48] = {
                    52,
                    49,
                    47,
                    53
                },
                [49] = {
                    nil,
                    50,
                    48,
                    52
                },
                [50] = {
                    [4] = 49
                },
                [51] = {
                    nil,
                    50,
                    52
                },
                [52] = {
                    51,
                    49,
                    48,
                    53
                },
                [53] = {
                    52,
                    48,
                    41
                }
            }
        }
    },
    level_terrain_type = 5,
    locked_hero = false,
    max_upgrade_level = 5,
    nav_mesh = {
        {
            13,
            6,
            2
        },
        {
            1,
            3
        },
        {
            6,
            4,
            nil,
            2
        },
        {
            6,
            5,
            nil,
            3
        },
        {
            6,
            nil,
            nil,
            4
        },
        {
            7,
            5,
            3,
            1
        },
        {
            8,
            nil,
            6,
            1
        },
        {
            12,
            9,
            7,
            13
        },
        {
            nil,
            10,
            8,
            12
        },
        {
            [4] = 9
        },
        {
            nil,
            10,
            nil,
            12
        },
        {
            11,
            9,
            8,
            13
        },
        {
            12,
            8,
            1
        }
    },
    required_exoskeletons = {
        "stage31_wood_holder_cuernosDef",
        "stage31_wood_holder_dragon_rootDef",
        "stage31_wood_holder_dragonDef",
        "stage31_wood_holder_gradienteDef",
        "stage31_wood_holder_habilidad_1Def",
        "stage31_wood_holder_jarraDef",
        "stage31_wood_holder_jarrahojasDef",
        "stage31_wood_holder_rayo_explosionDef",
        "stage31_wood_holder_rayoDef",
        "stage31_wood_holder_root1Def",
        "stage31_wood_holder_root2Def",
        "stage31_wood_holder_root3Def",
        "stage31_wood_holder_root4Def",
        "stage31_wood_holder_animations_parcheDef",
        "fireholder_cuernosDef",
        "fireholder_dragonDef",
        "fireholder_dragon_executionDef",
        "fireholder_dragon_rootDef",
        "fireholder_gradienteDef",
        "fireholder_habilidad_1Def",
        "fireholder_jarraDef",
        "fireholder_jarrahojasDef",
        "fireholder_rayoDef",
        "fireholder_rayo_explosionDef",
        "fireholder_dragon_executionDef",
        "dirtholder_cuernosDef",
        "dirtholder_dragonDef",
        "dirtholder_gradienteDef",
        "dirtholder_habilidad_1Def",
        "dirtholder_jarraDef",
        "dirtholder_jarrahojasDef",
        "dirtholder_parcheDef",
        "dirtholder_rayo_explosionDef",
        "dirtholder_rayoDef",
        "stage33_water_mistDef",
        "stage33_water_holder_animations_parcheDef",
        "stage33_water_holder_cuernosDef",
        "stage33_water_holder_dragonDef",
        "stage33_water_holder_gradienteDef",
        "stage33_water_holder_habilidad_1Def",
        "stage33_water_holder_healDef",
        "stage33_water_holder_jarraDef",
        "stage33_water_holder_jarrahojasDef",
        "stage33_water_holder_rayo_explosionDef",
        "stage33_water_holder_rayoDef",
        "stage33_water_dragonflyDef",
        "stage33_water_dragonrootDef",
        "stage33_water_debuff_midDef",
        "ironfan_stage5Def",
        "redboy_stage5Def",
        "stage_35_fireball_lDef",
        "stage_35_fireball_rDef",
        "stage5_samadhi_1Def",
        "stage5_samadhi_2Def",
        "stage_35_boss_bull_1Def",
        "stage_35_boss_bull_2Def",
        "stage_35_domoDef",
        "stage_35_fx_animeDef",
        "stage_35_hitDef",
        "stage_35_stun_towerDef",
        "goldholder_coin_splashDef",
        "goldholder_cuernosDef",
        "goldholder_dragon_rootDef",
        "goldholder_dragonDef",
        "goldholder_gradienteDef",
        "goldholder_habilidad_1Def",
        "goldholder_jarraDef",
        "goldholder_jarrahojasDef",
        "goldholder_rayo_explosionDef",
        "goldholder_rayoDef",
        "stage_5_puerta_princessDef",
        "stage_5_puerta_redboyDef",
        "stage_5_spawnerDef",
        "stage_5_splash_aguaDef",
        "stage_5_splash_lavaDef",
        "stage_35_stun_unitDef",
        "stage_35_areaattackDef",
        "stage5_destruccion_holderDef",
        "stage_5_cinematicaDef",
        "spawner_golden_beastDef"
    },
    required_sounds = {
        "music_stage35",
        "enemies_terrain_wukong_3",
        "stage_35",
        "terrain_wukong_common",
        "tower_rocket_gunners"
    },
    required_textures = {
        "go_stage35_bg",
        "go_stage35",
        "go_enemies_terrain_8_1",
        "go_enemies_terrain_8_2",
        "go_enemies_terrain_8_3",
        "go_towers_rocket_gunners",
        "go_towers_paladin_covenant"
    }
}
