local a = {

    goldeneye_beast_hit_run = {from=1, to=6, prefix='goldeneye_beast_hit'},

    goldeneye_beast_shadow_run = {from=1, to=1, prefix='goldeneye_beast_shadow'},

    goldeneye_beast_creep_idle = {from=1, to=28, prefix='goldeneye_beast_creep'},
    goldeneye_beast_creep_spawn_up = {from=29, to=30, prefix='goldeneye_beast_creep'},
    goldeneye_beast_creep_spawn_down = {from=31, to=32, prefix='goldeneye_beast_creep'},
    goldeneye_beast_creep_spawn_in = {from=33, to=38, prefix='goldeneye_beast_creep'},
    goldeneye_beast_creep_walk = {from=39, to=62, prefix='goldeneye_beast_creep'},
    goldeneye_beast_creep_walkdown = {from=63, to=86, prefix='goldeneye_beast_creep'},
    goldeneye_beast_creep_walkup = {from=87, to=110, prefix='goldeneye_beast_creep'},
    goldeneye_beast_creep_attack = {from=111, to=162, prefix='goldeneye_beast_creep'},
    goldeneye_beast_creep_skill = {from=163, to=206, prefix='goldeneye_beast_creep'},
    goldeneye_beast_creep_death = {from=207, to=242, prefix='goldeneye_beast_creep'},

    goldeneye_beast_mod_loop = {from=1, to=22, prefix='darksteel_anvil_skill_FX'},
}

return a
    
